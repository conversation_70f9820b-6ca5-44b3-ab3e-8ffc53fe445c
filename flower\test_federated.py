import subprocess
import time
import os
import sys
import threading
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("test.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("FederatedTest")

def run_server():
    """启动服务器"""
    logger.info("Starting server...")
    server_process = subprocess.Popen(
        ["python", "server.py"],
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True
    )
    
    # 等待服务器启动
    time.sleep(5)
    logger.info("Server started")
    return server_process

def run_client(port):
    """启动客户端"""
    logger.info(f"Starting client on port {port}...")
    client_process = subprocess.Popen(
        ["python", "client.py", "--port", str(port), "--direct"],
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True
    )
    logger.info(f"Client on port {port} started")
    return client_process

def log_output(process, name):
    """记录进程输出"""
    for line in process.stdout:
        logger.info(f"[{name}] {line.strip()}")

def main():
    """主函数"""
    try:
        # 启动服务器
        server_process = run_server()
        server_thread = threading.Thread(target=log_output, args=(server_process, "Server"))
        server_thread.daemon = True
        server_thread.start()
        
        # 等待服务器完全启动
        time.sleep(5)
        
        # 启动客户端
        client1_process = run_client(5001)
        client1_thread = threading.Thread(target=log_output, args=(client1_process, "Client1"))
        client1_thread.daemon = True
        client1_thread.start()
        
        client2_process = run_client(5002)
        client2_thread = threading.Thread(target=log_output, args=(client2_process, "Client2"))
        client2_thread.daemon = True
        client2_thread.start()
        
        # 等待训练完成
        logger.info("All processes started. Press Ctrl+C to stop...")
        while True:
            time.sleep(1)
    
    except KeyboardInterrupt:
        logger.info("Stopping all processes...")
        
        # 终止所有进程
        for process in [server_process, client1_process, client2_process]:
            if process and process.poll() is None:
                process.terminate()
                process.wait()
        
        logger.info("All processes stopped")
    
    except Exception as e:
        logger.error(f"Error in test script: {e}")
        logger.exception("Full traceback:")

if __name__ == "__main__":
    main() 