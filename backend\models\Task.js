/**
 * 任务数据模型
 */
const { query } = require('../db/init');
const Log = require('./Log');
const axios = require('axios');
const Participant = require('./Participant');
const Transaction = require('./Transaction');

class Task {
  /**
   * 获取所有任务
   * @returns {Promise<Array>} 任务列表
   */
  static async getAll() {
    try {
      return await query('SELECT * FROM tasks ORDER BY created_at DESC');
    } catch (error) {
      console.error('获取所有任务失败:', error);
      throw error;
    }
  }

  /**
   * 按状态获取任务
   * @param {string} status 状态
   * @returns {Promise<Array>} 任务列表
   */
  static async getByStatus(status) {
    try {
      return await query('SELECT * FROM tasks WHERE status = ? ORDER BY created_at DESC', [status]);
    } catch (error) {
      console.error(`获取状态为 ${status} 的任务失败:`, error);
      throw error;
    }
  }

  /**
   * 根据ID获取任务
   * @param {number} id 任务ID
   * @returns {Promise<Object>} 任务信息
   */
  static async getById(id) {
    try {
      const tasks = await query('SELECT * FROM tasks WHERE id = ?', [id]);
      return tasks.length > 0 ? tasks[0] : null;
    } catch (error) {
      console.error(`获取任务(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 创建新任务
   * @param {Object} taskData 任务数据
   * @returns {Promise<Object>} 创建的任务信息
   */
  static async create(taskData) {
    try {
      console.log('Task.create接收到的数据:', taskData);
      
      // 准备基本参数
      const name = taskData.name || '';
      const description = taskData.description || '';
      const status = taskData.status || 'pending';
      const data_type = taskData.data_type || 'image';
      const total_rounds = taskData.total_rounds || 5;
      const required_participants = taskData.required_participants || 2;
      const learning_rate = taskData.learning_rate || 0.001;
      const batch_size = taskData.batch_size || 32;
      const timeout = taskData.timeout || 60;
      const current_round = taskData.current_round || 0;
      const reward = taskData.reward || 0;
      const reward_distribution = taskData.reward_distribution || 'equal';
      
      // 支付相关参数
      const payment_currency = taskData.payment_currency || 'USD';
      const installment_payment = taskData.installment_payment !== undefined ? taskData.installment_payment : true;
      const first_payment_rate = taskData.first_payment_rate || 40;
      const platform_fee_rate = taskData.platform_fee_rate || 10;
      const payment_status = taskData.payment_status || 'pending';
      const payment_method = taskData.payment_method || null;
      
      // 确保所有参数都有默认值
      console.log('准备插入数据:', {
        name, description, status, data_type, 
        total_rounds, required_participants,
        learning_rate, batch_size, timeout,
        current_round, reward, reward_distribution,
        payment_currency, installment_payment, first_payment_rate,
        platform_fee_rate, payment_status, payment_method
      });

      // 插入任务数据
      const result = await query(
        `INSERT INTO tasks 
         (name, description, status, data_type, total_rounds, 
          required_participants, learning_rate, batch_size, timeout, current_round,
          reward, reward_distribution, payment_currency, installment_payment, 
          first_payment_rate, platform_fee_rate, payment_status, payment_method,
          created_at) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
        [
          name, description, status, data_type, 
          total_rounds, required_participants,
          learning_rate, batch_size, timeout,
          current_round, reward, reward_distribution,
          payment_currency, installment_payment ? 1 : 0, 
          first_payment_rate, platform_fee_rate, payment_status, payment_method
        ]
      );

      // 获取新创建的任务
      const createdTask = await this.getById(result.insertId);
      
      // 如果任务状态为已发布且支付状态为已完成首期付款，则创建交易记录
      if (status === 'published' && payment_status === 'first_payment_completed') {
        // 这里应该从认证中获取用户ID，暂时使用模拟数据
        const userId = taskData.user_id || 1;
        
        // 创建首期付款交易
        await Transaction.createFirstPayment(createdTask, userId);
      }
      
      return createdTask;
    } catch (error) {
      console.error('创建任务失败:', error);
      throw error;
    }
  }

  /**
   * 更新任务
   * @param {number} id 任务ID
   * @param {Object} taskData 更新数据
   * @returns {Promise<boolean>} 更新结果
   */
  static async update(id, taskData) {
    try {
      // 构建更新字段和值
      const updateFields = [];
      const updateValues = [];
      
      // 遍历taskData的所有键值对，构建更新语句
      Object.entries(taskData).forEach(([key, value]) => {
        // 跳过id字段
        if (key === 'id') return;
        
        // 将驼峰命名转换为下划线命名
        const fieldName = key.replace(/([A-Z])/g, '_$1').toLowerCase();
        
        updateFields.push(`${fieldName} = ?`);
        updateValues.push(value);
      });
      
      // 添加更新时间
      updateFields.push('updated_at = NOW()');
      
      // 如果没有可更新的字段，直接返回
      if (updateFields.length === 0) {
        return false;
      }
      
      // 添加ID作为条件
      updateValues.push(id);
      
      // 执行更新操作
      const result = await query(
        `UPDATE tasks SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );
      
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`更新任务(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 更新任务状态
   * @param {number} id 任务ID
   * @param {string} status 新状态
   * @returns {Promise<boolean>} 更新结果
   */
  static async updateStatus(id, status) {
    try {
      const result = await query(
        'UPDATE tasks SET status = ?, updated_at = NOW() WHERE id = ?',
        [status, id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`更新任务(ID: ${id})状态失败:`, error);
      throw error;
    }
  }

  /**
   * 更新任务支付状态
   * @param {number} id 任务ID
   * @param {string} paymentStatus 新支付状态
   * @returns {Promise<boolean>} 更新结果
   */
  static async updatePaymentStatus(id, paymentStatus) {
    try {
      const result = await query(
        'UPDATE tasks SET payment_status = ?, updated_at = NOW() WHERE id = ?',
        [paymentStatus, id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`更新任务(ID: ${id})支付状态失败:`, error);
      throw error;
    }
  }

  /**
   * 开始训练任务
   * @param {number} id 任务ID
   * @returns {Promise<boolean>} 更新结果
   */
  static async startTraining(id) {
    try {
      console.log(`正在启动任务(ID: ${id})的训练...`);
      await Log.task(`开始启动训练任务`, id);
      
      // 获取任务详情
      const task = await this.getById(id);
      if (!task) {
        throw new Error(`任务(ID: ${id})不存在`);
      }
      
      // 获取任务参与者
      const participants = await this.getParticipants(id);
      console.log(`任务(ID: ${id})的总参与者数量: ${participants.length}`);
      
      // 过滤出已批准的参与者（状态不是registered和rejected的）
      const approvedParticipants = participants.filter(p => p.status !== 'registered' && p.status !== 'rejected');
      console.log(`任务(ID: ${id})的已批准参与者数量: ${approvedParticipants.length}`);
      
      // 准备训练配置
      const trainingConfig = {
        taskId: id,
        rounds: task.total_rounds,
        participants: approvedParticipants.map(p => p.client_address),
        minParticipants: task.required_participants || approvedParticipants.length,
        learningRate: task.learning_rate,
        batchSize: task.batch_size,
        timeout: task.timeout || 60,
        dataType: task.data_type
      };
      
      console.log(`发送训练配置到Flower服务器:`, trainingConfig);
      
      try {
        // 先检查Flower服务器是否在运行
        console.log('检查Flower服务器状态...');
        
        try {
          // 尝试获取服务器状态
          const statusResponse = await axios.get(
            'http://localhost:5000/api/server/status',
            { timeout: 5000 }
          );
          console.log('Flower服务器状态:', statusResponse.data);
          await Log.task(`Flower服务器状态: ${JSON.stringify(statusResponse.data)}`, id);
        } catch (statusError) {
          console.error('Flower服务器状态检查失败:', statusError.message);
          await Log.error(`Flower服务器状态检查失败: ${statusError.message}`, id);
          
          // 即使状态检查失败，我们仍然尝试启动训练
          console.log('尽管状态检查失败，仍将尝试启动训练');
        }
        
        // 发送训练配置到Flower服务器
        const response = await axios.post(
          'http://localhost:5000/api/server/start', 
          trainingConfig, 
          { 
            headers: { 'Content-Type': 'application/json' },
            timeout: 10000 // 10秒超时
          }
        );
        
        console.log('Flower服务器响应:', response.data);
        await Log.task(`成功通知Flower服务器启动训练: ${JSON.stringify(response.data)}`, id);
        
        // 通知所有已批准的参与者开始训练
        for (const participant of approvedParticipants) {
          try {
            // 将参与者状态更新为training
            await Participant.updateStatus(participant.id, 'training');
            await Log.participant(`参与者(${participant.client_address})状态已更新为training`, id);
            
            // 这里可以添加额外的通知逻辑，例如向客户端发送WebSocket消息或推送通知
            console.log(`已通知参与者 ${participant.client_address} 开始训练`);
          } catch (err) {
            console.error(`更新参与者(ID: ${participant.id})状态失败:`, err);
            await Log.error(`更新参与者(${participant.client_address})状态失败: ${err.message}`, id);
            // 继续处理其他参与者，不中断整个流程
          }
        }
      } catch (flowerError) {
        console.error('通知Flower服务器失败:', flowerError.message);
        await Log.error(`通知Flower服务器失败: ${flowerError.message}`, id);
        
        // 检查是否为连接问题
        if (flowerError.code === 'ECONNREFUSED' || flowerError.code === 'ETIMEDOUT') {
          console.error('无法连接到Flower服务器，请确保服务器已启动');
          await Log.error(`无法连接到Flower服务器(${flowerError.code})，请确保服务器已启动`, id);
          
          // 提示如何启动Flower服务器
          console.log('可以使用以下命令启动Flower服务器:');
          console.log('python start_system.py server');
          await Log.task('建议使用命令 "python start_system.py server" 启动Flower服务器', id);
        }
        
        // 即使通知Flower服务器失败，我们仍然更新数据库中的状态
        console.log('尽管Flower服务器通知失败，仍将更新任务状态为进行中');
        await Log.task('尽管Flower服务器通知失败，仍将更新任务状态为进行中', id);
      }
      
      // 更新任务状态为"in_progress"
      const now = new Date();
      const result = await query(
        'UPDATE tasks SET status = ?, start_time = ?, current_round = ? WHERE id = ?',
        ['in_progress', now, 0, id]
      );
      
      if (result.affectedRows > 0) {
        console.log(`任务(ID: ${id})已成功启动训练`);
        await Log.task(`任务状态已更新为"正在进行"并已开始训练`, id);
        return true;
      } else {
        console.error(`任务(ID: ${id})状态更新失败`);
        await Log.error(`任务状态更新失败`, id);
        return false;
      }
    } catch (error) {
      console.error(`启动任务(ID: ${id})训练失败:`, error);
      await Log.error(`启动训练失败: ${error.message}`, id);
      throw error;
    }
  }

  /**
   * 完成训练任务
   * @param {number} id 任务ID
   * @returns {Promise<boolean>} 更新结果
   */
  static async completeTraining(id) {
    try {
      const now = new Date();
      const result = await query(
        'UPDATE tasks SET status = ?, end_time = ? WHERE id = ?',
        ['completed', now, id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`完成任务(ID: ${id})训练失败:`, error);
      throw error;
    }
  }

  /**
   * 停止训练任务
   * @param {number} id 任务ID
   * @returns {Promise<boolean>} 更新结果
   */
  static async stopTraining(id) {
    try {
      const now = new Date();
      const result = await query(
        'UPDATE tasks SET status = ?, end_time = ? WHERE id = ?',
        ['stopped', now, id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`停止任务(ID: ${id})训练失败:`, error);
      throw error;
    }
  }

  /**
   * 更新任务当前轮次
   * @param {number} id 任务ID
   * @param {number} round 当前轮次
   * @returns {Promise<boolean>} 更新结果
   */
  static async updateCurrentRound(id, round) {
    try {
      const result = await query(
        'UPDATE tasks SET current_round = ? WHERE id = ?',
        [round, id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`更新任务(ID: ${id})当前轮次失败:`, error);
      throw error;
    }
  }

  /**
   * 删除任务
   * @param {number} id 任务ID
   * @returns {Promise<boolean>} 删除结果
   */
  static async delete(id) {
    try {
      const result = await query('DELETE FROM tasks WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`删除任务(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 获取任务参与者
   * @param {number} taskId 任务ID
   * @returns {Promise<Array>} 参与者列表
   */
  static async getParticipants(taskId) {
    try {
      // 修改后的逻辑：返回所有参与者，包括registered状态
      return await query(
        'SELECT * FROM participants WHERE task_id = ?', 
        [taskId]
      );
    } catch (error) {
      console.error(`获取任务(ID: ${taskId})参与者失败:`, error);
      throw error;
    }
  }

  /**
   * 获取进行中的任务
   * @returns {Promise<Array>} 进行中的任务列表
   */
  static async getActiveTask() {
    try {
      const tasks = await query('SELECT * FROM tasks WHERE status = ? LIMIT 1', ['in_progress']);
      return tasks.length > 0 ? tasks[0] : null;
    } catch (error) {
      console.error('获取活跃任务失败:', error);
      throw error;
    }
  }

  /**
   * 获取任务训练历史
   * @param {number} taskId 任务ID
   * @returns {Promise<Array>} 训练历史记录
   */
  static async getTrainingHistory(taskId) {
    try {
      return await query('SELECT * FROM metrics WHERE task_id = ? ORDER BY round ASC', [taskId]);
    } catch (error) {
      console.error(`获取任务(ID: ${taskId})训练历史失败:`, error);
      throw error;
    }
  }

  /**
   * 完成模型下载和二期付款（标准支付方式）
   * @param {string} taskId - 任务ID
   * @param {string} userId - 用户ID
   * @returns {object} 交易记录
   */
  static async completeModelDownload(taskId, userId) {
    try {
      // 获取任务信息
      const task = await this.getById(taskId);
      
      if (!task) {
        throw new Error('任务不存在');
      }
      
      if (task.status !== 'completed') {
        throw new Error('任务未完成，无法处理模型下载');
      }
      
      if (task.payment_status !== 'first_payment_completed' && task.payment_status !== 'pending_second_payment') {
        throw new Error('首付款状态异常，无法处理二期付款');
      }
      
      // 创建二期付款交易
      const transaction = await Transaction.createSecondPayment(task, userId);
      
      // 更新任务状态
      await this.updatePaymentStatus(taskId, 'second_payment_completed');
      
      console.log(`完成了任务${taskId}的第二期付款，交易ID: ${transaction.id}`);
      
      return transaction;
    } catch (error) {
      console.error('处理模型下载付款失败:', error);
      throw error;
    }
  }

  /**
   * 使用区块链支付完成模型下载和二期付款
   * @param {string} taskId - 任务ID
   * @param {string} userId - 用户ID
   * @param {string} txHash - 区块链交易哈希
   * @returns {object} 交易记录
   */
  static async completeModelDownloadWithBlockchain(taskId, userId, txHash) {
    try {
      console.log(`开始处理区块链支付的模型下载请求 - 任务ID: ${taskId}, 用户ID: ${userId}, 交易哈希: ${txHash}`);
      
      // 验证参数
      if (!taskId) throw new Error('任务ID不能为空');
      if (!userId) throw new Error('用户ID不能为空');
      if (!txHash) throw new Error('区块链交易哈希不能为空');
      
      // 获取任务信息
      const task = await this.getById(taskId);
      console.log(`获取到任务信息:`, JSON.stringify(task));
      
      if (!task) {
        throw new Error(`任务不存在 (ID: ${taskId})`);
      }
      
      if (task.status !== 'completed') {
        throw new Error(`任务未完成，当前状态: ${task.status}`);
      }
      
      // 检查支付状态
      console.log(`当前任务支付状态: ${task.payment_status}`);
      if (task.payment_status !== 'first_payment_completed' && task.payment_status !== 'pending_second_payment') {
        throw new Error(`首付款状态异常，当前支付状态: ${task.payment_status}`);
      }
      
      // 使用Transaction.createBlockchainSecondPayment创建交易记录
      console.log(`调用Transaction.createBlockchainSecondPayment创建交易记录`);
      const transaction = await Transaction.createBlockchainSecondPayment(task, userId, txHash);
      
      if (!transaction) {
        throw new Error('创建交易记录失败，未返回交易数据');
      }
      
      // 更新任务状态
      console.log(`更新任务支付状态为: second_payment_completed`);
      await this.update(
        taskId,
        {
          payment_status: 'second_payment_completed',
          blockchain_second_payment_tx: txHash,
          updated_at: new Date()
        }
      );
      
      console.log(`使用区块链钱包完成了任务${taskId}的第二期付款，交易ID: ${transaction.id}`);
      
      return transaction;
    } catch (error) {
      console.error(`使用区块链处理模型下载付款失败:`, error);
      console.error('错误详情:', error.stack);
      throw new Error(`区块链支付处理失败: ${error.message}`);
    }
  }

  /**
   * 分配参与者奖励
   * @param {number} id 任务ID
   * @param {Array<Object>} participants 参与者列表，包含贡献度信息
   * @returns {Promise<Array>} 交易结果列表
   */
  static async distributeRewards(id, participants) {
    try {
      // 获取任务详情
      const task = await this.getById(id);
      
      if (!task) {
        throw new Error('任务不存在');
      }
      
      // 检查任务状态和支付状态
      if (task.status !== 'completed') {
        throw new Error('只有已完成的任务才能分配奖励');
      }
      
      if (task.payment_status !== 'all_payments_completed') {
        throw new Error('需要完成所有付款后才能分配奖励');
      }
      
      // 根据奖励分配方式计算每个参与者的奖励
      let contributionsList = [];
      
      if (task.reward_distribution === 'equal') {
        // 平均分配
        const equalContribution = 100 / participants.length;
        contributionsList = participants.map(participant => ({
          ...participant,
          contributionPercentage: equalContribution
        }));
      } else if (task.reward_distribution === 'contribution') {
        // 按贡献度分配
        // 假设参与者对象中有contribution字段表示贡献度
        const totalContribution = participants.reduce((sum, p) => sum + (p.contribution || 0), 0);
        contributionsList = participants.map(participant => ({
          ...participant,
          contributionPercentage: totalContribution > 0 
            ? ((participant.contribution || 0) / totalContribution) * 100
            : 100 / participants.length
        }));
      } else if (task.reward_distribution === 'data') {
        // 按数据量分配
        // 假设参与者对象中有dataSize字段表示数据量
        const totalDataSize = participants.reduce((sum, p) => sum + (p.dataSize || 0), 0);
        contributionsList = participants.map(participant => ({
          ...participant,
          contributionPercentage: totalDataSize > 0 
            ? ((participant.dataSize || 0) / totalDataSize) * 100
            : 100 / participants.length
        }));
      }
      
      // 创建所有参与者的奖励交易
      const transactions = [];
      
      for (const participant of contributionsList) {
        const transaction = await Transaction.createParticipantReward(
          participant,
          task,
          participant.contributionPercentage
        );
        transactions.push(transaction);
      }
      
      // 更新任务状态
      await this.updatePaymentStatus(id, 'rewards_distributed');
      
      return transactions;
    } catch (error) {
      console.error(`分配任务(ID: ${id})奖励失败:`, error);
      throw error;
    }
  }

  /**
   * 获取统计数据
   * @returns {Promise<Object>} 统计数据
   */
  static async getStatistics() {
    try {
      const totalTasks = await query('SELECT COUNT(*) as count FROM tasks');
      const activeTasks = await query('SELECT COUNT(*) as count FROM tasks WHERE status IN (?, ?)', ['pending', 'in_progress']);
      const completedTasks = await query('SELECT COUNT(*) as count FROM tasks WHERE status = ?', ['completed']);
      const totalReward = await query('SELECT SUM(reward) as sum FROM tasks');

      return {
        totalTasks: totalTasks[0].count || 0,
        activeTasks: activeTasks[0].count || 0,
        completedTasks: completedTasks[0].count || 0,
        totalReward: totalReward[0].sum || 0
      };
    } catch (error) {
      console.error('获取任务统计数据失败:', error);
      throw error;
    }
  }
}

module.exports = Task; 