import * as ecc from 'tiny-secp256k1';
import { ECPairFactory } from 'ecpair';
import { sha3_256 } from 'js-sha3';

const ECPair = ECPairFactory(ecc);

// 使用浏览器兼容的随机数生成方法
function getRandomBytes(size) {
  const array = new Uint8Array(size);
  window.crypto.getRandomValues(array);
  return array;
}

// 生成密钥对
export function generateKeyPair() {
const privateKey = getRandomBytes(32);
  const keyPair = ECPair.fromPrivateKey(privateKey);
  
  return {
    privateKey: keyPair.privateKey.toString('hex'),
    publicKey: keyPair.publicKey.toString('hex')
  };
}

// 加密消息
export function encryptMessage(publicKey, message) {
  // TODO: 实现加密逻辑
  return message;
}

// 解密消息
export function decryptMessage(privateKey, encryptedMessage) {
  // TODO: 实现解密逻辑
  return encryptedMessage;
}

// 签名消息
export function signMessage(privateKey, message) {
  const keyPair = ECPair.fromPrivateKey(Buffer.from(privateKey, 'hex'));
  const signature = keyPair.sign(Buffer.from(message));
  return signature.toString('hex');
}

// 验证签名
export function verifySignature(publicKey, message, signature) {
  const keyPair = ECPair.fromPublicKey(Buffer.from(publicKey, 'hex'));
  return keyPair.verify(
    Buffer.from(message),
    Buffer.from(signature, 'hex')
  );
}

// keccak256 哈希函数
export function keccak256(data) {
return sha3_256(data);
}
