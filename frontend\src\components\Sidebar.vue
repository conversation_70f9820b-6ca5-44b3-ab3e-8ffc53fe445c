<template>
  <div class="sidebar">
    <!-- Logo和名字 -->
    <div class="sidebar-header">
      <div class="logo-container">
        <img src="@/assets/logo.png" alt="Logo" class="logo">
        <span class="logo-text">数据交易平台</span>
      </div>
    </div>

    <!-- 主导航菜单 -->
    <nav class="sidebar-nav">
      <!-- 服务端菜单 -->
      <template v-if="userRole === 'server'">
        <router-link to="/server" class="nav-item" :class="{ 'active': currentRoute === '/server' }" exact title="控制台首页">
          <div class="nav-indicator"></div>
          <i class="fas fa-tachometer-alt"></i>
          <span>控制台首页</span>
        </router-link>
        
        <div class="nav-group">
          <div class="nav-group-title">任务管理</div>
          <router-link to="/server/tasks" class="nav-item" :class="{ 'active': currentRoute.includes('/server/tasks') && !currentRoute.includes('/create') }" title="训练任务">
            <div class="nav-indicator"></div>
            <i class="fas fa-tasks"></i>
            <span>训练任务</span>
          </router-link>
          
          <router-link to="/server/tasks/create" class="nav-item" :class="{ 'active': currentRoute.includes('/server/tasks/create') }" title="创建任务">
            <div class="nav-indicator"></div>
            <i class="fas fa-plus-circle"></i>
            <span>创建任务</span>
          </router-link>
        </div>
        
        <router-link to="/server/client-management" class="nav-item" :class="{ 'active': currentRoute.includes('/server/client-management') }" title="客户端管理">
          <div class="nav-indicator"></div>
          <i class="fas fa-users"></i>
          <span>客户端管理</span>
        </router-link>
        
        <router-link to="/server/training" class="nav-item" :class="{ 'active': currentRoute.includes('/server/training') }" title="训练监控">
          <div class="nav-indicator"></div>
          <i class="fas fa-chart-line"></i>
          <span>训练监控</span>
        </router-link>
        
        <router-link to="/server/models" class="nav-item" :class="{ 'active': currentRoute.includes('/server/models') }" title="模型管理">
          <div class="nav-indicator"></div>
          <i class="fas fa-brain"></i>
          <span>模型管理</span>
        </router-link>
        
        <router-link to="/server/transactions" class="nav-item" :class="{ 'active': currentRoute.includes('/server/transactions') }" title="交易记录">
          <div class="nav-indicator"></div>
          <i class="fas fa-exchange-alt"></i>
          <span>交易记录</span>
        </router-link>
      </template>
      
      <!-- 客户端菜单 -->
      <template v-else-if="userRole === 'client'">
        <router-link to="/client" class="nav-item" :class="{ 'active': currentRoute === '/client' }" exact title="客户端首页">
          <div class="nav-indicator"></div>
          <i class="fas fa-home"></i>
          <span>客户端首页</span>
        </router-link>
        
        <router-link to="/client/available-tasks" class="nav-item" :class="{ 'active': currentRoute.includes('/client/available-tasks') }" title="可用任务">
          <div class="nav-indicator"></div>
          <i class="fas fa-list-alt"></i>
          <span>可用任务</span>
        </router-link>
        
        <router-link to="/client/my-applications" class="nav-item" :class="{ 'active': currentRoute.includes('/client/my-applications') }" title="我的申请">
          <div class="nav-indicator"></div>
          <i class="fas fa-clipboard-list"></i>
          <span>我的申请</span>
        </router-link>
        
        <router-link to="/client/my-tasks" class="nav-item" :class="{ 'active': currentRoute.includes('/client/my-tasks') }" title="我的任务">
          <div class="nav-indicator"></div>
          <i class="fas fa-tasks"></i>
          <span>我的任务</span>
        </router-link>
        
        <router-link to="/client/data-management" class="nav-item" :class="{ 'active': currentRoute.includes('/client/data-management') }" title="数据管理">
          <div class="nav-indicator"></div>
          <i class="fas fa-database"></i>
          <span>数据管理</span>
        </router-link>
        
        <router-link to="/client/contribution" class="nav-item" :class="{ 'active': currentRoute.includes('/client/contribution') }" title="我的贡献">
          <div class="nav-indicator"></div>
          <i class="fas fa-award"></i>
          <span>我的贡献</span>
        </router-link>
        
        <router-link to="/client/transactions" class="nav-item" :class="{ 'active': currentRoute.includes('/client/transactions') }" title="交易记录">
          <div class="nav-indicator"></div>
          <i class="fas fa-exchange-alt"></i>
          <span>交易记录</span>
        </router-link>
      </template>
    </nav>

    <!-- 用户信息 -->
    <div class="user-info">
      <div class="avatar">
        <img :src="userAvatar" alt="用户头像" />
      </div>
      <div class="user-details">
        <div class="username">{{ shortenAddress(userAddress) }}</div>
        <div class="role">{{ userRole === 'server' ? '服务端' : '客户端' }}</div>
      </div>
    </div>

    <div class="sidebar-footer">
      <button class="logout-btn" @click="handleLogout">
        <i class="fas fa-sign-out-alt"></i>
        <span>退出登录</span>
      </button>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';

export default {
  name: 'Sidebar',
  
  setup() {
    const store = useStore();
    const route = useRoute();
    const router = useRouter();

    const userAddress = computed(() => store.state.user.address);
    const userRole = computed(() => store.state.user.role);
    const currentRoute = computed(() => route.path);
    
    const userAvatar = computed(() => {
      if (!userAddress.value) return '';
      return `https://api.dicebear.com/7.x/avataaars/svg?seed=${userAddress.value}&backgroundColor=b6e3f4,c0aede,d1d4f9&radius=50&randomizeIds=true`;
    });

    const shortenAddress = (address) => {
      if (!address) return '';
      return `${address.slice(0, 6)}...${address.slice(-4)}`;
    };

    const switchRole = async (role) => {
      try {
        await store.dispatch('user/switchRole', role);
        // 根据角色切换路由
        router.push(role === 'server' ? '/server' : '/client');
      } catch (error) {
        console.error('切换角色失败:', error);
      }
    };

    const handleLogout = async () => {
      try {
        await store.dispatch('user/logout');
        router.push('/login');
      } catch (error) {
        console.error('退出登录失败:', error);
      }
    };

    return {
      userAddress,
      userRole,
      userAvatar,
      currentRoute,
      shortenAddress,
      switchRole,
      handleLogout
    };
  }
};
</script>

<style scoped>
.sidebar {
  width: 240px;
  height: 100vh;
  background: linear-gradient(135deg, #ffeef2 0%, #ffe0e6 100%);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 100;
  overflow-y: auto;
  color: #86666c;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.05);
  border-right: 1px solid rgba(255, 255, 255, 0.7);
}

.sidebar-header {
  padding: 24px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.4);
  min-height: 80px;
  position: relative;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(5px);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: flex-start;
  padding: 0 20px;
}

.logo {
  width: 42px;
  height: 42px;
  object-fit: contain;
  transition: all 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.logo-text {
  font-size: 1.4em;
  font-weight: 700;
  color: #ff8fa3;
  white-space: nowrap;
  background: linear-gradient(135deg, #ff8fa3 0%, #ff6b8b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 10px rgba(255, 143, 163, 0.2);
}

.nav-indicator {
  position: absolute;
  left: 0;
  top: 50%;
  bottom: auto;
  width: 3px;
  height: 0;
  background: #ff8fa3;
  transition: all 0.3s ease;
  border-radius: 0 4px 4px 0;
  transform: translateY(-50%);
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #86666c;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  font-size: 15px;
  margin: 3px 8px;
  border-radius: 12px;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background: linear-gradient(90deg, rgba(255, 143, 163, 0.1) 0%, rgba(255, 143, 163, 0) 100%);
  transition: width 0.3s ease;
  z-index: -1;
}

.nav-item:hover::before {
  width: 100%;
}

.nav-item i {
  font-size: 1.1em;
  width: 22px;
  text-align: center;
  margin-right: 14px;
  transition: all 0.3s ease;
  color: #9a7980;
  opacity: 0.9;
}

.nav-item span {
  display: block;
  transition: all 0.3s ease;
  position: relative;
  font-weight: 500;
  font-size: 14px;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.7);
  color: #ff6b8b;
  transform: translateX(2px);
}

.nav-item:hover i {
  color: #ff8fa3;
}

.nav-item.active {
  background: rgba(255, 255, 255, 0.85);
  color: #ff6b8b;
  box-shadow: 0 4px 10px rgba(255, 143, 163, 0.15);
  font-weight: 600;
}

.nav-item:hover .nav-indicator {
  background: #ffb3be;
}

.nav-item.active .nav-indicator {
  height: 70%;
  background: linear-gradient(to bottom, #ff8fa3, #ff6b8b);
  box-shadow: 0 0 8px rgba(255, 143, 163, 0.3);
}

.nav-item.active i {
  transform: scale(1.1);
  color: #ff6b8b;
  filter: drop-shadow(0 2px 3px rgba(255, 107, 139, 0.2));
}

.sidebar-nav {
  flex: 1;
  padding: 16px 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  overflow-y: auto;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.3) 100%);
}

.nav-group {
  margin: 4px 0 12px 0;
  position: relative;
}

.nav-group::after {
  content: '';
  position: absolute;
  left: 12px;
  top: 36px;
  bottom: 8px;
  width: 1px;
  background: linear-gradient(to bottom, rgba(255, 143, 163, 0.3), transparent);
}

.nav-group-title {
  padding: 0 20px;
  margin-bottom: 8px;
  margin-top: 4px;
  font-size: 13px;
  font-weight: 700;
  color: #9a7980;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
}

.nav-group-title::before {
  content: '';
  display: inline-block;
  width: 16px;
  height: 2px;
  background: linear-gradient(90deg, #ff8fa3, transparent);
  margin-right: 6px;
  border-radius: 2px;
}

.nav-group .nav-item {
  margin-left: 12px;
  padding-left: 22px;
}

.user-info {
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 14px;
  border-top: 1px solid rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
}

.avatar {
  width: 52px;
  height: 52px;
  border-radius: 16px;
  overflow: hidden;
  background: #f8f9fa;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid rgba(255, 255, 255, 0.7);
}

.avatar:hover {
  transform: scale(1.05) rotate(3deg);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 143, 163, 0.4);
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  flex: 1;
  min-width: 0;
}

.username {
  font-weight: 700;
  color: #86666c;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 1.05em;
}

.role {
  font-size: 0.9em;
  font-weight: 500;
  color: #b68e93;
  display: inline-block;
  padding: 3px 10px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 30px;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
}

.logout-btn {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.7);
  color: #ff6b8b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.03);
  font-size: 1.05em;
}

.logout-btn:hover {
  background: linear-gradient(135deg, #ff8fa3 0%, #ff6b8b 100%);
  color: white;
  box-shadow: 0 6px 12px rgba(255, 143, 163, 0.4);
  transform: translateY(-2px);
}

.logout-btn i {
  font-size: 1.1em;
}

/* 动画效果 */
.sidebar * {
  transition: all 0.3s ease;
}

/* 添加自定义滚动条 */
.sidebar-nav::-webkit-scrollbar {
  width: 6px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(255, 143, 163, 0.3);
  border-radius: 10px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 143, 163, 0.5);
}

@media (max-width: 768px) {
  .sidebar {
    width: 240px;
  }
  
  .logo-container {
    justify-content: center;
  }
  
  .nav-item {
    padding: 16px;
  }
}

/* 角色选择器样式 */
.role-selector {
  padding: 16px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  display: flex;
  gap: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
}

.role-btn {
  flex: 1;
  padding: 8px;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: #86666c;
  cursor: pointer;
  transition: all 0.3s ease;
}

.role-btn.active {
  background: rgba(255, 255, 255, 0.8);
  color: #ff8fa3;
  box-shadow: 0 2px 4px rgba(255, 143, 163, 0.1);
}

/* 分类标题 */
.nav-section-title {
  padding: 16px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9em;
  margin-top: 16px;
}

/* 未读消息标记 */
.notification-badge {
  background: #ff6b6b;
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.8em;
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
}

/* 新增导航组样式 */
.nav-group {
  margin: 8px 0;
}

.nav-group-title {
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 600;
  color: #555;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* 为了让任务管理下的子菜单更加协调，添加特殊样式 */
.nav-group .nav-item:first-of-type {
  margin-top: 2px;
}

/* 确保导航组标题和菜单项之间的间距适当 */
.nav-group-title + .nav-item {
  margin-top: 6px;
}
</style> 