<!-- 创建训练任务页面 -->
<template>
  <div class="create-task">
    <div class="page-header">
      <h1>创建训练任务</h1>
      <div class="page-actions">
        <el-button @click="goBack">
          <i class="fas fa-arrow-left"></i> 返回
        </el-button>
        <el-button type="success" @click="saveAsDraft" :loading="saving">
          保存为草稿
        </el-button>
        <el-button type="primary" @click="submitTask" :loading="submitting">
          发布任务
        </el-button>
      </div>
    </div>
    
    <Breadcrumb />
    
    <el-form
      :ref="el => taskFormRef = el"
      :model="taskForm"
      :rules="rules"
      label-width="120px"
      class="task-form"
    >
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <i class="fas fa-info-circle"></i>
            <span>基本信息</span>
          </div>
        </template>
        
        <el-form-item label="任务名称" prop="name">
          <el-input 
            v-model="taskForm.name" 
            placeholder="请输入任务名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="任务描述" prop="description">
          <el-input 
            v-model="taskForm.description" 
            type="textarea" 
            :rows="4"
            placeholder="请详细描述任务内容、目标和要求"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="数据类型" prop="dataType">
          <el-select v-model="taskForm.dataType" placeholder="请选择数据类型">
            <el-option label="图像数据" value="image" />
            <el-option label="文本数据" value="text" />
            <el-option label="表格数据" value="tabular" />
            <el-option label="音频数据" value="audio" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="截止日期" prop="deadline">
          <el-date-picker
            v-model="taskForm.deadline"
            type="date"
            placeholder="选择截止日期"
            :disabledDate="disabledDate"
          />
        </el-form-item>
      </el-card>
      
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <i class="fas fa-cog"></i>
            <span>训练配置</span>
          </div>
        </template>
        
        <el-form-item label="训练轮次" prop="totalRounds">
          <el-input-number 
            v-model="taskForm.totalRounds" 
            :min="1" 
            :max="100"
            controls-position="right"
          />
        </el-form-item>
        
        <el-form-item label="参与者数量" prop="requiredParticipants">
          <el-input-number 
            v-model="taskForm.requiredParticipants" 
            :min="2" 
            :max="100"
            controls-position="right"
          />
        </el-form-item>
        
        <el-form-item label="每轮超时时间" prop="timeout">
          <el-input-number 
            v-model="taskForm.timeout" 
            :min="10" 
            :max="3600"
            controls-position="right"
          />
          <span class="form-tip">秒</span>
        </el-form-item>
        
        <el-form-item label="学习率" prop="learningRate">
          <el-input-number 
            v-model="taskForm.learningRate" 
            :min="0.0001" 
            :max="0.1"
            :step="0.0001"
            :precision="4"
            controls-position="right"
          />
        </el-form-item>
        
        <el-form-item label="批次大小" prop="batchSize">
          <el-input-number 
            v-model="taskForm.batchSize" 
            :min="1" 
            :max="512"
            controls-position="right"
          />
        </el-form-item>
      </el-card>
      
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <i class="fas fa-list-ul"></i>
            <span>参与要求</span>
          </div>
        </template>
        
        <el-form-item 
          v-for="(requirement, index) in taskForm.requirements || []" 
          :key="index" 
          :label="index === 0 ? '要求列表' : ''"
          :prop="`requirements.${index}`"
          :rules="[
            { required: true, message: '请输入要求内容', trigger: 'blur' },
            { min: 5, message: '要求内容不能少于5个字符', trigger: 'blur' }
          ]"
        >
          <div class="requirement-item">
            <el-input 
              v-model="taskForm.requirements[index]" 
              placeholder="请输入参与要求"
            />
            <el-button 
              type="danger" 
              circle
              @click.prevent="removeRequirement(index)"
            >
              <i class="fas fa-times"></i>
            </el-button>
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="addRequirement" plain>
            <i class="fas fa-plus"></i> 添加要求
          </el-button>
        </el-form-item>
      </el-card>
      
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <i class="fas fa-coins"></i>
            <span>奖励设置</span>
          </div>
        </template>
        
        <el-form-item label="任务总奖励" prop="reward">
          <el-input-number 
            v-model="taskForm.reward" 
            :min="0" 
            :max="10000"
            controls-position="right"
          />
          <span class="form-tip">代币</span>
          <el-tooltip content="1代币 = 1USD" placement="top">
            <i class="fas fa-info-circle ml-2"></i>
          </el-tooltip>
        </el-form-item>
        
        <el-form-item label="支付币种" prop="paymentCurrency">
          <el-select v-model="taskForm.paymentCurrency" placeholder="请选择支付币种">
            <el-option label="USD (美元)" value="USD" />
            <el-option label="CNY (人民币)" value="CNY" />
          </el-select>
          <span class="form-tip ml-2" v-if="taskForm.paymentCurrency === 'CNY'">
            当前汇率: 1 USD ≈ {{ exchangeRate }} CNY
          </span>
        </el-form-item>
        
        <el-form-item label="分期付款" prop="installmentPayment">
          <el-switch v-model="taskForm.installmentPayment" />
          <span class="form-tip ml-2">开启后将分两期支付奖励</span>
        </el-form-item>
        
        <el-form-item v-if="taskForm.installmentPayment" label="首期付款比例" prop="firstPaymentRate">
          <el-slider 
            v-model="taskForm.firstPaymentRate" 
            :min="20" 
            :max="80" 
            :step="5"
            :format-tooltip="formatPaymentRate"
            :marks="{40: '40%'}"
          />
          <div class="payment-breakdown">
            <div class="payment-item">
              <span>首期付款(任务创建时):</span>
              <span>{{ formatCurrencyAmount(calculateFirstPayment()) }}</span>
            </div>
            <div class="payment-item">
              <span>二期付款(模型下载时):</span>
              <span>{{ formatCurrencyAmount(calculateSecondPayment()) }}</span>
            </div>
            <div class="payment-item">
              <span>平台服务费(10%):</span>
              <span>{{ formatCurrencyAmount(calculatePlatformFee()) }}</span>
            </div>
            <div class="payment-item total">
              <span>总支付金额:</span>
              <span>{{ formatCurrencyAmount(calculateTotalPayment()) }}</span>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="奖励分配" prop="rewardDistribution">
          <el-select v-model="taskForm.rewardDistribution" placeholder="请选择奖励分配方式">
            <el-option label="平均分配" value="equal" />
            <el-option label="按贡献度分配" value="contribution" />
            <el-option label="按数据量分配" value="data" />
          </el-select>
        </el-form-item>
      </el-card>
    </el-form>
    
    <div class="form-actions">
      <el-button @click="goBack">取消</el-button>
      <el-button type="success" @click="saveAsDraft" :loading="saving">
        保存为草稿
      </el-button>
      <el-button type="primary" @click="submitTask" :loading="submitting">
        发布任务
      </el-button>
    </div>
    
    <!-- 支付确认对话框 -->
    <el-dialog
      v-model="paymentDialogVisible"
      title="支付确认"
      width="500px"
    >
      <div class="payment-dialog-content">
        <div class="payment-summary">
          <h3>任务支付摘要</h3>
          <p class="task-name">{{ taskForm.name }}</p>
          
          <el-descriptions :column="1" border>
            <el-descriptions-item label="支付币种">
              {{ taskForm.paymentCurrency === 'USD' ? '美元 (USD)' : '人民币 (CNY)' }}
            </el-descriptions-item>
            <el-descriptions-item label="支付方式">
              {{ taskForm.installmentPayment ? '分期付款' : '一次性付款' }}
            </el-descriptions-item>
            <el-descriptions-item v-if="taskForm.installmentPayment" label="首期付款">
              {{ formatCurrencyAmount(calculateFirstPayment()) }} ({{ taskForm.firstPaymentRate }}%)
            </el-descriptions-item>
            <el-descriptions-item v-if="taskForm.installmentPayment" label="二期付款">
              {{ formatCurrencyAmount(calculateSecondPayment()) }} ({{ 100 - taskForm.firstPaymentRate }}%)
            </el-descriptions-item>
            <el-descriptions-item label="平台服务费">
              {{ formatCurrencyAmount(calculatePlatformFee()) }} (10%)
            </el-descriptions-item>
            <el-descriptions-item label="总计金额" class="total-amount">
              {{ formatCurrencyAmount(calculateTotalPayment()) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
        <div class="payment-methods">
          <h4>请选择支付方式</h4>
          <el-radio-group v-model="selectedPaymentMethod">
            <el-radio label="balance">账户余额</el-radio>
            <el-radio label="credit_card">信用卡</el-radio>
            <el-radio label="wallet">区块链钱包</el-radio>
            <el-radio v-if="taskForm.paymentCurrency === 'CNY'" label="alipay">支付宝</el-radio>
            <el-radio v-if="taskForm.paymentCurrency === 'CNY'" label="wechat">微信支付</el-radio>
            <el-radio v-if="taskForm.paymentCurrency === 'USD'" label="paypal">PayPal</el-radio>
          </el-radio-group>
          
          <div v-if="selectedPaymentMethod === 'wallet'" class="wallet-selection">
            <h5>选择区块链钱包</h5>
            <el-select v-model="selectedWallet" placeholder="请选择钱包">
              <el-option label="MetaMask" value="metamask" />
              <el-option label="WalletConnect" value="walletconnect" />
              <el-option label="Coinbase Wallet" value="coinbase" />
            </el-select>
            <div class="wallet-notice">
              <i class="fas fa-info-circle"></i>
              <span>将使用连接的钱包支付等值代币</span>
            </div>
          </div>
          
          <div class="payment-action">
            <el-button type="primary" @click="processPayment" :loading="processingPayment">
              确认支付
            </el-button>
            <el-button @click="paymentDialogVisible = false">取消</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import Breadcrumb from '@/components/Breadcrumb.vue';
import { TaskAPI } from '@/services/api';

export default {
  name: 'CreateTask',
  components: {
    Breadcrumb
  },
  setup() {
    const router = useRouter();
    const taskFormRef = ref(null);
    
    // 计算默认截止日期（当前日期+7天）
    const getDefaultDeadline = () => {
      const date = new Date();
      date.setDate(date.getDate() + 7);
      return date;
    };
    
    const taskForm = reactive({
      name: '',
      description: '',
      dataType: 'image', // 设置默认值避免验证失败
      deadline: getDefaultDeadline(), // 设置默认截止日期
      totalRounds: 5,
      requiredParticipants: 3,
      timeout: 60,
      learningRate: 0.001,
      batchSize: 32,
      requirements: [''], // 确保初始化为数组
      reward: 500,
      rewardDistribution: 'equal',
      // 新增支付相关字段
      paymentCurrency: 'USD',
      installmentPayment: true,
      firstPaymentRate: 40 // 默认首期付款40%
    });
    
    // 支付相关状态
    const exchangeRate = ref(7.2); // 美元兑人民币汇率
    const paymentDialogVisible = ref(false);
    const selectedPaymentMethod = ref('balance');
    const selectedWallet = ref('metamask');
    const processingPayment = ref(false);
    
    // 确保requirements始终是一个有效的数组
    onMounted(() => {
      if (!Array.isArray(taskForm.requirements) || taskForm.requirements.length === 0) {
        taskForm.requirements = [''];
      }
      
      // 这里可以添加获取最新汇率的API调用
      fetchExchangeRate();
    });
    
    // 获取当前汇率（模拟）
    const fetchExchangeRate = () => {
      // 实际项目中应该从外部API获取汇率
      setTimeout(() => {
        exchangeRate.value = 7.2;
      }, 500);
    };
    
    const rules = {
      name: [
        { required: true, message: '请输入任务名称', trigger: 'blur' },
        { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
      ],
      description: [
        { required: true, message: '请输入任务描述', trigger: 'blur' },
        { min: 10, max: 500, message: '长度在 10 到 500 个字符', trigger: 'blur' }
      ],
      dataType: [
        { required: true, message: '请选择数据类型', trigger: 'change' }
      ],
      deadline: [
        { required: true, message: '请选择截止日期', trigger: 'change' }
      ],
      totalRounds: [
        { required: true, message: '请输入训练轮次', trigger: 'blur' },
        { type: 'number', min: 1, message: '训练轮次必须大于0', trigger: 'blur' }
      ],
      requiredParticipants: [
        { required: true, message: '请输入参与者数量', trigger: 'blur' },
        { type: 'number', min: 2, message: '参与者数量必须大于1', trigger: 'blur' }
      ],
      rewardDistribution: [
        { required: true, message: '请选择奖励分配方式', trigger: 'change' }
      ],
      paymentCurrency: [
        { required: true, message: '请选择支付币种', trigger: 'change' }
      ]
    };
    
    // 格式化付款比率滑块提示
    const formatPaymentRate = (val) => {
      return `${val}%`;
    };
    
    // 计算首期付款金额
    const calculateFirstPayment = () => {
      return taskForm.reward * (taskForm.firstPaymentRate / 100);
    };
    
    // 计算二期付款金额
    const calculateSecondPayment = () => {
      return taskForm.reward * ((100 - taskForm.firstPaymentRate) / 100);
    };
    
    // 计算平台服务费
    const calculatePlatformFee = () => {
      return taskForm.reward * 0.1; // 10%的平台服务费
    };
    
    // 计算总支付金额
    const calculateTotalPayment = () => {
      return taskForm.reward + calculatePlatformFee();
    };
    
    // 格式化货币金额
    const formatCurrencyAmount = (amount) => {
      if (taskForm.paymentCurrency === 'USD') {
        return `$${amount.toFixed(2)}`;
      } else {
        return `¥${(amount * exchangeRate.value).toFixed(2)}`;
      }
    };
    
    const saving = ref(false);
    const submitting = ref(false);
    
    // 禁用过去的日期
    const disabledDate = (time) => {
      return time.getTime() < Date.now() - 8.64e7; // 今天之前的日期禁用
    };
    
    // 添加要求
    const addRequirement = () => {
      taskForm.requirements.push('');
    };
    
    // 移除要求
    const removeRequirement = (index) => {
      taskForm.requirements.splice(index, 1);
      if (taskForm.requirements.length === 0) {
        taskForm.requirements.push('');
      }
    };
    
    // 返回任务列表
    const goBack = () => {
      router.push('/server/tasks');
    };
    
    // 验证表单
    const validateTask = async () => {
      if (!taskFormRef.value) {
        ElMessage.warning('表单引用未初始化，请重试');
        return false;
      }
      
      // 额外确保requirements始终是一个有效的数组
      if (!Array.isArray(taskForm.requirements) || taskForm.requirements.length === 0) {
        taskForm.requirements = [''];
      }
      
      // 防御性编程：确保所有必填字段都有值
      if (!taskForm.name) taskForm.name = '';
      if (!taskForm.description) taskForm.description = '';
      if (!taskForm.dataType) taskForm.dataType = 'image';
      if (!taskForm.deadline) taskForm.deadline = getDefaultDeadline();
      
      return new Promise(resolve => {
        taskFormRef.value.validate((valid) => {
          if (!valid) {
            ElMessage.warning('请填写完整的任务信息');
            resolve(false);
          } else {
            resolve(true);
          }
        });
      });
    };
    
    // 构建任务数据
    const buildTaskData = () => {
      return {
        name: taskForm.name,
        description: taskForm.description,
        data_type: taskForm.dataType,
        status: taskForm.status || 'draft', // 前端状态
        deadline: taskForm.deadline,
        total_rounds: taskForm.totalRounds,
        required_participants: taskForm.requiredParticipants,
        timeout: taskForm.timeout,
        learning_rate: taskForm.learningRate,
        batch_size: taskForm.batchSize,
        reward: taskForm.reward,
        requirements: taskForm.requirements,
        reward_distribution: taskForm.rewardDistribution,
        current_round: 0,
        // 新增支付相关字段
        payment_currency: taskForm.paymentCurrency,
        installment_payment: taskForm.installmentPayment,
        first_payment_rate: taskForm.firstPaymentRate,
        platform_fee_rate: 10, // 平台服务费率10%
        first_payment_amount: calculateFirstPayment(),
        second_payment_amount: calculateSecondPayment(),
        platform_fee_amount: calculatePlatformFee(),
        total_payment_amount: calculateTotalPayment(),
        payment_status: 'pending' // 初始支付状态为待支付
      };
    };
    
    // 保存为草稿
    const saveAsDraft = async () => {
      try {
        // 至少需要填写任务名称
        if (!taskForm.name) {
          ElMessage.warning('请至少填写任务名称');
          return;
        }

        // 构建任务数据
        const taskData = buildTaskData();
        taskData.status = 'draft';

        // 保存到数据库
        console.log('保存草稿数据:', taskData);
        const result = await TaskAPI.create(taskData);
        console.log('保存草稿结果:', result);
        
        ElMessage.success('草稿已保存！');
        
        // 跳转回任务列表页
        router.push('/server/tasks');
      } catch (error) {
        console.error('保存草稿失败:', error);
        ElMessage.error('保存草稿失败，请重试');
      }
    };
    
    // 修改processPayment方法以处理区块链钱包支付
    const processPayment = async () => {
      try {
        processingPayment.value = true;
        
        // 处理区块链钱包支付
        if (selectedPaymentMethod.value === 'wallet') {
          // 连接钱包
          await connectWallet(selectedWallet.value);
          
          // 计算支付金额
          const paymentAmount = calculateFirstPayment() + calculatePlatformFee();
          
          // 发起区块链支付
          const txHash = await requestBlockchainPayment(paymentAmount);
          
          // 如果交易哈希为空，表示支付失败或用户取消
          if (!txHash) {
            ElMessage.warning('区块链支付已取消');
            processingPayment.value = false;
            return;
          }
          
          // 构建任务数据
          const taskData = buildTaskData();
          taskData.status = 'published';
          taskData.payment_status = 'first_payment_completed';
          taskData.payment_method = 'wallet';
          taskData.blockchain_tx_hash = txHash;
          taskData.wallet_type = selectedWallet.value;
          
          // 保存到数据库
          console.log('提交任务数据:', taskData);
          const result = await TaskAPI.create(taskData);
          console.log('创建任务结果:', result);
          
          // 关闭支付对话框
          paymentDialogVisible.value = false;
          
          // 显示成功消息
          ElMessage.success('区块链支付成功！任务已发布');
          
          // 跳转回任务列表页
          router.push('/server/tasks');
          return;
        }
        
        // 传统支付方式处理逻辑
        // 模拟支付处理
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // 构建任务数据
        const taskData = buildTaskData();
        taskData.status = 'published';
        taskData.payment_status = 'first_payment_completed';
        taskData.payment_method = selectedPaymentMethod.value;
        
        // 保存到数据库
        console.log('提交任务数据:', taskData);
        const result = await TaskAPI.create(taskData);
        console.log('创建任务结果:', result);
        
        // 关闭支付对话框
        paymentDialogVisible.value = false;
        
        // 显示成功消息
        ElMessage.success('付款成功！任务已发布');
        
        // 跳转回任务列表页
        router.push('/server/tasks');
      } catch (error) {
        console.error('支付处理失败:', error);
        ElMessage.error('支付处理失败: ' + (error.message || '请重试'));
      } finally {
        processingPayment.value = false;
      }
    };
    
    // 连接区块链钱包
    const connectWallet = async (walletType) => {
      ElMessage.info(`正在连接 ${walletType} 钱包...`);
      
      // 这里实现与不同钱包的连接逻辑
      // 实际项目中需要使用相应的SDK
      try {
        // 模拟连接过程
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 模拟连接成功
        ElMessage.success(`${walletType} 钱包连接成功`);
        return true;
      } catch (error) {
        console.error('连接钱包失败:', error);
        ElMessage.error('连接钱包失败，请重试');
        throw new Error('钱包连接失败');
      }
    };
    
    // 请求区块链支付
    const requestBlockchainPayment = async (amount) => {
      try {
        ElMessage.info(`正在发起区块链支付，金额: ${amount} 代币`);
        
        // 模拟支付过程
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 生成随机交易哈希
        const txHash = `0x${Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('')}`;
        
        ElMessage.success('区块链交易已发送');
        return txHash;
      } catch (error) {
        console.error('区块链支付失败:', error);
        ElMessage.error('区块链支付失败，请重试');
        throw new Error('区块链支付失败');
      }
    };
    
    // 提交任务
    const submitTask = async () => {
      try {
        // 验证表单
        const valid = await validateTask();
        if (!valid) {
          return;
        }
        
        submitting.value = true;
        
        // 确认发布
        await ElMessageBox.confirm(
          '确定要发布此任务吗？发布后将对客户端用户可见，且无法回到草稿状态。',
          '发布确认',
          {
            confirmButtonText: '确定发布',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );

        // 显示支付对话框
        paymentDialogVisible.value = true;
      } catch (error) {
        if (error === 'cancel') return;
        console.error('发布任务失败:', error);
        ElMessage.error('发布任务失败: ' + (error.message || '请重试'));
      } finally {
        submitting.value = false;
      }
    };
    
    return {
      taskForm,
      rules,
      saving,
      submitting,
      taskFormRef,
      disabledDate,
      addRequirement,
      removeRequirement,
      goBack,
      saveAsDraft,
      submitTask,
      validateTask,
      // 新增支付相关方法和状态
      exchangeRate,
      formatPaymentRate,
      calculateFirstPayment,
      calculateSecondPayment,
      calculatePlatformFee,
      calculateTotalPayment,
      formatCurrencyAmount,
      paymentDialogVisible,
      selectedPaymentMethod,
      selectedWallet,
      processingPayment,
      processPayment
    };
  }
};
</script>

<style scoped>
.create-task {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.page-actions {
  display: flex;
  gap: 12px;
}

.form-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
}

.card-header i {
  color: #409EFF;
}

.requirement-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.form-tip {
  margin-left: 8px;
  color: #909399;
}

.ml-2 {
  margin-left: 8px;
}

.payment-breakdown {
  margin-top: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 12px;
  background-color: #f9f9f9;
}

.payment-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px dashed #e0e0e0;
}

.payment-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.payment-item.total {
  font-weight: bold;
  color: #409EFF;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
  margin-bottom: 24px;
}

/* 支付对话框样式 */
.payment-dialog-content {
  padding: 0 8px;
}

.payment-summary {
  margin-bottom: 24px;
}

.payment-summary h3 {
  margin-top: 0;
  margin-bottom: 16px;
  text-align: center;
  color: #333;
}

.task-name {
  text-align: center;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 20px;
}

.payment-methods {
  margin-top: 24px;
}

.payment-methods h4 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #333;
}

.el-radio-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.payment-action {
  margin-top: 24px;
  display: flex;
  justify-content: center;
  gap: 16px;
}

.total-amount :deep(.el-descriptions-item__content) {
  font-weight: bold;
  color: #f56c6c;
  font-size: 16px;
}

.wallet-selection {
  margin-top: 16px;
  margin-bottom: 16px;
}

.wallet-notice {
  margin-top: 8px;
  color: #909399;
}
</style> 