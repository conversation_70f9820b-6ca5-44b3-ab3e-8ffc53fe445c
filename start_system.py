#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ZHOUSPROJECT - 联邦学习与区块链集成系统启动工具
此脚本用于一键启动整个系统，包括区块链、联邦学习服务器、客户端和前端应用
"""

import os
import sys
import time
import subprocess
import importlib
import webbrowser
import json
from pathlib import Path
import argparse

# 检查Python版本
if sys.version_info < (3, 6):
    print("❌ 需要Python 3.6或更高版本")
    sys.exit(1)

# 添加命令行参数解析
parser = argparse.ArgumentParser(description="ZHOUSPROJECT - 联邦学习与区块链集成系统启动工具")
parser.add_argument('--redeploy', action='store_true', help='强制重新部署智能合约')
parser.add_argument('--server-account', type=str, help='指定服务器账户地址')
parser.add_argument('--client1-account', type=str, help='指定客户端1账户地址')
parser.add_argument('--client2-account', type=str, help='指定客户端2账户地址')
parser.add_argument('--test', action='store_true', help='直接测试联邦学习功能')
args = parser.parse_args()

def check_dependencies():
    """检查系统依赖"""
    print("检查系统依赖...")
    all_passed = True
    
    # 检查Python依赖
    python_deps = {
        "web3": "用于区块链交互",
        "flask": "用于Web服务器",
        "torch": "用于机器学习",
        "flwr": "用于联邦学习",
        "numpy": "用于数值计算",
        "pandas": "用于数据处理"
    }
    
    print("检查Python依赖:")
    for dep, desc in python_deps.items():
        try:
            importlib.import_module(dep)
            print(f"  ✅ {dep}: {desc}")
        except ImportError:
            print(f"  ❌ {dep}: {desc} - 未安装")
            print(f"     请运行: pip install {dep}")
            all_passed = False
    
    # 检查Node.js和npm
    print("\n检查Node.js和npm:")
    try:
        node_version = subprocess.run(
            "node --version", 
            shell=True, 
            capture_output=True, 
            text=True
        )
        if node_version.returncode == 0:
            print(f"  ✅ Node.js: {node_version.stdout.strip()}")
        else:
            print("  ❌ Node.js: 未安装")
            print("     请从 https://nodejs.org 下载并安装")
            all_passed = False
    except:
        print("  ❌ Node.js: 未安装")
        print("     请从 https://nodejs.org 下载并安装")
        all_passed = False
        
    try:
        npm_version = subprocess.run(
            "npm --version", 
            shell=True, 
            capture_output=True, 
            text=True
        )
        if npm_version.returncode == 0:
            print(f"  ✅ npm: {npm_version.stdout.strip()}")
        else:
            print("  ❌ npm: 未安装")
            print("     npm通常随Node.js一起安装")
            all_passed = False
    except:
        print("  ❌ npm: 未安装")
        print("     npm通常随Node.js一起安装")
        all_passed = False
    
    # 检查Truffle和Ganache
    print("\n检查区块链工具:")
    try:
        truffle_version = subprocess.run(
            "truffle version", 
            shell=True, 
            capture_output=True, 
            text=True
        )
        if truffle_version.returncode == 0 and "Truffle" in truffle_version.stdout:
            print(f"  ✅ Truffle: {truffle_version.stdout.strip()}")
        else:
            print("  ❌ Truffle: 未安装")
            print("     请运行: npm install -g truffle")
            all_passed = False
    except:
        print("  ❌ Truffle: 未安装")
        print("     请运行: npm install -g truffle")
        all_passed = False
        
    try:
        ganache_version = subprocess.run(
            "ganache --version", 
            shell=True, 
            capture_output=True, 
            text=True
        )
        if ganache_version.returncode == 0:
            print(f"  ✅ Ganache: {ganache_version.stdout.strip()}")
        else:
            print("  ❌ Ganache: 未安装")
            print("     请运行: npm install -g ganache")
            all_passed = False
    except:
        print("  ❌ Ganache: 未安装")
        print("     请运行: npm install -g ganache")
        all_passed = False
    
    return all_passed

def start_ganache():
    """启动Ganache区块链"""
    print("检查Ganache区块链...")
    try:
        # 尝试连接到已运行的Ganache
        from web3 import Web3
        w3 = Web3(Web3.HTTPProvider("http://127.0.0.1:7545"))
        
        # 使用eth.block_number替代is_connected
        try:
            block_number = w3.eth.block_number
            print(f"✅ 已连接到Ganache，当前区块: {block_number}")
            # 如果已经连接成功，直接返回True而不是启动新实例
            return True
        except Exception as e:
            print(f"Ganache未运行，正在启动...")
            
            # 启动Ganache
            ganache_process = subprocess.Popen(
                "ganache --port 7545 --chain.networkId 5777",
                shell=True,
                # 不捕获输出，让它显示在控制台
            )
            
            # 等待Ganache启动
            time.sleep(5)
            
            # 再次检查连接
            try:
                w3 = Web3(Web3.HTTPProvider("http://127.0.0.1:7545"))
                block_number = w3.eth.block_number
                print(f"✅ Ganache启动成功，当前区块: {block_number}")
                return ganache_process
            except Exception as e2:
                print(f"❌ Ganache启动后仍无法连接: {e2}")
                try:
                    ganache_process.terminate()
                except:
                    pass
                return False
    except Exception as e:
        print(f"❌ 检查Ganache错误: {e}")
        return False

def check_existing_contract():
    """检查是否已有部署的合约"""
    try:
        from web3 import Web3
        import json
        import os
        
        # 连接到以太坊节点
        w3 = Web3(Web3.HTTPProvider('http://127.0.0.1:7545'))
        
        # 检查合约文件是否存在
        contract_path = os.path.abspath("blockchain/build/contracts/FederatedLearningContract.json")
        if not os.path.exists(contract_path):
            print("❌ 未找到已部署的合约文件")
            return None
            
        # 读取合约文件
        with open(contract_path, 'r', encoding='utf-8') as f:
            contract_json = json.load(f)
            
        # 获取网络ID和合约地址
        network_id = str(w3.eth.chain_id)
        if network_id not in contract_json['networks']:
            print("❌ 合约未部署到当前网络")
            return None
            
        contract_address = contract_json['networks'][network_id]['address']
        
        # 检查合约地址是否存在 - 使用兼容不同版本Web3.py的方式
        try:
            # 尝试将地址转换为校验和地址
            checksum_address = contract_address
            if hasattr(w3, 'to_checksum_address'):
                checksum_address = w3.to_checksum_address(contract_address)
            elif hasattr(Web3, 'to_checksum_address'):
                checksum_address = Web3.to_checksum_address(contract_address)
            elif hasattr(w3, 'toChecksumAddress'):
                checksum_address = w3.toChecksumAddress(contract_address)
            elif hasattr(Web3, 'toChecksumAddress'):
                checksum_address = Web3.toChecksumAddress(contract_address)
                
            # 检查合约代码
            if not w3.eth.get_code(checksum_address):
                print("❌ 合约地址无效")
                return None
        except Exception as e:
            print(f"❌ 检查合约地址失败: {e}")
            return None
            
        # 创建合约实例
        contract = w3.eth.contract(
            address=contract_address,
            abi=contract_json['abi']
        )
        
        # 尝试调用合约函数验证合约是否有效
        try:
            # 尝试获取当前轮次
            current_round = contract.functions.getCurrentRound().call()
            print(f"✅ 找到已部署的合约: {contract_address}")
            print(f"   当前训练轮次: {current_round}")
            
            # 尝试获取服务器地址
            try:
                server_address = contract.functions.serverAddress().call()
                if server_address and server_address != '******************************************':
                    print(f"   当前服务器地址: {server_address}")
                else:
                    print("   服务器地址未设置")
            except:
                print("   无法获取服务器地址")
                
            return contract_address
        except Exception as e:
            print(f"❌ 合约验证失败: {e}")
            return None
            
    except Exception as e:
        print(f"❌ 检查合约错误: {e}")
        return None

def deploy_contracts():
    """部署智能合约"""
    print("部署智能合约...")
    try:
        # 检查blockchain目录是否存在
        if not os.path.exists("blockchain"):
            print("❌ blockchain目录不存在")
            return False
            
        # 检查truffle.cmd或truffle是否可用
        try:
            # 检查truffle是否已安装
            truffle_check = subprocess.run(
                "truffle version", 
                shell=True, 
                capture_output=True, 
                text=True
            )
            if "Truffle" not in truffle_check.stdout:
                print("❌ Truffle未安装或不在PATH中")
                print("请运行: npm install -g truffle")
                return False
        except Exception:
            print("❌ Truffle未安装或不在PATH中")
            print("请运行: npm install -g truffle")
            return False
            
        # 切换到blockchain目录
        os.chdir("blockchain")
        
        # 编译合约
        print("编译智能合约...")
        compile_result = subprocess.run(
            "truffle compile", 
            shell=True, 
            capture_output=True, 
            text=True
        )
        
        if "Error" in compile_result.stderr:
            print(f"❌ 合约编译失败: {compile_result.stderr}")
            os.chdir("..")
            return False
            
        print("✅ 合约编译成功")
        
        # 部署合约
        print("部署智能合约到区块链...")
        result = subprocess.run(
            "truffle migrate --reset", 
            shell=True, 
            capture_output=True, 
            text=True
        )
        
        # 返回到原目录
        os.chdir("..")
        
        # 检查是否有错误
        if "Error" in result.stderr:
            print(f"❌ 合约部署失败: {result.stderr}")
            return False
            
        # 检查是否生成了合约文件
        contract_path = os.path.abspath("blockchain/build/contracts/FederatedLearningContract.json")
        if not os.path.exists(contract_path):
            print(f"❌ 合约部署后未找到合约文件: {contract_path}")
            return False
            
        print("✅ 智能合约部署成功")
        return True
    except Exception as e:
        print(f"❌ 合约部署错误: {e}")
        # 确保返回原目录
        try:
            os.chdir("..")
        except:
            pass
        return False

def start_server(account=None):
    """启动联邦学习服务器"""
    print("启动联邦学习服务器...")
    try:
        # 检查flower目录是否存在
        if not os.path.exists("flower"):
            print("❌ flower目录不存在")
            return None
            
        # 检查server.py是否存在
        if not os.path.exists("flower/server.py"):
            print("❌ flower/server.py文件不存在")
            return None
            
        # 切换到flower目录
        os.chdir("flower")
        
        # 构建命令，添加账户参数
        cmd = "python server.py"
        if account:
            cmd += f" --account {account}"
            print(f"使用服务器账户: {account}")
        
        # 创建日志目录
        os.makedirs("../logs", exist_ok=True)
        
        # 创建日志文件
        log_file = open("../logs/server.log", "w", encoding='utf-8')
        
        # 启动服务器，重定向输出到日志文件
        server_process = subprocess.Popen(
            cmd,
            shell=True,
            stdout=log_file,
            stderr=subprocess.STDOUT
        )
        
        # 返回到原目录
        os.chdir("..")
        
        # 等待服务器启动
        print("等待服务器启动...")
        time.sleep(5)
        
        # 检查进程是否还在运行
        if server_process.poll() is not None:
            print(f"❌ 服务器启动失败，退出代码: {server_process.returncode}")
            return None
            
        print("✅ 联邦学习服务器启动成功")
        print("📝 服务器日志保存在: logs/server.log")
        return server_process
    except Exception as e:
        print(f"❌ 启动服务器错误: {e}")
        # 确保返回原目录
        try:
            os.chdir("..")
        except:
            pass
        return None

def start_client(client_id=1, account=None, direct=False):
    """启动联邦学习客户端"""
    print(f"启动联邦学习客户端 {client_id}...")
    try:
        # 检查flower目录是否存在
        if not os.path.exists("flower"):
            print("❌ flower目录不存在")
            return None
            
        # 检查client.py是否存在
        if not os.path.exists("flower/client.py"):
            print("❌ flower/client.py文件不存在")
            return None
            
        # 切换到flower目录
        os.chdir("flower")
        
        # 构建命令，添加账户参数
        cmd = "python client.py"
        if account:
            cmd += f" --account {account}"
            print(f"使用客户端 {client_id} 账户: {account}")
            
        # 如果是第二个或更多客户端，修改端口
        port = 5001 + (client_id - 1)
        cmd = cmd.replace("client.py", f"client.py --port {port}")
        
        # 如果是直接模式，添加direct参数
        if direct:
            cmd += " --direct"
            print("使用直接模式启动客户端，绕过Flask API")
        
        print(f"执行命令: {cmd}")
        
        # 创建日志目录
        os.makedirs("../logs", exist_ok=True)
        
        # 创建日志文件
        log_file = open(f"../logs/client{client_id}.log", "w", encoding='utf-8')
        
        # 启动客户端，重定向输出到日志文件
        client_process = subprocess.Popen(
            cmd,
            shell=True,
            stdout=log_file,
            stderr=subprocess.STDOUT
        )
        
        # 返回到原目录
        os.chdir("..")
        
        # 等待客户端启动
        print(f"等待客户端 {client_id} 启动...")
        time.sleep(5)
        
        # 检查进程是否还在运行
        if client_process.poll() is not None:
            print(f"❌ 客户端 {client_id} 启动失败，退出代码: {client_process.returncode}")
            return None
            
        print(f"✅ 联邦学习客户端 {client_id} 启动成功")
        print(f"📝 客户端{client_id}日志保存在: logs/client{client_id}.log")
        
        # 不再尝试自动加入训练，让用户通过前端手动操作
        """
        # 如果不是直接模式，尝试调用客户端API加入训练
        if not direct and client_id == 1:
            try:
                import requests
                print(f"尝试让客户端 {client_id} 加入训练...")
                response = requests.post(
                    f"http://localhost:{port}/api/client/join",
                    json={
                        "address": account or get_accounts()[client_id],
                        "config": {
                            "batchSize": 32,
                            "learningRate": 0.001,
                            "epochs": 5
                        }
                    }
                )
                if response.status_code == 200:
                    print(f"✅ 客户端 {client_id} 成功加入训练")
                else:
                    print(f"❌ 客户端 {client_id} 加入训练失败: {response.text}")
            except Exception as e:
                print(f"❌ 调用客户端API错误: {e}")
        """
        
        return client_process
    except Exception as e:
        print(f"❌ 启动客户端 {client_id} 错误: {e}")
        # 确保返回原目录
        try:
            os.chdir("..")
        except:
            pass
        return None

def get_accounts():
    """获取区块链账户列表"""
    try:
        from web3 import Web3
        w3 = Web3(Web3.HTTPProvider('http://127.0.0.1:7545'))
        accounts = w3.eth.accounts
        return accounts
    except Exception as e:
        print(f"❌ 获取账户列表失败: {e}")
        return []

def start_frontend():
    """启动前端应用"""
    print("启动前端应用...")
    try:
        # 检查frontend目录是否存在
        if not os.path.exists("frontend"):
            print("❌ frontend目录不存在")
            return None
            
        # 检查package.json是否存在
        if not os.path.exists("frontend/package.json"):
            print("❌ frontend/package.json文件不存在")
            return None
            
        # 切换到frontend目录
        os.chdir("frontend")
        
        # 检查依赖是否已安装
        if not os.path.exists("node_modules"):
            print("正在安装前端依赖...")
            subprocess.run("npm install", shell=True, check=True)
            
        # 创建日志目录
        os.makedirs("../logs", exist_ok=True)
        
        # 创建日志文件
        log_file = open("../logs/frontend.log", "w", encoding='utf-8')
        
        # 启动前端，重定向输出到日志文件
        frontend_process = subprocess.Popen(
            "npm run dev",
            shell=True,
            stdout=log_file,
            stderr=subprocess.STDOUT
        )
        
        # 返回到原目录
        os.chdir("..")
        
        # 等待前端启动
        print("等待前端启动...")
        time.sleep(10)
        
        # 检查进程是否还在运行
        if frontend_process.poll() is not None:
            print(f"❌ 前端启动失败，退出代码: {frontend_process.returncode}")
            return None
            
        print("✅ 前端应用启动成功")
        print("🌐 请在浏览器中访问: http://localhost:5173")
        print("📝 前端日志保存在: logs/frontend.log")
        return frontend_process
    except Exception as e:
        print(f"❌ 启动前端错误: {e}")
        # 确保返回原目录
        try:
            os.chdir("..")
        except:
            pass
        return None

def start_db_api():
    """启动数据库API服务"""
    print("启动数据库API服务...")
    try:
        # 检查数据库API服务需要的依赖
        if not os.path.exists("backend/app.js"):
            print("❌ 未找到数据库API服务（backend/app.js）")
            return None
            
        # 创建日志目录
        os.makedirs("logs", exist_ok=True)
        
        # 创建日志文件
        log_file = open("logs/db_api.log", "w", encoding='utf-8')
        
        # 启动数据库API服务，重定向输出到日志文件
        api_process = subprocess.Popen(
            "npm run api",
            shell=True,
            stdout=log_file,
            stderr=subprocess.STDOUT
        )
        
        # 等待服务启动
        print("等待数据库API服务启动...")
        time.sleep(5)
        
        # 检查进程是否还在运行
        if api_process.poll() is not None:
            print(f"❌ 数据库API服务启动失败，退出代码: {api_process.returncode}")
            return None
            
        print("✅ 数据库API服务启动成功")
        print("📝 数据库API日志保存在: logs/db_api.log")
        return api_process
    except Exception as e:
        print(f"❌ 启动数据库API服务错误: {e}")
        return None

def test_federated_learning():
    """直接测试联邦学习功能"""
    print("开始测试联邦学习功能...")
    
    # 获取账户列表
    accounts = get_accounts()
    if not accounts:
        print("❌ 无法获取账户列表，使用默认账户")
        server_account = None
        client1_account = None
        client2_account = None
    else:
        server_account = accounts[0]
        client1_account = accounts[1]
        client2_account = accounts[2]
    
    # 启动服务器
    server_process = start_server(server_account)
    if not server_process:
        print("❌ 服务器启动失败，测试终止")
        return
    
    # 等待服务器完全启动
    print("等待服务器完全启动...")
    time.sleep(5)
    
    # 移除自动开始训练的代码
    print("✅ 联邦学习服务器已启动成功")
    print("📌 服务器已启动但不会自动开始训练")
    print("📌 请使用前端界面中的'开始训练'按钮手动启动训练")
    
    # 启动客户端（直接模式）
    client1_process = start_client(1, client1_account, direct=True)
    if not client1_process:
        print("❌ 客户端1启动失败，测试终止")
        return
    
    # 检查训练状态
    try:
        import requests
        response = requests.get("http://localhost:5000/api/server/status")
        if response.status_code == 200:
            status = response.json()
            print(f"服务器状态: {status}")
        else:
            print(f"❌ 获取服务器状态失败: {response.text}")
    except Exception as e:
        print(f"❌ 调用服务器API错误: {e}")
    
    print("系统部署完成，可以通过前端界面开始训练")

def main():
    """主函数，启动整个系统"""
    print("=" * 50)
    print("ZHOUSPROJECT - 联邦学习与区块链集成系统启动工具")
    print("=" * 50)
    
    # 存储所有启动的进程
    processes = []
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，请安装所需依赖后重试")
        return
        
    print("✅ 依赖检查通过")
    print("-" * 50)
    
    # 启动Ganache
    ganache_result = start_ganache()
    if isinstance(ganache_result, subprocess.Popen):
        processes.append(ganache_result)
        print("✅ Ganache启动成功")
    elif ganache_result is True:
        print("✅ 使用已运行的Ganache实例")
    else:
        print("❌ Ganache启动失败")
        cleanup_processes(processes)
        return
    
    print("-" * 50)
    
    # 检查是否已有部署的合约
    existing_contract = check_existing_contract()
    
    # 根据命令行参数或合约状态决定是否重新部署
    if args.redeploy or existing_contract is None:
        print("需要部署新合约...")
        if not deploy_contracts():
            print("❌ 合约部署失败")
            cleanup_processes(processes)
            return
    else:
        print(f"使用已部署的合约: {existing_contract}")
        
    print("-" * 50)
    
    # 获取账户列表
    accounts = get_accounts()
    if not accounts or len(accounts) < 3:
        print("❌ 没有足够的账户，需要至少3个账户")
        cleanup_processes(processes)
        return
    
    print(f"✅ 找到 {len(accounts)} 个账户:")
    for i, account in enumerate(accounts):
        print(f"  账户 {i+1}: {account}")
    
    # 使用命令行参数指定的账户或默认账户
    server_account = args.server_account if args.server_account else accounts[0]
    client1_account = args.client1_account if args.client1_account else accounts[1]
    client2_account = args.client2_account if args.client2_account else accounts[2]
    
    print(f"将使用账户作为服务器账户: {server_account}")
    print(f"将使用账户作为客户端1账户: {client1_account}")
    print(f"将使用账户作为客户端2账户: {client2_account}")
    
    print("-" * 50)
    
    # 启动数据库API服务
    db_api_process = start_db_api()
    if db_api_process:
        processes.append(db_api_process)
        print("✅ 数据库API服务启动成功")
    else:
        print("⚠️ 数据库API服务启动失败，但将继续启动其他服务")
        
    print("-" * 50)
    
    # 提示用户关于合约权限的信息
    print("⚠️ 注意: 如果遇到合约权限错误(Only server can call this function)，请确保:")
    print("  1. 服务器使用的是已在合约中注册的服务器账户")
    print("  2. 客户端使用的是不同于服务器的账户")
    print("  3. 如果需要重新设置服务器地址，请使用 --redeploy 参数重新部署合约")
    
    print("-" * 50)
    
    # 启动服务器
    server_process = start_server(server_account)
    if server_process:
        processes.append(server_process)
        print("✅ 服务器启动成功")
    else:
        print("❌ 服务器启动失败")
        cleanup_processes(processes)
        return
        
    print("-" * 50)
    
    # 启动客户端1 - 使用直接模式启动
    client1_process = start_client(1, client1_account, direct=True)
    if client1_process:
        processes.append(client1_process)
        print("✅ 客户端1启动成功 (直接模式)")
    else:
        print("❌ 客户端1启动失败")
        cleanup_processes(processes)
        return
        
    print("-" * 50)
    
    # 启动客户端2 - 使用直接模式启动
    client2_process = start_client(2, client2_account, direct=True)
    if client2_process:
        processes.append(client2_process)
        print("✅ 客户端2启动成功 (直接模式)")
    else:
        print("❌ 客户端2启动失败")
        cleanup_processes(processes)
        return
        
    print("-" * 50)
    
    # 启动前端
    frontend_process = start_frontend()
    if frontend_process:
        processes.append(frontend_process)
        print("✅ 前端启动成功")
    else:
        print("❌ 前端启动失败")
        cleanup_processes(processes)
        return
        
    print("-" * 50)
    print("✅ 系统启动完成！")
    print("🌐 请在浏览器中访问: http://localhost:5173")
    print("🔄 数据库API服务运行在: http://localhost:3001")
    print("🌟 联邦学习服务器运行在: http://localhost:5000")
    print("⚠️ 注意: 系统已启动1个服务器和2个客户端（直接模式）")
    print("  - 服务器账户: " + server_account)
    print("  - 客户端1账户: " + client1_account)
    print("  - 客户端2账户: " + client2_account)
    print("-" * 50)
    print("📌 重要提示: 服务器不会自动开始训练")
    print("📌 请执行以下步骤来启动训练:")
    print("  1. 使用管理员界面创建任务(或使用已有任务)")
    print("  2. 在客户端界面申请参与任务")
    print("  3. 在管理员界面批准申请")
    print("  4. 点击管理员界面的'开始训练'按钮手动启动训练")
    print("     - 系统已自动配置客户端以直接模式运行，点击开始训练后应立即开始")
    print("  5. 在客户端界面可以查看训练进度")
    print("-" * 50)
    print("📝 日志信息已重定向到logs目录:")
    print("  - 服务器日志: logs/server.log")
    print("  - 客户端日志: logs/client1.log, logs/client2.log")
    print("  - 数据库API日志: logs/db_api.log")
    print("  - 前端日志: logs/frontend.log")
    print("  这会减少控制台中显示的噪音，同时保留重要的系统消息")
    print("-" * 50)

    try:
        print("系统正在运行中，按Ctrl+C停止所有服务...")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n正在停止所有服务...")
        cleanup_processes(processes)
        print("✅ 所有服务已停止")

def cleanup_processes(processes):
    """清理所有启动的进程"""
    for process in processes:
        try:
            process.terminate()
            process.wait(timeout=5)
        except:
            try:
                process.kill()
            except:
                pass
    
if __name__ == "__main__":
    if args.test:
        test_federated_learning()
    else:
        # 原有的启动流程
        main() 