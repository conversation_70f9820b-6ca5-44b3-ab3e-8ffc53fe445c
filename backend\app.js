/**
 * 后端API服务
 * 提供数据库访问和业务逻辑接口
 */
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const { initializeDatabase } = require('./db/init');
const models = require('./models');

// 创建Express应用
const app = express();
const port = process.env.PORT || 3001; // 使用不同于Flask服务器的端口

// 中间件
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 设置内容类型为纯JSON，避免混入HTML
app.use((req, res, next) => {
  res.setHeader('Content-Type', 'application/json');
  next();
});

// 修复错误处理
app.use((err, req, res, next) => {
  console.error('全局错误捕获:', err);
  
  // 标准化错误响应
  res.status(err.status || 500).json({
    error: err.message || '服务器内部错误',
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
  });
});

// 初始化数据库
async function initDatabase() {
  try {
    await initializeDatabase();
    console.log('数据库初始化成功');
  } catch (error) {
    console.error('数据库初始化失败:', error);
    process.exit(1);
  }
}

// 任务相关API
app.get('/api/tasks', async (req, res, next) => {
  try {
    const tasks = await models.Task.getAll();
    console.log('获取所有任务，返回数据:', JSON.stringify(tasks));
    res.json(tasks);
  } catch (error) {
    console.error('获取所有任务失败:', error);
    next(error);
  }
});

app.get('/api/tasks/:id', async (req, res, next) => {
  try {
    const task = await models.Task.getById(req.params.id);
    if (!task) {
      return res.status(404).json({ error: '任务不存在' });
    }
    res.json(task);
  } catch (error) {
    next(error);
  }
});

app.post('/api/tasks', async (req, res, next) => {
  try {
    console.log('收到创建任务请求:', JSON.stringify(req.body, null, 2));
    
    // 验证必填字段
    const { name, description, status, data_type, total_rounds, required_participants } = req.body;
    
    if (!name || !description || !status || !data_type || !total_rounds || !required_participants) {
      return res.status(400).json({ 
        error: '缺少必填字段', 
        requiredFields: ['name', 'description', 'status', 'data_type', 'total_rounds', 'required_participants'] 
      });
    }
    
    // 默认值处理
    const taskData = {
      ...req.body,
      learning_rate: req.body.learning_rate || 0.001,
      batch_size: req.body.batch_size || 32,
      timeout: req.body.timeout || 60,
      blockchain_tx_hash: req.body.blockchain_tx_hash || null
    };
    
    console.log('处理后的任务数据:', JSON.stringify(taskData, null, 2));
    
    const task = await models.Task.create(taskData);
    console.log('创建任务成功:', task);
    res.status(201).json(task);
  } catch (error) {
    console.error('创建任务失败:', error);
    res.status(500).json({
      error: '创建任务失败',
      message: error.message
    });
  }
});

app.put('/api/tasks/:id', async (req, res, next) => {
  try {
    const success = await models.Task.update(req.params.id, req.body);
    if (!success) {
      return res.status(404).json({ error: '任务不存在或更新失败' });
    }
    const task = await models.Task.getById(req.params.id);
    res.json(task);
  } catch (error) {
    next(error);
  }
});

app.delete('/api/tasks/:id', async (req, res, next) => {
  try {
    const success = await models.Task.delete(req.params.id);
    if (!success) {
      return res.status(404).json({ error: '任务不存在或删除失败' });
    }
    res.status(204).end();
  } catch (error) {
    next(error);
  }
});

// 任务启动训练API
app.post('/api/tasks/:id/start', async (req, res, next) => {
  try {
    console.log(`收到启动任务(ID: ${req.params.id})训练请求`);
    
    // 检查任务是否存在
    const task = await models.Task.getById(req.params.id);
    if (!task) {
      return res.status(404).json({ error: '任务不存在' });
    }
    
    // 检查任务状态是否可以启动（只有pending状态的任务才能启动）
    if (task.status !== 'pending') {
      return res.status(400).json({ 
        error: '任务状态不允许启动训练', 
        currentStatus: task.status,
        requiredStatus: 'pending'
      });
    }
    
    // 获取参与者并检查参与者数量
    const participants = await models.Task.getParticipants(req.params.id);
    // 过滤出已批准的参与者（状态不是registered和rejected的）
    const approvedParticipants = participants.filter(p => p.status !== 'registered' && p.status !== 'rejected');
    if (approvedParticipants.length < task.required_participants) {
      return res.status(400).json({ 
        error: '已批准的参与者数量不足', 
        currentParticipants: approvedParticipants.length,
        requiredParticipants: task.required_participants
      });
    }
    
    // 启动训练任务
    const success = await models.Task.startTraining(req.params.id);
    if (!success) {
      return res.status(500).json({ error: '启动训练失败' });
    }
    
    // 获取更新后的任务信息
    const updatedTask = await models.Task.getById(req.params.id);
    res.json({ 
      message: '任务训练已成功启动',
      task: updatedTask
    });
  } catch (error) {
    console.error(`启动任务(ID: ${req.params.id})训练失败:`, error);
    res.status(500).json({
      error: '启动训练失败',
      message: error.message
    });
  }
});

// 任务完成训练API
app.post('/api/tasks/:id/complete', async (req, res, next) => {
  try {
    console.log(`收到完成任务(ID: ${req.params.id})训练请求`);
    
    // 检查任务是否存在
    const task = await models.Task.getById(req.params.id);
    if (!task) {
      return res.status(404).json({ error: '任务不存在' });
    }
    
    // 检查任务状态是否可以完成（只有in_progress状态的任务才能完成）
    if (task.status !== 'in_progress') {
      return res.status(400).json({ 
        error: '任务状态不允许完成训练', 
        currentStatus: task.status,
        requiredStatus: 'in_progress'
      });
    }
    
    // 完成训练任务
    const success = await models.Task.completeTraining(req.params.id);
    if (!success) {
      return res.status(500).json({ error: '完成训练失败' });
    }
    
    // 创建完成日志
    await models.Log.task(`任务训练已完成`, req.params.id);
    
    // 获取更新后的任务信息
    const updatedTask = await models.Task.getById(req.params.id);
    res.json({ 
      message: '任务训练已成功完成',
      task: updatedTask
    });
  } catch (error) {
    console.error(`完成任务(ID: ${req.params.id})训练失败:`, error);
    res.status(500).json({
      error: '完成训练失败',
      message: error.message
    });
  }
});

// 获取任务参与者
app.get('/api/tasks/:id/participants', async (req, res, next) => {
  try {
    console.log(`获取任务(ID: ${req.params.id})的参与者`);
    
    // 检查任务是否存在
    const task = await models.Task.getById(req.params.id);
    if (!task) {
      return res.status(404).json({ error: '任务不存在' });
    }
    
    // 获取参与者列表
    const participants = await models.Task.getParticipants(req.params.id);
    res.json(participants);
  } catch (error) {
    console.error(`获取任务(ID: ${req.params.id})参与者失败:`, error);
    res.status(500).json({
      error: '获取参与者失败',
      message: error.message
    });
  }
});

// 参与者相关API
app.get('/api/participants', async (req, res, next) => {
  try {
    const participants = await models.Participant.getAll();
    res.json(participants);
  } catch (error) {
    next(error);
  }
});

app.get('/api/participants/:id', async (req, res, next) => {
  try {
    const participant = await models.Participant.getById(req.params.id);
    if (!participant) {
      return res.status(404).json({ error: '参与者不存在' });
    }
    res.json(participant);
  } catch (error) {
    next(error);
  }
});

app.get('/api/tasks/:taskId/participants', async (req, res, next) => {
  try {
    const participants = await models.Participant.getByTaskId(req.params.taskId);
    res.json(participants);
  } catch (error) {
    next(error);
  }
});

// 更新参与者状态
app.put('/api/participants/:id/status', async (req, res, next) => {
  try {
    console.log(`更新参与者(ID: ${req.params.id})状态为: ${req.body.status}`);
    
    // 验证状态值
    const validStatuses = ['registered', 'training', 'completed', 'failed', 'disconnected', 'rejected'];
    if (!validStatuses.includes(req.body.status)) {
      return res.status(400).json({ 
        error: '无效的状态值',
        validStatuses
      });
    }
    
    // 检查参与者是否存在
    const participant = await models.Participant.getById(req.params.id);
    if (!participant) {
      return res.status(404).json({ error: '参与者不存在' });
    }
    
    // 获取任务信息以用于日志记录
    const task = await models.Task.getById(participant.task_id);
    
    // 更新状态
    const success = await models.Participant.updateStatus(req.params.id, req.body.status);
    
    if (!success) {
      return res.status(500).json({ error: '更新参与者状态失败' });
    }
    
    // 创建日志
    await models.Log.participant(
      `参与者(${participant.client_address})状态更新为 ${req.body.status}`, 
      participant.task_id
    );
    
    // 返回更新后的参与者
    const updatedParticipant = await models.Participant.getById(req.params.id);
    res.json(updatedParticipant);
  } catch (error) {
    console.error('更新参与者状态失败:', error);
    res.status(500).json({
      error: '更新参与者状态失败',
      message: error.message
    });
  }
});

// 创建参与者（任务申请）
app.post('/api/participants', async (req, res, next) => {
  try {
    console.log('收到创建参与者请求:', JSON.stringify(req.body, null, 2));
    
    // 验证必填字段
    const { task_id, client_address, status, data_size } = req.body;
    
    if (!task_id || !client_address || !data_size) {
      return res.status(400).json({ 
        error: '缺少必填字段', 
        requiredFields: ['task_id', 'client_address', 'data_size'] 
      });
    }
    
    // 检查任务是否存在
    const task = await models.Task.getById(task_id);
    if (!task) {
      return res.status(404).json({ error: '任务不存在' });
    }
    
    // 检查是否已经申请过该任务
    const existingParticipants = await models.Participant.getByTaskId(task_id);
    const alreadyApplied = existingParticipants.some(p => p.client_address === client_address);
    
    if (alreadyApplied) {
      return res.status(400).json({ error: '您已经申请过该任务' });
    }
    
    // 创建参与者
    const participant = await models.Participant.create(req.body);
    
    // 创建日志
    await models.Log.participant(`新参与者(${client_address})申请了任务`, task_id);
    
    res.status(201).json(participant);
  } catch (error) {
    console.error('创建参与者失败:', error);
    res.status(500).json({
      error: '创建参与者失败',
      message: error.message
    });
  }
});

// 删除参与者（取消申请）
app.delete('/api/participants/:id', async (req, res, next) => {
  try {
    console.log(`删除参与者(ID: ${req.params.id})请求`);
    
    // 检查参与者是否存在
    const participant = await models.Participant.getById(req.params.id);
    if (!participant) {
      return res.status(404).json({ error: '参与者不存在' });
    }
    
    // 获取任务信息以用于日志记录
    const task = await models.Task.getById(participant.task_id);
    
    // 删除参与者
    const success = await models.Participant.delete(req.params.id);
    
    if (!success) {
      return res.status(500).json({ error: '删除参与者失败' });
    }
    
    // 创建日志
    await models.Log.participant(
      `参与者(${participant.client_address})取消了申请`, 
      participant.task_id
    );
    
    res.status(200).json({ message: '参与者已删除' });
  } catch (error) {
    console.error('删除参与者失败:', error);
    res.status(500).json({
      error: '删除参与者失败',
      message: error.message
    });
  }
});

// 根据客户端地址获取参与者
app.get('/api/applications/client/:clientAddress', async (req, res) => {
  try {
    const clientAddress = req.params.clientAddress;
    if (!clientAddress) {
      return res.status(400).json({ error: '未提供客户端地址' });
    }
    
    console.log(`获取客户端地址 ${clientAddress} 的参与者记录`);
    
    // 使用参与者模型获取该客户端地址的所有参与者记录
    const participants = await models.Participant.getByClientAddress(clientAddress);
    
    console.log(`找到 ${participants.length} 条参与者记录`);
    
    res.json(participants);
  } catch (error) {
    console.error(`获取客户端地址 ${req.params.clientAddress} 的参与者记录失败:`, error);
    res.status(500).json({
      error: '获取参与者记录失败',
      message: error.message
    });
  }
});

// 模型相关API
app.get('/api/models', async (req, res, next) => {
  try {
    const models_list = await models.Model.getAll();
    res.json(models_list);
  } catch (error) {
    next(error);
  }
});

app.get('/api/models/:id', async (req, res, next) => {
  try {
    const model = await models.Model.getById(req.params.id);
    if (!model) {
      return res.status(404).json({ error: '模型不存在' });
    }
    res.json(model);
  } catch (error) {
    next(error);
  }
});

app.post('/api/models', async (req, res, next) => {
  try {
    console.log('收到创建模型请求:', JSON.stringify(req.body, null, 2));
    
    // 验证必要字段
    const { name, type, version, file_path } = req.body;
    
    if (!name || !type || !version || !file_path) {
      return res.status(400).json({ 
        error: '缺少必填字段', 
        requiredFields: ['name', 'type', 'version', 'file_path'],
        receivedFields: Object.keys(req.body)
      });
    }
    
    // 创建模型
    const model = await models.Model.create(req.body);
    console.log('模型创建成功:', JSON.stringify(model, null, 2));
    
    // 创建日志
    if (req.body.task_id) {
      try {
        await models.Log.model(`为任务 ${req.body.task_name || ''} 创建了模型: ${name}`, req.body.task_id);
      } catch (logError) {
        console.error('创建模型日志失败:', logError);
        // 不阻止流程继续
      }
    }
    
    res.status(201).json(model);
  } catch (error) {
    console.error('创建模型失败:', error);
    res.status(500).json({
      error: '创建模型失败',
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

app.put('/api/models/:id/publish', async (req, res, next) => {
  try {
    const success = await models.Model.publish(req.params.id);
    if (!success) {
      return res.status(404).json({ error: '模型不存在或发布失败' });
    }
    const model = await models.Model.getById(req.params.id);
    res.json(model);
  } catch (error) {
    next(error);
  }
});

// 重新定义更新模型信息路由
app.put('/api/models/:id', async (req, res, next) => {
  try {
    console.log(`收到更新模型请求 - 模型ID: ${req.params.id}, 请求体:`, JSON.stringify(req.body, null, 2));
    
    // 检查ID是否为数字
    const modelId = parseInt(req.params.id, 10);
    if (isNaN(modelId)) {
      return res.status(400).json({ error: '无效的模型ID格式' });
    }
    
    // 获取当前模型
    const existingModel = await models.Model.getById(modelId);
    if (!existingModel) {
      console.log(`模型不存在 - ID: ${modelId}`);
      return res.status(404).json({ error: '模型不存在' });
    }
    
    console.log(`找到模型 - ID: ${modelId}, 名称: ${existingModel.name}`);
    
    // 更新模型
    const success = await models.Model.update(modelId, req.body);
    if (!success) {
      return res.status(500).json({ error: '更新模型失败' });
    }
    
    // 获取更新后的模型
    const updatedModel = await models.Model.getById(modelId);
    
    // 添加日志 - 只有当名称更改时才记录日志
    if (req.body.name && existingModel.name !== req.body.name) {
      try {
        await models.Log.model(
          `模型名称从 "${existingModel.name}" 修改为 "${req.body.name}"`, 
          existingModel.task_id
        );
      } catch (logError) {
        console.error('记录模型更新日志失败:', logError);
        // 不阻止流程继续
      }
    }
    
    console.log('模型更新成功:', updatedModel);
    res.json(updatedModel);
  } catch (error) {
    console.error(`更新模型(ID: ${req.params.id})失败:`, error);
    res.status(500).json({
      error: '更新模型失败',
      message: error.message
    });
  }
});

// 增加模型下载计数
app.put('/api/models/:id/downloads/increment', async (req, res, next) => {
  try {
    const success = await models.Model.incrementDownloads(req.params.id);
    if (!success) {
      return res.status(404).json({ error: '模型不存在或更新下载计数失败' });
    }
    const model = await models.Model.getById(req.params.id);
    res.json(model);
  } catch (error) {
    next(error);
  }
});

// 下载模型
app.get('/api/models/:id/download', async (req, res, next) => {
  try {
    const model = await models.Model.getById(req.params.id);
    if (!model) {
      return res.status(404).json({ error: '模型不存在' });
    }
    
    // 增加下载计数
    await models.Model.incrementDownloads(req.params.id);
    
    // 检查文件路径
    const filePath = model.file_path;
    if (!filePath) {
      return res.status(404).json({ error: '模型文件路径不存在' });
    }
    
    // 这里应该是检查文件是否存在，不存在时创建一个示例文件
    const fs = require('fs');
    const path = require('path');
    
    // 从file_path中获取目录
    const modelDir = path.dirname(filePath);
    const absolutePath = path.join(__dirname, filePath);
    const absoluteDir = path.dirname(absolutePath);
    
    // 确保目录存在
    if (!fs.existsSync(absoluteDir)) {
      fs.mkdirSync(absoluteDir, { recursive: true });
      console.log(`创建目录: ${absoluteDir}`);
    }
    
    // 如果文件不存在，创建一个示例文件
    if (!fs.existsSync(absolutePath)) {
      // 创建一个简单的二进制文件作为示例模型
      const dummyModelData = Buffer.from(`这是一个示例模型文件，ID: ${model.id}, 名称: ${model.name}, 版本: ${model.version}`);
      fs.writeFileSync(absolutePath, dummyModelData);
      console.log(`创建示例模型文件: ${absolutePath}`);
    }
    
    // 设置文件下载头
    const filename = `${model.name}_v${model.version}.h5`;
    // 处理文件名，对其进行编码，避免特殊字符问题
    const encodedFilename = encodeURIComponent(filename);
    res.setHeader('Content-Disposition', `attachment; filename="${encodedFilename}"; filename*=UTF-8''${encodedFilename}`);
    res.setHeader('Content-Type', 'application/octet-stream');
    
    // 发送文件
    res.sendFile(absolutePath, (err) => {
      if (err) {
        console.error(`发送文件失败: ${absolutePath}`, err);
        return next(new Error('下载文件失败'));
      }
      console.log(`文件 ${absolutePath} 已发送`);
    });
  } catch (error) {
    console.error('下载模型失败:', error);
    next(error);
  }
});

// 指标相关API
app.get('/api/tasks/:taskId/metrics', async (req, res, next) => {
  try {
    const metrics = await models.Metric.getByTaskId(req.params.taskId);
    res.json(metrics);
  } catch (error) {
    next(error);
  }
});

app.post('/api/metrics', async (req, res, next) => {
  try {
    const metric = await models.Metric.create(req.body);
    res.status(201).json(metric);
  } catch (error) {
    next(error);
  }
});

// 获取所有指标
app.get('/api/metrics', async (req, res, next) => {
  try {
    const metrics = await models.Metric.getAll();
    res.json(metrics);
  } catch (error) {
    next(error);
  }
});

// 根据任务ID获取指标
app.get('/api/metrics/task/:taskId', async (req, res, next) => {
  try {
    const taskId = parseInt(req.params.taskId, 10);
    const metrics = await models.Metric.getByTaskId(taskId);
    res.json(metrics);
  } catch (error) {
    next(error);
  }
});

// 根据任务ID和轮次获取指标
app.get('/api/metrics/task/:taskId/round/:round', async (req, res, next) => {
  try {
    const taskId = parseInt(req.params.taskId, 10);
    const round = parseInt(req.params.round, 10);
    const metric = await models.Metric.getByTaskIdAndRound(taskId, round);
    if (!metric) {
      return res.status(404).json({ error: '找不到指定轮次的指标数据' });
    }
    res.json(metric);
  } catch (error) {
    next(error);
  }
});

// 获取任务的最新指标
app.get('/api/metrics/task/:taskId/latest', async (req, res, next) => {
  try {
    const taskId = parseInt(req.params.taskId, 10);
    const metric = await models.Metric.getLatestByTaskId(taskId);
    if (!metric) {
      return res.status(404).json({ error: '找不到该任务的指标数据' });
    }
    res.json(metric);
  } catch (error) {
    next(error);
  }
});

// 日志相关API
app.get('/api/tasks/:taskId/logs', async (req, res, next) => {
  try {
    const taskId = parseInt(req.params.taskId, 10);
    // 显式转换为整数类型
    const limit = parseInt(req.query.limit || 100, 10);
    const offset = parseInt(req.query.offset || 0, 10);
    
    console.log(`API路由: 获取任务(ID: ${taskId})的日志, limit: ${limit}, offset: ${offset}`);
    
    // 传递数字类型参数
    const logs = await models.Log.getByTaskId(taskId, limit, offset);
    res.json(logs);
  } catch (error) {
    next(error);
  }
});

app.get('/api/logs', async (req, res, next) => {
  try {
    // 显式转换为整数类型
    const limit = parseInt(req.query.limit || 100, 10);
    const offset = parseInt(req.query.offset || 0, 10);
    
    console.log(`API路由: 获取所有日志, limit: ${limit}, offset: ${offset}`);
    
    // 传递数字类型参数
    const logs = await models.Log.getAll(limit, offset);
    res.json(logs);
  } catch (error) {
    next(error);
  }
});

app.post('/api/logs', async (req, res, next) => {
  try {
    const log = await models.Log.create(req.body);
    res.status(201).json(log);
  } catch (error) {
    next(error);
  }
});

// 用户认证API
app.post('/api/auth/login', async (req, res, next) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ error: '用户名和密码不能为空' });
    }
    
    const user = await models.User.authenticate(username, password);
    
    if (!user) {
      return res.status(401).json({ error: '用户名或密码错误' });
    }
    
    // 这里可以添加JWT生成逻辑
    
    res.json({ 
      success: true, 
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      }
    });
  } catch (error) {
    next(error);
  }
});

// 完成模型下载和二期付款的API路由
app.post('/api/tasks/:id/complete-download', async (req, res) => {
  const taskId = req.params.id;
  const { userId, txHash } = req.body;
  
  console.log(`接收到模型下载和付款请求 - 任务ID: ${taskId}, 用户ID: ${userId}, 交易哈希:`, txHash ? txHash.substring(0, 10) + '...' : 'null');
  console.log('完整的请求体:', JSON.stringify(req.body));
  
  if (!taskId) {
    console.error('请求缺少任务ID');
    return res.status(400).json({ success: false, message: '缺少任务ID' });
  }
  
  if (!userId) {
    console.error('请求缺少用户ID');
    return res.status(400).json({ success: false, message: '缺少用户ID' });
  }
  
  console.log(`处理任务${taskId}的模型下载和付款请求，用户ID: ${userId}`);
  
  try {
    let transactionData;
    
    // 检查是否是区块链支付
    if (txHash) {
      console.log(`收到区块链交易哈希: ${txHash}`);
      try {
        // 调用区块链支付处理方法
        transactionData = await models.Task.completeModelDownloadWithBlockchain(taskId, userId, txHash);
        console.log(`区块链支付处理成功，交易数据:`, JSON.stringify(transactionData));
      } catch (blockchainError) {
        console.error(`区块链支付处理失败:`, blockchainError);
        console.error('错误堆栈:', blockchainError.stack);
        return res.status(500).json({
          success: false,
          message: `区块链支付处理失败: ${blockchainError.message || '服务器错误'}`,
          error: blockchainError.message
        });
      }
    } else {
      try {
        // 使用传统支付方式
        console.log(`使用传统支付方式处理付款`);
        transactionData = await models.Task.completeModelDownload(taskId, userId);
        console.log(`传统支付处理成功，交易数据:`, JSON.stringify(transactionData));
      } catch (paymentError) {
        console.error(`传统支付处理失败:`, paymentError);
        console.error('错误堆栈:', paymentError.stack);
        return res.status(500).json({
          success: false,
          message: `传统支付处理失败: ${paymentError.message || '服务器错误'}`,
          error: paymentError.message
        });
      }
    }
    
    if (!transactionData) {
      console.error(`未能获取交易数据，虽然没有抛出错误`);
      return res.status(500).json({
        success: false,
        message: '支付处理成功但未返回交易数据'
      });
    }
    
    console.log(`模型下载和付款完成，事务ID: ${transactionData.id}`);
    res.json({
      success: true,
      message: '二期付款处理成功',
      transaction: transactionData
    });
  } catch (error) {
    console.error(`处理模型下载付款失败:`, error);
    console.error('错误堆栈:', error.stack);
    
    // 发送详细的错误响应
    res.status(500).json({
      success: false,
      message: `处理付款失败: ${error.message || '服务器错误'}`,
      error: error.message,
      errorType: error.name,
      path: `/api/tasks/${taskId}/complete-download`
    });
  }
});

// 添加分配参与者奖励的API路由
app.post('/api/tasks/:id/distribute-rewards', async (req, res, next) => {
  try {
    const taskId = req.params.id;
    const { participants } = req.body;
    
    if (!participants || !Array.isArray(participants) || participants.length === 0) {
      return res.status(400).json({ error: '缺少有效的参与者列表' });
    }
    
    console.log(`开始处理任务(ID: ${taskId})的奖励分配，参与者数量: ${participants.length}`);
    
    const transactions = await models.Task.distributeRewards(taskId, participants);
    
    console.log('奖励分配处理成功，创建了', transactions.length, '条交易记录');
    res.status(200).json({
      success: true,
      message: '奖励分配已完成',
      transactions
    });
  } catch (error) {
    console.error('处理奖励分配失败:', error);
    res.status(500).json({
      error: '处理奖励分配失败',
      message: error.message
    });
  }
});

// 启动服务器
initDatabase().then(() => {
  // 打印当前注册的路由
  console.log('注册的路由:');
  app._router.stack.forEach(function(r){
    if (r.route && r.route.path){
      const methods = Object.keys(r.route.methods).join(',').toUpperCase();
      console.log(methods + ' ' + r.route.path);
    }
  });
  
  app.listen(port, () => {
    console.log(`API服务器运行在 http://localhost:${port}`);
  });
});

module.exports = app; 