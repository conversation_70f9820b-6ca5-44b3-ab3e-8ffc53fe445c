<!-- 服务端交易记录页面 -->
<template>
  <div class="transactions">
    <div class="page-header">
      <h1>交易记录</h1>
      <div class="page-actions">
        <el-button type="primary" @click="refreshData">
          <i class="fas fa-sync"></i> 刷新数据
        </el-button>
        <el-button type="success" @click="exportTransactions">
          <i class="fas fa-file-export"></i> 导出数据
        </el-button>
      </div>
    </div>
    
    <Breadcrumb />
    
    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.totalTransactions }}</div>
        <div class="stat-label">总交易数</div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-value">{{ formatAmount(statistics.totalAmount) }}</div>
        <div class="stat-label">总交易金额</div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.monthlyTransactions }}</div>
        <div class="stat-label">本月交易数</div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-value">{{ formatAmount(statistics.monthlyAmount) }}</div>
        <div class="stat-label">本月交易金额</div>
      </el-card>
    </div>
    
    <!-- 交易筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-group">
        <span class="filter-label">交易类型：</span>
        <el-select v-model="typeFilter" placeholder="所有类型" clearable>
          <el-option label="模型训练奖励" value="training_reward" />
          <el-option label="数据贡献奖励" value="data_reward" />
          <el-option label="模型使用费" value="model_usage" />
          <el-option label="平台手续费" value="platform_fee" />
        </el-select>
      </div>
      
      <div class="filter-group">
        <span class="filter-label">时间范围：</span>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
        />
      </div>
      
      <div class="search-box">
        <el-input v-model="searchQuery" placeholder="搜索客户端地址或交易ID" clearable>
          <template #prefix>
            <i class="fas fa-search"></i>
          </template>
        </el-input>
      </div>
    </div>
    
    <!-- 交易列表 -->
    <el-table
      :data="filteredTransactions"
      style="width: 100%"
      v-loading="loading"
      border
      row-key="id"
    >
      <el-table-column type="index" width="50" />
      
      <el-table-column prop="id" label="交易ID" width="140">
        <template #default="scope">
          <span class="transaction-id">{{ scope.row.id.slice(0, 8) }}...{{ scope.row.id.slice(-8) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="date" label="日期时间" width="180">
        <template #default="scope">
          {{ formatDateTime(scope.row.date) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="type" label="交易类型" width="140">
        <template #default="scope">
          <el-tag :type="getTransactionTypeTag(scope.row.type)">
            {{ getTransactionTypeText(scope.row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="clientAddress" label="客户端" min-width="180">
        <template #default="scope">
          <div class="client-info">
            <el-avatar :size="24" :src="getAvatarUrl(scope.row.clientAddress)" class="client-avatar"></el-avatar>
            <span>{{ shortenAddress(scope.row.clientAddress) }}</span>
            <el-tooltip v-if="scope.row.clientLabel" effect="dark" :content="scope.row.clientLabel" placement="top">
              <el-tag size="small" type="info">{{ scope.row.clientLabel }}</el-tag>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="taskName" label="相关任务" min-width="160">
        <template #default="scope">
          <router-link v-if="scope.row.taskId" :to="`/server/tasks/${scope.row.taskId}`">
            {{ scope.row.taskName }}
          </router-link>
          <span v-else>-</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="amount" label="金额" width="120" align="right">
        <template #default="scope">
          <span :class="{ 'positive-amount': scope.row.amount > 0, 'negative-amount': scope.row.amount < 0 }">
            {{ formatAmount(scope.row.amount) }}
          </span>
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="viewTransactionDetails(scope.row)" type="primary" plain>详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalTransactions"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 交易详情对话框 -->
    <el-dialog
      v-model="detailsVisible"
      title="交易详情"
      width="600px"
      v-if="currentTransaction"
    >
      <div class="transaction-details">
        <div class="details-item">
          <div class="item-label">交易ID：</div>
          <div class="item-value">{{ currentTransaction?.id }}</div>
        </div>
        
        <div class="details-item">
          <div class="item-label">交易类型：</div>
          <div class="item-value">
            <el-tag :type="getTransactionTypeTag(currentTransaction?.type)">
              {{ getTransactionTypeText(currentTransaction?.type) }}
            </el-tag>
          </div>
        </div>
        
        <div class="details-item">
          <div class="item-label">交易时间：</div>
          <div class="item-value">{{ formatDateTime(currentTransaction?.date) }}</div>
        </div>
        
        <div class="details-item">
          <div class="item-label">客户端：</div>
          <div class="item-value client-info">
            <el-avatar :size="24" :src="getAvatarUrl(currentTransaction?.clientAddress)" class="client-avatar"></el-avatar>
            <span>{{ currentTransaction?.clientAddress }}</span>
            <el-tag v-if="currentTransaction?.clientLabel" size="small" type="info">
              {{ currentTransaction?.clientLabel }}
            </el-tag>
          </div>
        </div>
        
        <div class="details-item">
          <div class="item-label">交易金额：</div>
          <div class="item-value" :class="{ 'positive-amount': currentTransaction?.amount > 0, 'negative-amount': currentTransaction?.amount < 0 }">
            {{ formatAmount(currentTransaction?.amount) }}
          </div>
        </div>
        
        <div class="details-item">
          <div class="item-label">交易状态：</div>
          <div class="item-value">
            <el-tag :type="getStatusTagType(currentTransaction?.status)">
              {{ getStatusText(currentTransaction?.status) }}
            </el-tag>
          </div>
        </div>
        
        <div class="details-item" v-if="currentTransaction?.taskId">
          <div class="item-label">相关任务：</div>
          <div class="item-value">
            <router-link :to="`/server/tasks/${currentTransaction?.taskId}`">
              {{ currentTransaction?.taskName }}
            </router-link>
          </div>
        </div>
        
        <div class="details-item" v-if="currentTransaction?.description">
          <div class="item-label">交易说明：</div>
          <div class="item-value">{{ currentTransaction?.description }}</div>
        </div>
        
        <div class="details-item">
          <div class="item-label">区块链交易：</div>
          <div class="item-value">
            <a :href="`https://etherscan.io/tx/${currentTransaction?.blockchainTx}`" target="_blank" class="blockchain-link" v-if="currentTransaction?.blockchainTx">
              {{ shortenAddress(currentTransaction?.blockchainTx) }}
              <i class="fas fa-external-link-alt"></i>
            </a>
            <span v-else>尚未上链</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import Breadcrumb from '@/components/Breadcrumb.vue';

export default {
  name: 'ServerTransactions',
  components: {
    Breadcrumb
  },
  setup() {
    const router = useRouter();
    const loading = ref(false);
    const transactions = ref([]);
    const typeFilter = ref('');
    const dateRange = ref([]);
    const searchQuery = ref('');
    const currentPage = ref(1);
    const pageSize = ref(10);
    const detailsVisible = ref(false);
    const currentTransaction = ref(null);
    
    // 统计数据
    const statistics = reactive({
      totalTransactions: 0,
      totalAmount: 0,
      monthlyTransactions: 0,
      monthlyAmount: 0
    });
    
    // 加载交易列表
    const loadTransactions = async () => {
      loading.value = true;
      try {
        // 这里应该调用API获取交易列表
        // 临时数据
        const now = Date.now();
        transactions.value = [
          {
            id: '0x' + Math.random().toString(16).substring(2, 34),
            date: new Date(now - 1 * 24 * 60 * 60 * 1000),
            type: 'training_reward',
            clientAddress: '0x' + Math.random().toString(16).substring(2, 42),
            clientLabel: '核心客户',
            taskId: 1,
            taskName: 'MNIST手写数字识别训练',
            amount: 500,
            status: 'confirmed',
            description: '完成MNIST手写数字识别训练任务的奖励',
            blockchainTx: '0x' + Math.random().toString(16).substring(2, 66)
          },
          {
            id: '0x' + Math.random().toString(16).substring(2, 34),
            date: new Date(now - 2 * 24 * 60 * 60 * 1000),
            type: 'data_reward',
            clientAddress: '0x' + Math.random().toString(16).substring(2, 42),
            clientLabel: '医疗机构',
            taskId: 2,
            taskName: '医疗影像分类模型',
            amount: 350,
            status: 'confirmed',
            description: '提供医疗影像数据的奖励',
            blockchainTx: '0x' + Math.random().toString(16).substring(2, 66)
          },
          {
            id: '0x' + Math.random().toString(16).substring(2, 34),
            date: new Date(now - 3 * 24 * 60 * 60 * 1000),
            type: 'platform_fee',
            clientAddress: '0x' + Math.random().toString(16).substring(2, 42),
            clientLabel: null,
            taskId: null,
            taskName: null,
            amount: -25,
            status: 'confirmed',
            description: '平台使用手续费',
            blockchainTx: '0x' + Math.random().toString(16).substring(2, 66)
          },
          {
            id: '0x' + Math.random().toString(16).substring(2, 34),
            date: new Date(now - 5 * 24 * 60 * 60 * 1000),
            type: 'model_usage',
            clientAddress: '0x' + Math.random().toString(16).substring(2, 42),
            clientLabel: '企业用户',
            taskId: 4,
            taskName: '自然语言情感分析',
            amount: -200,
            status: 'pending',
            description: '使用情感分析模型的费用',
            blockchainTx: null
          },
          {
            id: '0x' + Math.random().toString(16).substring(2, 34),
            date: new Date(now - 8 * 24 * 60 * 60 * 1000),
            type: 'training_reward',
            clientAddress: '0x' + Math.random().toString(16).substring(2, 42),
            clientLabel: null,
            taskId: 3,
            taskName: '金融交易欺诈检测',
            amount: 420,
            status: 'confirmed',
            description: '完成金融交易欺诈检测训练的奖励',
            blockchainTx: '0x' + Math.random().toString(16).substring(2, 66)
          }
        ];
        
        // 计算统计数据
        statistics.totalTransactions = transactions.value.length;
        statistics.totalAmount = transactions.value.reduce((sum, tx) => sum + tx.amount, 0);
        
        const thisMonth = new Date().getMonth();
        const thisYear = new Date().getFullYear();
        const monthlyTxs = transactions.value.filter(tx => {
          const txDate = new Date(tx.date);
          return txDate.getMonth() === thisMonth && txDate.getFullYear() === thisYear;
        });
        
        statistics.monthlyTransactions = monthlyTxs.length;
        statistics.monthlyAmount = monthlyTxs.reduce((sum, tx) => sum + tx.amount, 0);
      } catch (error) {
        console.error('加载交易列表失败:', error);
        ElMessage.error('加载交易列表失败，请重试');
      } finally {
        loading.value = false;
      }
    };
    
    // 格式化日期时间
    const formatDateTime = (date) => {
      if (!date) return '-';
      if (typeof date === 'string') {
        date = new Date(date);
      }
      
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    };
    
    // 格式化金额
    const formatAmount = (amount) => {
      if (amount === undefined || amount === null) return '-';
      const sign = amount > 0 ? '+' : '';
      return `${sign}${amount.toFixed(2)} 代币`;
    };
    
    // 获取交易类型标签
    const getTransactionTypeTag = (type) => {
      const types = {
        training_reward: 'success',
        data_reward: 'success',
        model_usage: 'warning',
        platform_fee: 'info'
      };
      return types[type] || 'info';
    };
    
    // 获取交易类型文本
    const getTransactionTypeText = (type) => {
      const texts = {
        training_reward: '训练奖励',
        data_reward: '数据奖励',
        model_usage: '模型使用',
        platform_fee: '平台费用'
      };
      return texts[type] || type;
    };
    
    // 获取状态标签类型
    const getStatusTagType = (status) => {
      const types = {
        pending: 'warning',
        confirmed: 'success',
        failed: 'danger'
      };
      return types[status] || 'info';
    };
    
    // 获取状态文本
    const getStatusText = (status) => {
      const texts = {
        pending: '待确认',
        confirmed: '已确认',
        failed: '失败'
      };
      return texts[status] || status;
    };
    
    // 获取头像URL
    const getAvatarUrl = (address) => {
      return `https://api.dicebear.com/7.x/identicon/svg?seed=${address}`;
    };
    
    // 缩短地址显示
    const shortenAddress = (address) => {
      if (!address) return '';
      return `${address.slice(0, 6)}...${address.slice(-4)}`;
    };
    
    // 计算过滤后的交易
    const filteredTransactions = computed(() => {
      let result = transactions.value;
      
      // 交易类型过滤
      if (typeFilter.value) {
        result = result.filter(tx => tx.type === typeFilter.value);
      }
      
      // 日期范围过滤
      if (dateRange.value && dateRange.value.length === 2) {
        const startDate = new Date(dateRange.value[0]);
        startDate.setHours(0, 0, 0, 0);
        
        const endDate = new Date(dateRange.value[1]);
        endDate.setHours(23, 59, 59, 999);
        
        result = result.filter(tx => {
          const txDate = new Date(tx.date);
          return txDate >= startDate && txDate <= endDate;
        });
      }
      
      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(tx => 
          tx.id.toLowerCase().includes(query) || 
          tx.clientAddress.toLowerCase().includes(query) ||
          (tx.clientLabel && tx.clientLabel.toLowerCase().includes(query))
        );
      }
      
      return result;
    });
    
    // 计算总交易数
    const totalTransactions = computed(() => filteredTransactions.value.length);
    
    // 页面大小改变
    const handleSizeChange = (size) => {
      pageSize.value = size;
    };
    
    // 当前页改变
    const handleCurrentChange = (page) => {
      currentPage.value = page;
    };
    
    // 查看交易详情
    const viewTransactionDetails = (tx) => {
      currentTransaction.value = tx;
      detailsVisible.value = true;
    };
    
    // 刷新数据
    const refreshData = () => {
      loadTransactions();
    };
    
    // 导出交易数据
    const exportTransactions = () => {
      ElMessage.success('交易数据导出功能将在后续版本实现');
    };
    
    onMounted(() => {
      loadTransactions();
    });
    
    return {
      loading,
      transactions,
      typeFilter,
      dateRange,
      searchQuery,
      currentPage,
      pageSize,
      detailsVisible,
      currentTransaction,
      statistics,
      filteredTransactions,
      totalTransactions,
      formatDateTime,
      formatAmount,
      getTransactionTypeTag,
      getTransactionTypeText,
      getStatusTagType,
      getStatusText,
      getAvatarUrl,
      shortenAddress,
      handleSizeChange,
      handleCurrentChange,
      viewTransactionDetails,
      refreshData,
      exportTransactions
    };
  }
};
</script>

<style scoped>
.transactions {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.page-actions {
  display: flex;
  gap: 12px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.filter-section {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-weight: 500;
  color: #555;
}

.search-box {
  width: 300px;
}

.transaction-id {
  font-family: monospace;
  font-size: 14px;
}

.client-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.client-avatar {
  flex-shrink: 0;
}

.positive-amount {
  color: #67C23A;
}

.negative-amount {
  color: #F56C6C;
}

.pagination {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

.transaction-details {
  padding: 16px;
}

.details-item {
  margin-bottom: 16px;
  display: flex;
}

.item-label {
  width: 100px;
  font-weight: 500;
  color: #606266;
}

.item-value {
  flex: 1;
}

.blockchain-link {
  color: #409EFF;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.blockchain-link:hover {
  text-decoration: underline;
}

@media (max-width: 1200px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }
  
  .filter-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-group {
    width: 100%;
  }
  
  .search-box {
    width: 100%;
  }
}
</style> 