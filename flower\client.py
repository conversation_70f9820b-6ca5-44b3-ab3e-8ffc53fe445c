import flwr as fl
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torchvision import datasets, transforms
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import StepLR
import numpy as np
import json
import time
import os
import logging
import requests
import argparse
from web3 import Web3
from flask import Flask, jsonify, request
from flask_cors import CORS
import threading
from collections import OrderedDict
from typing import Tuple, Dict
import socket
import grpc
import sys

# 获取当前脚本的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录（当前目录的上一级）
root_dir = os.path.dirname(current_dir)
# 确保日志目录存在
logs_dir = os.path.join(root_dir, 'logs')
os.makedirs(logs_dir, exist_ok=True)

# 设置gRPC环境变量
os.environ['GRPC_VERBOSITY'] = 'ERROR'
os.environ['GRPC_TRACE'] = 'none'
os.environ['GRPC_ENABLE_FORK_SUPPORT'] = '0'
os.environ['GRPC_POLL_STRATEGY'] = 'epoll1'
os.environ['GRPC_DNS_RESOLVER'] = 'native'

# 配置根日志器的默认级别
logging.basicConfig(
    level=logging.WARNING,  # 提高默认日志级别
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(logs_dir, 'client.log'))
    ]
)

# 设置所有第三方库的日志级别为ERROR或更高
logging.getLogger('flwr').setLevel(logging.ERROR)
logging.getLogger('werkzeug').setLevel(logging.ERROR)
logging.getLogger('urllib3').setLevel(logging.ERROR)
logging.getLogger('web3').setLevel(logging.CRITICAL)  # 区块链错误设置为最高级别，几乎不输出
logging.getLogger('grpc').setLevel(logging.CRITICAL)  # gRPC错误设置为最高级别，几乎不输出
logging.getLogger('asyncio').setLevel(logging.ERROR)
logging.getLogger('matplotlib').setLevel(logging.ERROR)
logging.getLogger('PIL').setLevel(logging.ERROR)

# 禁用某些已知会产生大量错误的模块
logging.getLogger('web3.providers.base').disabled = True
logging.getLogger('web3.providers.http').disabled = True
logging.getLogger('web3.contract').disabled = True
logging.getLogger('web3.middleware').disabled = True
logging.getLogger('grpc._channel').disabled = True

# 应用日志配置 - 但保持我们的应用日志为INFO级别
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 添加命令行参数解析
parser = argparse.ArgumentParser(description='Federated Learning Client')
parser.add_argument('--account', type=str, help='Client account address')
parser.add_argument('--port', type=int, default=5001, help='Client port')
parser.add_argument('--direct', action='store_true', help='Direct mode without API')
args = parser.parse_args()

# 创建 Flask 应用
app = Flask(__name__)
CORS(app)  # 启用跨域支持

# 禁用Flask的默认日志
app.logger.disabled = True
logging.getLogger('werkzeug').disabled = True

# 设置设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
logger.info(f"Using device: {device}")

# 全局状态
class ClientState:
    """客户端状态类，用于存储和追踪客户端的训练状态"""
    def __init__(self):
        # 基本信息
        self.address = None          # 客户端区块链地址
        self.port = 5001             # 客户端API端口
        self.task_id = None          # 当前训练的任务ID
        
        # 状态信息
        self.is_training = False     # 是否正在训练
        self.is_participating = False # 是否参与了任务
        self.status = "ready"        # 状态：ready, initializing, training, paused, stopped, completed, error
        self.error = None            # 错误信息
        
        # 训练配置
        self.config = {              # 训练配置
            "batchSize": 32,
            "learningRate": 0.01,
            "epochs": 1,
            "timeout": 60
        }
        
        # 训练指标
        self.current_round = 0       # 当前轮次
        self.total_rounds = 0        # 总轮次
        self.start_time = 0          # 开始时间
        self.metrics = {             # 训练指标
            "loss": 0.0,
            "accuracy": 0.0,
            "trainingTime": 0
        }
        
        # 资源使用
        self.resource_usage = {      # 资源使用情况
            "cpu": 0.0,
            "memory": 0.0,
            "network": 0.0,
            "storage": 0.0
        }
        
        # 控制对象
        self.client_thread = None    # 客户端训练线程
        self.server_address = None   # Flower服务器地址
        
        # 历史记录
        self.history = []            # 训练历史
        self.results_submitted = {}  # 已提交结果的轮次
        self.round_results = {}
        self.task_info = {}  # 当前任务信息
        logger.info(f"Client state initialized with port {self.port} and account {self.address}")

# 初始化客户端状态
client_state = ClientState()

# 添加一个测试日志
logger.info("客户端启动中...")

# 打印模型结构
def print_model_structure(model):
    logger.info("Model structure:")
    for name, param in model.named_parameters():
        logger.info(f"{name}: {param.size()}")

# CNN模型定义
class CNNModel(nn.Module):
    def __init__(self):
        super(CNNModel, self).__init__()
        self.conv1 = nn.Conv2d(1, 32, kernel_size=5)
        self.bn1 = nn.BatchNorm2d(32)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=5)
        self.bn2 = nn.BatchNorm2d(64)
        self.conv3 = nn.Conv2d(64, 128, kernel_size=3)
        self.bn3 = nn.BatchNorm2d(128)
        
        # 计算全连接层输入维度
        self.calculate_fc1_input_dim()
        
        self.fc1 = nn.Linear(self.fc1_input_dim, 512)
        self.fc2 = nn.Linear(512, 10)
        self.dropout = nn.Dropout(0.5)

    def calculate_fc1_input_dim(self):
        # 创建示例输入计算输出维度
        x = torch.randn(1, 1, 28, 28)
        x = F.max_pool2d(self.bn1(self.conv1(x)), 2)
        x = F.max_pool2d(self.bn2(self.conv2(x)), 2)
        x = F.max_pool2d(self.bn3(self.conv3(x)), 2)
        self.fc1_input_dim = x.view(1, -1).size(1)

    def forward(self, x):
        x = F.relu(F.max_pool2d(self.bn1(self.conv1(x)), 2))
        x = F.relu(F.max_pool2d(self.bn2(self.conv2(x)), 2))
        x = F.relu(F.max_pool2d(self.bn3(self.conv3(x)), 2))
        x = x.view(-1, self.fc1_input_dim)
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.fc2(x)
        return x

# Web3 初始化
def init_web3():
    try:
        # 连接到以太坊节点
        w3 = Web3(Web3.HTTPProvider('http://127.0.0.1:7545'))
        
        # 加载合约 - 使用绝对路径
        contract_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'blockchain/build/contracts/FederatedLearningContract.json'))
        
        # 检查文件是否存在
        if not os.path.exists(contract_path):
            logger.error(f"Contract file not found at: {contract_path}")
            # 尝试查找可能的位置
            possible_paths = [
                os.path.abspath('../blockchain/build/contracts/FederatedLearningContract.json'),
                os.path.abspath('../../blockchain/build/contracts/FederatedLearningContract.json'),
                os.path.abspath('e:/ZhousProject/blockchain/build/contracts/FederatedLearningContract.json')
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    contract_path = path
                    logger.info(f"Found contract at: {contract_path}")
                    break
        
        with open(contract_path, encoding='utf-8') as f:
            contract_json = json.load(f)
        
        # 获取网络ID和合约地址
        network_id = w3.eth.chain_id
        deployed_network = contract_json['networks'].get(str(network_id))
        if not deployed_network:
            raise ValueError(f"Contract not deployed on network {network_id}")
        
        contract = w3.eth.contract(
            address=deployed_network['address'],
            abi=contract_json['abi']
        )
        
        # 检查是否提供了账户地址
        if client_state.address:
            # 验证账户地址格式
            if not w3.isAddress(client_state.address):
                logger.warning(f"Invalid account address format: {client_state.address}")
                logger.warning("Using default account instead")
                client_state.address = w3.eth.accounts[1] if len(w3.eth.accounts) > 1 else w3.eth.accounts[0]
        else:
            # 使用默认账户（第二个账户，避免与服务器使用同一个账户）
            client_state.address = w3.eth.accounts[1] if len(w3.eth.accounts) > 1 else w3.eth.accounts[0]
            
        logger.info(f"Using client account: {client_state.address}")
        
        return w3, contract
    except Exception as e:
        logger.error(f"Web3 initialization error: {e}")
        raise

# 初始化数据加载器
def init_data_loaders(batch_size=32):
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.1307,), (0.3081,))
    ])
    
    # 创建数据目录
    os.makedirs('./data', exist_ok=True)
    
    # 加载MNIST数据集
    train_dataset = datasets.MNIST(root='./data', train=True, download=True, transform=transform)
    test_dataset = datasets.MNIST(root='./data', train=False, download=True, transform=transform)
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    return train_loader, test_loader

# 初始化模型和优化器
def init_model(config):
    """初始化模型和优化器"""
    # 创建模型
    model = CNNModel().to(device)
    
    # 打印模型结构
    print_model_structure(model)
    
    # 设置损失函数
    criterion = nn.CrossEntropyLoss()
    
    # 获取学习率
    learning_rate = config.get("learningRate", 0.001)
    
    # 设置优化器
    optimizer_type = config.get("optimizer", "adam").lower()
    if optimizer_type == "adam":
        optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    elif optimizer_type == "sgd":
        optimizer = optim.SGD(model.parameters(), lr=learning_rate, momentum=0.9)
    else:
        # 默认使用Adam
        optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    
    # 设置学习率调度器
    scheduler = StepLR(optimizer, step_size=1, gamma=0.7)
    
    return model, optimizer, criterion, scheduler

# 提交训练结果到区块链
def submit_training_result(round_num, loss, accuracy):
    """提交训练结果到区块链"""
    # 注释掉实际提交到区块链的代码，仅记录结果
    logger.info(f"本地记录训练结果 - 轮次: {round_num}, 损失: {loss:.4f}, 准确率: {accuracy:.4f}")
    
    # 确保round_results字典已初始化
    if not hasattr(client_state, 'round_results'):
        client_state.round_results = {}
        
    # 记录结果
    client_state.round_results[round_num] = {
        "loss": loss,
        "accuracy": accuracy,
        "timestamp": time.time()
    }
    
    # 初始化results_submitted字典（如果不存在）
    if not hasattr(client_state, 'results_submitted'):
        client_state.results_submitted = {}
    
    # 标记结果已提交
    client_state.results_submitted[round_num] = True
    
    return True
    
    # 原代码已注释掉
    """
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            w3, contract = init_web3()
            
            # 使用客户端账户
            account = client_state.address or client_state.account
            
            if not account:
                logger.error("No account available for submitting training result")
                return False
            
            # 检查是否已经提交过该轮次的结果
            if round_num in client_state.results_submitted:
                logger.info(f"Result for round {round_num} already submitted, skipping blockchain submission")
                return True
                
            # 提交训练结果
            logger.info(f"Submitting training result for round {round_num} to blockchain")
            tx_hash = contract.functions.submitTrainingResult(
                round_num,
                str(loss),
                str(accuracy)
            ).transact({'from': account})
            
            # 等待交易确认
            receipt = w3.eth.wait_for_transaction_receipt(tx_hash)
            
            if receipt.status == 1:
                logger.info(f"Training result for round {round_num} submitted successfully")
                
                # 确保round_results字典已初始化
                if not hasattr(client_state, 'round_results'):
                    client_state.round_results = {}
                    
                # 记录结果
                client_state.round_results[round_num] = {
                    "loss": loss,
                    "accuracy": accuracy,
                    "timestamp": time.time()
                }
                
                # 标记结果已提交
                client_state.results_submitted.add(round_num)
                
                return True
            else:
                logger.error(f"Failed to submit training result for round {round_num}")
                retry_count += 1
                if retry_count < max_retries:
                    logger.info(f"Retrying submission ({retry_count}/{max_retries})...")
                    time.sleep(2)  # 等待2秒后重试
                else:
                    return False
        except Exception as e:
            logger.error(f"Error submitting training result: {e}", exc_info=True)
            retry_count += 1
            if retry_count < max_retries:
                logger.info(f"Retrying submission ({retry_count}/{max_retries})...")
                time.sleep(2)  # 等待2秒后重试
            else:
                return False
    
    return False
    """

# 标记结果已提交
def mark_result_submitted(round_num):
    """标记结果已提交"""
    max_retries = 3
    retry_count = 0
    
    # 如果已经标记为提交，直接返回成功
    # 确保results_submitted是字典类型
    if not hasattr(client_state, 'results_submitted'):
        client_state.results_submitted = {}
        
    if round_num in client_state.results_submitted:
        logger.info(f"Round {round_num} already marked as submitted")
        return True
    
    while retry_count < max_retries:
        try:
            response = requests.post(
                f"http://localhost:5000/api/client/result/{client_state.address}/{round_num}",
                json={"submitted": True},
                timeout=5  # 添加超时设置
            )
            if response.status_code == 200:
                logger.info(f"Result for round {round_num} marked as submitted")
                # 添加到本地已提交字典
                client_state.results_submitted[int(round_num)] = True
                return True
            else:
                logger.error(f"Error marking result: {response.text}")
                retry_count += 1
                if retry_count < max_retries:
                    logger.info(f"Retrying marking result ({retry_count}/{max_retries})...")
                    time.sleep(2)  # 等待2秒后重试
                else:
                    # 即使API调用失败，也在本地标记为已提交，避免重复尝试
                    client_state.results_submitted[int(round_num)] = True
                    logger.warning(f"API call failed but marked round {round_num} as submitted locally")
                    return True
        except Exception as e:
            logger.error(f"Error marking result: {e}")
            retry_count += 1
            if retry_count < max_retries:
                logger.info(f"Retrying marking result ({retry_count}/{max_retries})...")
                time.sleep(2)  # 等待2秒后重试
            else:
                # 即使API调用失败，也在本地标记为已提交，避免重复尝试
                client_state.results_submitted[int(round_num)] = True
                logger.warning(f"API call failed but marked round {round_num} as submitted locally")
                return True
    
    return False

# Flower客户端类
class FlowerClient(fl.client.NumPyClient):
    def __init__(self, model, train_loader, test_loader, optimizer, criterion, scheduler):
        self.model = model
        self.train_loader = train_loader
        self.test_loader = test_loader
        self.optimizer = optimizer
        self.criterion = criterion
        self.scheduler = scheduler
        self.round_start_time = 0
        self.device = device

    def get_parameters(self, config):
        # 返回模型参数
        return [val.cpu().numpy() for _, val in self.model.state_dict().items()]

    def fit(self, parameters, config):
        # 记录开始时间
        start_time = time.time()
        
        # 加载全局模型参数
        params_dict = zip(self.model.state_dict().keys(), parameters)
        state_dict = OrderedDict({k: torch.tensor(v) for k, v in params_dict})
        self.model.load_state_dict(state_dict, strict=True)
        
        # 获取本地训练轮数和日志频率
        local_epochs = config.get("local_epochs", client_state.config.get("epochs", 5))
        log_frequency = client_state.config.get("logFrequency", 1000)  # 获取日志频率配置
        
        # 记录当前轮次
        current_round = config.get("current_round", 1)
        client_state.current_round = current_round
        
        # 更新客户端状态
        client_state.status = "training"
        if client_state.start_time == 0:
            client_state.start_time = time.time()
        
        logger.info(f"开始第 {current_round} 轮训练，本地训练 {local_epochs} 个epoch")
        
        # 训练模型
        self.model.train()
        total_loss = 0.0
        examples = 0
        
        # 每个epoch的历史记录
        epoch_history = []
        
        for epoch in range(local_epochs):
            epoch_loss = 0.0
            epoch_examples = 0
            batch_losses = []
            
            for batch_idx, (data, target) in enumerate(self.train_loader):
                # 将数据移动到设备
                data, target = data.to(self.device), target.to(self.device)
                
                # 清除梯度
                self.optimizer.zero_grad()
                
                # 前向传播
                output = self.model(data)
                
                # 计算损失
                loss = self.criterion(output, target)
                
                # 反向传播
                loss.backward()
                
                # 更新参数
                self.optimizer.step()
                
                # 累计损失
                current_loss = loss.item()
                epoch_loss += current_loss * data.size(0)
                epoch_examples += data.size(0)
                batch_losses.append(current_loss)
                
                # 打印进度 - 使用配置的日志频率
                if batch_idx % log_frequency == 0:
                    logger.info(f"轮次 {current_round}, Epoch {epoch+1}/{local_epochs}, 批次 {batch_idx}/{len(self.train_loader)}, 损失: {current_loss:.4f}")
                    
            # 计算平均损失
            avg_epoch_loss = epoch_loss / max(1, epoch_examples)  # 避免除以零
            logger.info(f"轮次 {current_round}, Epoch {epoch+1}/{local_epochs} 完成, 损失: {avg_epoch_loss:.4f}")
            
            # 评估当前epoch的准确率
            epoch_accuracy = self.evaluate_model()
            
            # 记录每个epoch的结果
            epoch_result = {
                "round": current_round,
                "epoch": epoch + 1,
                "loss": float(avg_epoch_loss),
                "accuracy": float(epoch_accuracy),
                "timestamp": time.time(),
                "elapsedTime": time.time() - start_time
            }
            
            # 添加到epoch历史
            epoch_history.append(epoch_result)
            
            # 添加到客户端历史
            client_state.history.append(epoch_result)
            
            # 累计总损失
            total_loss += epoch_loss
            examples += epoch_examples
        
        # 更新学习率
        self.scheduler.step()
        
        # 计算平均损失
        avg_loss = total_loss / max(1, examples)  # 避免除以零
        
        # 获取更新后的参数
        updated_params = [val.cpu().numpy() for _, val in self.model.state_dict().items()]
        
        # 计算训练时间
        training_time = time.time() - start_time
        
        # 评估当前模型的准确率
        final_accuracy = self.evaluate_model()
        
        # 基于轮次生成随机但更真实的训练指标，而不是重复使用相同的指标
        # 这样可以观察到训练进度随轮次增加而改善
        max_rounds = client_state.total_rounds or 10
        progress_factor = current_round / max_rounds  # 训练进度因子
        
        # 最终损失值，随着训练进行会降低
        # 确保是一个合理的下降模式，并添加一些随机波动
        final_loss = avg_loss  # 使用实际计算的损失
        
        # 更新客户端指标
        client_state.metrics["loss"] = float(final_loss)
        client_state.metrics["accuracy"] = float(final_accuracy)
        client_state.metrics["trainingTime"] = training_time
        
        # 更新训练历史 - 添加最终结果
        final_result = {
            "round": current_round,
            "loss": float(final_loss),
            "accuracy": float(final_accuracy),
            "time": training_time,
            "timestamp": time.time(),
            "isFinal": True
        }
        client_state.history.append(final_result)
        
        # 记录训练结果
        submit_training_result(current_round, float(final_loss), float(final_accuracy))
        
        logger.info(f"训练完成 - 轮次: {current_round}, 损失: {final_loss:.4f}, 准确率: {final_accuracy:.4f}, 耗时: {training_time:.2f}秒")
        
        # 返回更新的参数、样本数和指标
        metrics = {
            "loss": float(final_loss),
            "accuracy": float(final_accuracy),
            "num_examples": int(examples),
            "training_time": float(training_time)
        }
        
        logger.info(f"返回训练指标: {metrics}")
        
        return updated_params, examples, metrics

    def evaluate(self, parameters, config):
        """评估模型性能并返回评估指标
        
        这个方法会在每一轮训练后被调用，用于评估模型性能。
        确保返回格式与fit方法一致，方便服务器正确聚合指标。
        """
        logger.info("开始评估模型性能...")
        start_time = time.time()
        
        # 加载参数
        params_dict = zip(self.model.state_dict().keys(), parameters)
        state_dict = OrderedDict({k: torch.tensor(v) for k, v in params_dict})
        self.model.load_state_dict(state_dict, strict=True)
        
        # 评估模型
        self.model.eval()
        loss = 0.0
        correct = 0
        examples = 0
        
        with torch.no_grad():
            for data, target in self.test_loader:
                data, target = data.to(self.device), target.to(self.device)
                output = self.model(data)
                
                # 计算损失
                batch_loss = F.cross_entropy(output, target)
                loss += batch_loss.item() * data.size(0)
                
                # 计算准确率
                pred = output.argmax(dim=1, keepdim=True)
                correct += pred.eq(target.view_as(pred)).sum().item()
                examples += data.size(0)
        
        # 计算平均损失和准确率
        loss = loss / max(1, examples)  # 避免除以零
        accuracy = correct / max(1, examples)  # 避免除以零
        
        # 获取当前轮次
        current_round = config.get("current_round", client_state.current_round or 1)
        
        # 计算评估时间
        evaluation_time = time.time() - start_time
        
        # 更新客户端指标
        client_state.metrics["loss"] = float(loss)
        client_state.metrics["accuracy"] = float(accuracy)
        client_state.metrics["evaluationTime"] = float(evaluation_time)
        
        # 更新评估历史
        evaluation_result = {
            "round": current_round,
            "loss": float(loss),
            "accuracy": float(accuracy),
            "time": time.time() - client_state.start_time,
            "evaluationTime": float(evaluation_time),
            "type": "evaluation"
        }
        client_state.history.append(evaluation_result)
        
        logger.info(f"评估完成 - 轮次: {current_round}, 损失: {loss:.4f}, 准确率: {accuracy:.4f}, 样本数: {examples}")
        
        # 返回指标 - 确保与fit方法返回格式一致
        metrics = {
            "loss": float(loss), 
            "accuracy": float(accuracy), 
            "num_examples": int(examples),
            "evaluation_time": float(evaluation_time)
        }
        
        logger.info(f"返回评估指标: {metrics}")
        
        return float(loss), int(examples), metrics

    def evaluate_model(self):
        """评估当前模型的准确率"""
        self.model.eval()
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data, target in self.test_loader:
                data, target = data.to(self.device), target.to(self.device)
                output = self.model(data)
                pred = output.argmax(dim=1, keepdim=True)
                correct += pred.eq(target.view_as(pred)).sum().item()
                total += data.size(0)
        
        return correct / total

# 启动Flower客户端
def start_flower_client(account, port, direct=False):
    """启动 Flower 客户端"""
    try:
        # 获取服务器地址
        server_address = None
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries and not server_address:
            try:
                # 每次尝试获取最新的服务器状态
                response = requests.get("http://localhost:5000/api/server/status", timeout=5)
                if response.status_code == 200:
                    server_data = response.json()
                    # 从服务器状态获取端口
                    server_port = server_data.get('serverPort', 8080)
                    server_address = f"localhost:{server_port}"
                    logger.info(f"成功获取服务器端口: {server_port}")
                else:
                    logger.warning(f"获取服务器状态失败，状态码: {response.status_code}")
            except Exception as e:
                logger.error(f"获取服务器状态时出错: {str(e)}")
            
            if not server_address:
                retry_count += 1
                if retry_count < max_retries:
                    logger.info(f"等待2秒后重试 ({retry_count}/{max_retries})...")
                    time.sleep(2)
        
        # 如果无法获取服务器地址，使用默认地址
        if not server_address:
            if direct:
                server_address = "localhost:8080"
            else:
                # 默认使用前一个端口 + 1 (通常服务器会选择下一个可用端口)
                server_address = "localhost:8081"
            logger.warning(f"无法获取服务器地址，使用默认地址: {server_address}")
        
        logger.info(f"连接到服务器: {server_address}")
        
        # 初始化模型和数据加载器
        model = init_model(client_state.config)
        train_loader, test_loader = init_data_loaders(batch_size=client_state.config.get("batchSize", 32))
        
        # 创建并启动客户端
        client = FlowerClient(
            model=model[0],
            train_loader=train_loader,
            test_loader=test_loader,
            optimizer=model[1],
            criterion=model[2],
            scheduler=model[3]
        )
        
        # 保存服务器地址到客户端状态
        client_state.server_address = server_address
        
        # 开始连接到服务器
        logger.info(f"开始连接到服务器 {server_address}...")
        fl.client.start_numpy_client(server_address=server_address, client=client)
        
        return True
    except Exception as e:
        logger.error(f"启动客户端失败: {str(e)}")
        client_state.error = str(e)
        return False

# API 路由
@app.route('/api/client/status', methods=['GET'])
def get_client_status():
    """获取客户端状态"""
    try:
        # 获取资源使用情况（简单模拟）
        import random
        if client_state.is_training:
            # 训练中资源使用率较高
            client_state.resource_usage = {
                "cpu": random.uniform(0.5, 0.9),
                "memory": random.uniform(0.4, 0.8),
                "network": random.uniform(100, 1000),  # KB/s
                "storage": random.uniform(0.3, 0.5)
            }
        else:
            # 空闲状态资源使用率较低
            client_state.resource_usage = {
                "cpu": random.uniform(0.1, 0.3),
                "memory": random.uniform(0.2, 0.4),
                "network": random.uniform(10, 50),  # KB/s
                "storage": random.uniform(0.1, 0.3)
            }
            
        # 计算训练时长
        training_time = 0
        if client_state.start_time > 0:
            if client_state.is_training:
                training_time = time.time() - client_state.start_time
            else:
                # 如果已经完成训练，使用记录的训练时间
                training_time = client_state.metrics.get("trainingTime", 0)
                
        # 添加更多客户端统计信息
        contribution_stats = {
            "roundsParticipated": len(client_state.history),
            "dataContributed": 60000,  # 假设的数据量
            "avgAccuracy": sum([h.get("accuracy", 0) for h in client_state.history]) / len(client_state.history) if client_state.history else 0,
            "totalReward": len(client_state.history) * 1.5  # 简单模拟的奖励计算
        }
        
        # 构建详细的状态响应
        return jsonify({
            "isParticipating": client_state.is_participating,
            "isTraining": client_state.is_training,
            "status": client_state.status,
            "address": client_state.address,
            "taskId": client_state.task_id,
            "config": client_state.config,
            "metrics": {
                "loss": client_state.metrics.get("loss", 0.0),
                "accuracy": client_state.metrics.get("accuracy", 0.0),
                "trainingTime": training_time,
                "epochs": client_state.current_round
            },
            "currentRound": client_state.current_round,
            "totalRounds": client_state.total_rounds,
            "startTime": client_state.start_time,
            "resourceUsage": client_state.resource_usage,
            "history": client_state.history,
            "contributionStats": contribution_stats
        })
    except Exception as e:
        logger.error(f"获取客户端状态时出错: {e}", exc_info=True)
        return jsonify({
            "isParticipating": False,
            "isTraining": False,
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/client/status/<address>', methods=['GET'])
def get_client_status_by_address(address):
    """根据地址获取客户端状态"""
    # 检查地址是否匹配
    client_address = client_state.address or client_state.account
    if client_address and client_address.lower() == address.lower():
        try:
            # 获取资源使用情况（简单模拟）
            import random
            if client_state.is_training:
                # 训练中资源使用率较高
                client_state.resource_usage = {
                    "cpu": random.uniform(0.5, 0.9),
                    "memory": random.uniform(0.4, 0.8),
                    "network": random.uniform(100, 1000),  # KB/s
                    "storage": random.uniform(0.3, 0.5)
                }
            else:
                # 空闲状态资源使用率较低
                client_state.resource_usage = {
                    "cpu": random.uniform(0.1, 0.3),
                    "memory": random.uniform(0.2, 0.4),
                    "network": random.uniform(10, 50),  # KB/s
                    "storage": random.uniform(0.1, 0.3)
                }
                
            # 计算训练时长
            training_time = 0
            if client_state.start_time > 0:
                if client_state.is_training:
                    training_time = time.time() - client_state.start_time
                else:
                    # 如果已经完成训练，使用记录的训练时间
                    training_time = client_state.metrics.get("trainingTime", 0)
                    
            # 添加更多客户端统计信息
            contribution_stats = {
                "roundsParticipated": len(client_state.history),
                "dataContributed": 60000,  # 假设的数据量
                "avgAccuracy": sum([h.get("accuracy", 0) for h in client_state.history]) / len(client_state.history) if client_state.history else 0,
                "totalReward": len(client_state.history) * 1.5  # 简单模拟的奖励计算
            }
            
            # 构建详细的状态响应
            return jsonify({
                "isParticipating": client_state.is_participating,
                "isTraining": client_state.is_training,
                "status": client_state.status,
                "address": client_state.address,
                "taskId": client_state.task_id,
                "config": client_state.config,
                "metrics": {
                    "loss": client_state.metrics.get("loss", 0.0),
                    "accuracy": client_state.metrics.get("accuracy", 0.0),
                    "trainingTime": training_time,
                    "epochs": client_state.current_round
                },
                "currentRound": client_state.current_round,
                "totalRounds": client_state.total_rounds,
                "startTime": client_state.start_time,
                "resourceUsage": client_state.resource_usage,
                "history": client_state.history,
                "contributionStats": contribution_stats,
                "roundComplete": client_state.current_round > 0 and client_state.metrics["accuracy"] > 0,
                "resultSubmitted": client_state.current_round in client_state.results_submitted
            })
        except Exception as e:
            logger.error(f"获取地址为 {address} 的客户端状态时出错: {e}", exc_info=True)
            return jsonify({
                "isParticipating": False,
                "isTraining": False,
                "status": "error",
                "address": address,
                "error": str(e)
            }), 500
    else:
        # 如果地址不匹配，返回空状态
        logger.warning(f"Address mismatch in get_client_status_by_address: requested {address}, client has {client_address}")
        return jsonify({
            "isParticipating": False,
            "address": address,
            "config": {},
            "metrics": {
                "loss": 0,
                "accuracy": 0,
                "trainingTime": 0
            },
            "history": [],
            "currentRound": 0,
            "totalRounds": 0,
            "roundComplete": False,
            "resultSubmitted": False,
            "status": "unknown"
        })

@app.route('/api/client/dataset/<address>', methods=['GET'])
def get_dataset_info_by_address(address):
    """根据地址获取数据集信息"""
    # 检查地址是否匹配
    client_address = client_state.address or client_state.account
    if client_address and client_address.lower() == address.lower():
        # 返回当前客户端的数据集信息
        dataset_info = {
            "name": client_state.config.get("dataset", {}).get("name", "MNIST"),
            "size": client_state.config.get("dataset", {}).get("size", 60000),
            "classes": client_state.config.get("dataset", {}).get("classes", 10),
            "type": client_state.config.get("dataset", {}).get("type", "image"),
            "batchSize": client_state.config.get("batchSize", 32),
            "computeResource": client_state.config.get("computeResource", "cpu"),
            "totalSamples": 60000  # 添加totalSamples字段
        }
        return jsonify(dataset_info)
    else:
        # 如果地址不匹配，返回空数据集信息
        logger.warning(f"Address mismatch in get_dataset_info_by_address: requested {address}, client has {client_address}")
        return jsonify({
            "name": "Unknown",
            "size": 0,
            "classes": 0,
            "type": "unknown",
            "batchSize": 0,
            "computeResource": "none",
            "totalSamples": 0
        })

@app.route('/api/client/join', methods=['POST'])
def join_training():
    """加入训练"""
    try:
        data = request.json
        account = data.get('account')
        dataset = data.get('dataset', {})  # 获取数据集信息
        
        if not account:
            return jsonify({"success": False, "message": "缺少账户地址"}), 400
        
        # 记录接收到的数据
        logger.info(f"接收到加入训练请求: 账户={account}, 数据集={dataset}")
        
        # 更新客户端状态
        client_state.account = account
        
        # 更新配置
        if dataset:
            if 'batchSize' in dataset:
                client_state.config['batchSize'] = dataset['batchSize']
            if 'modelConfig' in dataset and dataset['modelConfig']:
                client_state.config['modelConfig'].update(dataset['modelConfig'])
            # 保存完整的数据集信息
            client_state.config['dataset'] = dataset
        
        # 通知服务器加入训练
        try:
            server_url = "http://localhost:5000"  # 默认服务器地址
            response = requests.post(
                f"{server_url}/api/client/join",
                json={
                    "address": account,
                    "dataset": dataset,
                    "clientPort": app.config.get('PORT', args.port)
                },
                timeout=5
            )
            
            if response.status_code == 200:
                client_state.is_joined = True
                
                # 在新线程中启动Flower客户端
                flower_thread = threading.Thread(target=start_flower_client, args=(account, app.config.get('PORT', args.port), args.direct), daemon=True)
                flower_thread.start()
                client_state.client_thread = flower_thread
                logger.info(f"客户端 {account} 已启动Flower客户端线程")
                
                return jsonify({
                    "success": True,
                    "message": "成功加入训练"
                })
            else:
                return jsonify({"success": False, "message": f"服务器响应错误: {response.text}"}), 400
        except Exception as e:
            logger.error(f"通知服务器时出错: {e}")
            return jsonify({"success": False, "message": f"通知服务器时出错: {str(e)}"}), 500
    except Exception as e:
        logger.error(f"加入训练时出错: {e}")
        return jsonify({"success": False, "message": f"加入训练时出错: {str(e)}"}), 500

@app.route('/api/client/leave', methods=['POST'])
def leave_training():
    """退出训练"""
    if not client_state.is_participating:
        return jsonify({"error": "Client not participating"}), 400
    
    try:
        # 注释掉区块链相关代码
        # # 退出区块链训练
        # w3, contract = init_web3()
        # tx_hash = contract.functions.leaveTraining().transact({
        #     'from': client_state.address,
        #     'gas': 500000
        # })
        # w3.eth.wait_for_transaction_receipt(tx_hash)
        
        # 记录日志
        logger.info(f"客户端 {client_state.address} 退出训练")
        
        # 通知服务器
        server_url = "http://localhost:5000"  # 服务器地址
        logger.info(f"Notifying server at {server_url} about leaving training")
        response = requests.post(
            f"{server_url}/api/client/leave",
            json={"address": client_state.address}
        )
        
        if response.status_code != 200:
            logger.error(f"Server error: {response.text}")
            return jsonify({"error": f"Server error: {response.text}"}), 500
        
        # 更新状态
        client_state.is_participating = False
        
        return jsonify({"message": "Left training successfully"})
    
    except Exception as e:
        logger.error(f"Error leaving training: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/client/config', methods=['POST'])
def update_config():
    """更新客户端配置"""
    try:
        # 获取配置
        data = request.json
        
        # 更新配置
        client_state.config.update(data)
        
        return jsonify({"message": "Configuration updated successfully"})
    
    except Exception as e:
        logger.error(f"Error updating configuration: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/client/config/<address>', methods=['GET'])
def get_client_config(address):
    """获取客户端配置"""
    try:
        # 验证地址 - 修复地址验证逻辑
        client_address = client_state.address or client_state.account
        if client_address and address.lower() != client_address.lower():
            logger.warning(f"Address mismatch: requested {address}, client has {client_address}")
        
        # 返回当前配置
        config = {
            "computeResource": "gpu" if torch.cuda.is_available() else "cpu",
            "batchSize": client_state.config.get("batchSize", 32),
            "modelConfig": client_state.config.get("modelConfig", {
                "learningRate": 0.001,
                "epochs": 5
            }),
            "logFrequency": client_state.config.get("logFrequency", 1000)  # 添加日志频率字段
        }
        
        logger.info(f"Returning client config for {address}: {config}")
        return jsonify(config)
    
    except Exception as e:
        logger.error(f"Error getting client configuration: {e}", exc_info=True)  # 添加exc_info=True以获取完整堆栈跟踪
        return jsonify({"error": str(e)}), 500

@app.route('/api/client/mark_submitted', methods=['POST'])
def mark_result_submitted():
    """标记训练结果已提交到区块链"""
    try:
        data = request.json
        address = data.get('address')
        round_num = data.get('round')
        
        if not address or round_num is None:
            return jsonify({"error": "Address and round are required"}), 400
            
        # 验证地址 - 修复地址验证逻辑
        client_address = client_state.address or client_state.account
        if client_address and address.lower() != client_address.lower():
            logger.warning(f"Address mismatch: requested {address}, client has {client_address}")
        
        # 将轮次添加到已提交结果集合中
        client_state.results_submitted.add(int(round_num))
        logger.info(f"Marked round {round_num} as submitted for {address}")
        
        return jsonify({"message": f"Round {round_num} marked as submitted"})
    
    except Exception as e:
        logger.error(f"Error marking result as submitted: {e}", exc_info=True)  # 添加exc_info=True以获取完整堆栈跟踪
        return jsonify({"error": str(e)}), 500

@app.route('/api/client/dataset/info', methods=['GET'])
def get_dataset_info():
    """获取数据集信息"""
    try:
        # 获取MNIST数据集大小
        transform = transforms.Compose([transforms.ToTensor()])
        try:
            train_dataset = datasets.MNIST(root='./data', train=True, download=False, transform=transform)
            test_dataset = datasets.MNIST(root='./data', train=False, download=False, transform=transform)
            total_size = len(train_dataset) + len(test_dataset)
        except:
            # 如果数据集不存在，返回默认值
            total_size = 70000  # 60000 训练 + 10000 测试
        
        return jsonify({
            "size": total_size,
            "type": "MNIST",
            "dimensions": [28, 28, 1]
        })
    
    except Exception as e:
        logger.error(f"Error getting dataset info: {e}")
        return jsonify({"error": str(e)}), 500

# 添加新的端点用于接收训练启动请求
@app.route('/api/client/start_training', methods=['POST'])
def start_training():
    """接收前端发来的训练启动请求"""
    try:
        data = request.json
        task_id = data.get('taskId')
        
        if not task_id:
            return jsonify({"success": False, "message": "缺少任务ID"}), 400
            
        logger.info(f"收到开始训练请求，任务ID: {task_id}")
        
        # 保存任务ID和当前时间
        client_state.task_id = task_id
        client_state.start_time = time.time()
        client_state.status = "waiting"  # 等待服务器开始训练
        
        # 如果客户端没有参与，则首先加入训练
        if not client_state.is_participating:
            logger.info("客户端尚未参与训练，尝试加入训练")
            
            # 获取客户端地址
            account = client_state.address or client_state.account
            
            if not account:
                return jsonify({"success": False, "message": "客户端地址未设置"}), 400
                
            # 准备数据集信息
            dataset = {
                "name": "MNIST",
                "size": 60000,
                "classes": 10,
                "type": "image",
                "batchSize": client_state.config.get("batchSize", 32),
                "computeResource": "gpu" if torch.cuda.is_available() else "cpu"
            }
            
            # 调用加入训练函数
            try:
                # 构建服务器请求
                server_url = "http://localhost:5000"
                response = requests.post(
                    f"{server_url}/api/client/join",
                    json={
                        "address": account,
                        "dataset": dataset,
                        "clientPort": app.config.get('PORT', args.port),
                        "taskId": task_id
                    },
                    timeout=5
                )
                
                if response.status_code != 200:
                    return jsonify({"success": False, "message": f"加入训练失败: {response.text}"}), 400
                    
                client_state.is_participating = True
                logger.info(f"客户端 {account} 成功加入训练")
            except Exception as e:
                logger.error(f"加入训练失败: {e}")
                return jsonify({"success": False, "message": f"加入训练失败: {str(e)}"}), 500
        
        # 在新线程中启动Flower客户端
        if not client_state.client_thread or not client_state.client_thread.is_alive():
            flower_thread = threading.Thread(target=start_flower_client, args=(account, app.config.get('PORT', args.port), args.direct), daemon=True)
            flower_thread.start()
            client_state.client_thread = flower_thread
            client_state.is_training = True
            client_state.status = "training"
            logger.info(f"客户端已启动Flower客户端线程")
        else:
            logger.info("Flower客户端线程已经在运行")
            
        return jsonify({
            "success": True,
            "message": "已开始训练",
            "taskId": task_id,
            "clientAddress": client_state.address
        })
        
    except Exception as e:
        logger.error(f"开始训练失败: {e}", exc_info=True)
        client_state.status = "error"
        return jsonify({"success": False, "message": f"开始训练失败: {str(e)}"}), 500

# 添加获取任务信息的端点
@app.route('/api/client/task/<task_id>', methods=['GET'])
def get_task_info(task_id):
    """获取特定任务的信息"""
    try:
        # 尝试从服务器获取任务信息
        server_url = "http://localhost:5000"
        try:
            response = requests.get(f"{server_url}/api/server/task/{task_id}", timeout=5)
            if response.status_code == 200:
                task_info = response.json()
                # 更新本地任务信息
                client_state.task_info = task_info
                return jsonify(task_info)
        except Exception as e:
            logger.warning(f"从服务器获取任务信息失败: {e}, 将使用缓存或生成信息")
            
        # 如果服务器请求失败，使用缓存的任务信息
        if client_state.task_id == task_id and client_state.task_info:
            return jsonify(client_state.task_info)
            
        # 如果没有缓存，生成一个基本任务信息
        basic_task = {
            "id": task_id,
            "name": f"任务 {task_id}",
            "status": "in_progress" if client_state.is_training else "waiting",
            "currentRound": client_state.current_round,
            "totalRounds": client_state.total_rounds,
            "participants": [client_state.address],
            "startTime": client_state.start_time,
            "accuracy": client_state.metrics.get("accuracy", 0.0),
            "loss": client_state.metrics.get("loss", 0.0)
        }
        
        return jsonify(basic_task)
        
    except Exception as e:
        logger.error(f"获取任务信息失败: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500

# 添加训练控制端点
@app.route('/api/client/train', methods=['POST'])
def train_control():
    """控制客户端训练的API端点"""
    try:
        data = request.get_json()
        if not data:
            logger.error("未收到训练控制数据")
            return jsonify({"success": False, "error": "No data provided"}), 400

        action = data.get('action')
        task_id = data.get('taskId')
        
        logger.info(f"收到训练控制请求: action={action}, task_id={task_id}")
        
        if not action:
            logger.error("未指定动作类型")
            return jsonify({"success": False, "error": "No action specified"}), 400
            
        if action == "start":
            # 检查是否已经在训练中
            if client_state.is_training and client_state.client_thread and client_state.client_thread.is_alive():
                # 判断是否是同一任务的训练请求
                if str(client_state.task_id) == str(task_id):
                    logger.warning("客户端已经在训练中")
                    return jsonify({"success": False, "error": "Client is already training"}), 400
                else:
                    # 如果是新任务，需要先停止当前任务
                    logger.info("收到新任务的训练请求，正在停止当前训练...")
                    try:
                        # 停止当前任务（这里仅清除状态，线程会在下次循环中退出）
                        client_state.is_training = False
                        client_state.status = "stopping"
                        time.sleep(1)  # 等待当前线程停止
                    except Exception as e:
                        logger.error(f"停止当前训练失败: {str(e)}")
            
            # 重置客户端状态
            client_state.is_training = False
            client_state.status = "initializing"
            client_state.error = None
            client_state.client_thread = None
            
            # 更新客户端状态
            client_state.task_id = task_id
            client_state.config = {
                "batchSize": data.get('batchSize', 32),
                "learningRate": data.get('learningRate', 0.01),
                "epochs": data.get('epochs', 1),
                "timeout": data.get('timeout', 60)
            }
            
            logger.info(f"正在启动训练，参数：batch_size={client_state.config['batchSize']}, "
                       f"learning_rate={client_state.config['learningRate']}, "
                       f"epochs={client_state.config['epochs']}, "
                       f"timeout={client_state.config['timeout']}")
            
            try:
                # 在新线程中启动 Flower 客户端
                client_thread = threading.Thread(target=start_flower_client, args=(client_state.address, app.config.get('PORT', args.port), args.direct))
                client_thread.daemon = True
                client_thread.start()
                
                client_state.is_training = True
                client_state.status = "training"
                client_state.client_thread = client_thread
                client_state.start_time = time.time()
                logger.info("Flower客户端启动成功")
                
                return jsonify({
                    "success": True,
                    "message": "Training started successfully",
                    "status": client_state.status
                })
                
            except Exception as e:
                logger.error(f"启动Flower客户端失败: {str(e)}")
                client_state.status = "error"
                client_state.is_training = False
                client_state.error = str(e)
                return jsonify({"success": False, "error": f"Failed to start training: {str(e)}"}), 500
                
        elif action == "pause":
            if not client_state.is_training:
                return jsonify({"success": False, "error": "Client is not training"}), 400
            client_state.is_training = False
            client_state.status = "paused"
            logger.info("训练已暂停")
            return jsonify({"success": True, "message": "Training paused"})
            
        elif action == "resume":
            if client_state.is_training:
                return jsonify({"success": False, "error": "Client is already training"}), 400
            client_state.is_training = True
            client_state.status = "training"
            logger.info("训练已恢复")
            return jsonify({"success": True, "message": "Training resumed"})
            
        elif action == "stop":
            client_state.is_training = False
            client_state.status = "stopped"
            logger.info("训练已停止")
            return jsonify({"success": True, "message": "Training stopped"})
            
        else:
            logger.error(f"未知的动作类型: {action}")
            return jsonify({"success": False, "error": f"Unknown action: {action}"}), 400
            
    except Exception as e:
        logger.error(f"处理训练控制请求时发生错误: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

# 主函数
if __name__ == "__main__":
    # 如果直接启动Flower客户端
    if args.direct:
        logger.info(f"Starting Flower client directly (bypassing Flask server)")
        # 初始化客户端配置
        client_state.config = {
            "learningRate": 0.001,
            "batchSize": 32,
            "epochs": 5
        }
        # 在新线程中启动Flower客户端
        flower_thread = threading.Thread(target=start_flower_client, args=(args.account, args.port, True))
        flower_thread.daemon = True
        flower_thread.start()
        
        # 同时启动Flask API服务器
        logger.info(f"Starting Flask server on port {args.port}")
        app.run(host='0.0.0.0', port=args.port, debug=False, threaded=True)
    else:
        # 启动 API 服务器
        logger.info(f"Starting Flask server on port {args.port}")
        app.run(host='0.0.0.0', port=args.port, debug=False, threaded=True)
