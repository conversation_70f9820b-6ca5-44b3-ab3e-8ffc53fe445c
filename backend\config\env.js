/**
 * 环境变量配置
 */
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });

// 环境变量配置
const env = {
  // 应用环境
  NODE_ENV: process.env.NODE_ENV || 'development',
  
  // 应用端口
  PORT: process.env.PORT || 3000,

  // 数据库配置
  DB: {
    HOST: process.env.DB_HOST || 'localhost',
    PORT: process.env.DB_PORT || 3306,
    USER: process.env.DB_USER || 'root',
    PASSWORD: '123456', // 直接硬编码密码
    DATABASE: process.env.DB_DATABASE || 'federated_learning',
    CONNECTION_LIMIT: parseInt(process.env.DB_CONNECTION_LIMIT || '10')
  },
  // JWT配置
  JWT: {
    SECRET: process.env.JWT_SECRET || 'your-secret-key',
    EXPIRES_IN: process.env.JWT_EXPIRES_IN || '1d'
  },

  // 文件上传路径
  UPLOAD_PATH: process.env.UPLOAD_PATH || path.resolve(__dirname, '../../uploads'),

  // 区块链配置
  BLOCKCHAIN: {
    PROVIDER_URL: process.env.BLOCKCHAIN_PROVIDER_URL || 'http://localhost:8545',
    PRIVATE_KEY: process.env.BLOCKCHAIN_PRIVATE_KEY || '',
    CONTRACT_ADDRESS: process.env.BLOCKCHAIN_CONTRACT_ADDRESS || ''
  },

  // 获取完整的数据库URL（用于某些ORM）
  getDatabaseUrl() {
    return `mysql://${this.DB.USER}:${this.DB.PASSWORD}@${this.DB.HOST}:${this.DB.PORT}/${this.DB.DATABASE}`;
  },

  // 判断是否为生产环境
  isProd() {
    return this.NODE_ENV === 'production';
  },

  // 判断是否为开发环境
  isDev() {
    return this.NODE_ENV === 'development';
  },

  // 判断是否为测试环境
  isTest() {
    return this.NODE_ENV === 'test';
  }
};

module.exports = env; 