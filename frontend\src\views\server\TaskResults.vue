<!-- 任务结果页面 -->
<template>
  <div class="task-results">
    <div class="page-header">
      <h1>任务训练结果</h1>
      <div class="page-actions">
        <el-button @click="goBack">
          <i class="fas fa-arrow-left"></i> 返回任务管理
        </el-button>
      </div>
    </div>
    
    <Breadcrumb />
    
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="6" animated />
    </div>
    
    <div v-else>
      <!-- 任务基本信息 -->
      <el-card class="task-info-card">
        <template #header>
          <div class="card-header">
            <h2>{{ task.name }}</h2>
            <el-tag :type="getStatusTagType(task.status)">{{ getStatusText(task.status) }}</el-tag>
          </div>
        </template>
        
        <div class="task-info">
          <p class="task-description">{{ task.description }}</p>
          <div class="task-meta">
            <div class="meta-item">
              <span class="meta-label">数据类型:</span>
              <span class="meta-value">{{ task.dataType }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">参与者数量:</span>
              <span class="meta-value">{{ task.participants?.length || 0 }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">轮次:</span>
              <span class="meta-value">{{ task.currentRound || 0 }}/{{ task.totalRounds }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">完成时间:</span>
              <span class="meta-value">{{ task.completedAt ? formatDate(task.completedAt) : '-' }}</span>
            </div>
          </div>
        </div>
      </el-card>
      
      <!-- 训练结果 -->
      <div class="results-grid">
        <!-- 准确率卡片 -->
        <el-card class="result-card accuracy-card">
          <template #header>
            <div class="card-header">
              <h3>模型准确率</h3>
            </div>
          </template>
          
          <div class="result-value">{{ (task.accuracy * 100).toFixed(2) }}%</div>
          <el-progress 
            :percentage="task.accuracy * 100" 
            :format="() => ''" 
            :color="accuracyColor"
          />
          
          <div class="result-comparison">
            <div class="comparison-item">
              <span class="comparison-label">初始准确率:</span>
              <span class="comparison-value">{{ (task.initialAccuracy * 100).toFixed(2) }}%</span>
            </div>
            <div class="comparison-item">
              <span class="comparison-label">提升:</span>
              <span class="comparison-value improvement">+{{ ((task.accuracy - task.initialAccuracy) * 100).toFixed(2) }}%</span>
            </div>
          </div>
        </el-card>
        
        <!-- 损失值卡片 -->
        <el-card class="result-card loss-card">
          <template #header>
            <div class="card-header">
              <h3>损失值</h3>
            </div>
          </template>
          
          <div class="result-value">{{ task.loss.toFixed(4) }}</div>
          <el-progress 
            :percentage="100 - (task.loss / task.initialLoss) * 100" 
            :format="() => ''" 
            :color="lossColor"
          />
          
          <div class="result-comparison">
            <div class="comparison-item">
              <span class="comparison-label">初始损失值:</span>
              <span class="comparison-value">{{ task.initialLoss.toFixed(4) }}</span>
            </div>
            <div class="comparison-item">
              <span class="comparison-label">降低:</span>
              <span class="comparison-value improvement">-{{ (task.initialLoss - task.loss).toFixed(4) }}</span>
            </div>
          </div>
        </el-card>
        
        <!-- 训练时间卡片 -->
        <el-card class="result-card time-card">
          <template #header>
            <div class="card-header">
              <h3>训练时间</h3>
            </div>
          </template>
          
          <div class="result-value">{{ formatTrainingTime(task.trainingTime) }}</div>
          
          <div class="result-comparison">
            <div class="comparison-item">
              <span class="comparison-label">每轮平均:</span>
              <span class="comparison-value">{{ formatTrainingTime(task.trainingTime / task.totalRounds) }}</span>
            </div>
            <div class="comparison-item">
              <span class="comparison-label">总轮次:</span>
              <span class="comparison-value">{{ task.totalRounds }}</span>
            </div>
          </div>
        </el-card>
        
        <!-- 参与者贡献卡片 -->
        <el-card class="result-card contributors-card">
          <template #header>
            <div class="card-header">
              <h3>参与者贡献</h3>
            </div>
          </template>
          
          <div class="result-value">{{ task.participants?.length || 0 }} 名参与者</div>
          
          <div class="contributors-list">
            <div v-for="(participant, index) in task.participants" :key="participant" class="contributor-item">
              <span class="contributor-rank">#{{ index + 1 }}</span>
              <span class="contributor-address">{{ formatAddress(participant) }}</span>
              <span class="contributor-score">{{ (0.9 - index * 0.1).toFixed(2) }} 贡献度</span>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 训练历史图表 -->
      <el-card class="training-history-card">
        <template #header>
          <div class="card-header">
            <h3>训练历史</h3>
            <div class="card-actions">
              <el-button size="small" type="primary" @click="downloadModel">
                <i class="fas fa-download"></i> 下载模型
              </el-button>
              <el-button size="small" type="success" @click="exportResults">
                <i class="fas fa-file-export"></i> 导出结果
              </el-button>
            </div>
          </div>
        </template>
        
        <div class="history-chart-container">
          <div class="chart-placeholder">
            <i class="fas fa-chart-line"></i>
            <p>训练历史图表将在这里显示</p>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import Breadcrumb from '@/components/Breadcrumb.vue';
import apiService from '@/services/api';

export default {
  name: 'TaskResults',
  components: {
    Breadcrumb
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const loading = ref(true);
    const task = ref({});
    const taskId = route.params.id;
    
    // 加载任务数据
    const loadTask = async () => {
      loading.value = true;
      try {
        // 获取任务信息
        const allTasks = await apiService.getAllTasks();
        const currentTask = allTasks.find(t => t.id.toString() === taskId.toString());
        
        if (!currentTask) {
          ElMessage.error('任务不存在');
          router.push('/server/tasks');
          return;
        }
        
        // 如果任务未完成，添加模拟的训练结果数据
        if (currentTask.status === 'completed' && !currentTask.accuracy) {
          currentTask.accuracy = Math.random() * 0.3 + 0.7; // 70%-100%的准确率
          currentTask.initialAccuracy = Math.random() * 0.4 + 0.2; // 20%-60%的初始准确率
          currentTask.loss = Math.random() * 0.5; // 0-0.5的损失值
          currentTask.initialLoss = currentTask.loss + Math.random() * 1.5; // 比当前损失值大1-2.5的初始损失值
          currentTask.trainingTime = Math.floor(Math.random() * 10000) + 5000; // 5000-15000秒的训练时间
          currentTask.completedAt = new Date();
          
          // 更新localStorage中的任务数据
          localStorage.setItem('tasks', JSON.stringify(allTasks));
        }
        
        task.value = currentTask;
      } catch (error) {
        console.error('加载任务数据失败:', error);
        ElMessage.error('加载数据失败，请重试');
      } finally {
        loading.value = false;
      }
    };
    
    // 格式化日期
    const formatDate = (date) => {
      if (!date) return '';
      if (typeof date === 'string') {
        date = new Date(date);
      }
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    };
    
    // 格式化地址
    const formatAddress = (address) => {
      if (!address) return '';
      return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
    };
    
    // 格式化训练时间
    const formatTrainingTime = (seconds) => {
      if (!seconds) return '0秒';
      
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      
      let result = '';
      if (hours > 0) result += `${hours}小时 `;
      if (minutes > 0 || hours > 0) result += `${minutes}分钟 `;
      result += `${remainingSeconds}秒`;
      
      return result;
    };
    
    // 获取任务状态标签类型
    const getStatusTagType = (status) => {
      const types = {
        draft: 'info',
        published: 'warning',
        in_progress: 'success',
        completed: 'primary',
        cancelled: 'danger'
      };
      return types[status] || 'info';
    };
    
    // 获取任务状态文本
    const getStatusText = (status) => {
      const texts = {
        draft: '草稿',
        published: '已发布',
        in_progress: '进行中',
        completed: '已完成',
        cancelled: '已取消'
      };
      return texts[status] || status;
    };
    
    // 计算准确率颜色
    const accuracyColor = computed(() => {
      const accuracy = task.value.accuracy || 0;
      if (accuracy < 0.6) return '#F56C6C'; // 红色
      if (accuracy < 0.8) return '#E6A23C'; // 橙色
      return '#67C23A'; // 绿色
    });
    
    // 计算损失值颜色
    const lossColor = computed(() => {
      const loss = task.value.loss || 0;
      if (loss > 1.0) return '#F56C6C'; // 红色
      if (loss > 0.5) return '#E6A23C'; // 橙色
      return '#67C23A'; // 绿色
    });
    
    // 下载模型
    const downloadModel = () => {
      ElMessage.success('模型下载已开始，请稍候...');
    };
    
    // 导出结果
    const exportResults = () => {
      ElMessage.success('训练结果导出已开始，请稍候...');
    };
    
    // 返回任务管理页面
    const goBack = () => {
      router.push('/server/tasks');
    };
    
    onMounted(() => {
      loadTask();
    });
    
    return {
      loading,
      task,
      formatDate,
      formatAddress,
      formatTrainingTime,
      getStatusTagType,
      getStatusText,
      accuracyColor,
      lossColor,
      downloadModel,
      exportResults,
      goBack
    };
  }
};
</script>

<style scoped>
.task-results {
  width: 100%;
  padding: var(--spacing-md);
  box-sizing: border-box;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: var(--text-primary);
  font-weight: 600;
}

.task-info-card {
  margin-bottom: var(--spacing-md);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.task-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  line-height: 1.5;
}

.task-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.meta-item {
  display: flex;
  gap: var(--spacing-xs);
}

.meta-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.meta-value {
  color: var(--text-primary);
  font-weight: 600;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.result-card {
  display: flex;
  flex-direction: column;
}

.result-value {
  font-size: 36px;
  font-weight: 700;
  color: var(--text-primary);
  margin: var(--spacing-md) 0;
  text-align: center;
}

.result-comparison {
  display: flex;
  justify-content: space-between;
  margin-top: var(--spacing-md);
}

.comparison-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.comparison-label {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: 4px;
}

.comparison-value {
  color: var(--text-primary);
  font-weight: 600;
}

.comparison-value.improvement {
  color: var(--success-color);
}

.contributors-list {
  margin-top: var(--spacing-md);
  overflow-y: auto;
  max-height: 150px;
}

.contributor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color);
}

.contributor-item:last-child {
  border-bottom: none;
}

.contributor-rank {
  font-weight: 600;
  color: var(--primary-color);
  width: 40px;
}

.contributor-address {
  flex-grow: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.contributor-score {
  font-weight: 600;
  color: var(--success-color);
}

.training-history-card {
  margin-top: var(--spacing-md);
}

.card-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.history-chart-container {
  height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--text-hint);
}

.chart-placeholder i {
  font-size: 64px;
  margin-bottom: var(--spacing-sm);
}

.loading-container {
  padding: var(--spacing-md) 0;
}

@media (max-width: 992px) {
  .results-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .task-results {
    padding: var(--spacing-sm);
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
  
  .task-meta {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .result-comparison {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
  
  .card-actions {
    width: 100%;
    justify-content: space-between;
  }
}
</style> 