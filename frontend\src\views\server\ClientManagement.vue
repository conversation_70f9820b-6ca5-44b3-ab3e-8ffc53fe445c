<!-- 客户端管理页面 -->
<template>
  <div class="client-management">
    <div class="page-header">
      <h1>客户端管理</h1>
      <div class="page-actions">
        <el-button type="primary" @click="refreshData">
          <i class="fas fa-sync"></i> 刷新数据
        </el-button>
        <el-button type="success" @click="exportClients">
          <i class="fas fa-file-export"></i> 导出数据
        </el-button>
      </div>
    </div>
    
    <Breadcrumb />
    
    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.totalClients }}</div>
        <div class="stat-label">总客户端</div>
        <div class="stat-icon">
          <i class="fas fa-users"></i>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.activeClients }}</div>
        <div class="stat-label">活跃客户端</div>
        <div class="stat-icon active">
          <i class="fas fa-user-check"></i>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.newClients }}</div>
        <div class="stat-label">新增客户端</div>
        <div class="stat-icon new">
          <i class="fas fa-user-plus"></i>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.totalTasks }}</div>
        <div class="stat-label">当前任务</div>
        <div class="stat-icon tasks">
          <i class="fas fa-tasks"></i>
        </div>
      </el-card>
    </div>
    
    <!-- 过滤和搜索 -->
    <div class="filter-section">
      <el-radio-group v-model="statusFilter" @change="filterClients" class="status-filter">
        <el-radio-button value="all">全部</el-radio-button>
        <el-radio-button value="active">活跃</el-radio-button>
        <el-radio-button value="inactive">非活跃</el-radio-button>
        <el-radio-button value="blocked">已屏蔽</el-radio-button>
      </el-radio-group>
      
      <div class="search-box">
        <el-input v-model="searchQuery" placeholder="搜索地址或标签" clearable @input="filterClients">
          <template #prefix>
            <i class="fas fa-search"></i>
          </template>
        </el-input>
      </div>
    </div>
    
    <!-- 客户端列表 -->
    <el-table
      :data="filteredClients"
      style="width: 100%"
      v-loading="loading"
      border
      row-key="address"
    >
      <el-table-column type="index" width="50" />
      
      <el-table-column prop="address" label="客户端地址" min-width="220">
        <template #default="scope">
          <div class="client-info">
            <el-avatar :size="30" :src="getAvatarUrl(scope.row.address)" class="client-avatar"></el-avatar>
            <div class="client-details">
              <div class="client-address">{{ shortenAddress(scope.row.address) }}</div>
              <el-tag v-if="scope.row.label" size="small">{{ scope.row.label }}</el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="joinDate" label="加入日期" width="120">
        <template #default="scope">
          {{ formatDate(scope.row.joinDate) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="lastActive" label="最后活跃" width="120">
        <template #default="scope">
          {{ formatDate(scope.row.lastActive) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="taskCount" label="参与任务" width="100">
        <template #default="scope">
          {{ scope.row.taskCount }}
        </template>
      </el-table-column>
      
      <el-table-column prop="dataContributions" label="数据贡献" width="120">
        <template #default="scope">
          {{ scope.row.dataContributions }} 条
        </template>
      </el-table-column>
      
      <el-table-column prop="computeResource" label="计算资源" width="120">
        <template #default="scope">
          <el-tag size="small" :type="scope.row.computeResource === 'GPU' ? 'success' : 'info'">
            {{ scope.row.computeResource }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="reputation" label="信誉度" width="150">
        <template #default="scope">
          <el-progress 
            :percentage="scope.row.reputation" 
            :color="getReputationColor(scope.row.reputation)"
          />
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="220" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="viewClient(scope.row)" type="primary" plain>
            查看详情
          </el-button>
          
          <template v-if="scope.row.status !== 'blocked'">
            <el-button size="small" @click="blockClient(scope.row)" type="danger" plain>
              屏蔽
            </el-button>
          </template>
          
          <template v-else>
            <el-button size="small" @click="unblockClient(scope.row)" type="success" plain>
              解除屏蔽
            </el-button>
          </template>
          
          <el-button size="small" @click="addLabel(scope.row)" type="info" plain>
            添加标签
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalClients"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 添加标签对话框 -->
    <el-dialog
      v-model="labelDialogVisible"
      title="添加客户端标签"
      width="400px"
    >
      <div v-if="currentClient">
        <p>为客户端 <strong>{{ shortenAddress(currentClient.address) }}</strong> 添加标签：</p>
        
        <el-form :model="labelForm">
          <el-form-item label="标签名称">
            <el-input v-model="labelForm.label" placeholder="请输入标签名称"></el-input>
          </el-form-item>
          
          <el-form-item label="标签颜色">
            <el-color-picker v-model="labelForm.color" show-alpha></el-color-picker>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="labelDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveLabel">保存</el-button>
      </template>
    </el-dialog>
    
    <!-- 客户端详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="客户端详情"
      width="700px"
    >
      <div v-if="currentClient" class="client-detail">
        <div class="client-header">
          <el-avatar :size="60" :src="getAvatarUrl(currentClient.address)" class="client-detail-avatar"></el-avatar>
          <div class="client-basic-info">
            <h3>{{ shortenAddress(currentClient.address) }}</h3>
            <div class="client-tags">
              <el-tag v-if="currentClient.label" size="small">{{ currentClient.label }}</el-tag>
              <el-tag :type="getStatusTagType(currentClient.status)" size="small">
                {{ getStatusText(currentClient.status) }}
              </el-tag>
            </div>
          </div>
        </div>
        
        <el-divider />
        
        <div class="detail-grid">
          <div class="detail-item">
            <div class="item-label">加入日期</div>
            <div class="item-value">{{ formatDate(currentClient.joinDate) }}</div>
          </div>
          
          <div class="detail-item">
            <div class="item-label">最后活跃</div>
            <div class="item-value">{{ formatDate(currentClient.lastActive) }}</div>
          </div>
          
          <div class="detail-item">
            <div class="item-label">参与任务</div>
            <div class="item-value">{{ currentClient.taskCount }}</div>
          </div>
          
          <div class="detail-item">
            <div class="item-label">数据贡献</div>
            <div class="item-value">{{ currentClient.dataContributions }} 条</div>
          </div>
          
          <div class="detail-item">
            <div class="item-label">计算资源</div>
            <div class="item-value">{{ currentClient.computeResource }}</div>
          </div>
          
          <div class="detail-item">
            <div class="item-label">信誉度</div>
            <div class="item-value">{{ currentClient.reputation }}%</div>
          </div>
        </div>
        
        <el-divider />
        
        <h4>参与历史任务</h4>
        <el-table :data="currentClient.tasks || []" style="width: 100%">
          <el-table-column prop="name" label="任务名称" min-width="180" />
          <el-table-column prop="joinDate" label="参与时间" width="120">
            <template #default="scope">
              {{ formatDate(scope.row.joinDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="任务状态" width="100">
            <template #default="scope">
              <el-tag :type="getTaskStatusTagType(scope.row.status)">
                {{ getTaskStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="contribution" label="贡献度" width="100">
            <template #default="scope">
              {{ scope.row.contribution }}%
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import Breadcrumb from '@/components/Breadcrumb.vue';

export default {
  name: 'ClientManagement',
  components: {
    Breadcrumb
  },
  setup() {
    const loading = ref(false);
    const clients = ref([]);
    const statusFilter = ref('all');
    const searchQuery = ref('');
    const currentPage = ref(1);
    const pageSize = ref(10);
    
    const labelDialogVisible = ref(false);
    const detailDialogVisible = ref(false);
    const currentClient = ref(null);
    const labelForm = reactive({
      label: '',
      color: '#409EFF'
    });
    
    // 统计数据
    const statistics = reactive({
      totalClients: 0,
      activeClients: 0,
      newClients: 0,
      totalTasks: 0
    });
    
    // 加载客户端列表
    const loadClients = async () => {
      loading.value = true;
      try {
        // 这里应该调用API获取客户端列表
        // 临时数据
        clients.value = [
          {
            address: '0x1234567890abcdef1234567890abcdef12345678',
            label: '高质量数据',
            joinDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            lastActive: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
            taskCount: 5,
            dataContributions: 12000,
            computeResource: 'GPU',
            reputation: 95,
            status: 'active',
            tasks: [
              {
                name: 'MNIST手写数字识别',
                joinDate: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
                status: 'completed',
                contribution: 92
              },
              {
                name: '医疗影像分类模型',
                joinDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
                status: 'in_progress',
                contribution: 88
              }
            ]
          },
          {
            address: '0x2345678901abcdef2345678901abcdef23456789',
            label: '专家',
            joinDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
            lastActive: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
            taskCount: 12,
            dataContributions: 25000,
            computeResource: 'GPU',
            reputation: 98,
            status: 'active',
            tasks: [
              {
                name: '自然语言情感分析',
                joinDate: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000),
                status: 'completed',
                contribution: 95
              }
            ]
          },
          {
            address: '0x3456789012abcdef3456789012abcdef34567890',
            label: '',
            joinDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
            lastActive: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
            taskCount: 1,
            dataContributions: 3000,
            computeResource: 'CPU',
            reputation: 60,
            status: 'inactive',
            tasks: []
          },
          {
            address: '0x4567890123abcdef4567890123abcdef45678901',
            label: '不稳定',
            joinDate: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
            lastActive: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000),
            taskCount: 3,
            dataContributions: 8000,
            computeResource: 'CPU',
            reputation: 75,
            status: 'inactive',
            tasks: []
          },
          {
            address: '0x5678901234abcdef5678901234abcdef56789012',
            label: '违规',
            joinDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
            lastActive: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            taskCount: 4,
            dataContributions: 10000,
            computeResource: 'GPU',
            reputation: 30,
            status: 'blocked',
            tasks: []
          }
        ];
        
        // 更新统计数据
        statistics.totalClients = clients.value.length;
        statistics.activeClients = clients.value.filter(client => client.status === 'active').length;
        statistics.newClients = clients.value.filter(client => {
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
          return new Date(client.joinDate) >= thirtyDaysAgo;
        }).length;
        statistics.totalTasks = clients.value.reduce((sum, client) => sum + client.taskCount, 0);
        
      } catch (error) {
        console.error('加载客户端列表失败:', error);
        ElMessage.error('加载客户端列表失败，请重试');
      } finally {
        loading.value = false;
      }
    };
    
    // 格式化日期
    const formatDate = (date) => {
      if (!date) return '';
      if (typeof date === 'string') {
        date = new Date(date);
      }
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    };
    
    // 获取状态标签类型
    const getStatusTagType = (status) => {
      const types = {
        active: 'success',
        inactive: 'info',
        blocked: 'danger'
      };
      return types[status] || 'info';
    };
    
    // 获取状态文本
    const getStatusText = (status) => {
      const texts = {
        active: '活跃',
        inactive: '非活跃',
        blocked: '已屏蔽'
      };
      return texts[status] || status;
    };
    
    // 获取任务状态标签类型
    const getTaskStatusTagType = (status) => {
      const types = {
        draft: 'info',
        published: 'warning',
        in_progress: 'success',
        completed: 'primary',
        cancelled: 'danger'
      };
      return types[status] || 'info';
    };
    
    // 获取任务状态文本
    const getTaskStatusText = (status) => {
      const texts = {
        draft: '草稿',
        published: '已发布',
        in_progress: '进行中',
        completed: '已完成',
        cancelled: '已取消'
      };
      return texts[status] || status;
    };
    
    // 获取信誉度颜色
    const getReputationColor = (reputation) => {
      if (reputation >= 90) return '#67C23A';
      if (reputation >= 70) return '#E6A23C';
      if (reputation >= 50) return '#F56C6C';
      return '#909399';
    };
    
    // 获取头像URL
    const getAvatarUrl = (address) => {
      return `https://api.dicebear.com/7.x/identicon/svg?seed=${address}`;
    };
    
    // 缩短地址显示
    const shortenAddress = (address) => {
      if (!address) return '';
      return `${address.slice(0, 6)}...${address.slice(-4)}`;
    };
    
    // 计算过滤后的客户端
    const filteredClients = computed(() => {
      let result = clients.value;
      
      // 状态过滤
      if (statusFilter.value !== 'all') {
        result = result.filter(client => client.status === statusFilter.value);
      }
      
      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(client => 
          client.address.toLowerCase().includes(query) || 
          (client.label && client.label.toLowerCase().includes(query))
        );
      }
      
      return result;
    });
    
    // 计算总客户端数
    const totalClients = computed(() => filteredClients.value.length);
    
    // 过滤客户端方法
    const filterClients = () => {
      currentPage.value = 1;  // 重置到第一页
    };
    
    // 页面大小改变
    const handleSizeChange = (size) => {
      pageSize.value = size;
    };
    
    // 当前页改变
    const handleCurrentChange = (page) => {
      currentPage.value = page;
    };
    
    // 查看客户端详情
    const viewClient = (client) => {
      currentClient.value = client;
      detailDialogVisible.value = true;
    };
    
    // 屏蔽客户端
    const blockClient = async (client) => {
      try {
        await ElMessageBox.confirm(
          `确定要屏蔽客户端 ${shortenAddress(client.address)} 吗？屏蔽后该客户端将无法参与任何任务。`,
          '屏蔽确认',
          {
            confirmButtonText: '确定屏蔽',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );
        
        // 更新客户端状态
        client.status = 'blocked';
        ElMessage.success(`客户端 ${shortenAddress(client.address)} 已屏蔽`);
      } catch (error) {
        if (error !== 'cancel') {
          console.error('屏蔽客户端失败:', error);
          ElMessage.error('屏蔽客户端失败，请重试');
        }
      }
    };
    
    // 解除屏蔽
    const unblockClient = async (client) => {
      try {
        await ElMessageBox.confirm(
          `确定要解除对客户端 ${shortenAddress(client.address)} 的屏蔽吗？`,
          '解除屏蔽确认',
          {
            confirmButtonText: '确定解除',
            cancelButtonText: '取消',
            type: 'info',
          }
        );
        
        // 更新客户端状态
        client.status = 'inactive';
        ElMessage.success(`已解除对客户端 ${shortenAddress(client.address)} 的屏蔽`);
      } catch (error) {
        if (error !== 'cancel') {
          console.error('解除屏蔽失败:', error);
          ElMessage.error('解除屏蔽失败，请重试');
        }
      }
    };
    
    // 添加标签
    const addLabel = (client) => {
      currentClient.value = client;
      labelForm.label = client.label || '';
      labelForm.color = '#409EFF';
      labelDialogVisible.value = true;
    };
    
    // 保存标签
    const saveLabel = () => {
      if (!currentClient.value) return;
      
      // 更新客户端标签
      currentClient.value.label = labelForm.label;
      
      // 清空表单并关闭对话框
      labelForm.label = '';
      labelForm.color = '#409EFF';
      labelDialogVisible.value = false;
      
      ElMessage.success('标签已更新');
    };
    
    // 刷新数据
    const refreshData = () => {
      loadClients();
    };
    
    // 导出客户端数据
    const exportClients = () => {
      ElMessage.success('客户端数据导出功能将在后续版本实现');
    };
    
    onMounted(() => {
      loadClients();
    });
    
    return {
      loading,
      statusFilter,
      searchQuery,
      currentPage,
      pageSize,
      statistics,
      labelDialogVisible,
      detailDialogVisible,
      currentClient,
      labelForm,
      filteredClients,
      totalClients,
      formatDate,
      getStatusTagType,
      getStatusText,
      getTaskStatusTagType,
      getTaskStatusText,
      getReputationColor,
      getAvatarUrl,
      shortenAddress,
      filterClients,
      handleSizeChange,
      handleCurrentChange,
      viewClient,
      blockClient,
      unblockClient,
      addLabel,
      saveLabel,
      refreshData,
      exportClients
    };
  }
};
</script>

<style scoped>
.client-management {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.page-actions {
  display: flex;
  gap: 12px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  position: relative;
  padding: 16px;
  overflow: hidden;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-icon {
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  font-size: 36px;
  opacity: 0.15;
  color: #409EFF;
}

.stat-icon.active {
  color: #67C23A;
}

.stat-icon.new {
  color: #E6A23C;
}

.stat-icon.tasks {
  color: #F56C6C;
}

.filter-section {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  align-items: center;
}

.search-box {
  width: 300px;
}

.client-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.client-avatar {
  flex-shrink: 0;
}

.client-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.client-address {
  font-weight: 500;
}

.pagination {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

.client-header {
  display: flex;
  gap: 16px;
  align-items: center;
}

.client-detail-avatar {
  flex-shrink: 0;
}

.client-basic-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.client-basic-info h3 {
  margin: 0;
  font-size: 18px;
}

.client-tags {
  display: flex;
  gap: 8px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin: 16px 0;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.item-label {
  font-size: 14px;
  color: #909399;
}

.item-value {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

@media (max-width: 1024px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .detail-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
}
</style> 