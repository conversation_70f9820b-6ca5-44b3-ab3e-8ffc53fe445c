import Web3 from 'web3';
import FederatedLearningContract from '../../../blockchain/build/contracts/FederatedLearningContract.json';
class Web3Service {
    constructor() {
        this.web3 = null;
        this.account = null;
        this.contract = null;
        
        // 确保在构造函数中初始化 Web3
        if (typeof window.ethereum !== 'undefined') {
            this.web3 = new Web3(window.ethereum);
        } else {
            // 回退到本地节点
            this.web3 = new Web3('http://127.0.0.1:7545');
        }
    }

    async init(retryCount = 3) {
        try {
            console.log('开始初始化Web3...');
            
            // 检查是否已安装 MetaMask
            if (typeof window.ethereum === 'undefined') {
                console.error('MetaMask未安装');
                throw new Error('请安装MetaMask并刷新页面');
            }

            // 确保web3实例存在（构造函数已初始化）
            if (!this.web3) {
                console.log('创建新的Web3实例');
                this.web3 = new Web3(window.ethereum);
                if (!this.web3) {
                    console.error('Web3实例创建失败');
                    throw new Error('Web3 instance not initialized');
                }
            }
            
            // 请求账户访问权限
            console.log('请求MetaMask账户访问权限...');
            let accounts;
            try {
                accounts = await window.ethereum.request({ 
                    method: 'eth_requestAccounts' 
                });
                console.log('获取到账户:', accounts);
            } catch (error) {
                console.error('请求账户访问失败:', error);
                throw new Error(`无法访问MetaMask账户: ${error.message}`);
            }
            
            if (!accounts || accounts.length === 0) {
                console.error('未找到账户');
                throw new Error('No accounts found');
            }
            
            this.account = this.web3.utils.toChecksumAddress(accounts[0]);
            console.log('当前使用账户:', this.account);
            
            // 初始化合约
            console.log('获取当前网络ID...');
            let networkId;
            try {
                networkId = await this.web3.eth.net.getId();
                console.log('当前网络ID:', networkId);
            } catch (error) {
                console.error('获取网络ID失败:', error);
                throw new Error(`无法获取网络ID: ${error.message}`);
            }
            
            console.log('查找合约部署网络...');
            const deployedNetwork = FederatedLearningContract.networks[networkId];
            
            if (!deployedNetwork) {
                console.error(`合约未部署在当前网络(ID: ${networkId})上`);
                throw new Error(`合约未部署在当前网络(ID: ${networkId})上，请切换到正确的网络`);
            }
            
            console.log('初始化合约实例...');
            try {
                this.contract = new this.web3.eth.Contract(
                    FederatedLearningContract.abi,
                    deployedNetwork.address
                );
                console.log('合约地址:', deployedNetwork.address);
            } catch (error) {
                console.error('初始化合约实例失败:', error);
                throw new Error(`初始化合约失败: ${error.message}`);
            }
            
            // 验证合约是否正确初始化
            if (!this.contract || !this.contract.methods) {
                console.error('合约方法不可用');
                throw new Error('合约初始化失败，无法访问合约方法');
            }
            
            // 尝试调用一个简单的合约方法来验证连接
            console.log('测试合约连接...');
            try {
                const isActive = await this.contract.methods.isTrainingActive().call();
                console.log('合约连接测试成功，训练状态:', isActive);
            } catch (error) {
                console.error('合约方法调用测试失败:', error);
                
                // 检查是否是JSON-RPC错误
                if (error.message.includes('Internal JSON-RPC error')) {
                    console.error('检测到JSON-RPC错误，可能是网络连接问题或合约状态问题');
                    const errorDetails = JSON.stringify(error);
                    console.error('错误详情:', errorDetails);
                }
                
                throw new Error(`合约连接测试失败: ${error.message}`);
            }
            
            // 监听账户变化
            window.ethereum.on('accountsChanged', (accounts) => {
                console.log('MetaMask账户已变更:', accounts);
                if (accounts.length > 0) {
                    this.account = this.web3.utils.toChecksumAddress(accounts[0]);
                    console.log('当前使用新账户:', this.account);
                } else {
                    this.account = null;
                    console.warn('未选择账户');
                }
            });

            console.log('Web3 initialized successfully');
            return true;
        } catch (error) {
            console.error('Web3 initialization error:', error);
            
            // 如果还有重试次数，则重试
            if (retryCount > 0) {
                console.log(`重试初始化Web3，剩余尝试次数: ${retryCount-1}`);
                // 短暂延迟后重试
                await new Promise(resolve => setTimeout(resolve, 1000));
                return this.init(retryCount - 1);
            }
            
            throw error;
        }
    }

    async connectWallet() {
        try {
            // 如果 web3 未初始化，则先初始化
            if (!this.web3) {
                await this.init();
            }

            const accounts = await window.ethereum.request({
                method: 'eth_requestAccounts'
            });
            
            if (!accounts || accounts.length === 0) {
                throw new Error('No accounts found');
            }
            
            this.account = this.web3.utils.toChecksumAddress(accounts[0]);
            console.log('Connected account:', this.account);
            return this.account;
        } catch (error) {
            console.error('Error connecting wallet:', error);
            throw error;
        }
    }

    async getNetworkId() {
        try {
            return await this.web3.eth.net.getId();
        } catch (error) {
            console.error('Error getting network ID:', error);
            throw error;
        }
    }

    getContract() {
        if (!this.contract) {
            throw new Error('Contract not initialized');
        }
        return this.contract;
    }

    async registerUser(username, passwordHash, role) {
        try {
            if (!this.web3 || !this.contract) {
                await this.init();
            }
            
            if (!this.account) {
                await this.connectWallet();
            }

            console.log('Registering user with params:', {
                username,
                passwordHash,
                role,
                from: this.account
            });

            // 检查用户是否已注册
            const isRegistered = await this.isUserRegistered(this.account);
            if (isRegistered) {
                throw new Error('User already registered');
            }

            // 调用合约的注册方法
            const result = await this.contract.methods.registerUser(
                username,
                passwordHash,
                role
            ).send({ 
                from: this.account,
                gas: 500000  // 增加 gas 限制
            });
                
            console.log('Registration result:', result);
            return result;
        } catch (error) {
            console.error('Registration error:', error);
            throw error;
        }
    }

    async verifyLogin(userAddress, username, passwordHash) {
        try {
            console.log('Calling contract verifyLogin with:', {
                userAddress,
                username,
                passwordHash
            });
            const result = await this.contract.methods.verifyLogin(
                userAddress,
                username,
                passwordHash
            ).call();
            console.log('Contract verifyLogin response:', result);
            return result;
        } catch (error) {
            console.error('Error in verifyLogin:', error);
            throw error;
        }
    }

    async getUserRole(address) {
        try {
            if (!this.web3 || !this.contract) {
                await this.init();
            }

            // 先检查用户是否已注册
            const isRegistered = await this.isUserRegistered(address);
            if (!isRegistered) {
                console.log('用户未注册，返回默认客户端角色');
                return 0;  // 默认返回客户端角色 (0)
            }

            const accountInfo = await this.contract.methods.getAccountInfo(address, 0).call();
            console.log('getUserRole - Raw account info:', accountInfo);
            
            // 确保返回数字格式的角色
            const roleNumber = parseInt(accountInfo.role);
            console.log('getUserRole - Role number:', roleNumber);
            
            return roleNumber;
        } catch (error) {
            console.error('Error getting user role:', error);
            // 发生错误时也返回默认客户端角色，确保应用不会崩溃
            return 0;
        }
    }

    async isUserRegistered(address) {
        try {
            if (!this.web3 || !this.contract) {
                await this.init();
            }

            const accountCount = await this.contract.methods.getAccountCount(address).call();
            return accountCount > 0;
        } catch (error) {
            console.error('Error checking user registration:', error);
            throw error;
        }
    }

    // 检查训练是否正在进行
    async isTrainingActive() {
        try {
            if (!this.web3 || !this.contract) {
                await this.init();
            }

            const result = await this.contract.methods.isTrainingActive().call();
            return result;
        } catch (error) {
            console.error('Error checking training status:', error);
            throw error;
        }
    }
    // 获取当前网络 ID
    async getNetworkId() {
        try {
            if (!this.web3) {
                throw new Error('Web3 not initialized');
            }
            return await this.web3.eth.net.getId();
        } catch (error) {
            console.error('Error getting network ID:', error);
            throw error;
        }
    }

    // 获取合约实例
    getContract() {
        if (!this.contract) {
            throw new Error('Contract not initialized');
        }
        return this.contract;
    }

    async updateUser(publicKey, metadata) {
        if (!this.contract) {
            throw new Error('Contract not initialized');
        }
        return await this.contract.methods.updateUser(
            publicKey,
            JSON.stringify(metadata)
        ).send({ from: this.account });
    }

    async getAccountInfo(address, index) {
        try {
            if (!this.web3 || !this.contract) {
                await this.init();
            }

            console.log('Getting account info for:', {
                address,
                index
            });

            const result = await this.contract.methods.getAccountInfo(address, index).call();
            console.log('Account info response:', result);
            
            // 返回结构化的数据
            return {
                username: result[0],
                role: result[1],
                isActive: result[2]
            };
        } catch (error) {
            console.error('Error getting account info:', error);
            throw error;
        }
    }

    // 角色转换辅助方法
    convertRoleToNumber(role) {
        console.log('Converting role to number:', role);
        const roleNumber = role === 'server' ? 1 : 0;
        console.log('Role number result:', roleNumber);
        return roleNumber;
    }

    convertNumberToRole(roleNumber) {
        console.log('Converting number to role:', roleNumber);
        const role = parseInt(roleNumber) === 1 ? 'server' : 'client';
        console.log('Role string result:', role);
        return role;
    }

    // 断开Web3连接
    async disconnect() {
        try {
            console.log('Disconnecting Web3 connection');
            // 重置账户和合约状态
            this.account = null;
            
            console.log('Web3 connection disconnected');
            return true;
        } catch (error) {
            console.error('Error disconnecting Web3:', error);
            throw error;
        }
    }
}

// 创建单例实例
const web3Service = new Web3Service();

// 导出之前尝试初始化
web3Service.init().catch(error => {
    console.error('Failed to initialize Web3Service:', error);
});

export default web3Service;
