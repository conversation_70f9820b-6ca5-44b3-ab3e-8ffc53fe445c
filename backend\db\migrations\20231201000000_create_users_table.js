/**
 * 迁移: create_users_table
 * 创建于: 2023-12-01T00:00:00.000Z
 * 描述: 创建用户表，用于系统管理和认证
 */

/**
 * 向上迁移 - 应用更改
 * @param {Function} query 数据库查询函数
 */
exports.up = async function(query) {
  // 创建用户表
  await query(`
    CREATE TABLE IF NOT EXISTS users (
      id INT AUTO_INCREMENT PRIMARY KEY,
      username VARCHAR(50) NOT NULL,
      password VARCHAR(255) NOT NULL,
      email VARCHAR(100) NOT NULL,
      role ENUM('admin', 'manager', 'user') NOT NULL DEFAULT 'user',
      status ENUM('active', 'inactive', 'blocked') NOT NULL DEFAULT 'active',
      last_login DATETIME,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      UNIQUE KEY unique_username (username),
      UNIQUE KEY unique_email (email),
      INDEX idx_role (role),
      INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
  `);

  // 创建默认管理员账户
  await query(`
    INSERT INTO users (username, password, email, role)
    VALUES ('admin', '$2b$10$X7FXnv9/lfiI0Yq7E9UvjeCWON9SGJtA.bVm6/KzgIdeL5XlQpCB.', '<EMAIL>', 'admin')
  `);
};

/**
 * 向下迁移 - 撤销更改
 * @param {Function} query 数据库查询函数
 */
exports.down = async function(query) {
  // 删除用户表
  await query('DROP TABLE IF EXISTS users');
}; 