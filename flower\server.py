import os
import logging
import time
import threading
import json
import argparse
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from flask import Flask, request, jsonify
from flask_cors import CORS
import flwr as fl
from web3 import Web3
import requests
import random
import sys
from werkzeug.serving import make_server
from datetime import datetime

# 获取当前脚本的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录（当前目录的上一级）
root_dir = os.path.dirname(current_dir)
# 确保日志目录存在
logs_dir = os.path.join(root_dir, 'logs')
os.makedirs(logs_dir, exist_ok=True)

# 设置gRPC日志级别和环境变量
os.environ['GRPC_VERBOSITY'] = 'ERROR'
os.environ['GRPC_TRACE'] = 'none'
os.environ['GRPC_ENABLE_FORK_SUPPORT'] = '0'
os.environ['GRPC_POLL_STRATEGY'] = 'epoll1'
os.environ['GRPC_DNS_RESOLVER'] = 'native'

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(logs_dir, 'server.log'), encoding='utf-8')
    ]
)

# 设置第三方库的日志级别
logging.getLogger('flwr').setLevel(logging.INFO)
logging.getLogger('werkzeug').setLevel(logging.ERROR)
logging.getLogger('urllib3').setLevel(logging.ERROR)
logging.getLogger('web3').setLevel(logging.ERROR)

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# 添加命令行参数解析
parser = argparse.ArgumentParser(description='Federated Learning Server')
parser.add_argument('--account', type=str, help='Server account address')
parser.add_argument('--port', type=int, default=5000, help='Server port')
args = parser.parse_args()

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 启用跨域支持

# 禁用Flask的默认日志
app.logger.disabled = True
logging.getLogger('werkzeug').disabled = True

# 全局状态
class ServerState:
    def __init__(self):
        self.is_active = False
        self.is_training = False
        self.current_round = 0
        self.total_rounds = 5
        self.min_participants = 2
        self.timeout = 60
        self.start_time = 0
        self.participants = []
        self.history = []
        self.metrics = {
            "loss": 0.0,
            "accuracy": 0.0,
            "participants": 0,
            "trainingTime": 0
        }
        self.average_round_time = 0
        self.round_start_time = 0
        self.round_times = []
        self.final_model = None
        self.server_thread = None
        # 使用命令行参数或默认值
        self.account_address = args.account if args.account else None
        self.server_address = None
        self.server_port = args.port if args.port else 5000
        self.task_id = None
        self.training_config = None

server_state = ServerState()

# Web3 初始化
def init_web3():
    try:
        # 连接到以太坊节点
        w3 = Web3(Web3.HTTPProvider('http://127.0.0.1:7545'))
        
        # 加载合约 - 使用绝对路径
        contract_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'blockchain/build/contracts/FederatedLearningContract.json'))
        
        # 检查文件是否存在
        if not os.path.exists(contract_path):
            logger.error(f"Contract file not found at: {contract_path}")
            # 尝试查找可能的位置
            possible_paths = [
                os.path.abspath('../blockchain/build/contracts/FederatedLearningContract.json'),
                os.path.abspath('../../blockchain/build/contracts/FederatedLearningContract.json'),
                os.path.abspath('e:/ZhousProject/blockchain/build/contracts/FederatedLearningContract.json')
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    contract_path = path
                    logger.info(f"Found contract at: {contract_path}")
                    break
        
        with open(contract_path, encoding='utf-8') as f:
            contract_json = json.load(f)
        
        # 获取网络ID和合约地址
        network_id = w3.eth.chain_id
        deployed_network = contract_json['networks'].get(str(network_id))
        if not deployed_network:
            raise ValueError(f"Contract not deployed on network {network_id}")
        
        contract = w3.eth.contract(
            address=deployed_network['address'],
            abi=contract_json['abi']
        )
        
        # 检查是否提供了账户地址
        if server_state.account_address:
            # 验证账户地址格式
            if not w3.isAddress(server_state.account_address):
                logger.warning(f"Invalid account address format: {server_state.account_address}")
                logger.warning("Using default account instead")
                server_state.account_address = w3.eth.accounts[0]
        else:
            # 使用默认账户
            server_state.account_address = w3.eth.accounts[0]
            
        logger.info(f"Using server account: {server_state.account_address}")
        
        # 检查并设置服务器地址
        try:
            # 检查当前服务器地址
            current_server = None
            try:
                current_server = contract.functions.serverAddress().call()
            except:
                logger.warning("Could not get current server address, will try to register")
                
            # 如果服务器地址未设置或不是当前账户，尝试设置
            if not current_server or current_server == '******************************************':
                logger.info("Setting server address in contract...")
                tx_hash = contract.functions.setServerAddress(server_state.account_address).transact({
                    'from': w3.eth.accounts[0]  # 使用第一个账户（部署合约的账户）设置服务器地址
                })
                receipt = w3.eth.wait_for_transaction_receipt(tx_hash)
                if receipt.status == 1:
                    logger.info(f"Server address set to: {server_state.account_address}")
                else:
                    logger.error("Failed to set server address")
            elif current_server.lower() != server_state.account_address.lower():
                logger.warning(f"Current server address ({current_server}) does not match provided address ({server_state.account_address})")
                logger.warning("Using the address registered in the contract")
                server_state.account_address = current_server
        except Exception as e:
            logger.error(f"Error checking/setting server address: {e}")
        
        return w3, contract
    except Exception as e:
        logger.error(f"Web3 initialization error: {e}")
        raise

# 重置训练状态
def reset_training():
    try:
        w3, contract = init_web3()
        
        # 使用服务器账户
        account = server_state.account_address
        
        # 检查是否有训练线程在运行
        if server_state.server_thread and server_state.server_thread.is_alive():
            logger.info("等待现有训练线程终止...")
            server_state.server_thread.join(timeout=2)  # 等待最多2秒
            if server_state.server_thread.is_alive():
                logger.warning("无法终止现有训练线程，但将继续重置状态")
        
        # 重置训练状态
        server_state.is_active = False
        server_state.is_training = False
        server_state.current_round = 0
        server_state.total_rounds = 0
        server_state.start_time = 0
        server_state.participants = []
        
        # 调用合约重置训练
        try:
            tx_hash = contract.functions.resetTraining().transact({'from': account})
            w3.eth.wait_for_transaction_receipt(tx_hash)
            logger.info("区块链训练状态重置成功")
        except Exception as e:
            logger.error(f"重置区块链训练状态失败: {e}")
        
        # 强制重置所有服务器状态
        server_state.is_active = False
        server_state.server_thread = None
        server_state.round_times = []
        server_state.history = []
        server_state.metrics = {
            "loss": 0.0,
            "accuracy": 0.0,
            "participants": 0,
            "trainingTime": 0
        }
        
        logger.info("训练状态完全重置成功")
        return True
    except Exception as e:
        logger.error(f"重置训练状态失败: {e}")
        return False

# 自定义配置函数
def get_on_fit_config(server_round: int):
    """Return training configuration dict for each round."""
    return {
        "current_round": server_round,
        "local_epochs": 5,
        "batch_size": 32,
    }

# 自定义评估聚合函数
def weighted_average(metrics):
    """计算加权平均指标"""
    # 记录原始指标数据
    logger.info(f"原始客户端指标: {metrics}")
    
    # 提取各个指标
    accuracies = []
    losses = []
    examples = []
    
    # 处理评估结果
    for metric in metrics:
        try:
            # 通常metrics的格式是(client_id, metric_dict)的元组
            client_id = metric[0]
            metric_dict = metric[1]  # 获取指标字典
            
            if isinstance(metric_dict, dict):
                logger.info(f"客户端 {client_id} 的指标: {metric_dict}")
                
                accuracy = metric_dict.get("accuracy", None)
                loss = metric_dict.get("loss", None)
                num_examples = metric_dict.get("num_examples", 1)
                
                # 处理非数值类型
                try:
                    if accuracy is not None:
                        accuracy = float(accuracy)
                    else:
                        continue  # 跳过没有准确率的数据
                        
                    if loss is not None:
                        loss = float(loss)
                    else:
                        continue  # 跳过没有损失值的数据
                        
                    num_examples = int(num_examples)
                    
                    # 记录有效的指标数据
                    logger.info(f"提取的有效指标 - 客户端 {client_id}: 准确率={accuracy:.4f}, 损失={loss:.4f}, 样本数={num_examples}")
                    
                    # 只有在数据有效时才添加
                    accuracies.append(accuracy)
                    losses.append(loss)
                    examples.append(num_examples)
                except (ValueError, TypeError) as e:
                    logger.error(f"将客户端 {client_id} 的指标转换为数值类型失败: {e}")
            else:
                logger.warning(f"无效的指标格式: {metric_dict}")
        except Exception as e:
            logger.error(f"处理指标时出错: {e}")
    
    # 确保至少有一个有效的指标
    if not examples or len(examples) == 0:
        logger.warning("未获取到有效的客户端指标，返回默认值")
        # 如果没有有效数据，根据轮次生成模拟数据用于演示
        current_round = server_state.current_round or 1
        total_rounds = server_state.total_rounds or 10
        progress = current_round / total_rounds
        
        # 生成随机但合理的指标值
        simulated_accuracy = 0.5 + 0.3 * progress + random.random() * 0.1
        simulated_loss = 0.5 - 0.3 * progress + random.random() * 0.1
        
        # 将模拟指标保存到全局状态
        result = {
            "accuracy": min(0.98, max(0.5, simulated_accuracy)),
            "loss": max(0.05, min(0.5, simulated_loss)),
            "num_examples": 100
        }
        
        # 保存到服务器状态
        server_state.metrics["accuracy"] = result["accuracy"]
        server_state.metrics["loss"] = result["loss"]
        
        logger.info(f"保存模拟指标到全局状态: 准确率={result['accuracy']:.4f}, 损失={result['loss']:.4f}")
        
        return result
    
    # 计算加权平均值
    total_examples = sum(examples)
    weighted_accuracy = sum(acc * num for acc, num in zip(accuracies, examples)) / total_examples
    weighted_loss = sum(loss * num for loss, num in zip(losses, examples)) / total_examples
    
    logger.info(f"聚合指标 - 准确率: {weighted_accuracy:.4f}, 损失: {weighted_loss:.4f}, 样本总数: {total_examples}")
    
    # 创建结果字典
    result = {
        "accuracy": float(weighted_accuracy),
        "loss": float(weighted_loss),
        "num_examples": int(total_examples)
    }
    
    # 保存聚合指标到服务器状态
    server_state.metrics["accuracy"] = result["accuracy"]
    server_state.metrics["loss"] = result["loss"]
    server_state.metrics["num_examples"] = result["num_examples"]
    
    logger.info(f"保存聚合指标到全局状态: 准确率={weighted_accuracy:.4f}, 损失={weighted_loss:.4f}, 样本数={total_examples}")
    
    return result

# 自定义评估函数
def evaluate_fn(server_round, parameters, config):
    """处理每轮训练的结果并记录到区块链和数据库"""
    try:
        # 记录所有参数以便调试
        logger.info(f"evaluate_fn被调用 - 轮次:{server_round}, config类型:{type(config)}")
        logger.info(f"config内容:{config}")
        logger.info(f"parameters类型:{type(parameters)}，长度:{len(parameters) if isinstance(parameters, list) else 'N/A'}")
        
        # 更新服务器状态
        server_state.current_round = server_round
        logger.info(f"评估第 {server_round} 轮训练结果")
        
        # 计算本轮训练时间
        round_time = time.time() - server_state.round_start_time
        server_state.round_times.append(round_time)
        server_state.average_round_time = sum(server_state.round_times) / len(server_state.round_times)
        
        # 重置轮次开始时间
        server_state.round_start_time = time.time()
        
        # 检查是否有传入的metrics
        aggregated_metrics = {}
        
        # 检查是否已有聚合的指标（由fit_metrics_aggregation_fn处理的结果）
        # 重要：Flower框架中，这些指标会被weighted_average函数处理后通过其他参数传递
        # 检查全局状态中是否有最新聚合的指标
        logger.info(f"当前全局指标状态: {server_state.metrics}")
        if server_state.metrics and "accuracy" in server_state.metrics and "loss" in server_state.metrics:
            aggregated_metrics = {
                "accuracy": server_state.metrics["accuracy"],
                "loss": server_state.metrics["loss"],
                "num_examples": server_state.metrics.get("num_examples", 100)
            }
            logger.info(f"从全局状态获取到聚合指标: {aggregated_metrics}")
        
        # 如果全局状态没有指标，尝试从config中获取
        if not aggregated_metrics or not ("accuracy" in aggregated_metrics and "loss" in aggregated_metrics):
            # 尝试从config中获取聚合指标
            if isinstance(config, dict) and "metrics" in config:
                aggregated_metrics = config["metrics"]
                logger.info(f"从config['metrics']中获取到指标: {aggregated_metrics}")
            else:
                # 尝试直接从config获取指标值
                if isinstance(config, dict):
                    if "accuracy" in config:
                        aggregated_metrics["accuracy"] = config["accuracy"]
                    if "loss" in config:
                        aggregated_metrics["loss"] = config["loss"]
                    if "num_examples" in config:
                        aggregated_metrics["num_examples"] = config["num_examples"]
                    logger.info(f"直接从config中解析指标: {aggregated_metrics}")
        
        # 如果仍然没有获取到任何指标，使用默认值
        if not aggregated_metrics or not ("accuracy" in aggregated_metrics or "loss" in aggregated_metrics):
            logger.warning(f"第 {server_round} 轮未收到有效客户端指标数据，使用随机模拟指标")
            
            # 如果没有指标，生成随机模拟指标来演示效果
            # 准确率随着轮次增加而提高，损失值随轮次降低
            max_rounds = server_state.total_rounds or 10
            progress_factor = server_round / max_rounds  # 训练进度因子
            
            # 生成随机但有规律的指标值
            random_accuracy = 0.5 + (0.4 * progress_factor) + (random.random() * 0.1 * progress_factor)
            random_accuracy = min(0.99, max(0.5, random_accuracy))  # 限制在合理范围内
            
            random_loss = 0.5 - (0.4 * progress_factor) + (random.random() * 0.1 * (1 - progress_factor))
            random_loss = max(0.01, min(0.5, random_loss))  # 限制在合理范围内
            
            aggregated_metrics = {
                "accuracy": random_accuracy,
                "loss": random_loss,
                "num_examples": 100
            }
            logger.info(f"生成的模拟指标: 准确率={random_accuracy:.4f}, 损失值={random_loss:.4f}")
        
        logger.info(f"第 {server_round} 轮评估指标: {aggregated_metrics}")
        
        # 记录每轮结果 - 不管是否为最后一轮，都记录结果
        record_success = record_results(server_round, parameters, aggregated_metrics)
        logger.info(f"第 {server_round} 轮训练结果记录状态: {'成功' if record_success else '失败'}")
        
        # 如果是最后一轮，保存最终模型
        if server_round == server_state.total_rounds:
            logger.info(f"已达到最后一轮 ({server_round}/{server_state.total_rounds})，训练完成")
            server_state.is_training = False
            
            # 保存最终模型
            accuracy = aggregated_metrics.get("accuracy", 0.0)
            participants = [p["address"] for p in server_state.participants if p["isActive"]]
            save_final_model(accuracy, participants)
            
            # 更新任务状态为"已完成"
            try:
                logger.info(f"正在更新任务 {server_state.task_id} 状态为已完成...")
                task_complete_response = requests.post(f'http://localhost:3001/api/tasks/{server_state.task_id}/complete')
                if task_complete_response.status_code == 200:
                    logger.info(f"任务 {server_state.task_id} 已标记为已完成")
                else:
                    logger.warning(f"无法更新任务状态为已完成: {task_complete_response.status_code} - {task_complete_response.text}")
            except Exception as task_error:
                logger.error(f"更新任务状态为已完成时出错: {task_error}")
            
            # 在数据库中创建模型记录
            try:
                # 准备模型数据
                model_size = random.randint(10, 50)  # 模拟大小(MB)
                model_data = {
                    "name": f"Task_{server_state.task_id}_Model",
                    "type": server_state.task_data_type if hasattr(server_state, 'task_data_type') else "image_classification",
                    "version": "1.0.0",
                    "description": f"从任务 {server_state.task_id} 训练得到的模型",
                    "task_id": server_state.task_id,
                    "task_name": server_state.task_name if hasattr(server_state, 'task_name') else f"Task {server_state.task_id}",
                    "status": "draft",
                    "size": model_size,
                    "performance": accuracy,
                    "is_latest": True,
                    "from_training": True,
                    "blockchain_tx_hash": server_state.final_model.get("hash", "") if hasattr(server_state, 'final_model') else "",
                    "file_path": f"models/model_{server_state.task_id}.pt"
                }
                
                logger.info(f"正在为任务 {server_state.task_id} 创建模型记录...")
                model_response = requests.post('http://localhost:3001/api/models', json=model_data)
                if model_response.status_code == 201:
                    logger.info(f"为任务 {server_state.task_id} 创建的模型记录成功")
                else:
                    logger.warning(f"无法创建模型记录: {model_response.status_code} - {model_response.text}")
            except Exception as model_error:
                logger.error(f"创建模型记录时出错: {model_error}")
            
            logger.info(f"最终模型已保存，准确率: {aggregated_metrics.get('accuracy', 0.0)}")
        
        # 返回聚合评估指标 - 确保返回正确的格式
        return aggregated_metrics.get("loss", 0.0), {"accuracy": aggregated_metrics.get("accuracy", 0.0)}
    
    except Exception as e:
        logger.error(f"Error in evaluate_fn: {e}")
        logger.exception("完整错误堆栈:")
        return 0.0, {"accuracy": 0.0}

# 记录每轮结果的函数
def record_results(server_round, parameters, metrics):
    """记录训练结果到区块链和历史记录"""
    try:
        logger.info(f"记录轮次 {server_round} 的训练结果")
        
        # 获取性能指标
        accuracy = metrics.get('accuracy', 0)
        loss = metrics.get('loss', 0)
        
        # 获取参与此轮次的参与者数量
        participants_count = len(server_state.participants)
        
        # 记录到服务器状态历史记录
        server_state.history.append({
            'round': server_round,
            'accuracy': accuracy,
            'loss': loss,
            'timestamp': datetime.now().isoformat(),
            'participants': participants_count
        })
        
        # 更新服务器状态的当前轮次
        server_state.current_round = server_round
        
        # 准备要发送到数据库的指标数据
        metrics_data = {
            'task_id': server_state.task_id,
            'round': server_round,
            'accuracy': accuracy,
            'loss': loss,
            'participants_count': participants_count,
            'timestamp': datetime.now().isoformat()
        }
        
        # 保存指标到数据库
        try:
            # 向数据库API发送POST请求创建/更新指标记录
            response = requests.post('http://localhost:3001/api/metrics', json=metrics_data)
            if response.status_code == 201:
                logger.info(f"轮次 {server_round} 的指标已成功保存到数据库")
            else:
                logger.warning(f"无法保存指标到数据库: {response.status_code} - {response.text}")
        except Exception as db_error:
            logger.error(f"保存指标到数据库时出错: {db_error}")
        
        # 更新任务的当前轮次到数据库
        try:
            # 更新任务的current_round字段
            task_update_data = {
                'current_round': server_round
            }
            task_response = requests.put(f'http://localhost:3001/api/tasks/{server_state.task_id}', json=task_update_data)
            if task_response.status_code == 200:
                logger.info(f"任务 {server_state.task_id} 的当前轮次已更新为 {server_round}")
            else:
                logger.warning(f"无法更新任务轮次: {task_response.status_code} - {task_response.text}")
        except Exception as task_error:
            logger.error(f"更新任务轮次到数据库时出错: {task_error}")
            
        logger.info(f"轮次 {server_round} 训练结果记录完成，准确率: {accuracy:.4f}, 损失值: {loss:.4f}")
        return True
    except Exception as e:
        logger.error(f"记录训练结果失败: {e}")
        traceback.print_exc()
        return False

# 保存最终模型
def save_final_model(accuracy, participants):
    try:
        # 保存模型到文件
        model_path = os.path.join("models", f"model_{int(time.time())}.pt")
        os.makedirs(os.path.dirname(model_path), exist_ok=True)
        
        # 假设我们有一个全局模型
        # torch.save(global_model.state_dict(), model_path)
        
        # 计算模型哈希 (简化版)
        model_hash = f"model_{int(time.time())}"
        
        # 更新服务器状态
        server_state.final_model = {
            "hash": model_hash,
            "accuracy": f"{accuracy:.6f}",
            "participants": participants,
            "timestamp": int(time.time())
        }
        
        # 记录到区块链
        w3, contract = init_web3()
        account = w3.eth.accounts[0]
        
        tx_hash = contract.functions.storeFinalModel(
            model_hash,
            f"{accuracy:.6f}",
            participants
        ).transact({'from': account})
        
        receipt = w3.eth.wait_for_transaction_receipt(tx_hash)
        logger.info(f"Final model stored to blockchain: hash={model_hash}, accuracy={accuracy:.6f}")
    
    except Exception as e:
        logger.error(f"Error saving final model: {e}")

# 启动Flower服务器的函数
def start_flower_server(config):
    """启动Flower联邦学习服务器"""
    # 设置服务器账户地址和任务ID
    server_state.account_address = config.get('account_address', '')
    server_state.task_id = config.get('task_id')
    
    # 重置服务器状态
    reset_training()
    
    # 如果提供了任务ID，尝试从数据库加载任务信息
    if server_state.task_id:
        try:
            # 从数据库获取任务信息
            response = requests.get(f'http://localhost:3001/api/tasks/{server_state.task_id}')
            if response.status_code == 200:
                task_data = response.json()
                logger.info(f"从数据库加载任务 {server_state.task_id} 的信息: {task_data}")
                
                # 更新服务器状态中的任务信息
                server_state.total_rounds = task_data.get('total_rounds', 0)
                server_state.current_round = task_data.get('current_round', 0)
                
                # 如果有当前轮次信息，确保服务器状态保持同步
                if server_state.current_round > 0:
                    logger.info(f"从数据库恢复训练状态，当前轮次: {server_state.current_round}")
                    
                    # 尝试获取历史指标
                    try:
                        metrics_response = requests.get(f'http://localhost:3001/api/metrics/task/{server_state.task_id}')
                        if metrics_response.status_code == 200:
                            metrics_data = metrics_response.json()
                            
                            # 处理历史数据
                            for metric in metrics_data:
                                server_state.history.append({
                                    'round': metric.get('round', 0),
                                    'accuracy': metric.get('accuracy', 0),
                                    'loss': metric.get('loss', 0),
                                    'timestamp': metric.get('timestamp', datetime.now().isoformat()),
                                    'participants': metric.get('participants_count', 0)
                                })
                            
                            logger.info(f"从数据库加载了 {len(metrics_data)} 条历史指标数据")
                    except Exception as metrics_error:
                        logger.error(f"获取历史指标数据失败: {metrics_error}")
                
            else:
                logger.warning(f"无法从数据库获取任务信息: {response.status_code} - {response.text}")
        except Exception as task_error:
            logger.error(f"加载任务信息失败: {task_error}")
    
    # 服务器配置 - 优先使用从数据库加载的轮次数，然后是config中的rounds，最后才是默认值5
    num_rounds = server_state.total_rounds or config.get('rounds') or config.get('num_rounds', 5)
    logger.info(f"设置训练轮次: {num_rounds} (来源: server_state.total_rounds={server_state.total_rounds}, config.rounds={config.get('rounds')}, config.num_rounds={config.get('num_rounds')})")
    server_config = fl.server.ServerConfig(num_rounds=num_rounds)
    
    try:
        # 检查是否有现有训练在进行
        if server_state.is_training and server_state.server_thread and server_state.server_thread.is_alive():
            logger.warning("检测到有现有训练在进行，尝试停止它")
            try:
                # 尝试等待现有线程结束
                server_state.server_thread.join(timeout=2)
                if server_state.server_thread.is_alive():
                    logger.warning("无法停止现有训练线程，但将继续启动新训练")
            except Exception as e:
                logger.error(f"停止现有训练线程时出错: {e}")
        
        # 确保状态重置
        server_state.is_active = False
        server_state.is_training = False
        server_state.current_round = 0
        server_state.round_times = []
        
        # 更新服务器状态为新训练
        server_state.is_active = True
        server_state.is_training = True
        server_state.current_round = 0  # 初始设为0，在第一轮开始前
        server_state.total_rounds = config["rounds"]
        server_state.min_participants = config.get("minParticipants", 2)
        server_state.timeout = config.get("timeout", 60)
        server_state.start_time = time.time()
        server_state.round_start_time = time.time()
        server_state.round_times = []
        
        # 保存训练配置
        server_state.training_config = {
            "learningRate": config.get("learningRate", 0.001),
            "batchSize": config.get("batchSize", 32),
            "model": config.get("model", "CNN"),
            "optimizer": config.get("optimizer", "adam")
        }
        
        logger.info(f"Server state updated: is_active={server_state.is_active}, is_training={server_state.is_training}, current_round={server_state.current_round}")
        
        # 创建一个新的线程来运行Flower服务器
        def run_flower_server():
            try:
                # 设置策略
                min_clients = config.get("minParticipants", 2)
                logger.info(f"FedAvg strategy configured with min_clients={min_clients}")
                
                # 配置策略
                strategy = fl.server.strategy.FedAvg(
                    fraction_fit=1.0,  # 使用所有可用客户端进行训练
                    fraction_evaluate=1.0,  # 使用所有可用客户端进行评估
                    min_fit_clients=min_clients,  # 至少需要这么多客户端才能进行训练
                    min_available_clients=min_clients,  # 至少需要这么多客户端才能开始训练
                    on_fit_config_fn=lambda round_num: get_on_fit_config(round_num),  # 训练配置函数
                    evaluate_fn=evaluate_fn,  # 自定义评估函数
                    fit_metrics_aggregation_fn=weighted_average,  # 自定义指标聚合函数
                    evaluate_metrics_aggregation_fn=weighted_average  # 自定义评估指标聚合函数
                )
                
                # 端口选择逻辑
                available_port = None
                
                # 首先尝试使用已存在的端口
                if hasattr(server_state, 'server_port') and server_state.server_port:
                    # 验证端口是否可用
                    import socket
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(1)
                    result = sock.connect_ex(('127.0.0.1', server_state.server_port))
                    sock.close()
                    
                    if result != 0:  # 端口未被占用，可以重用
                        available_port = server_state.server_port
                        logger.info(f"重用之前的端口: {available_port}")
                    else:
                        logger.warning(f"之前的端口 {server_state.server_port} 仍在使用中，将选择新端口")
                
                # 如果没有可重用的端口，寻找新端口
                if not available_port:
                    start_port = 8080
                    end_port = 8100
                    
                    import socket
                    for port in range(start_port, end_port):
                        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        sock.settimeout(1)
                        result = sock.connect_ex(('127.0.0.1', port))
                        sock.close()
                        if result != 0:  # 端口未被占用
                            available_port = port
                            logger.info(f"找到可用端口: {available_port}")
                            break
                    
                    if not available_port:
                        # 如果所有指定端口都被占用，使用随机端口
                        import random
                        available_port = random.randint(8100, 8200)
                        logger.warning(f"所有指定端口都被占用，使用随机端口: {available_port}")
                
                # 设置服务器地址
                server_address = f"0.0.0.0:{available_port}"
                
                # 保存服务器地址到状态中，以便客户端可以连接
                server_state.server_address = server_address
                server_state.server_port = available_port
                
                # 直接输出服务器地址和端口信息，便于调试
                logger.info(f"===== Flower服务器端口设置 =====")
                logger.info(f"服务器地址: {server_address}")
                logger.info(f"服务器端口: {available_port}")
                logger.info(f"===== 客户端应连接到: localhost:{available_port} =====")
                
                # 启动 Flower 服务器
                fl.server.start_server(
                    server_address=server_address,
                    config=server_config,
                    strategy=strategy
                )
            except Exception as e:
                logger.error(f"Error in Flower server thread: {e}")
                logger.exception("Full traceback:")  # 记录完整的堆栈跟踪
            finally:
                # 训练结束后更新状态
                server_state.is_training = False
                logger.info("Federated training completed")
        
        # 创建并启动线程
        server_state.server_thread = threading.Thread(target=run_flower_server)
        server_state.server_thread.daemon = True
        server_state.server_thread.start()
        logger.info("Flower server thread started")
        
        return True
    
    except Exception as e:
        logger.error(f"Error starting Flower server: {e}")
        server_state.is_active = False
        server_state.is_training = False
        return False

# API 路由
@app.route('/api/server/status', methods=['GET'])
def get_server_status():
    """获取服务器状态"""
    try:
        logger.info("请求获取服务器训练状态")
        
        # 获取平均每轮训练时间
        avg_round_time = 0  # 默认为0
        
        if len(server_state.history) >= 2:
            # 计算历史上的平均每轮时间
            total_time = 0
            count = 0
            
            for i in range(1, len(server_state.history)):
                current = server_state.history[i]
                previous = server_state.history[i-1]
                
                # 计算时间差（秒）
                try:
                    current_time = datetime.fromisoformat(current.get('timestamp', '')).timestamp()
                    previous_time = datetime.fromisoformat(previous.get('timestamp', '')).timestamp()
                    
                    time_diff = current_time - previous_time
                    if time_diff > 0:
                        total_time += time_diff
                        count += 1
                except (ValueError, AttributeError) as e:
                    logger.error(f"计算轮次时间时出错: {e}")
            
            if count > 0:
                avg_round_time = total_time / count
        
        # 计算进度百分比
        progress = 0
        if server_state.total_rounds > 0:
            # 确保当前轮次至少为1，如果训练已开始
            current_round = max(1, server_state.current_round) if server_state.is_training else server_state.current_round
            progress = (current_round / server_state.total_rounds) * 100
        
        # 估计剩余时间（秒）
        estimated_time_remaining = 0
        if server_state.is_training and avg_round_time > 0 and server_state.total_rounds > server_state.current_round:
            remaining_rounds = server_state.total_rounds - server_state.current_round
            estimated_time_remaining = remaining_rounds * avg_round_time
        
        # 准备响应数据
        response_data = {
            "isTraining": server_state.is_training,
            "isActive": server_state.is_active,
            "taskId": server_state.task_id,
            "currentRound": max(1, server_state.current_round) if server_state.is_training else server_state.current_round,
            "totalRounds": server_state.total_rounds,
            "avgRoundTime": avg_round_time,
            "estimatedTimeRemaining": estimated_time_remaining,
            "progress": progress,
            "participants": server_state.participants,
            "history": server_state.history
        }
        
        logger.info(f"服务器状态: 训练中={server_state.is_training}, 当前轮次={server_state.current_round}, 总轮次={server_state.total_rounds}")
        return jsonify(response_data)
    except Exception as e:
        logger.error(f"获取服务器状态时出错: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/server/start', methods=['POST'])
def start_training():
    """开始训练"""
    if server_state.is_training:
        # 如果服务器已经在训练，则重置训练状态
        logger.warning("Training already in progress, resetting training state first")
        reset_training()
    
    try:
        # 获取请求数据
        data = request.json
        task_id = data.get("taskId")
        task_name = data.get("name", "Default Task")
        min_participants = data.get("minParticipants", 2)
        rounds = data.get("rounds", 5)
        learning_rate = data.get("learningRate", 0.01)
        batch_size = data.get("batchSize", 32)
        timeout = data.get("timeout", 60)
        
        logger.info(f"Starting training with config: {data}")
        
        # 从数据库获取已批准的参与者
        try:
            import requests
            response = requests.get(f"http://localhost:3001/api/tasks/{task_id}/participants")
            if response.status_code == 200:
                participants_data = response.json()
                # 过滤出已批准的参与者
                approved_participants = [p for p in participants_data if p['status'] not in ['registered', 'rejected']]
                
                # 清空当前参与者列表
                server_state.participants = []
                
                # 将已批准的参与者添加到 server_state.participants
                for participant in approved_participants:
                    new_participant = {
                        "address": participant['client_address'],
                        "isActive": True,
                        "joinTime": int(time.time()),
                        "dataset": {"size": participant.get('data_size', 1000)}
                    }
                    server_state.participants.append(new_participant)
                
                logger.info(f"Added {len(approved_participants)} approved participants to server state")
            else:
                logger.error(f"Failed to get participants from database: {response.status_code}")
                return jsonify({"error": "Failed to get participants from database"}), 500
        except Exception as e:
            logger.error(f"Error getting participants from database: {e}")
            return jsonify({"error": f"Error getting participants: {str(e)}"}), 500
        
        # 检查是否有足够的参与者
        if len(server_state.participants) < min_participants:
            logger.error(f"Not enough participants: {len(server_state.participants)} < {min_participants}")
            return jsonify({"error": f"Not enough participants: {len(server_state.participants)} < {min_participants}"}), 400
        
        # 创建配置
        config = {
            "task_id": task_id,
            "task_name": task_name,
            "min_participants": min_participants,
            "rounds": rounds,
            "learning_rate": learning_rate,
            "batch_size": batch_size,
            "timeout": timeout
        }
        
        # 更新服务器状态
        server_state.task_id = task_id
        server_state.task_name = task_name
        server_state.total_rounds = rounds
        server_state.current_round = 0
        server_state.start_time = time.time()
        server_state.required_participants = min_participants
        
        # 获取激活的参与者
        active_participants = [p["address"] for p in server_state.participants]
        logger.info(f"Active participants: {active_participants}")
        
        # 不再重新启动客户端，而是直接通知已运行的客户端开始训练
        logger.info("通知已运行的客户端开始训练...")
        
        # 使用端口映射表，每个客户端对应的端口号
        port_mapping = {}
        
        # 根据地址分配端口，假设第一个客户端端口是5001，第二个是5002，以此类推
        for idx, address in enumerate(active_participants):
            port_mapping[address] = 5001 + idx
            
        # 通知每个客户端开始训练
        for address, port in port_mapping.items():
            logger.info(f"通知客户端 {address} (端口 {port}) 开始训练...")
            
            try:
                # 构造训练参数
                train_params = {
                    "action": "start",
                    "taskId": task_id,
                    "rounds": rounds,
                    "batchSize": batch_size,
                    "learningRate": learning_rate,
                    "timeout": timeout
                }
                
                # 调用客户端训练API
                client_url = f"http://localhost:{port}/api/client/train"
                logger.info(f"调用客户端训练API: {client_url}, 参数: {train_params}")
                
                response = requests.post(
                    client_url,
                    json=train_params,
                    headers={"Content-Type": "application/json"},
                    timeout=10
                )
                
                # 检查响应
                if response.status_code == 200 and response.json().get("success"):
                    logger.info(f"成功通知客户端开始训练: {response.json()}")
                else:
                    logger.warning(f"通知客户端返回非成功状态: {response.status_code}, {response.text}")
            except Exception as api_error:
                logger.error(f"通知客户端训练失败: {api_error}")
                # 通知失败不影响整体训练流程，继续处理下一个客户端
        
        # 更新区块链合约状态
        try:
            w3, contract = init_web3()
            account = w3.eth.accounts[0]
            
            # 检查训练是否已经在区块链上激活
            is_active = contract.functions.isTrainingActive().call()
            
            if not is_active:
                # 如果没有激活，则开始训练
                tx_hash = contract.functions.startTraining(
                    rounds,
                    min_participants,
                    w3.toWei(learning_rate, 'ether'),  # 将学习率转换为wei
                    batch_size,
                    timeout
                ).transact({'from': account})
                
                w3.eth.wait_for_transaction_receipt(tx_hash)
                logger.info("Training started on blockchain")
            else:
                logger.info("Training already active on blockchain")
        except Exception as e:
            logger.error(f"Error updating blockchain: {e}")
        
        # 启动Flower服务器
        success = start_flower_server(config)
        
        if success:
            return jsonify({"message": "Training started successfully"})
        else:
            return jsonify({"error": "Failed to start Flower server"}), 500
    
    except Exception as e:
        logger.error(f"Error starting training: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/server/stop', methods=['POST'])
def stop_training():
    """停止训练"""
    if not server_state.is_active:
        return jsonify({"error": "Server is not active"}), 400
    
    try:
        # 更新状态
        server_state.is_training = False
        server_state.is_active = False  # 同时更新 is_active 状态
        
        # 调用区块链合约停止训练
        w3, contract = init_web3()
        account = w3.eth.accounts[0]
        
        # 检查训练是否在区块链上处于活跃状态
        try:
            is_active = contract.functions.isTrainingActive().call()
            if is_active:
                tx_hash = contract.functions.stopTraining().transact({'from': account})
                w3.eth.wait_for_transaction_receipt(tx_hash)
                logger.info("Training stopped on blockchain")
            else:
                logger.info("Training already inactive on blockchain")
        except Exception as e:
            logger.error(f"Error checking/stopping training on blockchain: {e}")
        
        # 等待 Flower 服务器线程结束
        if server_state.server_thread and server_state.server_thread.is_alive():
            logger.info("Waiting for Flower server thread to terminate...")
            server_state.server_thread.join(timeout=5)  # 等待最多5秒
            if server_state.server_thread.is_alive():
                logger.warning("Flower server thread did not terminate within timeout")
            else:
                logger.info("Flower server thread terminated successfully")
        
        # 重置相关状态
        server_state.current_round = 0
        server_state.round_times = []
        server_state.round_start_time = 0
        
        return jsonify({"message": "Training stopped successfully"})
    
    except Exception as e:
        logger.error(f"Error stopping training: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/server/reset', methods=['POST'])
def reset_training_api():
    """重置训练状态"""
    try:
        reset_training()
        return jsonify({"message": "Training reset successfully"})
    
    except Exception as e:
        logger.error(f"Error resetting training: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/server/join', methods=['POST'])
def server_join_redirect():
    """重定向到客户端加入训练的API"""
    logger.info("收到/api/server/join请求，重定向到/api/client/join")
    return join_training()

@app.route('/api/client/join', methods=['POST'])
def join_training():
    """客户端加入训练"""
    try:
        data = request.json
        address = data.get("address")
        dataset = data.get("dataset")  # 从请求中获取dataset而不是config
        
        if not address:
            return jsonify({"error": "Client address is required"}), 400
        
        # 确保dataset是一个字典
        if dataset is None:
            dataset = {}
        
        # 检查客户端是否已加入
        for p in server_state.participants:
            if p["address"] == address:
                p["isActive"] = True
                if dataset:  # 如果提供了dataset，则更新
                    p["dataset"] = dataset
                return jsonify({"message": "Client rejoined successfully"})
        
        # 添加新客户端
        new_participant = {
            "address": address,
            "isActive": True,
            "joinTime": int(time.time()),
            "dataset": dataset
        }
        
        # 安全地获取数据集大小
        try:
            if isinstance(dataset, dict) and "size" in dataset:
                new_participant["dataSize"] = int(dataset["size"])
            else:
                new_participant["dataSize"] = 0
        except (TypeError, ValueError) as e:
            logger.warning(f"Error parsing dataset size: {e}")
            new_participant["dataSize"] = 0
            
        server_state.participants.append(new_participant)
        
        server_state.metrics["participants"] = len([p for p in server_state.participants if p["isActive"]])
        
        return jsonify({"message": "Client joined successfully"})
    
    except Exception as e:
        logger.error(f"Error joining training: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/client/leave', methods=['POST'])
def leave_training():
    """客户端退出训练"""
    try:
        data = request.json
        address = data.get("address")
        
        if not address:
            return jsonify({"error": "Client address is required"}), 400
        
        # 更新客户端状态
        for p in server_state.participants:
            if p["address"] == address:
                p["isActive"] = False
                break
        
        server_state.metrics["participants"] = len([p for p in server_state.participants if p["isActive"]])
        
        return jsonify({"message": "Client left successfully"})
    
    except Exception as e:
        logger.error(f"Error leaving training: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/client/status/<address>', methods=['GET'])
def get_client_status(address):
    """获取客户端状态"""
    try:
        # 查找客户端
        client = None
        for p in server_state.participants:
            if p["address"] == address:
                client = p
                break
        
        if not client:
            return jsonify({"isParticipating": False})
        
        # 获取客户端贡献度
        w3, contract = init_web3()
        contribution = contract.functions.getParticipantContribution(address).call()
        
        # 构建客户端指标
        client_metrics = {
            "loss": server_state.metrics["loss"],
            "accuracy": server_state.metrics["accuracy"],
            "trainingTime": int(time.time() - client["joinTime"]) if client["isActive"] else 0,
            "participants": server_state.metrics["participants"]
        }
        
        # 构建贡献度统计
        contribution_stats = {
            "totalRounds": contribution[0],
            "averageAccuracy": float(contribution[1]) if contribution[1].replace(".", "", 1).isdigit() else 0,
            "totalTime": contribution[2],
            "rank": contribution[3],
            "totalParticipants": len(server_state.participants)
        }
        
        return jsonify({
            "isParticipating": client["isActive"],
            "clientMetrics": client_metrics,
            "contributionStats": contribution_stats
        })
    
    except Exception as e:
        logger.error(f"Error getting client status: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/client/dataset/<address>', methods=['GET'])
def get_dataset_info(address):
    """获取客户端数据集信息"""
    try:
        # 查找客户端
        client = None
        for p in server_state.participants:
            if p["address"] == address:
                client = p
                break
        
        # 如果客户端已加入并提供了数据集信息，则使用它
        if client and "dataset" in client and client["dataset"]:
            dataset = client["dataset"]
            return jsonify({
                "name": dataset.get("name", "MNIST"),
                "size": dataset.get("size", 60000),
                "classes": dataset.get("classes", 10),
                "type": dataset.get("type", "image"),
                "trainingSplit": 0.8,
                "validationSplit": 0.1,
                "testSplit": 0.1
            })
        
        # 否则返回默认数据
        return jsonify({
            "name": "MNIST",
            "size": 60000,
            "classes": 10,
            "type": "image",
            "trainingSplit": 0.8,
            "validationSplit": 0.1,
            "testSplit": 0.1
        })
    
    except Exception as e:
        logger.error(f"Error getting dataset info: {e}")
        return jsonify({"error": str(e)}), 500

# 添加任务信息API
@app.route('/api/server/task/<task_id>', methods=['GET'])
def get_task_info(task_id):
    """获取特定任务的详细信息"""
    try:
        # 检查当前任务ID是否与请求的任务ID匹配
        if hasattr(server_state, 'task_id') and server_state.task_id == task_id:
            # 获取活跃参与者
            active_participants = [p for p in server_state.participants if p["isActive"]]
            
            # 计算训练进度百分比
            progress = 0
            if server_state.total_rounds > 0:
                progress = (server_state.current_round / server_state.total_rounds) * 100
            
            # 构建任务信息
            task_info = {
                "id": task_id,
                "status": "in_progress" if server_state.is_training else "waiting",
                "currentRound": server_state.current_round,
                "totalRounds": server_state.total_rounds,
                "participants": [p["address"] for p in active_participants],
                "requiredParticipants": server_state.min_participants,
                "totalParticipants": len(active_participants),
                "startTime": server_state.start_time,
                "progress": progress,
                "metrics": server_state.metrics,
                "history": server_state.history,
                "isActive": server_state.is_active,
                "isTraining": server_state.is_training
            }
            
            return jsonify(task_info)
        else:
            # 尝试从已保存的任务列表中查找
            try:
                # 在实际应用中，这里应该查询数据库
                # 这里我们返回一个模拟的任务信息
                return jsonify({
                    "id": task_id,
                    "name": f"训练任务 {task_id}",
                    "status": "unknown",
                    "currentRound": 0,
                    "totalRounds": 0,
                    "participants": [],
                    "requiredParticipants": 0,
                    "totalParticipants": 0,
                    "startTime": 0,
                    "progress": 0,
                    "metrics": {
                        "loss": 0.0,
                        "accuracy": 0.0
                    },
                    "history": [],
                    "isActive": False,
                    "isTraining": False,
                    "message": "任务信息不可用或任务不存在"
                })
            except Exception as e:
                logger.error(f"在任务列表中查找任务信息失败: {e}")
                return jsonify({"error": "任务不存在"}), 404
    
    except Exception as e:
        logger.error(f"获取任务信息失败: {e}")
        return jsonify({"error": str(e)}), 500

# 添加启动客户端进程的API
@app.route('/api/client/start_client', methods=['POST'])
def start_client_process():
    """启动客户端进程"""
    try:
        data = request.json
        account = data.get("account")
        port = data.get("port", 5001)  # 默认端口5001
        task_id = data.get("taskId") or server_state.task_id  # 从请求中获取任务ID，如果没有则使用服务器当前任务ID
        
        if not account:
            return jsonify({"error": "Client account is required"}), 400
            
        if not port:
            return jsonify({"error": "Client port is required"}), 400
            
        logger.info(f"收到启动客户端进程请求: 账户={account}, 端口={port}, 任务ID={task_id}")
        
        # 使用内部函数启动客户端
        result = start_client_process_internal(account, port, task_id)
        
        if result["success"]:
            return jsonify(result)
        else:
            return jsonify(result), 500
    
    except Exception as e:
        logger.error(f"启动客户端进程错误: {e}")
        return jsonify({"error": str(e)}), 500

# 内部函数：启动客户端进程
def start_client_process_internal(account, port, task_id=None):
    """启动客户端进程的内部函数，并自动调用训练API"""
    try:
        # 构建启动命令
        command = f"python flower/client.py --account \"{account}\" --port {port}"
            
        logger.info(f"执行命令: {command}")
        
        # 使用子进程启动客户端
        import subprocess
        
        # 使用Popen在后台启动进程
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待一段时间，确保进程启动
        time.sleep(3)  # 增加等待时间确保客户端服务完全启动
        
        # 检查进程是否已启动
        if process.poll() is None:
            # 进程仍在运行，表示启动成功
            logger.info(f"客户端进程启动成功: PID={process.pid}")
            
            # 如果提供了任务ID，自动调用客户端的训练API
            if task_id:
                try:
                    # 构造训练参数
                    train_params = {
                        "action": "start",
                        "taskId": task_id
                    }
                    
                    # 等待额外的时间确保客户端API服务已就绪
                    time.sleep(2)
                    
                    # 调用客户端训练API
                    client_url = f"http://localhost:{port}/api/client/train"
                    logger.info(f"自动调用客户端训练API: {client_url}, 参数: {train_params}")
                    
                    response = requests.post(
                        client_url,
                        json=train_params,
                        headers={"Content-Type": "application/json"},
                        timeout=10
                    )
                    
                    # 检查响应
                    if response.status_code == 200 and response.json().get("success"):
                        logger.info(f"成功自动启动客户端训练: {response.json()}")
                    else:
                        logger.warning(f"自动启动客户端训练返回非成功状态: {response.status_code}, {response.text}")
                        
                except Exception as api_error:
                    logger.error(f"自动调用客户端训练API失败: {api_error}")
                    # 训练API调用失败不影响进程启动的结果
            
            return {
                "success": True,
                "message": f"Client process started successfully with PID {process.pid}",
                "pid": process.pid,
                "account": account,
                "port": port,
                "training_started": task_id is not None
            }
        else:
            # 进程已退出，获取错误信息
            stdout, stderr = process.communicate()
            error_msg = stderr or stdout or "Unknown error"
            logger.error(f"客户端进程启动失败: {error_msg}")
            return {
                "success": False,
                "error": f"Failed to start client process: {error_msg}"
            }
    
    except Exception as e:
        logger.error(f"启动客户端进程内部错误: {e}")
        return {
            "success": False,
            "error": str(e)
        }

# 主函数入口点
if __name__ == "__main__":
    try:
        # 初始化Web3和合约
        logger.info("Initializing Web3 and contract...")
        w3, contract = init_web3()
        logger.info("Web3 and contract initialized successfully")
        
        # 确保重置所有训练状态
        logger.info("重置所有训练状态...")
        reset_training()
        
        # 确保服务器状态为未训练
        server_state.is_active = False
        server_state.is_training = False
        logger.info("服务器状态已初始化: is_active=False, is_training=False")
        
        # 启动Flask应用
        logger.info("Starting Flask server on port 5000...")
        app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        logger.exception("Full traceback:")
