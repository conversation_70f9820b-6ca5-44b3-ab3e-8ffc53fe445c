<template>
  <div class="chart-config">
    <div class="config-header">
      <h3>图表配置</h3>
      <button class="toggle-btn" @click="isExpanded = !isExpanded">
        {{ isExpanded ? '收起' : '展开' }}
      </button>
    </div>
    
    <div v-if="isExpanded" class="config-content">
      <div class="config-item">
        <label>显示指标</label>
        <div class="checkbox-group">
          <label>
            <input type="checkbox" v-model="config.showLoss">
            损失值
          </label>
          <label>
            <input type="checkbox" v-model="config.showAccuracy">
            准确率
          </label>
        </div>
      </div>
      
      <div class="config-item">
        <label>时间范围</label>
        <select v-model="config.timeRange">
          <option value="all">全部</option>
          <option value="10">最近10轮</option>
          <option value="20">最近20轮</option>
          <option value="50">最近50轮</option>
        </select>
      </div>
      
      <div class="config-item">
        <label>更新频率</label>
        <select v-model="config.updateInterval">
          <option value="1000">1秒</option>
          <option value="2000">2秒</option>
          <option value="5000">5秒</option>
        </select>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, watch } from 'vue';

export default {
  name: 'ChartConfig',
  emits: ['update:config'],
  setup(props, { emit }) {
    const isExpanded = ref(false);
    const config = reactive({
      showLoss: true,
      showAccuracy: true,
      timeRange: 'all',
      updateInterval: '2000'
    });
    
    watch(config, (newConfig) => {
      emit('update:config', { ...newConfig });
    }, { deep: true });
    
    return {
      isExpanded,
      config
    };
  }
}
</script>

<style scoped>
.chart-config {
  background: #f5f5f5;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 15px;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toggle-btn {
  padding: 4px 8px;
  background: #2196F3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.config-content {
  margin-top: 10px;
}

.config-item {
  margin: 10px 0;
}

.checkbox-group {
  display: flex;
  gap: 15px;
}

select {
  width: 100%;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
}
</style> 