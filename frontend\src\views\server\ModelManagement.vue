<!-- 服务端模型管理页面 -->
<template>
  <div class="model-management">
    <div class="page-header">
      <h1>模型管理</h1>
      <div class="page-actions">
        <el-button type="primary" @click="importModel">
          <i class="fas fa-file-import"></i> 导入模型
        </el-button>
        <el-button type="success" @click="refreshData">
          <i class="fas fa-sync"></i> 刷新数据
        </el-button>
      </div>
    </div>
    
    <Breadcrumb />
    
    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.totalModels }}</div>
        <div class="stat-label">总模型数</div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.activeModels }}</div>
        <div class="stat-label">已激活模型</div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.publishedModels }}</div>
        <div class="stat-label">已发布模型</div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.trainingModels }}</div>
        <div class="stat-label">训练任务创建</div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.modelDownloads }}</div>
        <div class="stat-label">总下载次数</div>
      </el-card>
    </div>
    
    <!-- 模型筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-group">
        <span class="filter-label">模型类型：</span>
        <el-select v-model="typeFilter" placeholder="所有类型" clearable>
          <el-option label="图像分类" value="image_classification" />
          <el-option label="目标检测" value="object_detection" />
          <el-option label="自然语言处理" value="nlp" />
          <el-option label="表格数据" value="tabular" />
          <el-option label="时间序列" value="time_series" />
        </el-select>
      </div>
      
      <div class="filter-group">
        <span class="filter-label">模型状态：</span>
        <el-select v-model="statusFilter" placeholder="所有状态" clearable>
          <el-option label="草稿" value="draft" />
          <el-option label="已发布" value="published" />
          <el-option label="已归档" value="archived" />
        </el-select>
      </div>
      
      <div class="filter-group">
        <span class="filter-label">模型来源：</span>
        <el-select v-model="sourceFilter" placeholder="所有来源" clearable>
          <el-option label="训练任务" value="training" />
          <el-option label="手动导入" value="manual" />
        </el-select>
      </div>
      
      <div class="search-box">
        <el-input v-model="searchQuery" placeholder="搜索模型名称或描述" clearable>
          <template #prefix>
            <i class="fas fa-search"></i>
          </template>
        </el-input>
      </div>
    </div>
    
    <!-- 模型列表 -->
    <el-table
      :data="filteredModels"
      style="width: 100%"
      v-loading="loading"
      border
      row-key="id"
    >
      <el-table-column type="index" width="50" />
      
      <el-table-column prop="name" label="模型名称" min-width="200">
        <template #default="scope">
          <div class="model-name-container">
            <div class="model-name" :class="{ 'highlighted-model': isHighlightedModel(scope.row) }">
              <span class="model-title">{{ scope.row.name }}</span>
              <div class="model-tags">
                <el-tag v-if="scope.row.isLatest" size="small" type="success">最新</el-tag>
                <el-tag v-if="isHighlightedModel(scope.row)" size="small" type="warning">本次训练</el-tag>
                <el-tag v-if="scope.row.fromTraining" size="small" type="info">自动创建</el-tag>
              </div>
            </div>
            <div class="model-meta">
              <span class="version">版本: v{{ scope.row.version }}</span>
              <span class="date">创建: {{ formatDate(scope.row.createdAt) }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="type" label="类型" width="140">
        <template #default="scope">
          <el-tag :type="getModelTypeTag(scope.row.type)">
            {{ getModelTypeText(scope.row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="performance" label="性能" width="120">
        <template #default="scope">
          <div v-if="scope.row.performance" class="model-performance">
            <el-progress 
              type="circle" 
              :percentage="parseFloat(scope.row.performance * 100).toFixed(0)" 
              :width="40"
              :stroke-width="6"
              :status="getPerformanceStatus(scope.row.performance)"
            ></el-progress>
            <span>{{ formatPerformance(scope.row.performance) }}</span>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="task" label="相关任务" min-width="140">
        <template #default="scope">
          <router-link v-if="scope.row.taskId" :to="`/server/tasks/${scope.row.taskId}`">
            {{ scope.row.taskName }}
          </router-link>
          <span v-else>手动导入</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="size" label="大小" width="100">
        <template #default="scope">
          {{ formatSize(scope.row.size) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="downloads" label="下载" width="80" align="center">
        <template #default="scope">
          {{ scope.row.downloads || 0 }}
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="scope">
          <div class="model-action">
            <el-button @click="viewModelDetails(scope.row)" size="small" type="primary">
              <i class="fas fa-info-circle"></i> 查看详情
            </el-button>
            <el-dropdown trigger="click" size="small">
              <el-button size="small" type="primary">
                操作 <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="downloadModel(scope.row)">
                    <i class="fas fa-download"></i> 下载模型
                  </el-dropdown-item>
                  <el-dropdown-item @click="editModelName(scope.row)">
                    <i class="fas fa-edit"></i> 修改名称
                  </el-dropdown-item>
                  <el-dropdown-item v-if="scope.row.status === 'draft'" @click="publishModel(scope.row)">
                    <i class="fas fa-globe"></i> 发布模型
                  </el-dropdown-item>
                  <el-dropdown-item v-if="scope.row.status === 'published'" @click="archiveModel(scope.row)">
                    <i class="fas fa-archive"></i> 归档模型
                  </el-dropdown-item>
                  <el-dropdown-item v-if="scope.row.status === 'archived'" @click="restoreModel(scope.row)">
                    <i class="fas fa-undo"></i> 恢复为草稿
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="deleteModel(scope.row)">
                    <i class="fas fa-trash-alt"></i> 删除模型
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalModels"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 导入模型对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入模型"
      width="600px"
    >
      <el-form :model="importForm" ref="importFormRef" label-width="100px">
        <el-form-item label="模型名称" prop="name" required>
          <el-input v-model="importForm.name" placeholder="请输入模型名称"></el-input>
        </el-form-item>
        
        <el-form-item label="模型类型" prop="type" required>
          <el-select v-model="importForm.type" placeholder="请选择模型类型">
            <el-option label="图像分类" value="image_classification" />
            <el-option label="目标检测" value="object_detection" />
            <el-option label="自然语言处理" value="nlp" />
            <el-option label="表格数据" value="tabular" />
            <el-option label="时间序列" value="time_series" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="版本" prop="version" required>
          <el-input v-model="importForm.version" placeholder="请输入版本号，如：1.0.0"></el-input>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="importForm.description" 
            type="textarea" 
            rows="3"
            placeholder="请输入模型描述"
          ></el-input>
        </el-form-item>
        
        <el-form-item label="模型文件" prop="file" required>
          <el-upload
            action="#"
            :auto-upload="false"
            :limit="1"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
          >
            <el-button type="primary">选择模型文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持 .h5, .pb, .onnx, .pt, .pth 等格式的模型文件
              </div>
            </template>
          </el-upload>
        </el-form-item>
        
        <el-form-item label="性能指标" prop="performance">
          <el-input-number 
            v-model="importForm.performance" 
            :min="0" 
            :max="1" 
            :step="0.01"
            :precision="4"
            placeholder="请输入性能指标（0-1）"
          ></el-input-number>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitImport" :loading="importing">导入</el-button>
      </template>
    </el-dialog>
    
    <!-- 模型详情对话框 -->
    <el-dialog
      v-model="detailsVisible"
      title="模型详情"
      width="700px"
      v-if="currentModel"
    >
      <div class="model-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="模型名称" :span="2">
            {{ currentModel?.name }}
            <el-tag v-if="currentModel?.isLatest" size="small" type="success">最新</el-tag>
            <el-tag v-if="currentModel?.fromTraining" size="small" type="info">自动创建</el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="模型类型">
            <el-tag :type="getModelTypeTag(currentModel?.type)">
              {{ getModelTypeText(currentModel?.type) }}
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="版本">
            v{{ currentModel?.version }}
          </el-descriptions-item>
          
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(currentModel?.status)">
              {{ getStatusText(currentModel?.status) }}
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(currentModel?.createdAt) }}
          </el-descriptions-item>
          
          <el-descriptions-item label="相关任务" :span="2">
            <router-link v-if="currentModel?.taskId" :to="`/server/tasks/${currentModel?.taskId}`">
              {{ currentModel?.taskName }}
            </router-link>
            <span v-else>手动导入</span>
          </el-descriptions-item>
          
          <el-descriptions-item label="文件大小">
            {{ formatSize(currentModel?.size) }}
          </el-descriptions-item>
          
          <el-descriptions-item label="下载次数">
            {{ currentModel?.downloads || 0 }}
          </el-descriptions-item>
          
          <el-descriptions-item label="性能评分">
            {{ formatPerformance(currentModel?.performance) }}
          </el-descriptions-item>
          
          <el-descriptions-item label="最后更新">
            {{ formatDateTime(currentModel?.updatedAt) }}
          </el-descriptions-item>
          
          <el-descriptions-item label="描述" :span="2">
            {{ currentModel?.description || '无描述' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="任务来源" v-if="currentModel.fromTraining">
            自动从训练任务创建
            <el-button 
              type="text" 
              @click="viewTrainingTask(currentModel)" 
              style="margin-left: 10px">
              查看相关训练任务 <i class="fas fa-external-link-alt"></i>
            </el-button>
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="details-title">模型性能图表</div>
        
        <div class="performance-charts">
          <div class="chart-placeholder">
            <i class="fas fa-chart-line"></i> 性能图表将在后续版本实现
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="detailsVisible = false">关闭</el-button>
        <el-button type="primary" @click="downloadModel(currentModel)">下载模型</el-button>
        <el-button 
          type="success" 
          v-if="currentModel?.status === 'draft'"
          @click="publishModel(currentModel)"
        >发布模型</el-button>
      </template>
    </el-dialog>
    
    <!-- 修改模型名称对话框 -->
    <el-dialog
      v-model="renameDialogVisible"
      title="修改模型名称"
      width="400px"
    >
      <el-form :model="renameForm" label-width="80px">
        <el-form-item label="当前名称">
          <el-input v-model="renameForm.oldName" disabled></el-input>
        </el-form-item>
        <el-form-item label="新名称" required>
          <el-input v-model="renameForm.newName" placeholder="请输入新的模型名称"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="renameDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitRename" :loading="renaming">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import Breadcrumb from '@/components/Breadcrumb.vue';
import { ModelAPI, TaskAPI, apiService } from '@/services/api';

export default {
  name: 'ModelManagement',
  components: {
    Breadcrumb
  },
  setup() {
    const router = useRouter();
    const loading = ref(false);
    const importing = ref(false);
    const models = ref([]);
    const typeFilter = ref('');
    const statusFilter = ref('');
    const sourceFilter = ref('');
    const searchQuery = ref('');
    const currentPage = ref(1);
    const pageSize = ref(10);
    const importDialogVisible = ref(false);
    const detailsVisible = ref(false);
    const currentModel = ref(null);
    const importFormRef = ref(null);
    const highlightedTaskId = ref(null);
    
    // 导入表单
    const importForm = reactive({
      name: '',
      type: '',
      version: '1.0.0',
      description: '',
      file: null,
      performance: null
    });
    
    // 重命名对话框
    const renameDialogVisible = ref(false);
    const renaming = ref(false);
    const renameForm = reactive({
      id: null,
      oldName: '',
      newName: ''
    });
    
    // 统计数据
    const statistics = reactive({
      totalModels: 0,
      activeModels: 0,
      publishedModels: 0,
      modelDownloads: 0,
      trainingModels: 0
    });
    
    // 加载模型列表
    const loadModels = async () => {
      loading.value = true;
      try {
        console.log('正在从API获取模型列表...');
        // 调用API获取模型列表
        const modelList = await ModelAPI.getAll();
        console.log('获取到的模型列表:', modelList);
        
        if (Array.isArray(modelList)) {
          // 处理API返回的数据
          models.value = modelList.map(model => ({
            id: model.id,
            name: model.name,
            type: model.type,
            version: model.version,
            description: model.description,
            createdAt: new Date(model.created_at),
            updatedAt: new Date(model.updated_at),
            size: model.size,
            performance: parsePerformanceData(model.performance),
            downloads: model.downloads || 0,
            status: model.status,
            isLatest: Boolean(model.is_latest),
            fromTraining: Boolean(model.from_training),
            taskId: model.task_id,
            task_id: model.task_id,
            taskName: model.task_name,
            filePath: model.file_path
          }));
          
          console.log('转换后的模型数据:', models.value);
        } else {
          // 如果API返回格式不正确，使用空数组
          console.warn('API返回的模型列表格式不正确', modelList);
          models.value = [];
          ElMessage.warning('获取模型数据格式不正确');
        }
        
        // 计算统计数据
        updateStatistics();
        
        // 检查URL参数中是否有taskId，如果有则设置高亮
        const route = router.currentRoute.value;
        if (route.query.taskId) {
          highlightedTaskId.value = route.query.taskId;
          highlightTaskModels(route.query.taskId);
        } else {
          // 如果没有taskId参数，则显示最近的训练模型
          showLatestTrainingModel();
        }
      } catch (error) {
        console.error('加载模型列表失败:', error);
        ElMessage.error('加载模型列表失败: ' + (error.message || '未知错误'));
        // 使用空数组以便界面正常显示
        models.value = [];
      } finally {
        loading.value = false;
      }
    };
    
    // 解析性能数据
    const parsePerformanceData = (performance) => {
      if (!performance) return 0;
      
      try {
        // 尝试解析JSON字符串
        if (typeof performance === 'string') {
          const parsedData = JSON.parse(performance);
          if (typeof parsedData === 'object') {
            // 如果是对象，尝试获取accuracy字段
            return parsedData.accuracy || 0.8;
          } else if (typeof parsedData === 'number') {
            // 如果解析后是数字，直接返回
            return parsedData;
          }
        } 
        // 如果已经是对象，尝试获取accuracy
        else if (typeof performance === 'object') {
          return performance.accuracy || 0.8;
        } 
        // 如果是数字，直接返回
        else if (typeof performance === 'number') {
          return performance;
        }
        
        // 默认返回
        return 0.8;
      } catch (error) {
        console.error('解析性能数据错误:', error, performance);
        return 0.8;
      }
    };
    
    // 格式化日期
    const formatDate = (date) => {
      if (!date) return '-';
      if (typeof date === 'string') {
        date = new Date(date);
      }
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    };
    
    // 格式化日期时间
    const formatDateTime = (date) => {
      if (!date) return '-';
      if (typeof date === 'string') {
        date = new Date(date);
      }
      
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    };
    
    // 格式化性能
    const formatPerformance = (performance) => {
      if (performance === undefined || performance === null) return '-';
      return `${(performance * 100).toFixed(2)}%`;
    };
    
    // 获取性能状态
    const getPerformanceStatus = (performance) => {
      if (performance >= 0.9) return 'success';
      if (performance >= 0.8) return 'warning';
      if (performance >= 0.7) return '';
      return 'exception';
    };
    
    // 格式化文件大小
    const formatSize = (size) => {
      if (size === undefined || size === null) return '-';
      
      if (size < 1024) {
        return `${size} B`;
      } else if (size < 1024 * 1024) {
        return `${(size / 1024).toFixed(2)} KB`;
      } else if (size < 1024 * 1024 * 1024) {
        return `${(size / (1024 * 1024)).toFixed(2)} MB`;
      } else {
        return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
      }
    };
    
    // 获取模型类型标签
    const getModelTypeTag = (type) => {
      const types = {
        image_classification: 'success',
        object_detection: 'success',
        nlp: 'warning',
        tabular: 'info',
        time_series: 'primary'
      };
      return types[type] || 'info';
    };
    
    // 获取模型类型文本
    const getModelTypeText = (type) => {
      const texts = {
        image_classification: '图像分类',
        object_detection: '目标检测',
        nlp: '自然语言',
        tabular: '表格数据',
        time_series: '时间序列'
      };
      return texts[type] || type;
    };
    
    // 获取状态标签类型
    const getStatusTagType = (status) => {
      const types = {
        draft: 'info',
        published: 'success',
        archived: 'danger'
      };
      return types[status] || 'info';
    };
    
    // 获取状态文本
    const getStatusText = (status) => {
      const texts = {
        draft: '草稿',
        published: '已发布',
        archived: '已归档'
      };
      return texts[status] || status;
    };
    
    // 计算属性：过滤后的模型列表
    const filteredModels = computed(() => {
      let result = [...models.value];
      
      // 根据类型筛选
      if (typeFilter.value) {
        result = result.filter(model => model.type === typeFilter.value);
      }
      
      // 根据状态筛选
      if (statusFilter.value) {
        result = result.filter(model => model.status === statusFilter.value);
      }
      
      // 根据来源筛选
      if (sourceFilter.value) {
        if (sourceFilter.value === 'training') {
          result = result.filter(model => model.fromTraining);
        } else if (sourceFilter.value === 'manual') {
          result = result.filter(model => !model.fromTraining);
        }
      }
      
      // 根据搜索关键词筛选
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(model => 
          model.name.toLowerCase().includes(query) || 
          (model.description && model.description.toLowerCase().includes(query))
        );
      }
      
      // 排序:
      // 1. 如果有高亮任务ID，将相关模型排在前面
      // 2. 最近创建的草稿模型排在前面(过去7天内创建的)
      // 3. 已发布模型排在中间
      // 4. 归档模型排在后面
      result.sort((a, b) => {
        // 如果是从指定任务ID跳转来的
        if (highlightedTaskId.value) {
          const aIsHighlighted = a.taskId && a.taskId.toString() === highlightedTaskId.value.toString();
          const bIsHighlighted = b.taskId && b.taskId.toString() === highlightedTaskId.value.toString();
          
          if (aIsHighlighted && !bIsHighlighted) return -1;
          if (!aIsHighlighted && bIsHighlighted) return 1;
        }
        
        // 按状态和创建时间排序
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        
        const aIsRecentDraft = a.status === 'draft' && new Date(a.createdAt) > sevenDaysAgo;
        const bIsRecentDraft = b.status === 'draft' && new Date(b.createdAt) > sevenDaysAgo;
        
        if (aIsRecentDraft && !bIsRecentDraft) return -1;
        if (!aIsRecentDraft && bIsRecentDraft) return 1;
        
        // 然后按状态排序: 发布 > 草稿 > 归档
        const statusOrder = { 'published': 1, 'draft': 2, 'archived': 3 };
        if (statusOrder[a.status] !== statusOrder[b.status]) {
          return statusOrder[a.status] - statusOrder[b.status];
        }
        
        // 同状态按创建时间降序
        return new Date(b.createdAt) - new Date(a.createdAt);
      });
      
      return result;
    });
    
    // 计算总模型数
    const totalModels = computed(() => filteredModels.value.length);
    
    // 页面大小改变
    const handleSizeChange = (size) => {
      pageSize.value = size;
    };
    
    // 当前页改变
    const handleCurrentChange = (page) => {
      currentPage.value = page;
    };
    
    // 处理文件上传
    const handleFileChange = (file) => {
      importForm.file = file;
    };
    
    // 处理文件移除
    const handleFileRemove = () => {
      importForm.file = null;
    };
    
    // 导入模型
    const importModel = () => {
      // 重置表单
      importForm.name = '';
      importForm.type = '';
      importForm.version = '1.0.0';
      importForm.description = '';
      importForm.file = null;
      importForm.performance = null;
      
      importDialogVisible.value = true;
    };
    
    // 提交导入
    const submitImport = async () => {
      if (!importForm.name || !importForm.type || !importForm.version || !importForm.file) {
        ElMessage.warning('请填写必要的模型信息并上传模型文件');
        return;
      }
      
      importing.value = true;
      try {
        // 这里应该调用API导入模型
        await new Promise(resolve => setTimeout(resolve, 1500)); // 模拟API调用
        
        // 模拟新添加的模型
        const newModel = {
          id: models.value.length + 1,
          name: importForm.name,
          type: importForm.type,
          version: importForm.version,
          description: importForm.description,
          createdAt: new Date(),
          updatedAt: new Date(),
          size: Math.floor(Math.random() * 100000000) + 1000000, // 随机大小
          performance: importForm.performance,
          downloads: 0,
          status: 'draft',
          isLatest: true,
          taskId: null,
          taskName: null
        };
        
        // 添加到列表
        models.value.push(newModel);
        
        // 更新统计
        updateStatistics();
        
        ElMessage.success('模型导入成功');
        importDialogVisible.value = false;
      } catch (error) {
        console.error('模型导入失败:', error);
        ElMessage.error('模型导入失败，请重试');
      } finally {
        importing.value = false;
      }
    };
    
    // 查看模型详情
    const viewModelDetails = (model) => {
      currentModel.value = model;
      detailsVisible.value = true;
    };
    
    // 下载模型
    const downloadModel = async (model) => {
      try {
        loading.value = true;
        
        console.log('准备下载模型:', model);
        
        // 首先检查任务付款状态
        if (model.taskId) {
          console.log('模型关联任务ID:', model.taskId);
          
          const task = await TaskAPI.getById(model.taskId);
          console.log('获取关联任务信息:', task);
          
          // 如果任务存在且需要支付二期款项
          if (task && (task.payment_status === 'first_payment_completed' || task.payment_status === 'pending_second_payment')) {
            console.log('需要支付第二期款项，当前支付状态:', task.payment_status);
            // 显示二期付款确认对话框
            await showSecondPaymentConfirmation(task, model);
          } else {
            if (!task) {
              console.warn('未找到关联任务信息');
            } else {
              console.log('无需支付第二期款项，当前支付状态:', task.payment_status);
            }
            // 直接下载模型
            await processModelDownload(model);
          }
        } else {
          console.log('模型没有关联任务，直接下载');
          // 没有关联任务，直接下载
          await processModelDownload(model);
        }
      } catch (error) {
        console.error('下载模型失败:', error);
        ElMessage.error('下载模型失败: ' + (error.message || '请重试'));
      } finally {
        loading.value = false;
      }
    };
    
    // 显示第二期付款确认对话框
    const showSecondPaymentConfirmation = async (task, model) => {
      try {
        // 计算第二次付款金额和平台费用
        const secondPaymentRate = 100 - task.first_payment_rate;
        const secondPaymentAmount = (task.reward * secondPaymentRate) / 100;
        const platformFeeRate = task.platform_fee_rate || 10;
        const platformFeeAmount = (secondPaymentAmount * platformFeeRate) / 100;
        const totalAmount = secondPaymentAmount + platformFeeAmount;
        
        // 格式化金额
        const formatAmount = (amount) => {
          if (task.payment_currency === 'CNY') {
            return `¥${amount.toFixed(2)}`;
          }
          return `$${amount.toFixed(2)}`;
        };
        
        // 支付方式显示名称
        const paymentMethod = getPaymentMethodName(task.payment_method || 'balance');
        
        // 显示付款确认对话框
        const confirmResult = await ElMessageBox.confirm(
          `<div style="text-align:left;">
            <h3 style="margin-top:0;">下载模型需要支付剩余款项</h3>
            <p>任务ID: ${task.id}</p>
            <p>任务名称: ${task.name}</p>
            <p>当前支付状态: ${task.payment_status}</p>
            <p>初始付款比例: ${task.first_payment_rate}%</p>
            <p>剩余付款比例: ${secondPaymentRate}%</p>
            <p>剩余应付金额: ${formatAmount(secondPaymentAmount)}</p>
            <p>平台服务费(${platformFeeRate}%): ${formatAmount(platformFeeAmount)}</p>
            <p>总付款金额: ${formatAmount(totalAmount)}</p>
            <p>支付方式: ${paymentMethod}</p>
          </div>`,
          '二期付款确认',
          {
            confirmButtonText: '确认支付',
            cancelButtonText: '取消',
            dangerouslyUseHTMLString: true,
            type: 'warning'
          }
        );
        
        if (confirmResult === 'confirm') {
          // 处理二期付款
          await processSecondPayment(task, model);
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('显示付款确认失败:', error);
          ElMessage.error('显示付款确认失败: ' + (error.message || '请重试'));
        }
      }
    };
    
    // 获取支付方式显示名称
    const getPaymentMethodName = (methodKey) => {
      const methodNames = {
        'balance': '账户余额',
        'card': '信用卡',
        'alipay': '支付宝',
        'wechat': '微信支付',
        'paypal': 'PayPal',
        'wallet': '区块链钱包'
      };
      return methodNames[methodKey] || methodKey;
    };
    
    // 处理第二次付款
    const processSecondPayment = async (task, model) => {
      // 声明loading变量，确保可以在catch块中访问
      let loadingInstance;
      
      try {
        // 显示加载状态
        loadingInstance = ElLoading.service({
          lock: true,
          text: '正在处理付款...',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        // 处理区块链钱包支付
        if (task.payment_method === 'wallet') {
          // 计算第二次付款金额和平台费用
          const secondPaymentRate = 100 - task.first_payment_rate;
          const secondPaymentAmount = (task.reward * secondPaymentRate) / 100;
          const platformFeeRate = task.platform_fee_rate || 10;
          const platformFeeAmount = (task.reward * platformFeeRate) / 100;
          const totalAmount = secondPaymentAmount + platformFeeAmount;
          
          // 连接钱包并发起区块链支付
          await connectWallet(task.wallet_type || 'MetaMask');
          const txHash = await requestBlockchainPayment(totalAmount);
          
          if (!txHash) {
            loadingInstance.close();
            ElMessage.warning('区块链支付已取消');
            return;
          }
          
          console.log(`获取到区块链交易哈希: ${txHash}`);
          
          // 保存交易哈希到服务器
          await apiService.completeModelDownload(task.id, 1, { txHash });
          
          loadingInstance.close();
          ElMessage.success('区块链支付成功！');
          
          // 处理模型下载
          processModelDownload(model);
          return;
        }
        
        // 处理常规支付方式（非区块链）
        // 目前使用模拟用户ID=1，实际应该从认证获取
        const userId = 1;
        await apiService.completeModelDownload(task.id, userId);
        
        loadingInstance.close();
        ElMessage.success(`使用${getPaymentMethodName(task.payment_method || 'balance')}支付成功！`);
        
        // 处理模型下载
        processModelDownload(model);
      } catch (error) {
        // 确保关闭加载状态
        if (loadingInstance) {
          loadingInstance.close();
        }
        console.error('处理付款失败:', error);
        ElMessage.error('处理付款失败: ' + (error.message || '请重试'));
      }
    };
    
    // 连接区块链钱包
    const connectWallet = async (walletType) => {
      ElMessage.info(`正在连接 ${walletType} 钱包...`);
      
      // 这里实现与不同钱包的连接逻辑
      // 实际项目中需要使用相应的SDK
      try {
        // 模拟连接过程
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 模拟连接成功
        ElMessage.success(`${walletType} 钱包连接成功`);
        return true;
      } catch (error) {
        console.error('连接钱包失败:', error);
        ElMessage.error('连接钱包失败，请重试');
        throw new Error('钱包连接失败');
      }
    };
    
    // 请求区块链支付
    const requestBlockchainPayment = async (amount) => {
      try {
        ElMessage.info(`正在发起区块链支付，金额: ${amount} 代币`);
        
        // 模拟支付过程
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 生成随机交易哈希
        const txHash = `0x${Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('')}`;
        
        ElMessage.success('区块链交易已发送');
        return txHash;
      } catch (error) {
        console.error('区块链支付失败:', error);
        ElMessage.error('区块链支付失败，请重试');
        throw new Error('区块链支付失败');
      }
    };
    
    // 处理模型下载
    const processModelDownload = async (model) => {
      let loadingInstance;
      
      try {
        // 显示下载进度
        loadingInstance = ElLoading.service({
          lock: true,
          text: '正在准备下载模型...',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        
        ElMessage.info('正在准备下载模型...');
        console.log('处理模型下载:', model);
        
        // 增加下载次数
        try {
          await ModelAPI.incrementDownloads(model.id);
          console.log('成功增加模型下载次数');
        } catch (err) {
          console.warn('增加下载次数失败，但将继续下载:', err);
        }
        
        // 等待一小段时间，模拟下载准备过程
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 检查文件路径是否有效
        const downloadUrl = model.file_path || `../../../backend/models/model_${model.id}.pt`;
        console.log('模型下载URL:', downloadUrl);
        
        // 模拟文件下载
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `model_${model.id || Date.now()}.pt`;
        document.body.appendChild(link);
        
        // 关闭加载动画
        if (loadingInstance) {
          loadingInstance.close();
        }
        
        // 触发下载
        link.click();
        document.body.removeChild(link);
        
        ElMessage.success('模型下载成功！');
      } catch (error) {
        if (loadingInstance) {
          loadingInstance.close();
        }
        console.error('下载模型文件失败:', error);
        ElMessage.error('下载失败: ' + (error.message || '请重试'));
        throw error;
      }
    };
    
    // 发布模型
    const publishModel = async (model) => {
      try {
        let confirmMessage = `确定要发布模型 "${model.name} v${model.version}" 吗？发布后将对所有用户可见。`;
        
        // 如果是从训练任务创建的模型，添加额外提示
        if (model.fromTraining) {
          confirmMessage = `此模型是从训练任务"${model.taskName}"自动创建的。发布前，您可能需要检查模型质量并调整参数。确定要发布吗？`;
        }
        
        await ElMessageBox.confirm(
          confirmMessage,
          '发布确认',
          {
            confirmButtonText: '确定发布',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );
        
        // 这里应该调用API发布模型
        await new Promise(resolve => setTimeout(resolve, 500)); // 模拟API调用
        
        // 更新状态
        const modelIndex = models.value.findIndex(m => m.id === model.id);
        if (modelIndex !== -1) {
          models.value[modelIndex].status = 'published';
          // 更新发布时间
          models.value[modelIndex].updatedAt = new Date();
        }
        
        updateStatistics();
        
        const successMessage = model.fromTraining 
          ? `从训练任务创建的模型 ${model.name} v${model.version} 已成功发布` 
          : `模型 ${model.name} v${model.version} 已成功发布`;
        
        ElMessage.success(successMessage);
        
        // 如果在详情对话框中，更新当前模型
        if (currentModel.value && currentModel.value.id === model.id) {
          currentModel.value.status = 'published';
          currentModel.value.updatedAt = new Date();
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('发布模型失败:', error);
          ElMessage.error('发布模型失败，请重试');
        }
      }
    };
    
    // 归档模型
    const archiveModel = async (model) => {
      try {
        await ElMessageBox.confirm(
          `确定要归档模型 "${model.name} v${model.version}" 吗？归档后将不再对用户可见，但可以恢复。`,
          '归档确认',
          {
            confirmButtonText: '确定归档',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );
        
        // 这里应该调用API归档模型
        await new Promise(resolve => setTimeout(resolve, 500)); // 模拟API调用
        
        // 更新状态
        const modelIndex = models.value.findIndex(m => m.id === model.id);
        if (modelIndex !== -1) {
          models.value[modelIndex].status = 'archived';
        }
        
        updateStatistics();
        
        ElMessage.success(`模型 ${model.name} v${model.version} 已归档`);
      } catch (error) {
        if (error !== 'cancel') {
          console.error('归档模型失败:', error);
          ElMessage.error('归档模型失败，请重试');
        }
      }
    };
    
    // 恢复模型
    const restoreModel = async (model) => {
      try {
        await ElMessageBox.confirm(
          `确定要恢复模型 "${model.name} v${model.version}" 吗？恢复后将变为草稿状态。`,
          '恢复确认',
          {
            confirmButtonText: '确定恢复',
            cancelButtonText: '取消',
            type: 'info',
          }
        );
        
        // 这里应该调用API恢复模型
        await new Promise(resolve => setTimeout(resolve, 500)); // 模拟API调用
        
        // 更新状态
        const modelIndex = models.value.findIndex(m => m.id === model.id);
        if (modelIndex !== -1) {
          models.value[modelIndex].status = 'draft';
        }
        
        updateStatistics();
        
        ElMessage.success(`模型 ${model.name} v${model.version} 已恢复为草稿状态`);
      } catch (error) {
        if (error !== 'cancel') {
          console.error('恢复模型失败:', error);
          ElMessage.error('恢复模型失败，请重试');
        }
      }
    };
    
    // 删除模型
    const deleteModel = async (model) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除模型 "${model.name} v${model.version}" 吗？删除后无法恢复。`,
          '删除确认',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'danger',
          }
        );
        
        // 这里应该调用API删除模型
        await new Promise(resolve => setTimeout(resolve, 500)); // 模拟API调用
        
        // 从列表中移除
        models.value = models.value.filter(m => m.id !== model.id);
        
        updateStatistics();
        
        ElMessage.success(`模型 ${model.name} v${model.version} 已删除`);
        
        // 如果在详情对话框中，关闭对话框
        if (currentModel.value && currentModel.value.id === model.id) {
          detailsVisible.value = false;
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除模型失败:', error);
          ElMessage.error('删除模型失败，请重试');
        }
      }
    };
    
    // 刷新数据
    const refreshData = () => {
      loadModels();
    };
    
    // 更新统计逻辑
    const updateStatistics = () => {
      // 计算统计数据
      statistics.totalModels = models.value.length;
      statistics.activeModels = models.value.filter(m => m.status !== 'archived').length;
      statistics.publishedModels = models.value.filter(m => m.status === 'published').length;
      statistics.trainingModels = models.value.filter(m => m.fromTraining).length;
      statistics.modelDownloads = models.value.reduce((sum, model) => sum + (model.downloads || 0), 0);
    };
    
    // 添加方法：高亮显示与特定任务相关的模型
    const highlightTaskModels = (taskId) => {
      if (!taskId) return;
      
      // 查找相关模型
      const relatedModels = models.value.filter(model => 
        model.taskId && model.taskId.toString() === taskId.toString()
      );
      
      if (relatedModels.length > 0) {
        // 显示第一个相关模型的详情
        ElMessage.success(`已找到任务ID ${taskId} 相关的模型`);
        setTimeout(() => {
          viewModelDetails(relatedModels[0]);
        }, 500);
      } else {
        ElMessage.warning(`未找到任务ID ${taskId} 相关的模型，可能尚未创建`);
      }
    };
    
    // 添加方法：加载时显示最近的训练创建的模型
    const showLatestTrainingModel = () => {
      if (!models.value || models.value.length === 0) return;
      
      // 寻找最近的训练创建的模型
      const latestTrainingModel = models.value
        .filter(model => model.fromTraining)
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0];
        
      if (latestTrainingModel) {
        // 自动选择该模型并显示详情
        currentModel.value = latestTrainingModel;
        detailsVisible.value = true;
        
        // 可选：添加高亮效果
        highlightedTaskId.value = latestTrainingModel.taskId;
        
        // 显示提示消息
        ElMessage({
          message: `已自动显示最近从训练任务创建的模型：${latestTrainingModel.name}`,
          type: 'info'
        });
      }
    };
    
    // 在setup中添加方法
    const viewTrainingTask = (model) => {
      if (!model.taskId) {
        ElMessage.warning('没有找到关联的训练任务信息');
        return;
      }
      
      // 关闭当前打开的对话框
      if (detailsVisible.value) {
        detailsVisible.value = false;
      }
      
      // 导航到训练监控页面并传递任务ID
      router.push({
        path: '/server/training-monitor',
        query: { taskId: model.taskId }
      });
      
      ElMessage.info('正在跳转到训练监控页面...');
    };
    
    // 修改模型名称
    const editModelName = (model) => {
      renameForm.id = model.id;
      renameForm.oldName = model.name;
      renameForm.newName = model.name;
      renameDialogVisible.value = true;
    };
    
    // 提交重命名
    const submitRename = async () => {
      // 验证名称
      if (!renameForm.newName.trim()) {
        ElMessage.warning('请输入有效的模型名称');
        return;
      }
      
      // 如果名称没有变化
      if (renameForm.oldName === renameForm.newName) {
        renameDialogVisible.value = false;
        return;
      }
      
      renaming.value = true;
      try {
        // 调用API更新模型名称
        await ModelAPI.update(renameForm.id, { name: renameForm.newName.trim() });
        
        // 更新本地数据
        const modelIndex = models.value.findIndex(m => m.id === renameForm.id);
        if (modelIndex !== -1) {
          models.value[modelIndex].name = renameForm.newName.trim();
          
          // 更新当前模型详情中的名称（如果有打开详情对话框）
          if (currentModel.value && currentModel.value.id === renameForm.id) {
            currentModel.value.name = renameForm.newName.trim();
          }
        }
        
        ElMessage.success('模型名称修改成功');
        renameDialogVisible.value = false;
        
        // 强制刷新模型列表
        setTimeout(() => {
          loadModels();
        }, 500);
        
      } catch (error) {
        console.error('修改模型名称失败:', error);
        ElMessage.error(`修改名称失败: ${error.message || '未知错误'}`);
      } finally {
        renaming.value = false;
      }
    };
    
    onMounted(() => {
      loadModels();
    });
    
    return {
      loading,
      importing,
      models,
      typeFilter,
      statusFilter,
      sourceFilter,
      searchQuery,
      currentPage,
      pageSize,
      importDialogVisible,
      detailsVisible,
      currentModel,
      importForm,
      importFormRef,
      statistics,
      filteredModels,
      totalModels,
      formatDate,
      formatDateTime,
      formatPerformance,
      getPerformanceStatus,
      formatSize,
      getModelTypeTag,
      getModelTypeText,
      getStatusTagType,
      getStatusText,
      handleSizeChange,
      handleCurrentChange,
      handleFileChange,
      handleFileRemove,
      importModel,
      submitImport,
      viewModelDetails,
      downloadModel,
      showSecondPaymentConfirmation,
      getPaymentMethodName,
      processSecondPayment,
      connectWallet,
      requestBlockchainPayment,
      processModelDownload,
      publishModel,
      archiveModel,
      restoreModel,
      deleteModel,
      refreshData,
      isHighlightedModel: (model) => {
        return highlightedTaskId.value && 
               model.taskId && 
               model.taskId.toString() === highlightedTaskId.value.toString();
      },
      showLatestTrainingModel,
      viewTrainingTask,
      renameDialogVisible,
      renaming,
      renameForm,
      editModelName,
      submitRename,
    };
  }
};
</script>

<style scoped>
.model-management {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.page-actions {
  display: flex;
  gap: 12px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.filter-section {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 28px;
  align-items: center;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 240px;
}

.filter-label {
  font-weight: 500;
  color: #555;
  font-size: 15px;
  white-space: nowrap;
  min-width: 80px;
}

.search-box {
  width: 300px;
  margin-left: auto;
}

.model-name-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 4px 0;
}

.model-name {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 12px;
}

.model-title {
  font-weight: 500;
  font-size: 15px;
  color: #303133;
  line-height: 1.5;
  word-break: break-word;
}

.model-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 2px;
}

.model-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  color: #909399;
  font-size: 13px;
}

.model-meta .version {
  font-family: 'Courier New', monospace;
}

.model-action {
  display: flex;
  gap: 8px;
}

.model-performance {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.pagination {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

.model-details {
  padding: 16px;
}

.details-title {
  font-size: 16px;
  font-weight: 500;
  margin: 24px 0 16px 0;
  color: #303133;
}

.performance-charts {
  height: 300px;
  background-color: #f5f7fa;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
  color: #909399;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  font-style: italic;
}

.chart-placeholder i {
  font-size: 32px;
  color: #c0c4cc;
}

@media (max-width: 1200px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .filter-section {
    gap: 16px;
    padding: 12px;
  }
  
  .filter-group {
    min-width: 220px;
  }
}

@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }
  
  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .filter-group {
    width: 100%;
  }
  
  .search-box {
    width: 100%;
    margin-left: 0;
  }
}

/* 高亮效果 */
.highlighted-model {
  background-color: rgba(255, 230, 156, 0.15);
  padding: 6px 10px;
  border-radius: 4px;
  border-left: 3px solid #E6A23C;
  margin: -6px -10px;
}
</style> 