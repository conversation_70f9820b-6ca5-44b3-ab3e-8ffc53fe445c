/**
 * 迁移: add_payment_fields
 * 创建于: 2023-05-25T00:00:00.000Z
 * 描述: 添加支付相关字段到任务表并创建交易表
 */

const fs = require('fs');
const path = require('path');

/**
 * 向上迁移 - 应用更改
 * @param {Function} query 数据库查询函数
 */
exports.up = async function(query) {
  try {
    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, 'add_payment_fields.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // 将SQL拆分为单独的语句执行
    const statements = sqlContent.split(';').filter(stmt => stmt.trim());
    
    console.log(`开始执行支付系统字段迁移脚本，共 ${statements.length} 条语句`);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        console.log(`执行SQL语句 ${i+1}/${statements.length}: ${statement.substring(0, 80)}...`);
        await query(statement);
      }
    }
    
    console.log('支付系统字段迁移完成');
  } catch (error) {
    console.error('执行支付系统字段迁移失败:', error);
    throw error;
  }
};

/**
 * 向下迁移 - 撤销更改
 * @param {Function} query 数据库查询函数
 */
exports.down = async function(query) {
  try {
    // 删除添加的字段
    await query(`
      ALTER TABLE tasks
      DROP COLUMN IF EXISTS reward,
      DROP COLUMN IF EXISTS reward_distribution,
      DROP COLUMN IF EXISTS payment_currency,
      DROP COLUMN IF EXISTS installment_payment,
      DROP COLUMN IF EXISTS first_payment_rate,
      DROP COLUMN IF EXISTS platform_fee_rate,
      DROP COLUMN IF EXISTS payment_status,
      DROP COLUMN IF EXISTS payment_method,
      DROP COLUMN IF EXISTS wallet_type,
      DROP COLUMN IF EXISTS blockchain_tx_hash,
      DROP COLUMN IF EXISTS blockchain_second_payment_tx
    `);
    
    // 删除交易表
    await query('DROP TABLE IF EXISTS transactions');
    
    console.log('支付系统字段回滚完成');
  } catch (error) {
    console.error('支付系统字段回滚失败:', error);
    throw error;
  }
}; 