// 通知模块 - 管理全局通知

const state = {
  notifications: []  // 存储所有通知
};

const getters = {
  // 获取通知列表，按照创建时间降序排序
  getNotifications: (state) => {
    return [...state.notifications].sort((a, b) => b.timestamp - a.timestamp);
  }
};

const mutations = {
  // 添加新通知
  ADD_NOTIFICATION(state, notification) {
    // 生成一个唯一ID和添加时间戳
    const newNotification = {
      id: Date.now() + Math.random().toString(36).substring(2, 10),
      timestamp: Date.now(),
      ...notification
    };
    state.notifications.push(newNotification);
  },

  // 移除指定通知
  REMOVE_NOTIFICATION(state, id) {
    const index = state.notifications.findIndex(n => n.id === id);
    if (index !== -1) {
      state.notifications.splice(index, 1);
    }
  },

  // 清空所有通知
  CLEAR_NOTIFICATIONS(state) {
    state.notifications = [];
  }
};

const actions = {
  // 添加成功通知
  addSuccessNotification({ commit }, { title, message, timeout = 5000 }) {
    const notification = { type: 'success', title, message, timeout };
    commit('ADD_NOTIFICATION', notification);
    if (timeout > 0) {
      setTimeout(() => {
        commit('REMOVE_NOTIFICATION', notification.id);
      }, timeout);
    }
  },

  // 添加错误通知
  addErrorNotification({ commit }, { title, message, timeout = 8000 }) {
    const notification = { type: 'error', title, message, timeout };
    commit('ADD_NOTIFICATION', notification);
    if (timeout > 0) {
      setTimeout(() => {
        commit('REMOVE_NOTIFICATION', notification.id);
      }, timeout);
    }
  },

  // 添加警告通知
  addWarningNotification({ commit }, { title, message, timeout = 6000 }) {
    const notification = { type: 'warning', title, message, timeout };
    commit('ADD_NOTIFICATION', notification);
    if (timeout > 0) {
      setTimeout(() => {
        commit('REMOVE_NOTIFICATION', notification.id);
      }, timeout);
    }
  },

  // 添加信息通知
  addInfoNotification({ commit }, { title, message, timeout = 4000 }) {
    const notification = { type: 'info', title, message, timeout };
    commit('ADD_NOTIFICATION', notification);
    if (timeout > 0) {
      setTimeout(() => {
        commit('REMOVE_NOTIFICATION', notification.id);
      }, timeout);
    }
  },

  // 移除通知
  removeNotification({ commit }, id) {
    commit('REMOVE_NOTIFICATION', id);
  },

  // 清空所有通知
  clearNotifications({ commit }) {
    commit('CLEAR_NOTIFICATIONS');
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}; 