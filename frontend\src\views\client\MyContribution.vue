<!-- 客户端"我的贡献"页面 -->
<template>
  <div class="my-contribution">
    <div class="page-header">
      <h1>我的贡献</h1>
      <div class="page-actions">
        <el-button type="primary" @click="refreshData">
          <i class="fas fa-sync"></i> 刷新数据
        </el-button>
        <el-button type="success" @click="exportContribution">
          <i class="fas fa-file-export"></i> 导出数据
        </el-button>
      </div>
    </div>
    
    <Breadcrumb />
    
    <!-- 贡献统计卡片 -->
    <div class="stats-cards">
      <el-card class="stat-card total-tasks">
        <div class="stat-value">{{ statistics.totalTasks }}</div>
        <div class="stat-label">参与任务总数</div>
      </el-card>
      <el-card class="stat-card data-contribution">
        <div class="stat-value">{{ statistics.dataContribution }}</div>
        <div class="stat-label">数据贡献总量</div>
      </el-card>
      <el-card class="stat-card compute-contribution">
        <div class="stat-value">{{ statistics.computeHours }}小时</div>
        <div class="stat-label">算力贡献总时长</div>
      </el-card>
      <el-card class="stat-card total-rewards">
        <div class="stat-value">{{ statistics.totalRewards }}</div>
        <div class="stat-label">累计获得奖励</div>
      </el-card>
    </div>
    
    <!-- 贡献趋势图 -->
    <el-card class="chart-card">
      <template #header>
        <div class="card-header">
          <span>贡献趋势</span>
          <el-radio-group v-model="chartTimeRange" size="small" @change="updateCharts">
            <el-radio-button value="week">最近一周</el-radio-button>
            <el-radio-button value="month">最近一个月</el-radio-button>
            <el-radio-button value="year">最近一年</el-radio-button>
          </el-radio-group>
        </div>
      </template>
      <div class="chart-container">
        <div ref="contributionChart" class="chart"></div>
      </div>
    </el-card>
    
    <!-- 贡献详情表格 -->
    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>贡献详情</span>
          <div class="filter-actions">
            <el-select v-model="taskFilter" placeholder="按任务筛选" clearable @change="filterContributions">
              <el-option 
                v-for="task in taskOptions" 
                :key="task.value" 
                :label="task.label" 
                :value="task.value" 
              />
            </el-select>
            <el-select v-model="typeFilter" placeholder="按贡献类型筛选" clearable @change="filterContributions">
              <el-option label="数据贡献" value="data" />
              <el-option label="算力贡献" value="compute" />
              <el-option label="全部贡献" value="all" />
            </el-select>
          </div>
        </div>
      </template>
      
      <el-table
        :data="filteredContributions"
        style="width: 100%"
        v-loading="loading"
        row-key="id"
      >
        <el-table-column prop="taskName" label="任务名称" min-width="180">
          <template #default="scope">
            <div class="task-name">{{ scope.row.taskName }}</div>
            <div class="task-time">{{ formatDate(scope.row.date) }}</div>
          </template>
        </el-table-column>
        
        <el-table-column prop="type" label="贡献类型" width="120">
          <template #default="scope">
            <el-tag :type="getContributionTypeTag(scope.row.type)">
              {{ getContributionTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="value" label="贡献量" width="150">
          <template #default="scope">
            <span>
              {{ formatContributionValue(scope.row.value, scope.row.type) }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="quality" label="质量评分" width="150">
          <template #default="scope">
            <el-rate 
              v-model="scope.row.quality" 
              disabled 
              show-score 
              text-color="#ff9900"
              score-template="{value}" 
            />
          </template>
        </el-table-column>
        
        <el-table-column prop="reward" label="获得奖励" width="120">
          <template #default="scope">
            <span class="reward">{{ scope.row.reward }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="viewContributionDetail(scope.row)" type="primary" plain>详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalContributions"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 贡献详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="贡献详情"
      width="600px"
      v-if="currentContribution"
    >
      <div class="contribution-dialog-content">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="任务名称">{{ currentContribution.taskName }}</el-descriptions-item>
          <el-descriptions-item label="贡献日期">{{ formatDate(currentContribution.date) }}</el-descriptions-item>
          <el-descriptions-item label="贡献类型">
            <el-tag :type="getContributionTypeTag(currentContribution.type)">
              {{ getContributionTypeText(currentContribution.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="贡献量">
            {{ formatContributionValue(currentContribution.value, currentContribution.type) }}
          </el-descriptions-item>
          <el-descriptions-item label="质量评分">
            <el-rate 
              v-model="currentContribution.quality" 
              disabled 
              show-score 
              text-color="#ff9900"
              score-template="{value}" 
            />
          </el-descriptions-item>
          <el-descriptions-item label="获得奖励">{{ currentContribution.reward }}</el-descriptions-item>
          <el-descriptions-item label="贡献描述">{{ currentContribution.description }}</el-descriptions-item>
          <el-descriptions-item v-if="currentContribution.type === 'data'" label="数据分布">
            <el-progress 
              :percentage="currentContribution.dataDistribution.labeledPercentage" 
              :format="percentageFormat"
              :stroke-width="15"
              status="success"
            >
              <span>已标注数据</span>
            </el-progress>
            <el-progress 
              :percentage="currentContribution.dataDistribution.unlabeledPercentage" 
              :format="percentageFormat"
              :stroke-width="15"
              status="warning"
              style="margin-top: 10px;"
            >
              <span>未标注数据</span>
            </el-progress>
          </el-descriptions-item>
          <el-descriptions-item v-if="currentContribution.type === 'compute'" label="计算资源">
            <ul class="resource-list">
              <li><strong>CPU:</strong> {{ currentContribution.computeResources.cpu }}</li>
              <li><strong>内存:</strong> {{ currentContribution.computeResources.memory }}</li>
              <li><strong>GPU:</strong> {{ currentContribution.computeResources.gpu || '无' }}</li>
              <li><strong>带宽:</strong> {{ currentContribution.computeResources.bandwidth }}</li>
            </ul>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import Breadcrumb from '@/components/Breadcrumb.vue';
import * as echarts from 'echarts';

// 页面状态
const loading = ref(false);
const contributions = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const taskFilter = ref('');
const typeFilter = ref('');
const chartTimeRange = ref('month');

// 统计信息
const statistics = reactive({
  totalTasks: 0,
  dataContribution: 0,
  computeHours: 0,
  totalRewards: 0
});

// 贡献详情对话框
const detailDialogVisible = ref(false);
const currentContribution = ref(null);

// ECharts 实例
let contributionChart = null;
const contributionChartRef = ref(null);

// 任务选项
const taskOptions = ref([]);

// 计算属性：过滤后的贡献
const filteredContributions = computed(() => {
  let result = contributions.value;
  
  // 任务过滤
  if (taskFilter.value) {
    result = result.filter(contrib => contrib.taskId === taskFilter.value);
  }
  
  // 类型过滤
  if (typeFilter.value && typeFilter.value !== 'all') {
    result = result.filter(contrib => contrib.type === typeFilter.value);
  }
  
  return result.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value);
});

// 计算属性：总贡献数
const totalContributions = computed(() => {
  let result = contributions.value;
  
  // 任务过滤
  if (taskFilter.value) {
    result = result.filter(contrib => contrib.taskId === taskFilter.value);
  }
  
  // 类型过滤
  if (typeFilter.value && typeFilter.value !== 'all') {
    result = result.filter(contrib => contrib.type === typeFilter.value);
  }
  
  return result.length;
});

// 生命周期钩子
onMounted(() => {
  loadContributions();
});

// 加载贡献数据
async function loadContributions() {
  loading.value = true;
  
  try {
    // 实际项目中这里应该是API调用
    // const response = await api.getMyContributions();
    // contributions.value = response.data;
    
    // 模拟数据
    setTimeout(() => {
      contributions.value = generateMockContributions();
      updateStatistics();
      generateTaskOptions();
      loading.value = false;
      
      // 初始化图表
      nextTick(() => {
        initCharts();
      });
    }, 1000);
  } catch (error) {
    console.error('加载贡献数据失败:', error);
    ElMessage.error('加载贡献数据失败，请重试');
    loading.value = false;
  }
}

// 生成模拟贡献数据
function generateMockContributions() {
  const taskNames = ['MNIST手写数字识别', '医疗影像分类', '金融交易欺诈检测', '自然语言情感分析', '产品推荐系统'];
  const types = ['data', 'compute'];
  
  return Array.from({ length: 50 }, (_, i) => {
    const taskId = Math.floor(Math.random() * 5) + 1;
    const taskName = taskNames[taskId - 1];
    const type = types[Math.floor(Math.random() * types.length)];
    const value = type === 'data' 
      ? Math.floor(Math.random() * 5000) + 1000  // 数据样本量
      : Math.floor(Math.random() * 50) + 5;      // 计算时长
    const quality = (Math.random() * 2 + 3).toFixed(1); // 3-5分
    const reward = Math.floor(Math.random() * 500) + 100;
    
    return {
      id: i + 1,
      taskId: taskId.toString(),
      taskName,
      type,
      value,
      quality: parseFloat(quality),
      reward,
      date: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000),
      description: type === 'data' 
        ? `为${taskName}提供${value}个高质量训练样本` 
        : `为${taskName}提供${value}小时的算力支持`,
      dataDistribution: type === 'data' ? {
        labeledPercentage: Math.floor(Math.random() * 30) + 70, // 70%-100%
        unlabeledPercentage: Math.floor(Math.random() * 30)     // 0%-30%
      } : null,
      computeResources: type === 'compute' ? {
        cpu: `${Math.floor(Math.random() * 8) + 4}核`,
        memory: `${Math.floor(Math.random() * 16) + 8}GB`,
        gpu: Math.random() > 0.5 ? `NVIDIA RTX ${3060 + Math.floor(Math.random() * 3) * 10}` : null,
        bandwidth: `${Math.floor(Math.random() * 500) + 100}Mbps`
      } : null
    };
  });
}

// 生成任务选项
function generateTaskOptions() {
  const taskMap = new Map();
  
  contributions.value.forEach(contrib => {
    if (!taskMap.has(contrib.taskId)) {
      taskMap.set(contrib.taskId, {
        value: contrib.taskId,
        label: contrib.taskName
      });
    }
  });
  
  taskOptions.value = Array.from(taskMap.values());
}

// 更新统计信息
function updateStatistics() {
  // 计算参与的不同任务数量
  const taskIds = new Set(contributions.value.map(contrib => contrib.taskId));
  statistics.totalTasks = taskIds.size;
  
  // 计算数据贡献总量
  statistics.dataContribution = contributions.value
    .filter(contrib => contrib.type === 'data')
    .reduce((sum, contrib) => sum + contrib.value, 0)
    .toLocaleString();
  
  // 计算算力贡献总时长
  statistics.computeHours = contributions.value
    .filter(contrib => contrib.type === 'compute')
    .reduce((sum, contrib) => sum + contrib.value, 0);
  
  // 计算总奖励
  statistics.totalRewards = contributions.value
    .reduce((sum, contrib) => sum + contrib.reward, 0)
    .toLocaleString();
}

// 初始化图表
function initCharts() {
  if (contributionChart) {
    contributionChart.dispose();
  }
  
  contributionChart = echarts.init(document.querySelector('.chart'));
  updateCharts();
}

// 更新图表数据
function updateCharts() {
  if (!contributionChart) return;
  
  // 根据时间范围获取数据
  const { labels, dataValues, computeValues } = getChartData();
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['数据贡献', '算力贡献']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: labels
    },
    yAxis: [
      {
        type: 'value',
        name: '数据量',
        position: 'left',
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '计算时长(小时)',
        position: 'right',
        axisLabel: {
          formatter: '{value}'
        }
      }
    ],
    series: [
      {
        name: '数据贡献',
        type: 'bar',
        data: dataValues,
        yAxisIndex: 0,
        itemStyle: {
          color: '#91cc75'
        }
      },
      {
        name: '算力贡献',
        type: 'line',
        data: computeValues,
        yAxisIndex: 1,
        itemStyle: {
          color: '#ee6666'
        }
      }
    ]
  };
  
  contributionChart.setOption(option);
}

// 获取图表数据
function getChartData() {
  let days, format;
  
  // 根据选择的时间范围确定展示数据
  if (chartTimeRange.value === 'week') {
    days = 7;
    format = 'MM-DD';
  } else if (chartTimeRange.value === 'month') {
    days = 30;
    format = 'MM-DD';
  } else {
    days = 365;
    format = 'YYYY-MM';
  }
  
  // 在实际项目中这里会调用API获取相应时间范围的数据
  // 这里使用模拟数据
  const labels = [];
  const dataValues = [];
  const computeValues = [];
  
  // 创建日期标签
  const today = new Date();
  const dateMap = new Map();
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    
    // 格式化日期作为标签
    const label = formatDateForChart(date, format);
    labels.push(label);
    
    // 初始化数据
    dateMap.set(label, { dataValue: 0, computeValue: 0 });
  }
  
  // 统计数据
  contributions.value.forEach(contrib => {
    const date = new Date(contrib.date);
    if (date >= new Date(today.getTime() - days * 24 * 60 * 60 * 1000)) {
      const label = formatDateForChart(date, format);
      
      if (dateMap.has(label)) {
        const data = dateMap.get(label);
        
        if (contrib.type === 'data') {
          data.dataValue += contrib.value;
        } else if (contrib.type === 'compute') {
          data.computeValue += contrib.value;
        }
      }
    }
  });
  
  // 转换为数组
  labels.forEach(label => {
    const data = dateMap.get(label);
    dataValues.push(data.dataValue);
    computeValues.push(data.computeValue);
  });
  
  return { labels, dataValues, computeValues };
}

// 格式化日期用于图表
function formatDateForChart(date, format) {
  if (format === 'MM-DD') {
    return `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  } else {
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
  }
}

// 格式化日期
function formatDate(date) {
  if (!date) return '';
  const d = new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
}

// 格式化贡献类型
function getContributionTypeText(type) {
  return type === 'data' ? '数据贡献' : '算力贡献';
}

// 获取贡献类型标签类型
function getContributionTypeTag(type) {
  return type === 'data' ? 'success' : 'danger';
}

// 格式化贡献值
function formatContributionValue(value, type) {
  if (type === 'data') {
    return `${value.toLocaleString()} 样本`;
  } else {
    return `${value} 小时`;
  }
}

// 格式化进度条百分比
function percentageFormat(percentage) {
  return `${percentage}%`;
}

// 过滤贡献
function filterContributions() {
  currentPage.value = 1;
}

// 分页处理
function handleSizeChange(val) {
  pageSize.value = val;
}

function handleCurrentChange(val) {
  currentPage.value = val;
}

// 查看贡献详情
function viewContributionDetail(contribution) {
  currentContribution.value = contribution;
  detailDialogVisible.value = true;
}

// 刷新数据
function refreshData() {
  loadContributions();
}

// 导出贡献数据
function exportContribution() {
  ElMessage.success('贡献数据导出成功');
  // 实际项目中这里应该是下载CSV或Excel文件
}
</script>

<style scoped>
.my-contribution {
  padding: 20px;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.page-actions {
  display: flex;
  gap: 10px;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 24px;
}

@media (max-width: 1200px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }
}

.stat-card {
  border-radius: 8px;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.total-tasks .stat-value {
  color: #409EFF;
}

.data-contribution .stat-value {
  color: #67C23A;
}

.compute-contribution .stat-value {
  color: #E6A23C;
}

.total-rewards .stat-value {
  color: #F56C6C;
}

/* 图表卡片 */
.chart-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 400px;
}

.chart {
  width: 100%;
  height: 100%;
}

/* 详情卡片 */
.detail-card {
  margin-bottom: 24px;
}

.filter-actions {
  display: flex;
  gap: 10px;
}

.task-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.task-time {
  font-size: 12px;
  color: #909399;
}

.reward {
  color: #F56C6C;
  font-weight: bold;
}

/* 分页 */
.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 贡献详情对话框 */
.contribution-dialog-content {
  max-height: 60vh;
  overflow-y: auto;
}

.resource-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.resource-list li {
  margin-bottom: 8px;
}

@media (max-width: 768px) {
  .filter-actions {
    flex-direction: column;
    gap: 10px;
  }
}
</style> 