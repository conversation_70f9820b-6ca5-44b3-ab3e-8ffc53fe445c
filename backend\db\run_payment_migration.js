/**
 * 支付系统数据库更新脚本
 * 直接执行SQL文件内容
 */
const fs = require('fs');
const path = require('path');
const { pool, query, ensureDatabase } = require('./config');

/**
 * 执行SQL文件
 * @param {string} filePath SQL文件路径
 */
async function executeSqlFile(filePath) {
  try {
    console.log(`准备执行SQL文件: ${filePath}`);
    
    // 读取SQL文件内容
    const sqlContent = fs.readFileSync(filePath, 'utf8');
    
    // 对于MySQL，我们需要处理delimiter语句和多条SQL
    // 将SQL内容分割成多个语句并执行
    
    // 检查是否有delimiter语句
    if (sqlContent.includes('delimiter //')) {
      console.log('检测到delimiter语句，将作为整体执行');
      
      try {
        // 作为整体执行，保留所有分隔符
        await pool.query(sqlContent);
        console.log('SQL文件作为整体执行成功');
      } catch (error) {
        console.error('执行SQL文件失败:', error);
        
        // 如果作为整体执行失败，尝试拆分后逐条执行
        console.log('尝试拆分SQL语句后逐条执行...');
        
        // 先移除delimiter部分
        const cleanedSql = sqlContent
          .replace(/delimiter \/\/|delimiter ;/g, '')
          .replace(/\/\//g, ';');
        
        // 按分号拆分
        const statements = cleanedSql.split(';')
          .map(stmt => stmt.trim())
          .filter(stmt => stmt.length > 0);
        
        console.log(`拆分出 ${statements.length} 条SQL语句`);
        
        // 逐条执行
        for (let i = 0; i < statements.length; i++) {
          try {
            console.log(`执行第 ${i+1}/${statements.length} 条语句`);
            await pool.query(statements[i]);
            console.log(`第 ${i+1} 条语句执行成功`);
          } catch (error) {
            console.error(`第 ${i+1} 条语句执行失败:`, error);
            console.error('语句内容:', statements[i]);
            // 继续执行下一条语句
          }
        }
      }
    } else {
      // 按分号分割SQL语句，逐条执行
      const statements = sqlContent.split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0);
      
      console.log(`共有 ${statements.length} 条SQL语句需要执行`);
      
      for (let i = 0; i < statements.length; i++) {
        try {
          console.log(`执行第 ${i+1}/${statements.length} 条语句`);
          await pool.query(statements[i]);
          console.log(`第 ${i+1} 条语句执行成功`);
        } catch (error) {
          console.error(`第 ${i+1} 条语句执行失败:`, error);
          // 继续执行下一条语句
        }
      }
    }
    
    console.log('SQL文件执行完成');
    return true;
  } catch (error) {
    console.error('执行SQL文件出错:', error);
    return false;
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('开始支付系统数据库迁移');
    
    // 确保数据库存在
    await ensureDatabase();
    console.log('数据库连接成功');
    
    // 获取SQL文件路径
    const sqlFilePath = path.join(__dirname, 'migrations', 'add_payment_fields.sql');
    
    // 执行SQL文件
    const result = await executeSqlFile(sqlFilePath);
    
    if (result) {
      console.log('支付系统数据库迁移成功完成');
    } else {
      console.error('支付系统数据库迁移失败');
    }
    
  } catch (error) {
    console.error('支付系统数据库迁移出错:', error);
  } finally {
    // 关闭连接池
    try {
      await pool.end();
      console.log('数据库连接已关闭');
    } catch (err) {
      console.error('关闭数据库连接失败:', err);
    }
    
    process.exit();
  }
}

// 执行主函数
main(); 