<template>
  <div class="contribution-component">
    <div class="component-header">
      <h3>贡献统计</h3>
    </div>
    
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
    </div>
    
    <div v-else class="contribution-stats">
      <div class="stat-item">
        <div class="stat-label">参与轮次</div>
        <div class="stat-value">{{ stats.roundsParticipated || 0 }}</div>
      </div>
      
      <div class="stat-item">
        <div class="stat-label">数据贡献</div>
        <div class="stat-value">{{ formatData(stats.dataContributed) }}</div>
      </div>
      
      <div class="stat-item">
        <div class="stat-label">平均准确率</div>
        <div class="stat-value">{{ formatPercentage(stats.avgAccuracy) }}</div>
      </div>
      
      <div class="stat-item">
        <div class="stat-label">累计奖励</div>
        <div class="stat-value reward">{{ formatReward(stats.totalReward) }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ContributionStats',
  props: {
    stats: {
      type: Object,
      default: () => ({
        roundsParticipated: 0,
        dataContributed: 0,
        avgAccuracy: 0,
        totalReward: 0
      })
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    // 格式化百分比
    const formatPercentage = (value) => {
      if (typeof value !== 'number') return '0.00%';
      return (value * 100).toFixed(2) + '%';
    };
    
    // 格式化数据量
    const formatData = (value) => {
      if (!value || typeof value !== 'number') return '0';
      
      if (value < 1000) {
        return value.toString();
      } else if (value < 1000000) {
        return (value / 1000).toFixed(1) + 'K';
      } else {
        return (value / 1000000).toFixed(1) + 'M';
      }
    };
    
    // 格式化奖励
    const formatReward = (value) => {
      if (!value || typeof value !== 'number') return '0.00';
      return value.toFixed(2);
    };
    
    return {
      formatPercentage,
      formatData,
      formatReward
    };
  }
};
</script>

<style scoped>
.contribution-component {
  background: white;
  border-radius: 16px;
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.component-header {
  margin-bottom: 24px;
}

.component-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
  font-weight: 600;
}

.contribution-stats {
  flex-grow: 1;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.stat-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.stat-label {
  font-size: 14px;
  color: #5f6368;
  margin-bottom: 12px;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.stat-value.reward {
  color: #ff6d00;
  background: linear-gradient(120deg, #ff6d00, #ff9800);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.loading-container {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1976D2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .contribution-stats {
    grid-template-columns: 1fr;
  }
}
</style> 