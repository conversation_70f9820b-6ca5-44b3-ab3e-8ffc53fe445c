{"version": 3, "sources": ["../../ecpair/src/esm/index.js", "../../ecpair/src/esm/ecpair.js", "../../ecpair/src/esm/networks.js", "../../ecpair/src/esm/types.js", "../../valibot/dist/index.js", "../../wif/src/esm/index.js", "../../bs58check/src/esm/index.js", "../../@noble/hashes/src/sha256.ts", "../../@noble/hashes/src/_md.ts", "../../@noble/hashes/src/_assert.ts", "../../@noble/hashes/src/utils.ts", "../../@noble/hashes/src/crypto.ts", "../../bs58check/src/esm/base.js", "../../bs58/src/esm/index.js", "../../base-x/src/esm/index.js", "../../ecpair/src/esm/testecc.js", "../../uint8array-tools/src/mjs/browser.js"], "sourcesContent": ["export { ECPairFactory as default, ECPairFactory, networks } from './ecpair';\n", "import * as networks from './networks';\nimport * as types from './types';\nimport * as wif from 'wif';\nimport { testEcc } from './testecc';\nexport { networks };\nimport * as v from 'valibot';\nimport * as tools from 'uint8array-tools';\nconst ECPairOptionsSchema = v.optional(\n  v.object({\n    compressed: v.optional(v.boolean()),\n    network: v.optional(types.NetworkSchema),\n    // https://github.com/fabian-hiller/valibot/issues/243#issuecomment-2182514063\n    rng: v.optional(\n      v.pipe(\n        v.instance(Function),\n        v.transform((func) => {\n          return (arg) => {\n            const parsedArg = v.parse(v.optional(v.number()), arg);\n            const returnedValue = func(parsedArg);\n            const parsedReturn = v.parse(v.instance(Uint8Array), returnedValue);\n            return parsedReturn;\n          };\n        }),\n      ),\n    ),\n  }),\n);\nconst toXOnly = (pubKey) =>\n  pubKey.length === 32 ? pubKey : pubKey.subarray(1, 33);\nexport function ECPairFactory(ecc) {\n  testEcc(ecc);\n  function isPoint(maybePoint) {\n    return ecc.isPoint(maybePoint);\n  }\n  function fromPrivateKey(buffer, options) {\n    v.parse(types.Buffer256Bit, buffer);\n    if (!ecc.isPrivate(buffer))\n      throw new TypeError('Private key not in range [1, n)');\n    v.parse(ECPairOptionsSchema, options);\n    return new ECPair(buffer, undefined, options);\n  }\n  function fromPublicKey(buffer, options) {\n    if (!ecc.isPoint(buffer)) {\n      throw new Error('Point not on the curve');\n    }\n    v.parse(ECPairOptionsSchema, options);\n    return new ECPair(undefined, buffer, options);\n  }\n  function fromWIF(wifString, network) {\n    const decoded = wif.decode(wifString);\n    const version = decoded.version;\n    // list of networks?\n    if (Array.isArray(network)) {\n      network = network\n        .filter((x) => {\n          return version === x.wif;\n        })\n        .pop();\n      if (!network) throw new Error('Unknown network version');\n      // otherwise, assume a network object (or default to bitcoin)\n    } else {\n      network = network || networks.bitcoin;\n      if (version !== network.wif) throw new Error('Invalid network version');\n    }\n    return fromPrivateKey(decoded.privateKey, {\n      compressed: decoded.compressed,\n      network: network,\n    });\n  }\n  function makeRandom(options) {\n    v.parse(ECPairOptionsSchema, options);\n    if (options === undefined) options = {};\n    const rng =\n      options.rng || ((size) => crypto.getRandomValues(new Uint8Array(size)));\n    let d;\n    do {\n      d = rng(32);\n      v.parse(types.Buffer256Bit, d);\n    } while (!ecc.isPrivate(d));\n    return fromPrivateKey(d, options);\n  }\n  class ECPair {\n    __D;\n    __Q;\n    compressed;\n    network;\n    lowR;\n    constructor(__D, __Q, options) {\n      this.__D = __D;\n      this.__Q = __Q;\n      this.lowR = false;\n      if (options === undefined) options = {};\n      this.compressed =\n        options.compressed === undefined ? true : options.compressed;\n      this.network = options.network || networks.bitcoin;\n      if (__Q !== undefined) this.__Q = ecc.pointCompress(__Q, this.compressed);\n    }\n    get privateKey() {\n      return this.__D;\n    }\n    get publicKey() {\n      if (!this.__Q) {\n        // It is not possible for both `__Q` and `__D` to be `undefined` at the same time.\n        // The factory methods guard for this.\n        const p = ecc.pointFromScalar(this.__D, this.compressed);\n        // It is not possible for `p` to be null.\n        // `fromPrivateKey()` checks that `__D` is a valid scalar.\n        this.__Q = p;\n      }\n      return this.__Q;\n    }\n    toWIF() {\n      if (!this.__D) throw new Error('Missing private key');\n      return wif.encode({\n        compressed: this.compressed,\n        privateKey: this.__D,\n        version: this.network.wif,\n      });\n    }\n    tweak(t) {\n      if (this.privateKey) return this.tweakFromPrivateKey(t);\n      return this.tweakFromPublicKey(t);\n    }\n    sign(hash, lowR) {\n      if (!this.__D) throw new Error('Missing private key');\n      if (lowR === undefined) lowR = this.lowR;\n      if (lowR === false) {\n        return ecc.sign(hash, this.__D);\n      } else {\n        let sig = ecc.sign(hash, this.__D);\n        const extraData = new Uint8Array(32);\n        let counter = 0;\n        // if first try is lowR, skip the loop\n        // for second try and on, add extra entropy counting up\n        while (sig[0] > 0x7f) {\n          counter++;\n          tools.writeUInt32(extraData, 0, counter, 'LE');\n          sig = ecc.sign(hash, this.__D, extraData);\n        }\n        return sig;\n      }\n    }\n    signSchnorr(hash) {\n      if (!this.privateKey) throw new Error('Missing private key');\n      if (!ecc.signSchnorr)\n        throw new Error('signSchnorr not supported by ecc library');\n      return ecc.signSchnorr(hash, this.privateKey);\n    }\n    verify(hash, signature) {\n      return ecc.verify(hash, this.publicKey, signature);\n    }\n    verifySchnorr(hash, signature) {\n      if (!ecc.verifySchnorr)\n        throw new Error('verifySchnorr not supported by ecc library');\n      return ecc.verifySchnorr(hash, this.publicKey.subarray(1, 33), signature);\n    }\n    tweakFromPublicKey(t) {\n      const xOnlyPubKey = toXOnly(this.publicKey);\n      const tweakedPublicKey = ecc.xOnlyPointAddTweak(xOnlyPubKey, t);\n      if (!tweakedPublicKey || tweakedPublicKey.xOnlyPubkey === null)\n        throw new Error('Cannot tweak public key!');\n      const parityByte = Uint8Array.from([\n        tweakedPublicKey.parity === 0 ? 0x02 : 0x03,\n      ]);\n      return fromPublicKey(\n        tools.concat([parityByte, tweakedPublicKey.xOnlyPubkey]),\n        {\n          network: this.network,\n          compressed: this.compressed,\n        },\n      );\n    }\n    tweakFromPrivateKey(t) {\n      const hasOddY =\n        this.publicKey[0] === 3 ||\n        (this.publicKey[0] === 4 && (this.publicKey[64] & 1) === 1);\n      const privateKey = hasOddY\n        ? ecc.privateNegate(this.privateKey)\n        : this.privateKey;\n      const tweakedPrivateKey = ecc.privateAdd(privateKey, t);\n      if (!tweakedPrivateKey) throw new Error('Invalid tweaked private key!');\n      return fromPrivateKey(tweakedPrivateKey, {\n        network: this.network,\n        compressed: this.compressed,\n      });\n    }\n  }\n  return {\n    isPoint,\n    fromPrivateKey,\n    fromPublicKey,\n    fromWIF,\n    makeRandom,\n  };\n}\n", "export const bitcoin = {\n  messagePrefix: '\\x18Bitcoin Signed Message:\\n',\n  bech32: 'bc',\n  bip32: {\n    public: 0x0488b21e,\n    private: 0x0488ade4,\n  },\n  pubKeyHash: 0x00,\n  scriptHash: 0x05,\n  wif: 0x80,\n};\nexport const testnet = {\n  messagePrefix: '\\x18Bitcoin Signed Message:\\n',\n  bech32: 'tb',\n  bip32: {\n    public: 0x043587cf,\n    private: 0x04358394,\n  },\n  pubKeyHash: 0x6f,\n  scriptHash: 0xc4,\n  wif: 0xef,\n};\n", "import * as v from 'valibot';\nconst Uint32Schema = v.pipe(\n  v.number(),\n  v.integer(),\n  v.minValue(0),\n  v.maxValue(0xffffffff),\n);\nconst Uint8Schema = v.pipe(\n  v.number(),\n  v.integer(),\n  v.minValue(0),\n  v.maxValue(0xff),\n);\nexport const NetworkSchema = v.object({\n  messagePrefix: v.union([v.string(), v.instance(Uint8Array)]),\n  bech32: v.string(),\n  bip32: v.object({\n    public: Uint32Schema,\n    private: Uint32Schema,\n  }),\n  pubKeyHash: Uint8Schema,\n  scriptHash: Uint8Schema,\n  wif: Uint8Schema,\n});\nexport const Buffer256Bit = v.pipe(v.instance(Uint8Array), v.length(32));\n", "// src/actions/await/awaitAsync.ts\nfunction awaitAsync() {\n  return {\n    kind: \"transformation\",\n    type: \"await\",\n    reference: awaitAsync,\n    async: true,\n    async _run(dataset) {\n      dataset.value = await dataset.value;\n      return dataset;\n    }\n  };\n}\n\n// src/regex.ts\nvar BASE64_REGEX = /^(?:[\\da-z+/]{4})*(?:[\\da-z+/]{2}==|[\\da-z+/]{3}=)?$/iu;\nvar BIC_REGEX = /^[A-Z]{6}(?!00)[\\dA-Z]{2}(?:[\\dA-Z]{3})?$/u;\nvar CUID2_REGEX = /^[a-z][\\da-z]*$/u;\nvar DECIMAL_REGEX = /^\\d+$/u;\nvar EMAIL_REGEX = /^[\\w+-]+(?:\\.[\\w+-]+)*@[\\da-z]+(?:[.-][\\da-z]+)*\\.[a-z]{2,}$/iu;\nvar EMOJI_REGEX = (\n  // eslint-disable-next-line redos-detector/no-unsafe-regex, regexp/no-dupe-disjunctions -- false positives\n  /^(?:[\\u{1F1E6}-\\u{1F1FF}]{2}|\\u{1F3F4}[\\u{E0061}-\\u{E007A}]{2}[\\u{E0030}-\\u{E0039}\\u{E0061}-\\u{E007A}]{1,3}\\u{E007F}|(?:\\p{Emoji}\\uFE0F\\u20E3?|\\p{Emoji_Modifier_Base}\\p{Emoji_Modifier}?|\\p{Emoji_Presentation})(?:\\u200D(?:\\p{Emoji}\\uFE0F\\u20E3?|\\p{Emoji_Modifier_Base}\\p{Emoji_Modifier}?|\\p{Emoji_Presentation}))*)+$/u\n);\nvar HEXADECIMAL_REGEX = /^(?:0[hx])?[\\da-f]+$/iu;\nvar HEX_COLOR_REGEX = /^#(?:[\\da-f]{3,4}|[\\da-f]{6}|[\\da-f]{8})$/iu;\nvar IMEI_REGEX = /^\\d{15}$|^\\d{2}-\\d{6}-\\d{6}-\\d$/u;\nvar IPV4_REGEX = (\n  // eslint-disable-next-line redos-detector/no-unsafe-regex -- false positive\n  /^(?:(?:[1-9]|1\\d|2[0-4])?\\d|25[0-5])(?:\\.(?:(?:[1-9]|1\\d|2[0-4])?\\d|25[0-5])){3}$/u\n);\nvar IPV6_REGEX = /^(?:(?:[\\da-f]{1,4}:){7}[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){1,7}:|(?:[\\da-f]{1,4}:){1,6}:[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){1,5}(?::[\\da-f]{1,4}){1,2}|(?:[\\da-f]{1,4}:){1,4}(?::[\\da-f]{1,4}){1,3}|(?:[\\da-f]{1,4}:){1,3}(?::[\\da-f]{1,4}){1,4}|(?:[\\da-f]{1,4}:){1,2}(?::[\\da-f]{1,4}){1,5}|[\\da-f]{1,4}:(?::[\\da-f]{1,4}){1,6}|:(?:(?::[\\da-f]{1,4}){1,7}|:)|fe80:(?::[\\da-f]{0,4}){0,4}%[\\da-z]+|::(?:f{4}(?::0{1,4})?:)?(?:(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)\\.){3}(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)|(?:[\\da-f]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)\\.){3}(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d))$/iu;\nvar IP_REGEX = /^(?:(?:[1-9]|1\\d|2[0-4])?\\d|25[0-5])(?:\\.(?:(?:[1-9]|1\\d|2[0-4])?\\d|25[0-5])){3}$|^(?:(?:[\\da-f]{1,4}:){7}[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){1,7}:|(?:[\\da-f]{1,4}:){1,6}:[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){1,5}(?::[\\da-f]{1,4}){1,2}|(?:[\\da-f]{1,4}:){1,4}(?::[\\da-f]{1,4}){1,3}|(?:[\\da-f]{1,4}:){1,3}(?::[\\da-f]{1,4}){1,4}|(?:[\\da-f]{1,4}:){1,2}(?::[\\da-f]{1,4}){1,5}|[\\da-f]{1,4}:(?::[\\da-f]{1,4}){1,6}|:(?:(?::[\\da-f]{1,4}){1,7}|:)|fe80:(?::[\\da-f]{0,4}){0,4}%[\\da-z]+|::(?:f{4}(?::0{1,4})?:)?(?:(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)\\.){3}(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)|(?:[\\da-f]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)\\.){3}(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d))$/iu;\nvar ISO_DATE_REGEX = /^\\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\\d|0[1-9]|3[01])$/u;\nvar ISO_DATE_TIME_REGEX = /^\\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\\d|0[1-9]|3[01])T(?:0\\d|1\\d|2[0-3]):[0-5]\\d$/u;\nvar ISO_TIME_REGEX = /^(?:0\\d|1\\d|2[0-3]):[0-5]\\d$/u;\nvar ISO_TIME_SECOND_REGEX = /^(?:0\\d|1\\d|2[0-3])(?::[0-5]\\d){2}$/u;\nvar ISO_TIMESTAMP_REGEX = /^\\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\\d|0[1-9]|3[01])T(?:0\\d|1\\d|2[0-3])(?::[0-5]\\d){2}(?:\\.\\d{1,9})?(?:Z|[+-](?:0\\d|1\\d|2[0-3])(?::?[0-5]\\d)?)$/u;\nvar ISO_WEEK_REGEX = /^\\d{4}-W(?:0[1-9]|[1-4]\\d|5[0-3])$/u;\nvar MAC48_REGEX = /^(?:[\\da-f]{2}:){5}[\\da-f]{2}$|^(?:[\\da-f]{2}-){5}[\\da-f]{2}$|^(?:[\\da-f]{4}\\.){2}[\\da-f]{4}$/iu;\nvar MAC64_REGEX = /^(?:[\\da-f]{2}:){7}[\\da-f]{2}$|^(?:[\\da-f]{2}-){7}[\\da-f]{2}$|^(?:[\\da-f]{4}\\.){3}[\\da-f]{4}$|^(?:[\\da-f]{4}:){3}[\\da-f]{4}$/iu;\nvar MAC_REGEX = /^(?:[\\da-f]{2}:){5}[\\da-f]{2}$|^(?:[\\da-f]{2}-){5}[\\da-f]{2}$|^(?:[\\da-f]{4}\\.){2}[\\da-f]{4}$|^(?:[\\da-f]{2}:){7}[\\da-f]{2}$|^(?:[\\da-f]{2}-){7}[\\da-f]{2}$|^(?:[\\da-f]{4}\\.){3}[\\da-f]{4}$|^(?:[\\da-f]{4}:){3}[\\da-f]{4}$/iu;\nvar OCTAL_REGEX = /^(?:0o)?[0-7]+$/iu;\nvar ULID_REGEX = /^[\\da-hjkmnp-tv-z]{26}$/iu;\nvar UUID_REGEX = /^[\\da-f]{8}(?:-[\\da-f]{4}){3}-[\\da-f]{12}$/iu;\n\n// src/storages/globalConfig/globalConfig.ts\nvar store;\nfunction setGlobalConfig(config2) {\n  store = { ...store, ...config2 };\n}\nfunction getGlobalConfig(config2) {\n  return {\n    lang: config2?.lang ?? store?.lang,\n    message: config2?.message,\n    abortEarly: config2?.abortEarly ?? store?.abortEarly,\n    abortPipeEarly: config2?.abortPipeEarly ?? store?.abortPipeEarly\n  };\n}\nfunction deleteGlobalConfig() {\n  store = void 0;\n}\n\n// src/storages/globalMessage/globalMessage.ts\nvar store2;\nfunction setGlobalMessage(message, lang) {\n  if (!store2) store2 = /* @__PURE__ */ new Map();\n  store2.set(lang, message);\n}\nfunction getGlobalMessage(lang) {\n  return store2?.get(lang);\n}\nfunction deleteGlobalMessage(lang) {\n  store2?.delete(lang);\n}\n\n// src/storages/schemaMessage/schemaMessage.ts\nvar store3;\nfunction setSchemaMessage(message, lang) {\n  if (!store3) store3 = /* @__PURE__ */ new Map();\n  store3.set(lang, message);\n}\nfunction getSchemaMessage(lang) {\n  return store3?.get(lang);\n}\nfunction deleteSchemaMessage(lang) {\n  store3?.delete(lang);\n}\n\n// src/storages/specificMessage/specificMessage.ts\nvar store4;\nfunction setSpecificMessage(reference, message, lang) {\n  if (!store4) store4 = /* @__PURE__ */ new Map();\n  if (!store4.get(reference)) store4.set(reference, /* @__PURE__ */ new Map());\n  store4.get(reference).set(lang, message);\n}\nfunction getSpecificMessage(reference, lang) {\n  return store4?.get(reference)?.get(lang);\n}\nfunction deleteSpecificMessage(reference, lang) {\n  store4?.get(reference)?.delete(lang);\n}\n\n// src/utils/_stringify/_stringify.ts\nfunction _stringify(input) {\n  const type = typeof input;\n  if (type === \"string\") {\n    return `\"${input}\"`;\n  }\n  if (type === \"number\" || type === \"bigint\" || type === \"boolean\") {\n    return `${input}`;\n  }\n  if (type === \"object\" || type === \"function\") {\n    return (input && Object.getPrototypeOf(input)?.constructor?.name) ?? \"null\";\n  }\n  return type;\n}\n\n// src/utils/_addIssue/_addIssue.ts\nfunction _addIssue(context, label, dataset, config2, other) {\n  const input = other && \"input\" in other ? other.input : dataset.value;\n  const expected = other?.expected ?? context.expects ?? null;\n  const received = other?.received ?? _stringify(input);\n  const issue = {\n    kind: context.kind,\n    type: context.type,\n    input,\n    expected,\n    received,\n    message: `Invalid ${label}: ${expected ? `Expected ${expected} but r` : \"R\"}eceived ${received}`,\n    // @ts-expect-error\n    requirement: context.requirement,\n    path: other?.path,\n    issues: other?.issues,\n    lang: config2.lang,\n    abortEarly: config2.abortEarly,\n    abortPipeEarly: config2.abortPipeEarly\n  };\n  const isSchema = context.kind === \"schema\";\n  const message = other?.message ?? // @ts-expect-error\n  context.message ?? getSpecificMessage(context.reference, issue.lang) ?? (isSchema ? getSchemaMessage(issue.lang) : null) ?? config2.message ?? getGlobalMessage(issue.lang);\n  if (message) {\n    issue.message = typeof message === \"function\" ? message(issue) : message;\n  }\n  if (isSchema) {\n    dataset.typed = false;\n  }\n  if (dataset.issues) {\n    dataset.issues.push(issue);\n  } else {\n    dataset.issues = [issue];\n  }\n}\n\n// src/utils/_isLuhnAlgo/_isLuhnAlgo.ts\nvar NON_DIGIT_REGEX = /\\D/gu;\nfunction _isLuhnAlgo(input) {\n  const number2 = input.replace(NON_DIGIT_REGEX, \"\");\n  let length2 = number2.length;\n  let bit = 1;\n  let sum = 0;\n  while (length2) {\n    const value2 = +number2[--length2];\n    bit ^= 1;\n    sum += bit ? [0, 2, 4, 6, 8, 1, 3, 5, 7, 9][value2] : value2;\n  }\n  return sum % 10 === 0;\n}\n\n// src/utils/_isValidObjectKey/_isValidObjectKey.ts\nfunction _isValidObjectKey(object2, key) {\n  return Object.hasOwn(object2, key) && key !== \"__proto__\" && key !== \"prototype\" && key !== \"constructor\";\n}\n\n// src/utils/entriesFromList/entriesFromList.ts\nfunction entriesFromList(list, schema) {\n  const entries = {};\n  for (const key of list) {\n    entries[key] = schema;\n  }\n  return entries;\n}\n\n// src/utils/getDotPath/getDotPath.ts\nfunction getDotPath(issue) {\n  if (issue.path) {\n    let key = \"\";\n    for (const item of issue.path) {\n      if (typeof item.key === \"string\" || typeof item.key === \"number\") {\n        if (key) {\n          key += `.${item.key}`;\n        } else {\n          key += item.key;\n        }\n      } else {\n        return null;\n      }\n    }\n    return key;\n  }\n  return null;\n}\n\n// src/utils/isOfKind/isOfKind.ts\nfunction isOfKind(kind, object2) {\n  return object2.kind === kind;\n}\n\n// src/utils/isOfType/isOfType.ts\nfunction isOfType(type, object2) {\n  return object2.type === type;\n}\n\n// src/utils/isValiError/isValiError.ts\nfunction isValiError(error) {\n  return error instanceof ValiError;\n}\n\n// src/utils/ValiError/ValiError.ts\nvar ValiError = class extends Error {\n  /**\n   * The error issues.\n   */\n  issues;\n  /**\n   * Creates a Valibot error with useful information.\n   *\n   * @param issues The error issues.\n   */\n  constructor(issues) {\n    super(issues[0].message);\n    this.name = \"ValiError\";\n    this.issues = issues;\n  }\n};\n\n// src/actions/base64/base64.ts\nfunction base64(message) {\n  return {\n    kind: \"validation\",\n    type: \"base64\",\n    reference: base64,\n    async: false,\n    expects: null,\n    requirement: BASE64_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"Base64\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/bic/bic.ts\nfunction bic(message) {\n  return {\n    kind: \"validation\",\n    type: \"bic\",\n    reference: bic,\n    async: false,\n    expects: null,\n    requirement: BIC_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"BIC\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/brand/brand.ts\nfunction brand(name) {\n  return {\n    kind: \"transformation\",\n    type: \"brand\",\n    reference: brand,\n    async: false,\n    name,\n    _run(dataset) {\n      return dataset;\n    }\n  };\n}\n\n// src/actions/bytes/bytes.ts\nfunction bytes(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"bytes\",\n    reference: bytes,\n    async: false,\n    expects: `${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed) {\n        const length2 = new TextEncoder().encode(dataset.value).length;\n        if (length2 !== this.requirement) {\n          _addIssue(this, \"bytes\", dataset, config2, {\n            received: `${length2}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/check/check.ts\nfunction check(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"check\",\n    reference: check,\n    async: false,\n    expects: null,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"input\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/check/checkAsync.ts\nfunction checkAsync(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"check\",\n    reference: checkAsync,\n    async: true,\n    expects: null,\n    requirement,\n    message,\n    async _run(dataset, config2) {\n      if (dataset.typed && !await this.requirement(dataset.value)) {\n        _addIssue(this, \"input\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/checkItems/checkItems.ts\nfunction checkItems(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"check_items\",\n    reference: checkItems,\n    async: false,\n    expects: null,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed) {\n        for (let index = 0; index < dataset.value.length; index++) {\n          const item = dataset.value[index];\n          if (!this.requirement(item, index, dataset.value)) {\n            _addIssue(this, \"item\", dataset, config2, {\n              input: item,\n              path: [\n                {\n                  type: \"array\",\n                  origin: \"value\",\n                  input: dataset.value,\n                  key: index,\n                  value: item\n                }\n              ]\n            });\n          }\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/creditCard/creditCard.ts\nvar CREDIT_CARD_REGEX = /^(?:\\d{14,19}|\\d{4}(?: \\d{3,6}){2,4}|\\d{4}(?:-\\d{3,6}){2,4})$/u;\nvar SANITIZE_REGEX = /[- ]/gu;\nvar PROVIDER_REGEX_LIST = [\n  // American Express\n  /^3[47]\\d{13}$/u,\n  // Diners Club\n  /^3(?:0[0-5]|[68]\\d)\\d{11,13}$/u,\n  // Discover\n  /^6(?:011|5\\d{2})\\d{12,15}$/u,\n  // JCB\n  /^(?:2131|1800|35\\d{3})\\d{11}$/u,\n  // Mastercard\n  /^5[1-5]\\d{2}|(?:222\\d|22[3-9]\\d|2[3-6]\\d{2}|27[01]\\d|2720)\\d{12}$/u,\n  // UnionPay\n  /^(?:6[27]\\d{14,17}|81\\d{14,17})$/u,\n  // Visa\n  /^4\\d{12}(?:\\d{3,6})?$/u\n];\nfunction creditCard(message) {\n  return {\n    kind: \"validation\",\n    type: \"credit_card\",\n    reference: creditCard,\n    async: false,\n    expects: null,\n    requirement(input) {\n      let sanitized;\n      return CREDIT_CARD_REGEX.test(input) && // Remove any hyphens and blanks\n      (sanitized = input.replace(SANITIZE_REGEX, \"\")) && // Check if it matches a provider\n      PROVIDER_REGEX_LIST.some((regex2) => regex2.test(sanitized)) && // Check if passes luhn algorithm\n      _isLuhnAlgo(sanitized);\n    },\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"credit card\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/cuid2/cuid2.ts\nfunction cuid2(message) {\n  return {\n    kind: \"validation\",\n    type: \"cuid2\",\n    reference: cuid2,\n    async: false,\n    expects: null,\n    requirement: CUID2_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"Cuid2\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/decimal/decimal.ts\nfunction decimal(message) {\n  return {\n    kind: \"validation\",\n    type: \"decimal\",\n    reference: decimal,\n    async: false,\n    expects: null,\n    requirement: DECIMAL_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"decimal\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/description/description.ts\nfunction description(description_) {\n  return {\n    kind: \"metadata\",\n    type: \"description\",\n    reference: description,\n    description: description_\n  };\n}\n\n// src/actions/email/email.ts\nfunction email(message) {\n  return {\n    kind: \"validation\",\n    type: \"email\",\n    reference: email,\n    expects: null,\n    async: false,\n    requirement: EMAIL_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"email\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/emoji/emoji.ts\nfunction emoji(message) {\n  return {\n    kind: \"validation\",\n    type: \"emoji\",\n    reference: emoji,\n    async: false,\n    expects: null,\n    requirement: EMOJI_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"emoji\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/empty/empty.ts\nfunction empty(message) {\n  return {\n    kind: \"validation\",\n    type: \"empty\",\n    reference: empty,\n    async: false,\n    expects: \"0\",\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.length > 0) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/endsWith/endsWith.ts\nfunction endsWith(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"ends_with\",\n    reference: endsWith,\n    async: false,\n    expects: `\"${requirement}\"`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !dataset.value.endsWith(this.requirement)) {\n        _addIssue(this, \"end\", dataset, config2, {\n          received: `\"${dataset.value.slice(-this.requirement.length)}\"`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/everyItem/everyItem.ts\nfunction everyItem(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"every_item\",\n    reference: everyItem,\n    async: false,\n    expects: null,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !dataset.value.every(this.requirement)) {\n        _addIssue(this, \"item\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/excludes/excludes.ts\nfunction excludes(requirement, message) {\n  const received = _stringify(requirement);\n  return {\n    kind: \"validation\",\n    type: \"excludes\",\n    reference: excludes,\n    async: false,\n    expects: `!${received}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.includes(this.requirement)) {\n        _addIssue(this, \"content\", dataset, config2, { received });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/filterItems/filterItems.ts\nfunction filterItems(operation) {\n  return {\n    kind: \"transformation\",\n    type: \"filter_items\",\n    reference: filterItems,\n    async: false,\n    operation,\n    _run(dataset) {\n      dataset.value = dataset.value.filter(this.operation);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/findItem/findItem.ts\nfunction findItem(operation) {\n  return {\n    kind: \"transformation\",\n    type: \"find_item\",\n    reference: findItem,\n    async: false,\n    operation,\n    _run(dataset) {\n      dataset.value = dataset.value.find(this.operation);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/finite/finite.ts\nfunction finite(message) {\n  return {\n    kind: \"validation\",\n    type: \"finite\",\n    reference: finite,\n    async: false,\n    expects: null,\n    requirement: Number.isFinite,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"finite\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/hash/hash.ts\nvar HASH_LENGTHS = {\n  md4: 32,\n  md5: 32,\n  sha1: 40,\n  sha256: 64,\n  sha384: 96,\n  sha512: 128,\n  ripemd128: 32,\n  ripemd160: 40,\n  tiger128: 32,\n  tiger160: 40,\n  tiger192: 48,\n  crc32: 8,\n  crc32b: 8,\n  adler32: 8\n};\nfunction hash(types, message) {\n  return {\n    kind: \"validation\",\n    type: \"hash\",\n    reference: hash,\n    expects: null,\n    async: false,\n    requirement: RegExp(\n      types.map((type) => `^[a-f0-9]{${HASH_LENGTHS[type]}}$`).join(\"|\"),\n      \"iu\"\n    ),\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"hash\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/hexadecimal/hexadecimal.ts\nfunction hexadecimal(message) {\n  return {\n    kind: \"validation\",\n    type: \"hexadecimal\",\n    reference: hexadecimal,\n    async: false,\n    expects: null,\n    requirement: HEXADECIMAL_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"hexadecimal\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/hexColor/hexColor.ts\nfunction hexColor(message) {\n  return {\n    kind: \"validation\",\n    type: \"hex_color\",\n    reference: hexColor,\n    async: false,\n    expects: null,\n    requirement: HEX_COLOR_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"hex color\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/imei/imei.ts\nfunction imei(message) {\n  return {\n    kind: \"validation\",\n    type: \"imei\",\n    reference: imei,\n    async: false,\n    expects: null,\n    requirement(input) {\n      return IMEI_REGEX.test(input) && _isLuhnAlgo(input);\n    },\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"IMEI\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/includes/includes.ts\nfunction includes(requirement, message) {\n  const expects = _stringify(requirement);\n  return {\n    kind: \"validation\",\n    type: \"includes\",\n    reference: includes,\n    async: false,\n    expects,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !dataset.value.includes(this.requirement)) {\n        _addIssue(this, \"content\", dataset, config2, {\n          received: `!${expects}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/integer/integer.ts\nfunction integer(message) {\n  return {\n    kind: \"validation\",\n    type: \"integer\",\n    reference: integer,\n    async: false,\n    expects: null,\n    requirement: Number.isInteger,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"integer\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/ip/ip.ts\nfunction ip(message) {\n  return {\n    kind: \"validation\",\n    type: \"ip\",\n    reference: ip,\n    async: false,\n    expects: null,\n    requirement: IP_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"IP\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/ipv4/ipv4.ts\nfunction ipv4(message) {\n  return {\n    kind: \"validation\",\n    type: \"ipv4\",\n    reference: ipv4,\n    async: false,\n    expects: null,\n    requirement: IPV4_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"IPv4\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/ipv6/ipv6.ts\nfunction ipv6(message) {\n  return {\n    kind: \"validation\",\n    type: \"ipv6\",\n    reference: ipv6,\n    async: false,\n    expects: null,\n    requirement: IPV6_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"IPv6\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoDate/isoDate.ts\nfunction isoDate(message) {\n  return {\n    kind: \"validation\",\n    type: \"iso_date\",\n    reference: isoDate,\n    async: false,\n    expects: null,\n    requirement: ISO_DATE_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"date\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoDateTime/isoDateTime.ts\nfunction isoDateTime(message) {\n  return {\n    kind: \"validation\",\n    type: \"iso_date_time\",\n    reference: isoDateTime,\n    async: false,\n    expects: null,\n    requirement: ISO_DATE_TIME_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"date-time\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoTime/isoTime.ts\nfunction isoTime(message) {\n  return {\n    kind: \"validation\",\n    type: \"iso_time\",\n    reference: isoTime,\n    async: false,\n    expects: null,\n    requirement: ISO_TIME_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"time\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoTimeSecond/isoTimeSecond.ts\nfunction isoTimeSecond(message) {\n  return {\n    kind: \"validation\",\n    type: \"iso_time_second\",\n    reference: isoTimeSecond,\n    async: false,\n    expects: null,\n    requirement: ISO_TIME_SECOND_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"time-second\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoTimestamp/isoTimestamp.ts\nfunction isoTimestamp(message) {\n  return {\n    kind: \"validation\",\n    type: \"iso_timestamp\",\n    reference: isoTimestamp,\n    async: false,\n    expects: null,\n    requirement: ISO_TIMESTAMP_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"timestamp\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoWeek/isoWeek.ts\nfunction isoWeek(message) {\n  return {\n    kind: \"validation\",\n    type: \"iso_week\",\n    reference: isoWeek,\n    async: false,\n    expects: null,\n    requirement: ISO_WEEK_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"week\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/length/length.ts\nfunction length(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"length\",\n    reference: length,\n    async: false,\n    expects: `${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.length !== this.requirement) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/mac/mac.ts\nfunction mac(message) {\n  return {\n    kind: \"validation\",\n    type: \"mac\",\n    reference: mac,\n    async: false,\n    expects: null,\n    requirement: MAC_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"MAC\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/mac48/mac48.ts\nfunction mac48(message) {\n  return {\n    kind: \"validation\",\n    type: \"mac48\",\n    reference: mac48,\n    async: false,\n    expects: null,\n    requirement: MAC48_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"48-bit MAC\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/mac64/mac64.ts\nfunction mac64(message) {\n  return {\n    kind: \"validation\",\n    type: \"mac64\",\n    reference: mac64,\n    async: false,\n    expects: null,\n    requirement: MAC64_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"64-bit MAC\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/mapItems/mapItems.ts\nfunction mapItems(operation) {\n  return {\n    kind: \"transformation\",\n    type: \"map_items\",\n    reference: mapItems,\n    async: false,\n    operation,\n    _run(dataset) {\n      dataset.value = dataset.value.map(this.operation);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/maxBytes/maxBytes.ts\nfunction maxBytes(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"max_bytes\",\n    reference: maxBytes,\n    async: false,\n    expects: `<=${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed) {\n        const length2 = new TextEncoder().encode(dataset.value).length;\n        if (length2 > this.requirement) {\n          _addIssue(this, \"bytes\", dataset, config2, {\n            received: `${length2}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/maxLength/maxLength.ts\nfunction maxLength(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"max_length\",\n    reference: maxLength,\n    async: false,\n    expects: `<=${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.length > this.requirement) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/maxSize/maxSize.ts\nfunction maxSize(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"max_size\",\n    reference: maxSize,\n    async: false,\n    expects: `<=${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.size > this.requirement) {\n        _addIssue(this, \"size\", dataset, config2, {\n          received: `${dataset.value.size}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/maxValue/maxValue.ts\nfunction maxValue(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"max_value\",\n    reference: maxValue,\n    async: false,\n    expects: `<=${requirement instanceof Date ? requirement.toJSON() : _stringify(requirement)}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value > this.requirement) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/mimeType/mimeType.ts\nfunction mimeType(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"mime_type\",\n    reference: mimeType,\n    async: false,\n    expects: requirement.map((option) => `\"${option}\"`).join(\" | \") || \"never\",\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.includes(dataset.value.type)) {\n        _addIssue(this, \"MIME type\", dataset, config2, {\n          received: `\"${dataset.value.type}\"`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/minBytes/minBytes.ts\nfunction minBytes(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"min_bytes\",\n    reference: minBytes,\n    async: false,\n    expects: `>=${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed) {\n        const length2 = new TextEncoder().encode(dataset.value).length;\n        if (length2 < this.requirement) {\n          _addIssue(this, \"bytes\", dataset, config2, {\n            received: `${length2}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/minLength/minLength.ts\nfunction minLength(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"min_length\",\n    reference: minLength,\n    async: false,\n    expects: `>=${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.length < this.requirement) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/minSize/minSize.ts\nfunction minSize(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"min_size\",\n    reference: minSize,\n    async: false,\n    expects: `>=${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.size < this.requirement) {\n        _addIssue(this, \"size\", dataset, config2, {\n          received: `${dataset.value.size}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/minValue/minValue.ts\nfunction minValue(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"min_value\",\n    reference: minValue,\n    async: false,\n    expects: `>=${requirement instanceof Date ? requirement.toJSON() : _stringify(requirement)}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value < this.requirement) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/multipleOf/multipleOf.ts\nfunction multipleOf(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"multiple_of\",\n    reference: multipleOf,\n    async: false,\n    expects: `%${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value % this.requirement !== 0) {\n        _addIssue(this, \"multiple\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/nonEmpty/nonEmpty.ts\nfunction nonEmpty(message) {\n  return {\n    kind: \"validation\",\n    type: \"non_empty\",\n    reference: nonEmpty,\n    async: false,\n    expects: \"!0\",\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.length === 0) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: \"0\"\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/normalize/normalize.ts\nfunction normalize(form) {\n  return {\n    kind: \"transformation\",\n    type: \"normalize\",\n    reference: normalize,\n    async: false,\n    form,\n    _run(dataset) {\n      dataset.value = dataset.value.normalize(this.form);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notBytes/notBytes.ts\nfunction notBytes(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"not_bytes\",\n    reference: notBytes,\n    async: false,\n    expects: `!${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed) {\n        const length2 = new TextEncoder().encode(dataset.value).length;\n        if (length2 === this.requirement) {\n          _addIssue(this, \"bytes\", dataset, config2, {\n            received: `${length2}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notLength/notLength.ts\nfunction notLength(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"not_length\",\n    reference: notLength,\n    async: false,\n    expects: `!${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.length === this.requirement) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notSize/notSize.ts\nfunction notSize(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"not_size\",\n    reference: notSize,\n    async: false,\n    expects: `!${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.size === this.requirement) {\n        _addIssue(this, \"size\", dataset, config2, {\n          received: `${dataset.value.size}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notValue/notValue.ts\nfunction notValue(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"not_value\",\n    reference: notValue,\n    async: false,\n    expects: requirement instanceof Date ? `!${requirement.toJSON()}` : `!${_stringify(requirement)}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && this.requirement <= dataset.value && this.requirement >= dataset.value) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/octal/octal.ts\nfunction octal(message) {\n  return {\n    kind: \"validation\",\n    type: \"octal\",\n    reference: octal,\n    async: false,\n    expects: null,\n    requirement: OCTAL_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"octal\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/partialCheck/utils/_isPartiallyTyped/_isPartiallyTyped.ts\nfunction _isPartiallyTyped(dataset, pathList) {\n  if (dataset.issues) {\n    for (const path of pathList) {\n      for (const issue of dataset.issues) {\n        let typed = false;\n        const bound = Math.min(path.length, issue.path?.length ?? 0);\n        for (let index = 0; index < bound; index++) {\n          if (path[index] !== issue.path[index].key) {\n            typed = true;\n            break;\n          }\n        }\n        if (!typed) {\n          return false;\n        }\n      }\n    }\n  }\n  return true;\n}\n\n// src/actions/partialCheck/partialCheck.ts\nfunction partialCheck(pathList, requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"partial_check\",\n    reference: partialCheck,\n    async: false,\n    expects: null,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (_isPartiallyTyped(dataset, pathList) && // @ts-expect-error\n      !this.requirement(dataset.value)) {\n        _addIssue(this, \"input\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/partialCheck/partialCheckAsync.ts\nfunction partialCheckAsync(pathList, requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"partial_check\",\n    reference: partialCheckAsync,\n    async: true,\n    expects: null,\n    requirement,\n    message,\n    async _run(dataset, config2) {\n      if (_isPartiallyTyped(dataset, pathList) && // @ts-expect-error\n      !await this.requirement(dataset.value)) {\n        _addIssue(this, \"input\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/rawCheck/rawCheck.ts\nfunction rawCheck(action) {\n  return {\n    kind: \"validation\",\n    type: \"raw_check\",\n    reference: rawCheck,\n    async: false,\n    expects: null,\n    _run(dataset, config2) {\n      action({\n        dataset,\n        config: config2,\n        addIssue: (info) => _addIssue(this, info?.label ?? \"input\", dataset, config2, info)\n      });\n      return dataset;\n    }\n  };\n}\n\n// src/actions/rawCheck/rawCheckAsync.ts\nfunction rawCheckAsync(action) {\n  return {\n    kind: \"validation\",\n    type: \"raw_check\",\n    reference: rawCheckAsync,\n    async: true,\n    expects: null,\n    async _run(dataset, config2) {\n      await action({\n        dataset,\n        config: config2,\n        addIssue: (info) => _addIssue(this, info?.label ?? \"input\", dataset, config2, info)\n      });\n      return dataset;\n    }\n  };\n}\n\n// src/actions/rawTransform/rawTransform.ts\nfunction rawTransform(action) {\n  return {\n    kind: \"transformation\",\n    type: \"raw_transform\",\n    reference: rawTransform,\n    async: false,\n    _run(dataset, config2) {\n      const output = action({\n        dataset,\n        config: config2,\n        addIssue: (info) => _addIssue(this, info?.label ?? \"input\", dataset, config2, info),\n        NEVER: null\n      });\n      if (dataset.issues) {\n        dataset.typed = false;\n      } else {\n        dataset.value = output;\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/rawTransform/rawTransformAsync.ts\nfunction rawTransformAsync(action) {\n  return {\n    kind: \"transformation\",\n    type: \"raw_transform\",\n    reference: rawTransformAsync,\n    async: true,\n    async _run(dataset, config2) {\n      const output = await action({\n        dataset,\n        config: config2,\n        addIssue: (info) => _addIssue(this, info?.label ?? \"input\", dataset, config2, info),\n        NEVER: null\n      });\n      if (dataset.issues) {\n        dataset.typed = false;\n      } else {\n        dataset.value = output;\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/readonly/readonly.ts\nfunction readonly() {\n  return {\n    kind: \"transformation\",\n    type: \"readonly\",\n    reference: readonly,\n    async: false,\n    _run(dataset) {\n      return dataset;\n    }\n  };\n}\n\n// src/actions/reduceItems/reduceItems.ts\nfunction reduceItems(operation, initial) {\n  return {\n    kind: \"transformation\",\n    type: \"reduce_items\",\n    reference: reduceItems,\n    async: false,\n    operation,\n    initial,\n    _run(dataset) {\n      dataset.value = dataset.value.reduce(this.operation, this.initial);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/regex/regex.ts\nfunction regex(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"regex\",\n    reference: regex,\n    async: false,\n    expects: `${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"format\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/safeInteger/safeInteger.ts\nfunction safeInteger(message) {\n  return {\n    kind: \"validation\",\n    type: \"safe_integer\",\n    reference: safeInteger,\n    async: false,\n    expects: null,\n    requirement: Number.isSafeInteger,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"safe integer\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/size/size.ts\nfunction size(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"size\",\n    reference: size,\n    async: false,\n    expects: `${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.size !== this.requirement) {\n        _addIssue(this, \"size\", dataset, config2, {\n          received: `${dataset.value.size}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/someItem/someItem.ts\nfunction someItem(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"some_item\",\n    reference: someItem,\n    async: false,\n    expects: null,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !dataset.value.some(this.requirement)) {\n        _addIssue(this, \"item\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/sortItems/sortItems.ts\nfunction sortItems(operation) {\n  return {\n    kind: \"transformation\",\n    type: \"sort_items\",\n    reference: sortItems,\n    async: false,\n    operation,\n    _run(dataset) {\n      dataset.value = dataset.value.sort(this.operation);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/startsWith/startsWith.ts\nfunction startsWith(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"starts_with\",\n    reference: startsWith,\n    async: false,\n    expects: `\"${requirement}\"`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !dataset.value.startsWith(this.requirement)) {\n        _addIssue(this, \"start\", dataset, config2, {\n          received: `\"${dataset.value.slice(0, this.requirement.length)}\"`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/toLowerCase/toLowerCase.ts\nfunction toLowerCase() {\n  return {\n    kind: \"transformation\",\n    type: \"to_lower_case\",\n    reference: toLowerCase,\n    async: false,\n    _run(dataset) {\n      dataset.value = dataset.value.toLowerCase();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/toMaxValue/toMaxValue.ts\nfunction toMaxValue(requirement) {\n  return {\n    kind: \"transformation\",\n    type: \"to_max_value\",\n    reference: toMaxValue,\n    async: false,\n    requirement,\n    _run(dataset) {\n      dataset.value = dataset.value > this.requirement ? this.requirement : dataset.value;\n      return dataset;\n    }\n  };\n}\n\n// src/actions/toMinValue/toMinValue.ts\nfunction toMinValue(requirement) {\n  return {\n    kind: \"transformation\",\n    type: \"to_min_value\",\n    reference: toMinValue,\n    async: false,\n    requirement,\n    _run(dataset) {\n      dataset.value = dataset.value < this.requirement ? this.requirement : dataset.value;\n      return dataset;\n    }\n  };\n}\n\n// src/actions/toUpperCase/toUpperCase.ts\nfunction toUpperCase() {\n  return {\n    kind: \"transformation\",\n    type: \"to_upper_case\",\n    reference: toUpperCase,\n    async: false,\n    _run(dataset) {\n      dataset.value = dataset.value.toUpperCase();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/transform/transform.ts\nfunction transform(operation) {\n  return {\n    kind: \"transformation\",\n    type: \"transform\",\n    reference: transform,\n    async: false,\n    operation,\n    _run(dataset) {\n      dataset.value = this.operation(dataset.value);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/transform/transformAsync.ts\nfunction transformAsync(operation) {\n  return {\n    kind: \"transformation\",\n    type: \"transform\",\n    reference: transformAsync,\n    async: true,\n    operation,\n    async _run(dataset) {\n      dataset.value = await this.operation(dataset.value);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/trim/trim.ts\nfunction trim() {\n  return {\n    kind: \"transformation\",\n    type: \"trim\",\n    reference: trim,\n    async: false,\n    _run(dataset) {\n      dataset.value = dataset.value.trim();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/trimEnd/trimEnd.ts\nfunction trimEnd() {\n  return {\n    kind: \"transformation\",\n    type: \"trim_end\",\n    reference: trimEnd,\n    async: false,\n    _run(dataset) {\n      dataset.value = dataset.value.trimEnd();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/trimStart/trimStart.ts\nfunction trimStart() {\n  return {\n    kind: \"transformation\",\n    type: \"trim_start\",\n    reference: trimStart,\n    async: false,\n    _run(dataset) {\n      dataset.value = dataset.value.trimStart();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/ulid/ulid.ts\nfunction ulid(message) {\n  return {\n    kind: \"validation\",\n    type: \"ulid\",\n    reference: ulid,\n    async: false,\n    expects: null,\n    requirement: ULID_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"ULID\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/url/url.ts\nfunction url(message) {\n  return {\n    kind: \"validation\",\n    type: \"url\",\n    reference: url,\n    async: false,\n    expects: null,\n    requirement(input) {\n      try {\n        new URL(input);\n        return true;\n      } catch {\n        return false;\n      }\n    },\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"URL\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/uuid/uuid.ts\nfunction uuid(message) {\n  return {\n    kind: \"validation\",\n    type: \"uuid\",\n    reference: uuid,\n    async: false,\n    expects: null,\n    requirement: UUID_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"UUID\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/value/value.ts\nfunction value(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"value\",\n    reference: value,\n    async: false,\n    expects: requirement instanceof Date ? requirement.toJSON() : _stringify(requirement),\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !(this.requirement <= dataset.value && this.requirement >= dataset.value)) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/config/config.ts\nfunction config(schema, config2) {\n  return {\n    ...schema,\n    _run(dataset, config_) {\n      return schema._run(dataset, { ...config_, ...config2 });\n    }\n  };\n}\n\n// src/methods/getFallback/getFallback.ts\nfunction getFallback(schema, dataset, config2) {\n  return typeof schema.fallback === \"function\" ? (\n    // @ts-expect-error\n    schema.fallback(dataset, config2)\n  ) : (\n    // @ts-expect-error\n    schema.fallback\n  );\n}\n\n// src/methods/fallback/fallback.ts\nfunction fallback(schema, fallback2) {\n  return {\n    ...schema,\n    fallback: fallback2,\n    _run(dataset, config2) {\n      const outputDataset = schema._run(dataset, config2);\n      return outputDataset.issues ? { typed: true, value: getFallback(this, outputDataset, config2) } : outputDataset;\n    }\n  };\n}\n\n// src/methods/fallback/fallbackAsync.ts\nfunction fallbackAsync(schema, fallback2) {\n  return {\n    ...schema,\n    fallback: fallback2,\n    async: true,\n    async _run(dataset, config2) {\n      const outputDataset = await schema._run(dataset, config2);\n      return outputDataset.issues ? (\n        // @ts-expect-error\n        { typed: true, value: await getFallback(this, outputDataset, config2) }\n      ) : outputDataset;\n    }\n  };\n}\n\n// src/methods/flatten/flatten.ts\nfunction flatten(issues) {\n  const flatErrors = {};\n  for (const issue of issues) {\n    if (issue.path) {\n      const dotPath = getDotPath(issue);\n      if (dotPath) {\n        if (!flatErrors.nested) {\n          flatErrors.nested = {};\n        }\n        if (flatErrors.nested[dotPath]) {\n          flatErrors.nested[dotPath].push(issue.message);\n        } else {\n          flatErrors.nested[dotPath] = [issue.message];\n        }\n      } else {\n        if (flatErrors.other) {\n          flatErrors.other.push(issue.message);\n        } else {\n          flatErrors.other = [issue.message];\n        }\n      }\n    } else {\n      if (flatErrors.root) {\n        flatErrors.root.push(issue.message);\n      } else {\n        flatErrors.root = [issue.message];\n      }\n    }\n  }\n  return flatErrors;\n}\n\n// src/methods/forward/forward.ts\nfunction forward(action, pathKeys) {\n  return {\n    ...action,\n    _run(dataset, config2) {\n      const prevIssues = dataset.issues && [...dataset.issues];\n      action._run(dataset, config2);\n      if (dataset.issues) {\n        for (const issue of dataset.issues) {\n          if (!prevIssues?.includes(issue)) {\n            let pathInput = dataset.value;\n            for (const key of pathKeys) {\n              const pathValue = pathInput[key];\n              const pathItem = {\n                type: \"unknown\",\n                origin: \"value\",\n                input: pathInput,\n                key,\n                value: pathValue\n              };\n              if (issue.path) {\n                issue.path.push(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              if (!pathValue) {\n                break;\n              }\n              pathInput = pathValue;\n            }\n          }\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/forward/forwardAsync.ts\nfunction forwardAsync(action, pathKeys) {\n  return {\n    ...action,\n    async: true,\n    async _run(dataset, config2) {\n      const prevIssues = dataset.issues && [...dataset.issues];\n      await action._run(dataset, config2);\n      if (dataset.issues) {\n        for (const issue of dataset.issues) {\n          if (!prevIssues?.includes(issue)) {\n            let pathInput = dataset.value;\n            for (const key of pathKeys) {\n              const pathValue = pathInput[key];\n              const pathItem = {\n                type: \"unknown\",\n                origin: \"value\",\n                input: pathInput,\n                key,\n                value: pathValue\n              };\n              if (issue.path) {\n                issue.path.push(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              if (!pathValue) {\n                break;\n              }\n              pathInput = pathValue;\n            }\n          }\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/getDefault/getDefault.ts\nfunction getDefault(schema, dataset, config2) {\n  return typeof schema.default === \"function\" ? (\n    // @ts-expect-error\n    schema.default(dataset, config2)\n  ) : (\n    // @ts-expect-error\n    schema.default\n  );\n}\n\n// src/methods/getDefaults/getDefaults.ts\nfunction getDefaults(schema) {\n  if (\"entries\" in schema) {\n    const object2 = {};\n    for (const key in schema.entries) {\n      object2[key] = getDefaults(schema.entries[key]);\n    }\n    return object2;\n  }\n  if (\"items\" in schema) {\n    return schema.items.map(getDefaults);\n  }\n  return getDefault(schema);\n}\n\n// src/methods/getDefaults/getDefaultsAsync.ts\nasync function getDefaultsAsync(schema) {\n  if (\"entries\" in schema) {\n    return Object.fromEntries(\n      await Promise.all(\n        Object.entries(schema.entries).map(async ([key, value2]) => [\n          key,\n          await getDefaultsAsync(value2)\n        ])\n      )\n    );\n  }\n  if (\"items\" in schema) {\n    return Promise.all(schema.items.map(getDefaultsAsync));\n  }\n  return getDefault(schema);\n}\n\n// src/methods/getFallbacks/getFallbacks.ts\nfunction getFallbacks(schema) {\n  if (\"entries\" in schema) {\n    const object2 = {};\n    for (const key in schema.entries) {\n      object2[key] = getFallbacks(schema.entries[key]);\n    }\n    return object2;\n  }\n  if (\"items\" in schema) {\n    return schema.items.map(getFallbacks);\n  }\n  return getFallback(schema);\n}\n\n// src/methods/getFallbacks/getFallbacksAsync.ts\nasync function getFallbacksAsync(schema) {\n  if (\"entries\" in schema) {\n    return Object.fromEntries(\n      await Promise.all(\n        Object.entries(schema.entries).map(async ([key, value2]) => [\n          key,\n          await getFallbacksAsync(value2)\n        ])\n      )\n    );\n  }\n  if (\"items\" in schema) {\n    return Promise.all(schema.items.map(getFallbacksAsync));\n  }\n  return getFallback(schema);\n}\n\n// src/methods/is/is.ts\nfunction is(schema, input) {\n  return !schema._run({ typed: false, value: input }, { abortEarly: true }).issues;\n}\n\n// src/schemas/any/any.ts\nfunction any() {\n  return {\n    kind: \"schema\",\n    type: \"any\",\n    reference: any,\n    expects: \"any\",\n    async: false,\n    _run(dataset) {\n      dataset.typed = true;\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/array/array.ts\nfunction array(item, message) {\n  return {\n    kind: \"schema\",\n    type: \"array\",\n    reference: array,\n    expects: \"Array\",\n    async: false,\n    item,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < input.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.item._run({ typed: false, value: value2 }, config2);\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/array/arrayAsync.ts\nfunction arrayAsync(item, message) {\n  return {\n    kind: \"schema\",\n    type: \"array\",\n    reference: arrayAsync,\n    expects: \"Array\",\n    async: true,\n    item,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const itemDatasets = await Promise.all(\n          input.map((value2) => this.item._run({ typed: false, value: value2 }, config2))\n        );\n        for (let key = 0; key < itemDatasets.length; key++) {\n          const itemDataset = itemDatasets[key];\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: input[key]\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/bigint/bigint.ts\nfunction bigint(message) {\n  return {\n    kind: \"schema\",\n    type: \"bigint\",\n    reference: bigint,\n    expects: \"bigint\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (typeof dataset.value === \"bigint\") {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/blob/blob.ts\nfunction blob(message) {\n  return {\n    kind: \"schema\",\n    type: \"blob\",\n    reference: blob,\n    expects: \"Blob\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value instanceof Blob) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/boolean/boolean.ts\nfunction boolean(message) {\n  return {\n    kind: \"schema\",\n    type: \"boolean\",\n    reference: boolean,\n    expects: \"boolean\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (typeof dataset.value === \"boolean\") {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/custom/custom.ts\nfunction custom(check2, message) {\n  return {\n    kind: \"schema\",\n    type: \"custom\",\n    reference: custom,\n    expects: \"unknown\",\n    async: false,\n    check: check2,\n    message,\n    _run(dataset, config2) {\n      if (this.check(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/custom/customAsync.ts\nfunction customAsync(check2, message) {\n  return {\n    kind: \"schema\",\n    type: \"custom\",\n    reference: customAsync,\n    expects: \"unknown\",\n    async: true,\n    check: check2,\n    message,\n    async _run(dataset, config2) {\n      if (await this.check(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/date/date.ts\nfunction date(message) {\n  return {\n    kind: \"schema\",\n    type: \"date\",\n    reference: date,\n    expects: \"Date\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value instanceof Date) {\n        if (!isNaN(dataset.value)) {\n          dataset.typed = true;\n        } else {\n          _addIssue(this, \"type\", dataset, config2, {\n            received: '\"Invalid Date\"'\n          });\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/enum/enum.ts\nfunction enum_(enum__, message) {\n  const options = Object.entries(enum__).filter(([key]) => isNaN(+key)).map(([, value2]) => value2);\n  return {\n    kind: \"schema\",\n    type: \"enum\",\n    reference: enum_,\n    expects: options.map(_stringify).join(\" | \") || \"never\",\n    async: false,\n    enum: enum__,\n    options,\n    message,\n    _run(dataset, config2) {\n      if (this.options.includes(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/file/file.ts\nfunction file(message) {\n  return {\n    kind: \"schema\",\n    type: \"file\",\n    reference: file,\n    expects: \"File\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value instanceof File) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/function/function.ts\nfunction function_(message) {\n  return {\n    kind: \"schema\",\n    type: \"function\",\n    reference: function_,\n    expects: \"Function\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (typeof dataset.value === \"function\") {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/instance/instance.ts\nfunction instance(class_, message) {\n  return {\n    kind: \"schema\",\n    type: \"instance\",\n    reference: instance,\n    expects: class_.name,\n    async: false,\n    class: class_,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value instanceof this.class) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/intersect/utils/_merge/_merge.ts\nfunction _merge(value1, value2) {\n  if (typeof value1 === typeof value2) {\n    if (value1 === value2 || value1 instanceof Date && value2 instanceof Date && +value1 === +value2) {\n      return { value: value1 };\n    }\n    if (value1 && value2 && value1.constructor === Object && value2.constructor === Object) {\n      for (const key in value2) {\n        if (key in value1) {\n          const dataset = _merge(value1[key], value2[key]);\n          if (dataset.issue) {\n            return dataset;\n          }\n          value1[key] = dataset.value;\n        } else {\n          value1[key] = value2[key];\n        }\n      }\n      return { value: value1 };\n    }\n    if (Array.isArray(value1) && Array.isArray(value2)) {\n      if (value1.length === value2.length) {\n        for (let index = 0; index < value1.length; index++) {\n          const dataset = _merge(value1[index], value2[index]);\n          if (dataset.issue) {\n            return dataset;\n          }\n          value1[index] = dataset.value;\n        }\n        return { value: value1 };\n      }\n    }\n  }\n  return { issue: true };\n}\n\n// src/schemas/intersect/intersect.ts\nfunction intersect(options, message) {\n  return {\n    kind: \"schema\",\n    type: \"intersect\",\n    reference: intersect,\n    expects: [...new Set(options.map((option) => option.expects))].join(\" & \") || \"never\",\n    async: false,\n    options,\n    message,\n    _run(dataset, config2) {\n      if (this.options.length) {\n        const input = dataset.value;\n        let outputs;\n        dataset.typed = true;\n        for (const schema of this.options) {\n          const optionDataset = schema._run(\n            { typed: false, value: input },\n            config2\n          );\n          if (optionDataset.issues) {\n            if (dataset.issues) {\n              dataset.issues.push(...optionDataset.issues);\n            } else {\n              dataset.issues = optionDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!optionDataset.typed) {\n            dataset.typed = false;\n          }\n          if (dataset.typed) {\n            if (outputs) {\n              outputs.push(optionDataset.value);\n            } else {\n              outputs = [optionDataset.value];\n            }\n          }\n        }\n        if (dataset.typed) {\n          dataset.value = outputs[0];\n          for (let index = 1; index < outputs.length; index++) {\n            const mergeDataset = _merge(dataset.value, outputs[index]);\n            if (mergeDataset.issue) {\n              _addIssue(this, \"type\", dataset, config2, {\n                received: \"unknown\"\n              });\n              break;\n            }\n            dataset.value = mergeDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/intersect/intersectAsync.ts\nfunction intersectAsync(options, message) {\n  return {\n    kind: \"schema\",\n    type: \"intersect\",\n    reference: intersectAsync,\n    expects: [...new Set(options.map((option) => option.expects))].join(\" & \") || \"never\",\n    async: true,\n    options,\n    message,\n    async _run(dataset, config2) {\n      if (this.options.length) {\n        const input = dataset.value;\n        let outputs;\n        dataset.typed = true;\n        const optionDatasets = await Promise.all(\n          this.options.map(\n            (schema) => schema._run({ typed: false, value: input }, config2)\n          )\n        );\n        for (const optionDataset of optionDatasets) {\n          if (optionDataset.issues) {\n            if (dataset.issues) {\n              dataset.issues.push(...optionDataset.issues);\n            } else {\n              dataset.issues = optionDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!optionDataset.typed) {\n            dataset.typed = false;\n          }\n          if (dataset.typed) {\n            if (outputs) {\n              outputs.push(optionDataset.value);\n            } else {\n              outputs = [optionDataset.value];\n            }\n          }\n        }\n        if (dataset.typed) {\n          dataset.value = outputs[0];\n          for (let index = 1; index < outputs.length; index++) {\n            const mergeDataset = _merge(dataset.value, outputs[index]);\n            if (mergeDataset.issue) {\n              _addIssue(this, \"type\", dataset, config2, {\n                received: \"unknown\"\n              });\n              break;\n            }\n            dataset.value = mergeDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/lazy/lazy.ts\nfunction lazy(getter) {\n  return {\n    kind: \"schema\",\n    type: \"lazy\",\n    reference: lazy,\n    expects: \"unknown\",\n    async: false,\n    getter,\n    _run(dataset, config2) {\n      return this.getter(dataset.value)._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/lazy/lazyAsync.ts\nfunction lazyAsync(getter) {\n  return {\n    kind: \"schema\",\n    type: \"lazy\",\n    reference: lazyAsync,\n    expects: \"unknown\",\n    async: true,\n    getter,\n    async _run(dataset, config2) {\n      return (await this.getter(dataset.value))._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/literal/literal.ts\nfunction literal(literal_, message) {\n  return {\n    kind: \"schema\",\n    type: \"literal\",\n    reference: literal,\n    expects: _stringify(literal_),\n    async: false,\n    literal: literal_,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === this.literal) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/looseObject/looseObject.ts\nfunction looseObject(entries, message) {\n  return {\n    kind: \"schema\",\n    type: \"loose_object\",\n    reference: looseObject,\n    expects: \"Object\",\n    async: false,\n    entries,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const key in this.entries) {\n          const value2 = input[key];\n          const valueDataset = this.entries[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (_isValidObjectKey(input, key) && !(key in this.entries)) {\n              dataset.value[key] = input[key];\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/looseObject/looseObjectAsync.ts\nfunction looseObjectAsync(entries, message) {\n  return {\n    kind: \"schema\",\n    type: \"loose_object\",\n    reference: looseObjectAsync,\n    expects: \"Object\",\n    async: true,\n    entries,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const valueDatasets = await Promise.all(\n          Object.entries(this.entries).map(async ([key, schema]) => {\n            const value2 = input[key];\n            return [\n              key,\n              value2,\n              await schema._run({ typed: false, value: value2 }, config2)\n            ];\n          })\n        );\n        for (const [key, value2, valueDataset] of valueDatasets) {\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (_isValidObjectKey(input, key) && !(key in this.entries)) {\n              dataset.value[key] = input[key];\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/looseTuple/looseTuple.ts\nfunction looseTuple(items, message) {\n  return {\n    kind: \"schema\",\n    type: \"loose_tuple\",\n    reference: looseTuple,\n    expects: \"Array\",\n    async: false,\n    items,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < this.items.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.items[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (let key = this.items.length; key < input.length; key++) {\n            dataset.value.push(input[key]);\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/looseTuple/looseTupleAsync.ts\nfunction looseTupleAsync(items, message) {\n  return {\n    kind: \"schema\",\n    type: \"loose_tuple\",\n    reference: looseTupleAsync,\n    expects: \"Array\",\n    async: true,\n    items,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const itemDatasets = await Promise.all(\n          this.items.map(async (item, key) => {\n            const value2 = input[key];\n            return [\n              key,\n              value2,\n              await item._run({ typed: false, value: value2 }, config2)\n            ];\n          })\n        );\n        for (const [key, value2, itemDataset] of itemDatasets) {\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (let key = this.items.length; key < input.length; key++) {\n            dataset.value.push(input[key]);\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/map/map.ts\nfunction map(key, value2, message) {\n  return {\n    kind: \"schema\",\n    type: \"map\",\n    reference: map,\n    expects: \"Map\",\n    async: false,\n    key,\n    value: value2,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input instanceof Map) {\n        dataset.typed = true;\n        dataset.value = /* @__PURE__ */ new Map();\n        for (const [inputKey, inputValue] of input) {\n          const keyDataset = this.key._run(\n            { typed: false, value: inputKey },\n            config2\n          );\n          if (keyDataset.issues) {\n            const pathItem = {\n              type: \"map\",\n              origin: \"key\",\n              input,\n              key: inputKey,\n              value: inputValue\n            };\n            for (const issue of keyDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = keyDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          const valueDataset = this.value._run(\n            { typed: false, value: inputValue },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"map\",\n              origin: \"value\",\n              input,\n              key: inputKey,\n              value: inputValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!keyDataset.typed || !valueDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.set(keyDataset.value, valueDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/map/mapAsync.ts\nfunction mapAsync(key, value2, message) {\n  return {\n    kind: \"schema\",\n    type: \"map\",\n    reference: mapAsync,\n    expects: \"Map\",\n    async: true,\n    key,\n    value: value2,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input instanceof Map) {\n        dataset.typed = true;\n        dataset.value = /* @__PURE__ */ new Map();\n        const datasets = await Promise.all(\n          [...input].map(\n            ([inputKey, inputValue]) => Promise.all([\n              inputKey,\n              inputValue,\n              this.key._run({ typed: false, value: inputKey }, config2),\n              this.value._run({ typed: false, value: inputValue }, config2)\n            ])\n          )\n        );\n        for (const [\n          inputKey,\n          inputValue,\n          keyDataset,\n          valueDataset\n        ] of datasets) {\n          if (keyDataset.issues) {\n            const pathItem = {\n              type: \"map\",\n              origin: \"key\",\n              input,\n              key: inputKey,\n              value: inputValue\n            };\n            for (const issue of keyDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = keyDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"map\",\n              origin: \"value\",\n              input,\n              key: inputKey,\n              value: inputValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!keyDataset.typed || !valueDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.set(keyDataset.value, valueDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/nan/nan.ts\nfunction nan(message) {\n  return {\n    kind: \"schema\",\n    type: \"nan\",\n    reference: nan,\n    expects: \"NaN\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (Number.isNaN(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/never/never.ts\nfunction never(message) {\n  return {\n    kind: \"schema\",\n    type: \"never\",\n    reference: never,\n    expects: \"never\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      _addIssue(this, \"type\", dataset, config2);\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/nonNullable/nonNullable.ts\nfunction nonNullable(wrapped, message) {\n  return {\n    kind: \"schema\",\n    type: \"non_nullable\",\n    reference: nonNullable,\n    expects: \"!null\",\n    async: false,\n    wrapped,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === null) {\n        _addIssue(this, \"type\", dataset, config2);\n        return dataset;\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/nonNullable/nonNullableAsync.ts\nfunction nonNullableAsync(wrapped, message) {\n  return {\n    kind: \"schema\",\n    type: \"non_nullable\",\n    reference: nonNullableAsync,\n    expects: \"!null\",\n    async: true,\n    wrapped,\n    message,\n    async _run(dataset, config2) {\n      if (dataset.value === null) {\n        _addIssue(this, \"type\", dataset, config2);\n        return dataset;\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/nonNullish/nonNullish.ts\nfunction nonNullish(wrapped, message) {\n  return {\n    kind: \"schema\",\n    type: \"non_nullish\",\n    reference: nonNullish,\n    expects: \"!null & !undefined\",\n    async: false,\n    wrapped,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === null || dataset.value === void 0) {\n        _addIssue(this, \"type\", dataset, config2);\n        return dataset;\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/nonNullish/nonNullishAsync.ts\nfunction nonNullishAsync(wrapped, message) {\n  return {\n    kind: \"schema\",\n    type: \"non_nullish\",\n    reference: nonNullishAsync,\n    expects: \"!null & !undefined\",\n    async: true,\n    wrapped,\n    message,\n    async _run(dataset, config2) {\n      if (dataset.value === null || dataset.value === void 0) {\n        _addIssue(this, \"type\", dataset, config2);\n        return dataset;\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/nonOptional/nonOptional.ts\nfunction nonOptional(wrapped, message) {\n  return {\n    kind: \"schema\",\n    type: \"non_optional\",\n    reference: nonOptional,\n    expects: \"!undefined\",\n    async: false,\n    wrapped,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === void 0) {\n        _addIssue(this, \"type\", dataset, config2);\n        return dataset;\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/nonOptional/nonOptionalAsync.ts\nfunction nonOptionalAsync(wrapped, message) {\n  return {\n    kind: \"schema\",\n    type: \"non_optional\",\n    reference: nonOptionalAsync,\n    expects: \"!undefined\",\n    async: true,\n    wrapped,\n    message,\n    async _run(dataset, config2) {\n      if (dataset.value === void 0) {\n        _addIssue(this, \"type\", dataset, config2);\n        return dataset;\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/null/null.ts\nfunction null_(message) {\n  return {\n    kind: \"schema\",\n    type: \"null\",\n    reference: null_,\n    expects: \"null\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === null) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/nullable/nullable.ts\nfunction nullable(wrapped, ...args) {\n  const schema = {\n    kind: \"schema\",\n    type: \"nullable\",\n    reference: nullable,\n    expects: `${wrapped.expects} | null`,\n    async: false,\n    wrapped,\n    _run(dataset, config2) {\n      if (dataset.value === null) {\n        if (\"default\" in this) {\n          dataset.value = getDefault(\n            this,\n            dataset,\n            config2\n          );\n        }\n        if (dataset.value === null) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n  if (0 in args) {\n    schema.default = args[0];\n  }\n  return schema;\n}\n\n// src/schemas/nullable/nullableAsync.ts\nfunction nullableAsync(wrapped, ...args) {\n  const schema = {\n    kind: \"schema\",\n    type: \"nullable\",\n    reference: nullableAsync,\n    expects: `${wrapped.expects} | null`,\n    async: true,\n    wrapped,\n    async _run(dataset, config2) {\n      if (dataset.value === null) {\n        if (\"default\" in this) {\n          dataset.value = await getDefault(\n            this,\n            dataset,\n            config2\n          );\n        }\n        if (dataset.value === null) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n  if (0 in args) {\n    schema.default = args[0];\n  }\n  return schema;\n}\n\n// src/schemas/nullish/nullish.ts\nfunction nullish(wrapped, ...args) {\n  const schema = {\n    kind: \"schema\",\n    type: \"nullish\",\n    reference: nullish,\n    expects: `${wrapped.expects} | null | undefined`,\n    async: false,\n    wrapped,\n    _run(dataset, config2) {\n      if (dataset.value === null || dataset.value === void 0) {\n        if (\"default\" in this) {\n          dataset.value = getDefault(\n            this,\n            dataset,\n            config2\n          );\n        }\n        if (dataset.value === null || dataset.value === void 0) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n  if (0 in args) {\n    schema.default = args[0];\n  }\n  return schema;\n}\n\n// src/schemas/nullish/nullishAsync.ts\nfunction nullishAsync(wrapped, ...args) {\n  const schema = {\n    kind: \"schema\",\n    type: \"nullish\",\n    reference: nullishAsync,\n    expects: `${wrapped.expects} | null | undefined`,\n    async: true,\n    wrapped,\n    async _run(dataset, config2) {\n      if (dataset.value === null || dataset.value === void 0) {\n        if (\"default\" in this) {\n          dataset.value = await getDefault(\n            this,\n            dataset,\n            config2\n          );\n        }\n        if (dataset.value === null || dataset.value === void 0) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n  if (0 in args) {\n    schema.default = args[0];\n  }\n  return schema;\n}\n\n// src/schemas/number/number.ts\nfunction number(message) {\n  return {\n    kind: \"schema\",\n    type: \"number\",\n    reference: number,\n    expects: \"number\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (typeof dataset.value === \"number\" && !isNaN(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/object/object.ts\nfunction object(entries, message) {\n  return {\n    kind: \"schema\",\n    type: \"object\",\n    reference: object,\n    expects: \"Object\",\n    async: false,\n    entries,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const key in this.entries) {\n          const value2 = input[key];\n          const valueDataset = this.entries[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/object/objectAsync.ts\nfunction objectAsync(entries, message) {\n  return {\n    kind: \"schema\",\n    type: \"object\",\n    reference: objectAsync,\n    expects: \"Object\",\n    async: true,\n    entries,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const valueDatasets = await Promise.all(\n          Object.entries(this.entries).map(async ([key, schema]) => {\n            const value2 = input[key];\n            return [\n              key,\n              value2,\n              await schema._run({ typed: false, value: value2 }, config2)\n            ];\n          })\n        );\n        for (const [key, value2, valueDataset] of valueDatasets) {\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/objectWithRest/objectWithRest.ts\nfunction objectWithRest(entries, rest, message) {\n  return {\n    kind: \"schema\",\n    type: \"object_with_rest\",\n    reference: objectWithRest,\n    expects: \"Object\",\n    async: false,\n    entries,\n    rest,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const key in this.entries) {\n          const value2 = input[key];\n          const valueDataset = this.entries[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (_isValidObjectKey(input, key) && !(key in this.entries)) {\n              const value2 = input[key];\n              const valueDataset = this.rest._run(\n                { typed: false, value: value2 },\n                config2\n              );\n              if (valueDataset.issues) {\n                const pathItem = {\n                  type: \"object\",\n                  origin: \"value\",\n                  input,\n                  key,\n                  value: value2\n                };\n                for (const issue of valueDataset.issues) {\n                  if (issue.path) {\n                    issue.path.unshift(pathItem);\n                  } else {\n                    issue.path = [pathItem];\n                  }\n                  dataset.issues?.push(issue);\n                }\n                if (!dataset.issues) {\n                  dataset.issues = valueDataset.issues;\n                }\n                if (config2.abortEarly) {\n                  dataset.typed = false;\n                  break;\n                }\n              }\n              if (!valueDataset.typed) {\n                dataset.typed = false;\n              }\n              dataset.value[key] = valueDataset.value;\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/objectWithRest/objectWithRestAsync.ts\nfunction objectWithRestAsync(entries, rest, message) {\n  return {\n    kind: \"schema\",\n    type: \"object_with_rest\",\n    reference: objectWithRestAsync,\n    expects: \"Object\",\n    async: true,\n    entries,\n    rest,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const [normalDatasets, restDatasets] = await Promise.all([\n          // Parse schema of each normal entry\n          // Hint: We do not distinguish between missing and `undefined` entries.\n          // The reason for this decision is that it reduces the bundle size, and\n          // we also expect that most users will expect this behavior.\n          Promise.all(\n            Object.entries(this.entries).map(async ([key, schema]) => {\n              const value2 = input[key];\n              return [\n                key,\n                value2,\n                await schema._run({ typed: false, value: value2 }, config2)\n              ];\n            })\n          ),\n          // Parse other entries with rest schema\n          // Hint: We exclude specific keys for security reasons\n          Promise.all(\n            Object.entries(input).filter(\n              ([key]) => _isValidObjectKey(input, key) && !(key in this.entries)\n            ).map(\n              async ([key, value2]) => [\n                key,\n                value2,\n                await this.rest._run({ typed: false, value: value2 }, config2)\n              ]\n            )\n          )\n        ]);\n        for (const [key, value2, valueDataset] of normalDatasets) {\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const [key, value2, valueDataset] of restDatasets) {\n            if (valueDataset.issues) {\n              const pathItem = {\n                type: \"object\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of valueDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = valueDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!valueDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/optional/optional.ts\nfunction optional(wrapped, ...args) {\n  const schema = {\n    kind: \"schema\",\n    type: \"optional\",\n    reference: optional,\n    expects: `${wrapped.expects} | undefined`,\n    async: false,\n    wrapped,\n    _run(dataset, config2) {\n      if (dataset.value === void 0) {\n        if (\"default\" in this) {\n          dataset.value = getDefault(\n            this,\n            dataset,\n            config2\n          );\n        }\n        if (dataset.value === void 0) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n  if (0 in args) {\n    schema.default = args[0];\n  }\n  return schema;\n}\n\n// src/schemas/optional/optionalAsync.ts\nfunction optionalAsync(wrapped, ...args) {\n  const schema = {\n    kind: \"schema\",\n    type: \"optional\",\n    reference: optionalAsync,\n    expects: `${wrapped.expects} | undefined`,\n    async: true,\n    wrapped,\n    async _run(dataset, config2) {\n      if (dataset.value === void 0) {\n        if (\"default\" in this) {\n          dataset.value = await getDefault(\n            this,\n            dataset,\n            config2\n          );\n        }\n        if (dataset.value === void 0) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n  if (0 in args) {\n    schema.default = args[0];\n  }\n  return schema;\n}\n\n// src/schemas/picklist/picklist.ts\nfunction picklist(options, message) {\n  return {\n    kind: \"schema\",\n    type: \"picklist\",\n    reference: picklist,\n    expects: options.map(_stringify).join(\" | \") || \"never\",\n    async: false,\n    options,\n    message,\n    _run(dataset, config2) {\n      if (this.options.includes(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/promise/promise.ts\nfunction promise(message) {\n  return {\n    kind: \"schema\",\n    type: \"promise\",\n    reference: promise,\n    expects: \"Promise\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value instanceof Promise) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/record/record.ts\nfunction record(key, value2, message) {\n  return {\n    kind: \"schema\",\n    type: \"record\",\n    reference: record,\n    expects: \"Object\",\n    async: false,\n    key,\n    value: value2,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const entryKey in input) {\n          if (_isValidObjectKey(input, entryKey)) {\n            const entryValue = input[entryKey];\n            const keyDataset = this.key._run(\n              { typed: false, value: entryKey },\n              config2\n            );\n            if (keyDataset.issues) {\n              const pathItem = {\n                type: \"object\",\n                origin: \"key\",\n                input,\n                key: entryKey,\n                value: entryValue\n              };\n              for (const issue of keyDataset.issues) {\n                issue.path = [pathItem];\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = keyDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            const valueDataset = this.value._run(\n              { typed: false, value: entryValue },\n              config2\n            );\n            if (valueDataset.issues) {\n              const pathItem = {\n                type: \"object\",\n                origin: \"value\",\n                input,\n                key: entryKey,\n                value: entryValue\n              };\n              for (const issue of valueDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = valueDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!keyDataset.typed || !valueDataset.typed) {\n              dataset.typed = false;\n            }\n            if (keyDataset.typed) {\n              dataset.value[keyDataset.value] = valueDataset.value;\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/record/recordAsync.ts\nfunction recordAsync(key, value2, message) {\n  return {\n    kind: \"schema\",\n    type: \"record\",\n    reference: recordAsync,\n    expects: \"Object\",\n    async: true,\n    key,\n    value: value2,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const datasets = await Promise.all(\n          Object.entries(input).filter(([key2]) => _isValidObjectKey(input, key2)).map(\n            ([entryKey, entryValue]) => Promise.all([\n              entryKey,\n              entryValue,\n              this.key._run({ typed: false, value: entryKey }, config2),\n              this.value._run({ typed: false, value: entryValue }, config2)\n            ])\n          )\n        );\n        for (const [\n          entryKey,\n          entryValue,\n          keyDataset,\n          valueDataset\n        ] of datasets) {\n          if (keyDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"key\",\n              input,\n              key: entryKey,\n              value: entryValue\n            };\n            for (const issue of keyDataset.issues) {\n              issue.path = [pathItem];\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = keyDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key: entryKey,\n              value: entryValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!keyDataset.typed || !valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (keyDataset.typed) {\n            dataset.value[keyDataset.value] = valueDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/set/set.ts\nfunction set(value2, message) {\n  return {\n    kind: \"schema\",\n    type: \"set\",\n    reference: set,\n    expects: \"Set\",\n    async: false,\n    value: value2,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input instanceof Set) {\n        dataset.typed = true;\n        dataset.value = /* @__PURE__ */ new Set();\n        for (const inputValue of input) {\n          const valueDataset = this.value._run(\n            { typed: false, value: inputValue },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"set\",\n              origin: \"value\",\n              input,\n              key: null,\n              value: inputValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.add(valueDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/set/setAsync.ts\nfunction setAsync(value2, message) {\n  return {\n    kind: \"schema\",\n    type: \"set\",\n    reference: setAsync,\n    expects: \"Set\",\n    async: true,\n    value: value2,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input instanceof Set) {\n        dataset.typed = true;\n        dataset.value = /* @__PURE__ */ new Set();\n        const valueDatasets = await Promise.all(\n          [...input].map(\n            async (inputValue) => [\n              inputValue,\n              await this.value._run(\n                { typed: false, value: inputValue },\n                config2\n              )\n            ]\n          )\n        );\n        for (const [inputValue, valueDataset] of valueDatasets) {\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"set\",\n              origin: \"value\",\n              input,\n              key: null,\n              value: inputValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.add(valueDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/strictObject/strictObject.ts\nfunction strictObject(entries, message) {\n  return {\n    kind: \"schema\",\n    type: \"strict_object\",\n    reference: strictObject,\n    expects: \"Object\",\n    async: false,\n    entries,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const key in this.entries) {\n          const value2 = input[key];\n          const valueDataset = this.entries[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (!(key in this.entries)) {\n              const value2 = input[key];\n              _addIssue(this, \"type\", dataset, config2, {\n                input: value2,\n                expected: \"never\",\n                path: [\n                  {\n                    type: \"object\",\n                    origin: \"value\",\n                    input,\n                    key,\n                    value: value2\n                  }\n                ]\n              });\n              break;\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/strictObject/strictObjectAsync.ts\nfunction strictObjectAsync(entries, message) {\n  return {\n    kind: \"schema\",\n    type: \"strict_object\",\n    reference: strictObjectAsync,\n    expects: \"Object\",\n    async: true,\n    entries,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const valueDatasets = await Promise.all(\n          Object.entries(this.entries).map(async ([key, schema]) => {\n            const value2 = input[key];\n            return [\n              key,\n              value2,\n              await schema._run({ typed: false, value: value2 }, config2)\n            ];\n          })\n        );\n        for (const [key, value2, valueDataset] of valueDatasets) {\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (!(key in this.entries)) {\n              const value2 = input[key];\n              _addIssue(this, \"type\", dataset, config2, {\n                input: value2,\n                expected: \"never\",\n                path: [\n                  {\n                    type: \"object\",\n                    origin: \"value\",\n                    input,\n                    key,\n                    value: value2\n                  }\n                ]\n              });\n              break;\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/strictTuple/strictTuple.ts\nfunction strictTuple(items, message) {\n  return {\n    kind: \"schema\",\n    type: \"strict_tuple\",\n    reference: strictTuple,\n    expects: \"Array\",\n    async: false,\n    items,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < this.items.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.items[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!(dataset.issues && config2.abortEarly) && this.items.length < input.length) {\n          const value2 = input[items.length];\n          _addIssue(this, \"type\", dataset, config2, {\n            input: value2,\n            expected: \"never\",\n            path: [\n              {\n                type: \"array\",\n                origin: \"value\",\n                input,\n                key: this.items.length,\n                value: value2\n              }\n            ]\n          });\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/strictTuple/strictTupleAsync.ts\nfunction strictTupleAsync(items, message) {\n  return {\n    kind: \"schema\",\n    type: \"strict_tuple\",\n    reference: strictTupleAsync,\n    expects: \"Array\",\n    async: true,\n    items,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const itemDatasets = await Promise.all(\n          this.items.map(async (item, key) => {\n            const value2 = input[key];\n            return [\n              key,\n              value2,\n              await item._run({ typed: false, value: value2 }, config2)\n            ];\n          })\n        );\n        for (const [key, value2, itemDataset] of itemDatasets) {\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!(dataset.issues && config2.abortEarly) && this.items.length < input.length) {\n          const value2 = input[items.length];\n          _addIssue(this, \"type\", dataset, config2, {\n            input: value2,\n            expected: \"never\",\n            path: [\n              {\n                type: \"array\",\n                origin: \"value\",\n                input,\n                key: this.items.length,\n                value: value2\n              }\n            ]\n          });\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/string/string.ts\nfunction string(message) {\n  return {\n    kind: \"schema\",\n    type: \"string\",\n    reference: string,\n    expects: \"string\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (typeof dataset.value === \"string\") {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/symbol/symbol.ts\nfunction symbol(message) {\n  return {\n    kind: \"schema\",\n    type: \"symbol\",\n    reference: symbol,\n    expects: \"symbol\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (typeof dataset.value === \"symbol\") {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/tuple/tuple.ts\nfunction tuple(items, message) {\n  return {\n    kind: \"schema\",\n    type: \"tuple\",\n    reference: tuple,\n    expects: \"Array\",\n    async: false,\n    items,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < this.items.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.items[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/tuple/tupleAsync.ts\nfunction tupleAsync(items, message) {\n  return {\n    kind: \"schema\",\n    type: \"tuple\",\n    reference: tupleAsync,\n    expects: \"Array\",\n    async: true,\n    items,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const itemDatasets = await Promise.all(\n          this.items.map(async (item, key) => {\n            const value2 = input[key];\n            return [\n              key,\n              value2,\n              await item._run({ typed: false, value: value2 }, config2)\n            ];\n          })\n        );\n        for (const [key, value2, itemDataset] of itemDatasets) {\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/tupleWithRest/tupleWithRest.ts\nfunction tupleWithRest(items, rest, message) {\n  return {\n    kind: \"schema\",\n    type: \"tuple_with_rest\",\n    reference: tupleWithRest,\n    expects: \"Array\",\n    async: false,\n    items,\n    rest,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < this.items.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.items[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (let key = this.items.length; key < input.length; key++) {\n            const value2 = input[key];\n            const itemDataset = this.rest._run({ typed: false, value: value2 }, config2);\n            if (itemDataset.issues) {\n              const pathItem = {\n                type: \"array\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of itemDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = itemDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!itemDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value.push(itemDataset.value);\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/tupleWithRest/tupleWithRestAsync.ts\nfunction tupleWithRestAsync(items, rest, message) {\n  return {\n    kind: \"schema\",\n    type: \"tuple_with_rest\",\n    reference: tupleWithRestAsync,\n    expects: \"Array\",\n    async: true,\n    items,\n    rest,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const [normalDatasets, restDatasets] = await Promise.all([\n          // Parse schema of each normal item\n          Promise.all(\n            this.items.map(async (item, key) => {\n              const value2 = input[key];\n              return [\n                key,\n                value2,\n                await item._run({ typed: false, value: value2 }, config2)\n              ];\n            })\n          ),\n          // Parse other items with rest schema\n          Promise.all(\n            input.slice(this.items.length).map(async (value2, key) => {\n              return [\n                key + this.items.length,\n                value2,\n                await this.rest._run({ typed: false, value: value2 }, config2)\n              ];\n            })\n          )\n        ]);\n        for (const [key, value2, itemDataset] of normalDatasets) {\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const [key, value2, itemDataset] of restDatasets) {\n            if (itemDataset.issues) {\n              const pathItem = {\n                type: \"array\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of itemDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = itemDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!itemDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value.push(itemDataset.value);\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/undefined/undefined.ts\nfunction undefined_(message) {\n  return {\n    kind: \"schema\",\n    type: \"undefined\",\n    reference: undefined_,\n    expects: \"undefined\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === void 0) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/union/utils/_subIssues/_subIssues.ts\nfunction _subIssues(datasets) {\n  let issues;\n  if (datasets) {\n    for (const dataset of datasets) {\n      if (issues) {\n        issues.push(...dataset.issues);\n      } else {\n        issues = dataset.issues;\n      }\n    }\n  }\n  return issues;\n}\n\n// src/schemas/union/union.ts\nfunction union(options, message) {\n  return {\n    kind: \"schema\",\n    type: \"union\",\n    reference: union,\n    expects: [...new Set(options.map((option) => option.expects))].join(\" | \") || \"never\",\n    async: false,\n    options,\n    message,\n    _run(dataset, config2) {\n      let validDataset;\n      let typedDatasets;\n      let untypedDatasets;\n      for (const schema of this.options) {\n        const optionDataset = schema._run(\n          { typed: false, value: dataset.value },\n          config2\n        );\n        if (optionDataset.typed) {\n          if (optionDataset.issues) {\n            if (typedDatasets) {\n              typedDatasets.push(optionDataset);\n            } else {\n              typedDatasets = [optionDataset];\n            }\n          } else {\n            validDataset = optionDataset;\n            break;\n          }\n        } else {\n          if (untypedDatasets) {\n            untypedDatasets.push(optionDataset);\n          } else {\n            untypedDatasets = [optionDataset];\n          }\n        }\n      }\n      if (validDataset) {\n        return validDataset;\n      }\n      if (typedDatasets) {\n        if (typedDatasets.length === 1) {\n          return typedDatasets[0];\n        }\n        _addIssue(this, \"type\", dataset, config2, {\n          issues: _subIssues(typedDatasets)\n        });\n        dataset.typed = true;\n      } else if (untypedDatasets?.length === 1) {\n        return untypedDatasets[0];\n      } else {\n        _addIssue(this, \"type\", dataset, config2, {\n          issues: _subIssues(untypedDatasets)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/union/unionAsync.ts\nfunction unionAsync(options, message) {\n  return {\n    kind: \"schema\",\n    type: \"union\",\n    reference: unionAsync,\n    expects: [...new Set(options.map((option) => option.expects))].join(\" | \") || \"never\",\n    async: true,\n    options,\n    message,\n    async _run(dataset, config2) {\n      let validDataset;\n      let typedDatasets;\n      let untypedDatasets;\n      for (const schema of this.options) {\n        const optionDataset = await schema._run(\n          { typed: false, value: dataset.value },\n          config2\n        );\n        if (optionDataset.typed) {\n          if (optionDataset.issues) {\n            if (typedDatasets) {\n              typedDatasets.push(optionDataset);\n            } else {\n              typedDatasets = [optionDataset];\n            }\n          } else {\n            validDataset = optionDataset;\n            break;\n          }\n        } else {\n          if (untypedDatasets) {\n            untypedDatasets.push(optionDataset);\n          } else {\n            untypedDatasets = [optionDataset];\n          }\n        }\n      }\n      if (validDataset) {\n        return validDataset;\n      }\n      if (typedDatasets) {\n        if (typedDatasets.length === 1) {\n          return typedDatasets[0];\n        }\n        _addIssue(this, \"type\", dataset, config2, {\n          issues: _subIssues(typedDatasets)\n        });\n        dataset.typed = true;\n      } else if (untypedDatasets?.length === 1) {\n        return untypedDatasets[0];\n      } else {\n        _addIssue(this, \"type\", dataset, config2, {\n          issues: _subIssues(untypedDatasets)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/unknown/unknown.ts\nfunction unknown() {\n  return {\n    kind: \"schema\",\n    type: \"unknown\",\n    reference: unknown,\n    expects: \"unknown\",\n    async: false,\n    _run(dataset) {\n      dataset.typed = true;\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/variant/utils/_discriminators/_discriminators.ts\nfunction _discriminators(key, options, set2 = /* @__PURE__ */ new Set()) {\n  for (const schema of options) {\n    if (schema.type === \"variant\") {\n      _discriminators(key, schema.options, set2);\n    } else {\n      set2.add(schema.entries[key].expects);\n    }\n  }\n  return set2;\n}\n\n// src/schemas/variant/variant.ts\nfunction variant(key, options, message) {\n  let expectedDiscriminators;\n  return {\n    kind: \"schema\",\n    type: \"variant\",\n    reference: variant,\n    expects: \"Object\",\n    async: false,\n    key,\n    options,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        const discriminator = input[this.key];\n        if (this.key in input) {\n          let outputDataset;\n          for (const schema of this.options) {\n            if (schema.type === \"variant\" || !schema.entries[this.key]._run(\n              { typed: false, value: discriminator },\n              config2\n            ).issues) {\n              const optionDataset = schema._run(\n                { typed: false, value: input },\n                config2\n              );\n              if (!optionDataset.issues) {\n                return optionDataset;\n              }\n              if (!outputDataset || !outputDataset.typed && optionDataset.typed) {\n                outputDataset = optionDataset;\n              }\n            }\n          }\n          if (outputDataset) {\n            return outputDataset;\n          }\n        }\n        if (!expectedDiscriminators) {\n          expectedDiscriminators = [..._discriminators(this.key, this.options)].join(\" | \") || \"never\";\n        }\n        _addIssue(this, \"type\", dataset, config2, {\n          input: discriminator,\n          expected: expectedDiscriminators,\n          path: [\n            {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key: this.key,\n              value: discriminator\n            }\n          ]\n        });\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/variant/variantAsync.ts\nfunction variantAsync(key, options, message) {\n  let expectedDiscriminators;\n  return {\n    kind: \"schema\",\n    type: \"variant\",\n    reference: variantAsync,\n    expects: \"Object\",\n    async: true,\n    key,\n    options,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        const discriminator = input[this.key];\n        if (this.key in input) {\n          let outputDataset;\n          for (const schema of this.options) {\n            if (schema.type === \"variant\" || !(await schema.entries[this.key]._run(\n              { typed: false, value: discriminator },\n              config2\n            )).issues) {\n              const optionDataset = await schema._run(\n                { typed: false, value: input },\n                config2\n              );\n              if (!optionDataset.issues) {\n                return optionDataset;\n              }\n              if (!outputDataset || !outputDataset.typed && optionDataset.typed) {\n                outputDataset = optionDataset;\n              }\n            }\n          }\n          if (outputDataset) {\n            return outputDataset;\n          }\n        }\n        if (!expectedDiscriminators) {\n          expectedDiscriminators = [..._discriminators(this.key, this.options)].join(\" | \") || \"never\";\n        }\n        _addIssue(this, \"type\", dataset, config2, {\n          input: discriminator,\n          expected: expectedDiscriminators,\n          path: [\n            {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key: this.key,\n              value: discriminator\n            }\n          ]\n        });\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/void/void.ts\nfunction void_(message) {\n  return {\n    kind: \"schema\",\n    type: \"void\",\n    reference: void_,\n    expects: \"void\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === void 0) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/keyof/keyof.ts\nfunction keyof(schema, message) {\n  return picklist(Object.keys(schema.entries), message);\n}\n\n// src/methods/omit/omit.ts\nfunction omit(schema, keys) {\n  const entries = {\n    ...schema.entries\n  };\n  for (const key of keys) {\n    delete entries[key];\n  }\n  return { ...schema, entries };\n}\n\n// src/methods/parse/parse.ts\nfunction parse(schema, input, config2) {\n  const dataset = schema._run(\n    { typed: false, value: input },\n    getGlobalConfig(config2)\n  );\n  if (dataset.issues) {\n    throw new ValiError(dataset.issues);\n  }\n  return dataset.value;\n}\n\n// src/methods/parse/parseAsync.ts\nasync function parseAsync(schema, input, config2) {\n  const dataset = await schema._run(\n    { typed: false, value: input },\n    getGlobalConfig(config2)\n  );\n  if (dataset.issues) {\n    throw new ValiError(dataset.issues);\n  }\n  return dataset.value;\n}\n\n// src/methods/parser/parser.ts\nfunction parser(schema, config2) {\n  const func = (input) => parse(schema, input, config2);\n  func.schema = schema;\n  func.config = config2;\n  return func;\n}\n\n// src/methods/parser/parserAsync.ts\nfunction parserAsync(schema, config2) {\n  const func = (input) => parseAsync(schema, input, config2);\n  func.schema = schema;\n  func.config = config2;\n  return func;\n}\n\n// src/methods/partial/partial.ts\nfunction partial(schema, keys) {\n  const entries = {};\n  for (const key in schema.entries) {\n    entries[key] = !keys || keys.includes(key) ? optional(schema.entries[key]) : schema.entries[key];\n  }\n  return { ...schema, entries };\n}\n\n// src/methods/partial/partialAsync.ts\nfunction partialAsync(schema, keys) {\n  const entries = {};\n  for (const key in schema.entries) {\n    entries[key] = !keys || keys.includes(key) ? optionalAsync(schema.entries[key]) : schema.entries[key];\n  }\n  return { ...schema, entries };\n}\n\n// src/methods/pick/pick.ts\nfunction pick(schema, keys) {\n  const entries = {};\n  for (const key of keys) {\n    entries[key] = schema.entries[key];\n  }\n  return { ...schema, entries };\n}\n\n// src/methods/pipe/pipe.ts\nfunction pipe(...pipe2) {\n  return {\n    ...pipe2[0],\n    pipe: pipe2,\n    _run(dataset, config2) {\n      for (const item of pipe2) {\n        if (item.kind !== \"metadata\") {\n          if (dataset.issues && (item.kind === \"schema\" || item.kind === \"transformation\")) {\n            dataset.typed = false;\n            break;\n          }\n          if (!dataset.issues || !config2.abortEarly && !config2.abortPipeEarly) {\n            dataset = item._run(dataset, config2);\n          }\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/pipe/pipeAsync.ts\nfunction pipeAsync(...pipe2) {\n  return {\n    ...pipe2[0],\n    pipe: pipe2,\n    async: true,\n    async _run(dataset, config2) {\n      for (const item of pipe2) {\n        if (item.kind !== \"metadata\") {\n          if (dataset.issues && (item.kind === \"schema\" || item.kind === \"transformation\")) {\n            dataset.typed = false;\n            break;\n          }\n          if (!dataset.issues || !config2.abortEarly && !config2.abortPipeEarly) {\n            dataset = await item._run(dataset, config2);\n          }\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/required/required.ts\nfunction required(schema, arg2, arg3) {\n  const keys = Array.isArray(arg2) ? arg2 : void 0;\n  const message = Array.isArray(arg2) ? arg3 : arg2;\n  const entries = {};\n  for (const key in schema.entries) {\n    entries[key] = !keys || keys.includes(key) ? nonOptional(schema.entries[key], message) : schema.entries[key];\n  }\n  return { ...schema, entries };\n}\n\n// src/methods/required/requiredAsync.ts\nfunction requiredAsync(schema, arg2, arg3) {\n  const keys = Array.isArray(arg2) ? arg2 : void 0;\n  const message = Array.isArray(arg2) ? arg3 : arg2;\n  const entries = {};\n  for (const key in schema.entries) {\n    entries[key] = !keys || keys.includes(key) ? nonOptionalAsync(schema.entries[key], message) : schema.entries[key];\n  }\n  return { ...schema, entries };\n}\n\n// src/methods/safeParse/safeParse.ts\nfunction safeParse(schema, input, config2) {\n  const dataset = schema._run(\n    { typed: false, value: input },\n    getGlobalConfig(config2)\n  );\n  return {\n    typed: dataset.typed,\n    success: !dataset.issues,\n    output: dataset.value,\n    issues: dataset.issues\n  };\n}\n\n// src/methods/safeParse/safeParseAsync.ts\nasync function safeParseAsync(schema, input, config2) {\n  const dataset = await schema._run(\n    { typed: false, value: input },\n    getGlobalConfig(config2)\n  );\n  return {\n    typed: dataset.typed,\n    success: !dataset.issues,\n    output: dataset.value,\n    issues: dataset.issues\n  };\n}\n\n// src/methods/safeParser/safeParser.ts\nfunction safeParser(schema, config2) {\n  const func = (input) => safeParse(schema, input, config2);\n  func.schema = schema;\n  func.config = config2;\n  return func;\n}\n\n// src/methods/safeParser/safeParserAsync.ts\nfunction safeParserAsync(schema, config2) {\n  const func = (input) => safeParseAsync(schema, input, config2);\n  func.schema = schema;\n  func.config = config2;\n  return func;\n}\n\n// src/methods/unwrap/unwrap.ts\nfunction unwrap(schema) {\n  return schema.wrapped;\n}\nexport {\n  BASE64_REGEX,\n  BIC_REGEX,\n  CUID2_REGEX,\n  DECIMAL_REGEX,\n  EMAIL_REGEX,\n  EMOJI_REGEX,\n  HEXADECIMAL_REGEX,\n  HEX_COLOR_REGEX,\n  IMEI_REGEX,\n  IPV4_REGEX,\n  IPV6_REGEX,\n  IP_REGEX,\n  ISO_DATE_REGEX,\n  ISO_DATE_TIME_REGEX,\n  ISO_TIMESTAMP_REGEX,\n  ISO_TIME_REGEX,\n  ISO_TIME_SECOND_REGEX,\n  ISO_WEEK_REGEX,\n  MAC48_REGEX,\n  MAC64_REGEX,\n  MAC_REGEX,\n  OCTAL_REGEX,\n  ULID_REGEX,\n  UUID_REGEX,\n  ValiError,\n  _addIssue,\n  _isLuhnAlgo,\n  _isValidObjectKey,\n  _stringify,\n  any,\n  array,\n  arrayAsync,\n  awaitAsync,\n  base64,\n  bic,\n  bigint,\n  blob,\n  boolean,\n  brand,\n  bytes,\n  check,\n  checkAsync,\n  checkItems,\n  config,\n  creditCard,\n  cuid2,\n  custom,\n  customAsync,\n  date,\n  decimal,\n  deleteGlobalConfig,\n  deleteGlobalMessage,\n  deleteSchemaMessage,\n  deleteSpecificMessage,\n  description,\n  email,\n  emoji,\n  empty,\n  endsWith,\n  entriesFromList,\n  enum_ as enum,\n  enum_,\n  everyItem,\n  excludes,\n  fallback,\n  fallbackAsync,\n  file,\n  filterItems,\n  findItem,\n  finite,\n  flatten,\n  forward,\n  forwardAsync,\n  function_ as function,\n  function_,\n  getDefault,\n  getDefaults,\n  getDefaultsAsync,\n  getDotPath,\n  getFallback,\n  getFallbacks,\n  getFallbacksAsync,\n  getGlobalConfig,\n  getGlobalMessage,\n  getSchemaMessage,\n  getSpecificMessage,\n  hash,\n  hexColor,\n  hexadecimal,\n  imei,\n  includes,\n  instance,\n  integer,\n  intersect,\n  intersectAsync,\n  ip,\n  ipv4,\n  ipv6,\n  is,\n  isOfKind,\n  isOfType,\n  isValiError,\n  isoDate,\n  isoDateTime,\n  isoTime,\n  isoTimeSecond,\n  isoTimestamp,\n  isoWeek,\n  keyof,\n  lazy,\n  lazyAsync,\n  length,\n  literal,\n  looseObject,\n  looseObjectAsync,\n  looseTuple,\n  looseTupleAsync,\n  mac,\n  mac48,\n  mac64,\n  map,\n  mapAsync,\n  mapItems,\n  maxBytes,\n  maxLength,\n  maxSize,\n  maxValue,\n  mimeType,\n  minBytes,\n  minLength,\n  minSize,\n  minValue,\n  multipleOf,\n  nan,\n  never,\n  nonEmpty,\n  nonNullable,\n  nonNullableAsync,\n  nonNullish,\n  nonNullishAsync,\n  nonOptional,\n  nonOptionalAsync,\n  normalize,\n  notBytes,\n  notLength,\n  notSize,\n  notValue,\n  null_ as null,\n  null_,\n  nullable,\n  nullableAsync,\n  nullish,\n  nullishAsync,\n  number,\n  object,\n  objectAsync,\n  objectWithRest,\n  objectWithRestAsync,\n  octal,\n  omit,\n  optional,\n  optionalAsync,\n  parse,\n  parseAsync,\n  parser,\n  parserAsync,\n  partial,\n  partialAsync,\n  partialCheck,\n  partialCheckAsync,\n  pick,\n  picklist,\n  pipe,\n  pipeAsync,\n  promise,\n  rawCheck,\n  rawCheckAsync,\n  rawTransform,\n  rawTransformAsync,\n  readonly,\n  record,\n  recordAsync,\n  reduceItems,\n  regex,\n  required,\n  requiredAsync,\n  safeInteger,\n  safeParse,\n  safeParseAsync,\n  safeParser,\n  safeParserAsync,\n  set,\n  setAsync,\n  setGlobalConfig,\n  setGlobalMessage,\n  setSchemaMessage,\n  setSpecificMessage,\n  size,\n  someItem,\n  sortItems,\n  startsWith,\n  strictObject,\n  strictObjectAsync,\n  strictTuple,\n  strictTupleAsync,\n  string,\n  symbol,\n  toLowerCase,\n  toMaxValue,\n  toMinValue,\n  toUpperCase,\n  transform,\n  transformAsync,\n  trim,\n  trimEnd,\n  trimStart,\n  tuple,\n  tupleAsync,\n  tupleWithRest,\n  tupleWithRestAsync,\n  ulid,\n  undefined_ as undefined,\n  undefined_,\n  union,\n  unionAsync,\n  unknown,\n  unwrap,\n  url,\n  uuid,\n  value,\n  variant,\n  variantAsync,\n  void_ as void,\n  void_\n};\n", "import bs58Check from 'bs58check';\nexport function decodeRaw(buffer, version) {\n    // check version only if defined\n    if (version !== undefined && buffer[0] !== version)\n        throw new Error('Invalid network version');\n    // uncompressed\n    if (buffer.length === 33) {\n        return {\n            version: buffer[0],\n            privateKey: buffer.slice(1, 33),\n            compressed: false\n        };\n    }\n    // invalid length\n    if (buffer.length !== 34)\n        throw new Error('Invalid WIF length');\n    // invalid compression flag\n    if (buffer[33] !== 0x01)\n        throw new Error('Invalid compression flag');\n    return {\n        version: buffer[0],\n        privateKey: buffer.slice(1, 33),\n        compressed: true\n    };\n}\nexport function encodeRaw(version, privateKey, compressed) {\n    if (privateKey.length !== 32)\n        throw new TypeError('Invalid privateKey length');\n    var result = new Uint8Array(compressed ? 34 : 33);\n    var view = new DataView(result.buffer);\n    view.setUint8(0, version);\n    result.set(privateKey, 1);\n    if (compressed) {\n        result[33] = 0x01;\n    }\n    return result;\n}\nexport function decode(str, version) {\n    return decodeRaw(bs58Check.decode(str), version);\n}\nexport function encode(wif) {\n    return bs58Check.encode(encodeRaw(wif.version, wif.privateKey, wif.compressed));\n}\n", "'use strict';\nimport { sha256 } from '@noble/hashes/sha256';\nimport bs58checkBase from './base.js';\n// SHA256(SHA256(buffer))\nfunction sha256x2(buffer) {\n    return sha256(sha256(buffer));\n}\nexport default bs58checkBase(sha256x2);\n", "import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from './_md.js';\nimport { rotr, wrapConstructor } from './utils.js';\n\n// SHA2-256 need to try 2^128 hashes to execute birthday attack.\n// BTC network is doing 2^67 hashes/sec as per early 2023.\n\n// Round constants:\n// first 32 bits of the fractional parts of the cube roots of the first 64 primes 2..311)\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ new Uint32Array([\n  0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n  0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n  0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n  0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n  0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n  0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n  0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n  0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n\n// Initial state:\n// first 32 bits of the fractional parts of the square roots of the first 8 primes 2..19\n// prettier-ignore\nconst SHA256_IV = /* @__PURE__ */ new Uint32Array([\n  0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19\n]);\n\n// Temporary buffer, not used to store anything between runs\n// Named this way because it matches specification.\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nclass SHA256 extends HashMD<SHA256> {\n  // We cannot use array here since array allows indexing by variable\n  // which means optimizer/compiler cannot use registers.\n  A = SHA256_IV[0] | 0;\n  B = SHA256_IV[1] | 0;\n  C = SHA256_IV[2] | 0;\n  D = SHA256_IV[3] | 0;\n  E = SHA256_IV[4] | 0;\n  F = SHA256_IV[5] | 0;\n  G = SHA256_IV[6] | 0;\n  H = SHA256_IV[7] | 0;\n\n  constructor() {\n    super(64, 32, 8, false);\n  }\n  protected get(): [number, number, number, number, number, number, number, number] {\n    const { A, B, C, D, E, F, G, H } = this;\n    return [A, B, C, D, E, F, G, H];\n  }\n  // prettier-ignore\n  protected set(\n    A: number, B: number, C: number, D: number, E: number, F: number, G: number, H: number\n  ) {\n    this.A = A | 0;\n    this.B = B | 0;\n    this.C = C | 0;\n    this.D = D | 0;\n    this.E = E | 0;\n    this.F = F | 0;\n    this.G = G | 0;\n    this.H = H | 0;\n  }\n  protected process(view: DataView, offset: number): void {\n    // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n    for (let i = 0; i < 16; i++, offset += 4) SHA256_W[i] = view.getUint32(offset, false);\n    for (let i = 16; i < 64; i++) {\n      const W15 = SHA256_W[i - 15];\n      const W2 = SHA256_W[i - 2];\n      const s0 = rotr(W15, 7) ^ rotr(W15, 18) ^ (W15 >>> 3);\n      const s1 = rotr(W2, 17) ^ rotr(W2, 19) ^ (W2 >>> 10);\n      SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n    }\n    // Compression function main loop, 64 rounds\n    let { A, B, C, D, E, F, G, H } = this;\n    for (let i = 0; i < 64; i++) {\n      const sigma1 = rotr(E, 6) ^ rotr(E, 11) ^ rotr(E, 25);\n      const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n      const sigma0 = rotr(A, 2) ^ rotr(A, 13) ^ rotr(A, 22);\n      const T2 = (sigma0 + Maj(A, B, C)) | 0;\n      H = G;\n      G = F;\n      F = E;\n      E = (D + T1) | 0;\n      D = C;\n      C = B;\n      B = A;\n      A = (T1 + T2) | 0;\n    }\n    // Add the compressed chunk to the current hash value\n    A = (A + this.A) | 0;\n    B = (B + this.B) | 0;\n    C = (C + this.C) | 0;\n    D = (D + this.D) | 0;\n    E = (E + this.E) | 0;\n    F = (F + this.F) | 0;\n    G = (G + this.G) | 0;\n    H = (H + this.H) | 0;\n    this.set(A, B, C, D, E, F, G, H);\n  }\n  protected roundClean() {\n    SHA256_W.fill(0);\n  }\n  destroy() {\n    this.set(0, 0, 0, 0, 0, 0, 0, 0);\n    this.buffer.fill(0);\n  }\n}\n// Constants from https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf\nclass SHA224 extends SHA256 {\n  A = 0xc1059ed8 | 0;\n  B = 0x367cd507 | 0;\n  C = 0x3070dd17 | 0;\n  D = 0xf70e5939 | 0;\n  E = 0xffc00b31 | 0;\n  F = 0x68581511 | 0;\n  G = 0x64f98fa7 | 0;\n  H = 0xbefa4fa4 | 0;\n  constructor() {\n    super();\n    this.outputLen = 28;\n  }\n}\n\n/**\n * SHA2-256 hash function\n * @param message - data that would be hashed\n */\nexport const sha256 = /* @__PURE__ */ wrapConstructor(() => new SHA256());\nexport const sha224 = /* @__PURE__ */ wrapConstructor(() => new SHA224());\n", "import { exists, output } from './_assert.js';\nimport { Hash, createView, Input, toBytes } from './utils.js';\n\n// Polyfill for Safari 14\nfunction setBigUint64(view: DataView, byteOffset: number, value: bigint, isLE: boolean): void {\n  if (typeof view.setBigUint64 === 'function') return view.setBigUint64(byteOffset, value, isLE);\n  const _32n = BigInt(32);\n  const _u32_max = BigInt(0xffffffff);\n  const wh = Number((value >> _32n) & _u32_max);\n  const wl = Number(value & _u32_max);\n  const h = isLE ? 4 : 0;\n  const l = isLE ? 0 : 4;\n  view.setUint32(byteOffset + h, wh, isLE);\n  view.setUint32(byteOffset + l, wl, isLE);\n}\n\n// Choice: a ? b : c\nexport const Chi = (a: number, b: number, c: number) => (a & b) ^ (~a & c);\n// Majority function, true if any two inpust is true\nexport const Maj = (a: number, b: number, c: number) => (a & b) ^ (a & c) ^ (b & c);\n\n/**\n * Merkle-<PERSON>gard hash construction base class.\n * Could be used to create MD5, RIPEMD, SHA1, SHA2.\n */\nexport abstract class HashMD<T extends HashMD<T>> extends Hash<T> {\n  protected abstract process(buf: DataView, offset: number): void;\n  protected abstract get(): number[];\n  protected abstract set(...args: number[]): void;\n  abstract destroy(): void;\n  protected abstract roundClean(): void;\n  // For partial updates less than block size\n  protected buffer: Uint8Array;\n  protected view: DataView;\n  protected finished = false;\n  protected length = 0;\n  protected pos = 0;\n  protected destroyed = false;\n\n  constructor(\n    readonly blockLen: number,\n    public outputLen: number,\n    readonly padOffset: number,\n    readonly isLE: boolean\n  ) {\n    super();\n    this.buffer = new Uint8Array(blockLen);\n    this.view = createView(this.buffer);\n  }\n  update(data: Input): this {\n    exists(this);\n    const { view, buffer, blockLen } = this;\n    data = toBytes(data);\n    const len = data.length;\n    for (let pos = 0; pos < len; ) {\n      const take = Math.min(blockLen - this.pos, len - pos);\n      // Fast path: we have at least one block in input, cast it to view and process\n      if (take === blockLen) {\n        const dataView = createView(data);\n        for (; blockLen <= len - pos; pos += blockLen) this.process(dataView, pos);\n        continue;\n      }\n      buffer.set(data.subarray(pos, pos + take), this.pos);\n      this.pos += take;\n      pos += take;\n      if (this.pos === blockLen) {\n        this.process(view, 0);\n        this.pos = 0;\n      }\n    }\n    this.length += data.length;\n    this.roundClean();\n    return this;\n  }\n  digestInto(out: Uint8Array) {\n    exists(this);\n    output(out, this);\n    this.finished = true;\n    // Padding\n    // We can avoid allocation of buffer for padding completely if it\n    // was previously not allocated here. But it won't change performance.\n    const { buffer, view, blockLen, isLE } = this;\n    let { pos } = this;\n    // append the bit '1' to the message\n    buffer[pos++] = 0b10000000;\n    this.buffer.subarray(pos).fill(0);\n    // we have less than padOffset left in buffer, so we cannot put length in\n    // current block, need process it and pad again\n    if (this.padOffset > blockLen - pos) {\n      this.process(view, 0);\n      pos = 0;\n    }\n    // Pad until full block byte with zeros\n    for (let i = pos; i < blockLen; i++) buffer[i] = 0;\n    // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n    // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n    // So we just write lowest 64 bits of that value.\n    setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n    this.process(view, 0);\n    const oview = createView(out);\n    const len = this.outputLen;\n    // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n    if (len % 4) throw new Error('_sha2: outputLen should be aligned to 32bit');\n    const outLen = len / 4;\n    const state = this.get();\n    if (outLen > state.length) throw new Error('_sha2: outputLen bigger than state');\n    for (let i = 0; i < outLen; i++) oview.setUint32(4 * i, state[i], isLE);\n  }\n  digest() {\n    const { buffer, outputLen } = this;\n    this.digestInto(buffer);\n    const res = buffer.slice(0, outputLen);\n    this.destroy();\n    return res;\n  }\n  _cloneInto(to?: T): T {\n    to ||= new (this.constructor as any)() as T;\n    to.set(...this.get());\n    const { blockLen, buffer, length, finished, destroyed, pos } = this;\n    to.length = length;\n    to.pos = pos;\n    to.finished = finished;\n    to.destroyed = destroyed;\n    if (length % blockLen) to.buffer.set(buffer);\n    return to;\n  }\n}\n", "function number(n: number) {\n  if (!Number.isSafeInteger(n) || n < 0) throw new Error(`positive integer expected, not ${n}`);\n}\n\nfunction bool(b: boolean) {\n  if (typeof b !== 'boolean') throw new Error(`boolean expected, not ${b}`);\n}\n\n// copied from utils\nexport function isBytes(a: unknown): a is Uint8Array {\n  return (\n    a instanceof Uint8Array ||\n    (a != null && typeof a === 'object' && a.constructor.name === 'Uint8Array')\n  );\n}\n\nfunction bytes(b: Uint8Array | undefined, ...lengths: number[]) {\n  if (!isBytes(b)) throw new Error('Uint8Array expected');\n  if (lengths.length > 0 && !lengths.includes(b.length))\n    throw new Error(`Uint8Array expected of length ${lengths}, not of length=${b.length}`);\n}\n\ntype Hash = {\n  (data: Uint8Array): Uint8Array;\n  blockLen: number;\n  outputLen: number;\n  create: any;\n};\nfunction hash(h: Hash) {\n  if (typeof h !== 'function' || typeof h.create !== 'function')\n    throw new Error('Hash should be wrapped by utils.wrapConstructor');\n  number(h.outputLen);\n  number(h.blockLen);\n}\n\nfunction exists(instance: any, checkFinished = true) {\n  if (instance.destroyed) throw new Error('Hash instance has been destroyed');\n  if (checkFinished && instance.finished) throw new Error('Hash#digest() has already been called');\n}\nfunction output(out: any, instance: any) {\n  bytes(out);\n  const min = instance.outputLen;\n  if (out.length < min) {\n    throw new Error(`digestInto() expects output buffer of length at least ${min}`);\n  }\n}\n\nexport { number, bool, bytes, hash, exists, output };\n\nconst assert = { number, bool, bytes, hash, exists, output };\nexport default assert;\n", "/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\nimport { bytes as abytes } from './_assert.js';\n// export { isBytes } from './_assert.js';\n// We can't reuse isBytes from _assert, because somehow this causes huge perf issues\nexport function isBytes(a: unknown): a is Uint8Array {\n  return (\n    a instanceof Uint8Array ||\n    (a != null && typeof a === 'object' && a.constructor.name === 'Uint8Array')\n  );\n}\n\n// prettier-ignore\nexport type TypedArray = Int8Array | Uint8ClampedArray | Uint8Array |\n  Uint16Array | Int16Array | Uint32Array | Int32Array;\n\n// Cast array to different type\nexport const u8 = (arr: TypedArray) => new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\nexport const u32 = (arr: TypedArray) =>\n  new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n\n// Cast array to view\nexport const createView = (arr: TypedArray) =>\n  new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n\n// The rotate right (circular right shift) operation for uint32\nexport const rotr = (word: number, shift: number) => (word << (32 - shift)) | (word >>> shift);\n// The rotate left (circular left shift) operation for uint32\nexport const rotl = (word: number, shift: number) =>\n  (word << shift) | ((word >>> (32 - shift)) >>> 0);\n\nexport const isLE = new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44;\n// The byte swap operation for uint32\nexport const byteSwap = (word: number) =>\n  ((word << 24) & 0xff000000) |\n  ((word << 8) & 0xff0000) |\n  ((word >>> 8) & 0xff00) |\n  ((word >>> 24) & 0xff);\n// Conditionally byte swap if on a big-endian platform\nexport const byteSwapIfBE = isLE ? (n: number) => n : (n: number) => byteSwap(n);\n\n// In place byte swap for Uint32Array\nexport function byteSwap32(arr: Uint32Array) {\n  for (let i = 0; i < arr.length; i++) {\n    arr[i] = byteSwap(arr[i]);\n  }\n}\n\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) =>\n  i.toString(16).padStart(2, '0')\n);\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes: Uint8Array): string {\n  abytes(bytes);\n  // pre-caching improves the speed 6x\n  let hex = '';\n  for (let i = 0; i < bytes.length; i++) {\n    hex += hexes[bytes[i]];\n  }\n  return hex;\n}\n\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, _A: 65, _F: 70, _a: 97, _f: 102 } as const;\nfunction asciiToBase16(char: number): number | undefined {\n  if (char >= asciis._0 && char <= asciis._9) return char - asciis._0;\n  if (char >= asciis._A && char <= asciis._F) return char - (asciis._A - 10);\n  if (char >= asciis._a && char <= asciis._f) return char - (asciis._a - 10);\n  return;\n}\n\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex: string): Uint8Array {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  const hl = hex.length;\n  const al = hl / 2;\n  if (hl % 2) throw new Error('padded hex string expected, got unpadded hex of length ' + hl);\n  const array = new Uint8Array(al);\n  for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n    const n1 = asciiToBase16(hex.charCodeAt(hi));\n    const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n    if (n1 === undefined || n2 === undefined) {\n      const char = hex[hi] + hex[hi + 1];\n      throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n    }\n    array[ai] = n1 * 16 + n2;\n  }\n  return array;\n}\n\n// There is no setImmediate in browser and setTimeout is slow.\n// call of async fn will return Promise, which will be fullfiled only on\n// next scheduler queue processing step and this is exactly what we need.\nexport const nextTick = async () => {};\n\n// Returns control to thread each 'tick' ms to avoid blocking\nexport async function asyncLoop(iters: number, tick: number, cb: (i: number) => void) {\n  let ts = Date.now();\n  for (let i = 0; i < iters; i++) {\n    cb(i);\n    // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n    const diff = Date.now() - ts;\n    if (diff >= 0 && diff < tick) continue;\n    await nextTick();\n    ts += diff;\n  }\n}\n\n// Global symbols in both browsers and Node.js since v11\n// See https://github.com/microsoft/TypeScript/issues/31535\ndeclare const TextEncoder: any;\n\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str: string): Uint8Array {\n  if (typeof str !== 'string') throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n  return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n\nexport type Input = Uint8Array | string;\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data: Input): Uint8Array {\n  if (typeof data === 'string') data = utf8ToBytes(data);\n  abytes(data);\n  return data;\n}\n\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays: Uint8Array[]): Uint8Array {\n  let sum = 0;\n  for (let i = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    abytes(a);\n    sum += a.length;\n  }\n  const res = new Uint8Array(sum);\n  for (let i = 0, pad = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    res.set(a, pad);\n    pad += a.length;\n  }\n  return res;\n}\n\n// For runtime check if class implements interface\nexport abstract class Hash<T extends Hash<T>> {\n  abstract blockLen: number; // Bytes per block\n  abstract outputLen: number; // Bytes in output\n  abstract update(buf: Input): this;\n  // Writes digest into buf\n  abstract digestInto(buf: Uint8Array): void;\n  abstract digest(): Uint8Array;\n  /**\n   * Resets internal state. Makes Hash instance unusable.\n   * Reset is impossible for keyed hashes if key is consumed into state. If digest is not consumed\n   * by user, they will need to manually call `destroy()` when zeroing is necessary.\n   */\n  abstract destroy(): void;\n  /**\n   * Clones hash instance. Unsafe: doesn't check whether `to` is valid. Can be used as `clone()`\n   * when no options are passed.\n   * Reasons to use `_cloneInto` instead of clone: 1) performance 2) reuse instance => all internal\n   * buffers are overwritten => causes buffer overwrite which is used for digest in some cases.\n   * There are no guarantees for clean-up because it's impossible in JS.\n   */\n  abstract _cloneInto(to?: T): T;\n  // Safe version that clones internal state\n  clone(): T {\n    return this._cloneInto();\n  }\n}\n\n/**\n * XOF: streaming API to read digest in chunks.\n * Same as 'squeeze' in keccak/k12 and 'seek' in blake3, but more generic name.\n * When hash used in XOF mode it is up to user to call '.destroy' afterwards, since we cannot\n * destroy state, next call can require more bytes.\n */\nexport type HashXOF<T extends Hash<T>> = Hash<T> & {\n  xof(bytes: number): Uint8Array; // Read 'bytes' bytes from digest stream\n  xofInto(buf: Uint8Array): Uint8Array; // read buf.length bytes from digest stream into buf\n};\n\nconst toStr = {}.toString;\ntype EmptyObj = {};\nexport function checkOpts<T1 extends EmptyObj, T2 extends EmptyObj>(\n  defaults: T1,\n  opts?: T2\n): T1 & T2 {\n  if (opts !== undefined && toStr.call(opts) !== '[object Object]')\n    throw new Error('Options should be object or undefined');\n  const merged = Object.assign(defaults, opts);\n  return merged as T1 & T2;\n}\n\nexport type CHash = ReturnType<typeof wrapConstructor>;\n\nexport function wrapConstructor<T extends Hash<T>>(hashCons: () => Hash<T>) {\n  const hashC = (msg: Input): Uint8Array => hashCons().update(toBytes(msg)).digest();\n  const tmp = hashCons();\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = () => hashCons();\n  return hashC;\n}\n\nexport function wrapConstructorWithOpts<H extends Hash<H>, T extends Object>(\n  hashCons: (opts?: T) => Hash<H>\n) {\n  const hashC = (msg: Input, opts?: T): Uint8Array => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({} as T);\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = (opts: T) => hashCons(opts);\n  return hashC;\n}\n\nexport function wrapXOFConstructorWithOpts<H extends HashXOF<H>, T extends Object>(\n  hashCons: (opts?: T) => HashXOF<H>\n) {\n  const hashC = (msg: Input, opts?: T): Uint8Array => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({} as T);\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = (opts: T) => hashCons(opts);\n  return hashC;\n}\n\n/**\n * Secure PRNG. Uses `crypto.getRandomValues`, which defers to OS.\n */\nexport function randomBytes(bytesLength = 32): Uint8Array {\n  if (crypto && typeof crypto.getRandomValues === 'function') {\n    return crypto.getRandomValues(new Uint8Array(bytesLength));\n  }\n  throw new Error('crypto.getRandomValues must be defined');\n}\n", "// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// See utils.ts for details.\ndeclare const globalThis: Record<string, any> | undefined;\nexport const crypto =\n  typeof globalThis === 'object' && 'crypto' in globalThis ? globalThis.crypto : undefined;\n", "'use strict';\nimport base58 from 'bs58';\nexport default function (checksumFn) {\n    // Encode a buffer as a base58-check encoded string\n    function encode(payload) {\n        var payloadU8 = Uint8Array.from(payload);\n        var checksum = checksumFn(payloadU8);\n        var length = payloadU8.length + 4;\n        var both = new Uint8Array(length);\n        both.set(payloadU8, 0);\n        both.set(checksum.subarray(0, 4), payloadU8.length);\n        return base58.encode(both);\n    }\n    function decodeRaw(buffer) {\n        var payload = buffer.slice(0, -4);\n        var checksum = buffer.slice(-4);\n        var newChecksum = checksumFn(payload);\n        // eslint-disable-next-line\n        if (checksum[0] ^ newChecksum[0] |\n            checksum[1] ^ newChecksum[1] |\n            checksum[2] ^ newChecksum[2] |\n            checksum[3] ^ newChecksum[3])\n            return;\n        return payload;\n    }\n    // Decode a base58-check encoded string to a buffer, no result if checksum is wrong\n    function decodeUnsafe(str) {\n        var buffer = base58.decodeUnsafe(str);\n        if (buffer == null)\n            return;\n        return decodeRaw(buffer);\n    }\n    function decode(str) {\n        var buffer = base58.decode(str);\n        var payload = decodeRaw(buffer);\n        if (payload == null)\n            throw new Error('Invalid checksum');\n        return payload;\n    }\n    return {\n        encode: encode,\n        decode: decode,\n        decodeUnsafe: decodeUnsafe\n    };\n}\n", "import basex from 'base-x';\nvar ALPHABET = '**********************************************************';\nexport default basex(ALPHABET);\n", "// base-x encoding / decoding\n// Copyright (c) 2018 base-x contributors\n// Copyright (c) 2014-2018 The Bitcoin Core developers (base58.cpp)\n// Distributed under the MIT software license, see the accompanying\n// file LICENSE or http://www.opensource.org/licenses/mit-license.php.\nfunction base (ALPHABET) {\n  if (ALPHABET.length >= 255) { throw new TypeError('Alphabet too long') }\n  const BASE_MAP = new Uint8Array(256)\n  for (let j = 0; j < BASE_MAP.length; j++) {\n    BASE_MAP[j] = 255\n  }\n  for (let i = 0; i < ALPHABET.length; i++) {\n    const x = ALPHABET.charAt(i)\n    const xc = x.charCodeAt(0)\n    if (BASE_MAP[xc] !== 255) { throw new TypeError(x + ' is ambiguous') }\n    BASE_MAP[xc] = i\n  }\n  const BASE = ALPHABET.length\n  const LEADER = ALPHABET.charAt(0)\n  const FACTOR = Math.log(BASE) / Math.log(256) // log(BASE) / log(256), rounded up\n  const iFACTOR = Math.log(256) / Math.log(BASE) // log(256) / log(BASE), rounded up\n  function encode (source) {\n    // eslint-disable-next-line no-empty\n    if (source instanceof Uint8Array) { } else if (ArrayBuffer.isView(source)) {\n      source = new Uint8Array(source.buffer, source.byteOffset, source.byteLength)\n    } else if (Array.isArray(source)) {\n      source = Uint8Array.from(source)\n    }\n    if (!(source instanceof Uint8Array)) { throw new TypeError('Expected Uint8Array') }\n    if (source.length === 0) { return '' }\n    // Skip & count leading zeroes.\n    let zeroes = 0\n    let length = 0\n    let pbegin = 0\n    const pend = source.length\n    while (pbegin !== pend && source[pbegin] === 0) {\n      pbegin++\n      zeroes++\n    }\n    // Allocate enough space in big-endian base58 representation.\n    const size = ((pend - pbegin) * iFACTOR + 1) >>> 0\n    const b58 = new Uint8Array(size)\n    // Process the bytes.\n    while (pbegin !== pend) {\n      let carry = source[pbegin]\n      // Apply \"b58 = b58 * 256 + ch\".\n      let i = 0\n      for (let it1 = size - 1; (carry !== 0 || i < length) && (it1 !== -1); it1--, i++) {\n        carry += (256 * b58[it1]) >>> 0\n        b58[it1] = (carry % BASE) >>> 0\n        carry = (carry / BASE) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      pbegin++\n    }\n    // Skip leading zeroes in base58 result.\n    let it2 = size - length\n    while (it2 !== size && b58[it2] === 0) {\n      it2++\n    }\n    // Translate the result into a string.\n    let str = LEADER.repeat(zeroes)\n    for (; it2 < size; ++it2) { str += ALPHABET.charAt(b58[it2]) }\n    return str\n  }\n  function decodeUnsafe (source) {\n    if (typeof source !== 'string') { throw new TypeError('Expected String') }\n    if (source.length === 0) { return new Uint8Array() }\n    let psz = 0\n    // Skip and count leading '1's.\n    let zeroes = 0\n    let length = 0\n    while (source[psz] === LEADER) {\n      zeroes++\n      psz++\n    }\n    // Allocate enough space in big-endian base256 representation.\n    const size = (((source.length - psz) * FACTOR) + 1) >>> 0 // log(58) / log(256), rounded up.\n    const b256 = new Uint8Array(size)\n    // Process the characters.\n    while (source[psz]) {\n      // Decode character\n      let carry = BASE_MAP[source.charCodeAt(psz)]\n      // Invalid character\n      if (carry === 255) { return }\n      let i = 0\n      for (let it3 = size - 1; (carry !== 0 || i < length) && (it3 !== -1); it3--, i++) {\n        carry += (BASE * b256[it3]) >>> 0\n        b256[it3] = (carry % 256) >>> 0\n        carry = (carry / 256) >>> 0\n      }\n      if (carry !== 0) { throw new Error('Non-zero carry') }\n      length = i\n      psz++\n    }\n    // Skip leading zeroes in b256.\n    let it4 = size - length\n    while (it4 !== size && b256[it4] === 0) {\n      it4++\n    }\n    const vch = new Uint8Array(zeroes + (size - it4))\n    let j = zeroes\n    while (it4 !== size) {\n      vch[j++] = b256[it4++]\n    }\n    return vch\n  }\n  function decode (string) {\n    const buffer = decodeUnsafe(string)\n    if (buffer) { return buffer }\n    throw new Error('Non-base' + BASE + ' character')\n  }\n  return {\n    encode,\n    decodeUnsafe,\n    decode\n  }\n}\nexport default base\n", "const h = (hex) => Buffer.from(hex, 'hex');\nexport function testEcc(ecc) {\n  assert(\n    ecc.isPoint(\n      h('0279be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798'),\n    ),\n  );\n  assert(\n    !ecc.isPoint(\n      h('030000000000000000000000000000000000000000000000000000000000000005'),\n    ),\n  );\n  assert(\n    ecc.isPrivate(\n      h('79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798'),\n    ),\n  );\n  // order - 1\n  assert(\n    ecc.isPrivate(\n      h('fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140'),\n    ),\n  );\n  // 0\n  assert(\n    !ecc.isPrivate(\n      h('0000000000000000000000000000000000000000000000000000000000000000'),\n    ),\n  );\n  // order\n  assert(\n    !ecc.isPrivate(\n      h('fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141'),\n    ),\n  );\n  // order + 1\n  assert(\n    !ecc.isPrivate(\n      h('fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142'),\n    ),\n  );\n  // 1 + 0 == 1\n  assert(\n    Buffer.from(\n      ecc.privateAdd(\n        h('0000000000000000000000000000000000000000000000000000000000000001'),\n        h('0000000000000000000000000000000000000000000000000000000000000000'),\n      ),\n    ).equals(\n      h('0000000000000000000000000000000000000000000000000000000000000001'),\n    ),\n  );\n  // -3 + 3 == 0\n  assert(\n    ecc.privateAdd(\n      h('fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd036413e'),\n      h('0000000000000000000000000000000000000000000000000000000000000003'),\n    ) === null,\n  );\n  assert(\n    Buffer.from(\n      ecc.privateAdd(\n        h('e211078564db65c3ce7704f08262b1f38f1ef412ad15b5ac2d76657a63b2c500'),\n        h('b51fbb69051255d1becbd683de5848242a89c229348dd72896a87ada94ae8665'),\n      ),\n    ).equals(\n      h('9730c2ee69edbb958d42db7460bafa18fef9d955325aec99044c81c8282b0a24'),\n    ),\n  );\n  assert(\n    Buffer.from(\n      ecc.privateNegate(\n        h('0000000000000000000000000000000000000000000000000000000000000001'),\n      ),\n    ).equals(\n      h('fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140'),\n    ),\n  );\n  assert(\n    Buffer.from(\n      ecc.privateNegate(\n        h('fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd036413e'),\n      ),\n    ).equals(\n      h('0000000000000000000000000000000000000000000000000000000000000003'),\n    ),\n  );\n  assert(\n    Buffer.from(\n      ecc.privateNegate(\n        h('b1121e4088a66a28f5b6b0f5844943ecd9f610196d7bb83b25214b60452c09af'),\n      ),\n    ).equals(\n      h('4eede1bf775995d70a494f0a7bb6bc11e0b8cccd41cce8009ab1132c8b0a3792'),\n    ),\n  );\n  assert(\n    Buffer.from(\n      ecc.pointCompress(\n        h(\n          '0479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8',\n        ),\n        true,\n      ),\n    ).equals(\n      h('0279be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798'),\n    ),\n  );\n  assert(\n    Buffer.from(\n      ecc.pointCompress(\n        h(\n          '0479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8',\n        ),\n        false,\n      ),\n    ).equals(\n      h(\n        '0479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8',\n      ),\n    ),\n  );\n  assert(\n    Buffer.from(\n      ecc.pointCompress(\n        h('0279be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798'),\n        true,\n      ),\n    ).equals(\n      h('0279be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798'),\n    ),\n  );\n  assert(\n    Buffer.from(\n      ecc.pointCompress(\n        h('0279be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798'),\n        false,\n      ),\n    ).equals(\n      h(\n        '0479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8',\n      ),\n    ),\n  );\n  assert(\n    Buffer.from(\n      ecc.pointFromScalar(\n        h('b1121e4088a66a28f5b6b0f5844943ecd9f610196d7bb83b25214b60452c09af'),\n      ),\n    ).equals(\n      h('02b07ba9dca9523b7ef4bd97703d43d20399eb698e194704791a25ce77a400df99'),\n    ),\n  );\n  assert(\n    ecc.xOnlyPointAddTweak(\n      h('79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798'),\n      h('fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140'),\n    ) === null,\n  );\n  let xOnlyRes = ecc.xOnlyPointAddTweak(\n    h('1617d38ed8d8657da4d4761e8057bc396ea9e4b9d29776d4be096016dbd2509b'),\n    h('a8397a935f0dfceba6ba9618f6451ef4d80637abf4e6af2669fbc9de6a8fd2ac'),\n  );\n  assert(\n    Buffer.from(xOnlyRes.xOnlyPubkey).equals(\n      h('e478f99dab91052ab39a33ea35fd5e6e4933f4d28023cd597c9a1f6760346adf'),\n    ) && xOnlyRes.parity === 1,\n  );\n  xOnlyRes = ecc.xOnlyPointAddTweak(\n    h('2c0b7cf95324a07d05398b240174dc0c2be444d96b159aa6c7f7b1e668680991'),\n    h('823c3cd2142744b075a87eade7e1b8678ba308d566226a0056ca2b7a76f86b47'),\n  );\n  assert(\n    Buffer.from(xOnlyRes.xOnlyPubkey).equals(\n      h('9534f8dc8c6deda2dc007655981c78b49c5d96c778fbf363462a11ec9dfd948c'),\n    ) && xOnlyRes.parity === 0,\n  );\n  assert(\n    Buffer.from(\n      ecc.sign(\n        h('5e9f0a0d593efdcf78ac923bc3313e4e7d408d574354ee2b3288c0da9fbba6ed'),\n        h('fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140'),\n      ),\n    ).equals(\n      h(\n        '54c4a33c6423d689378f160a7ff8b61330444abb58fb470f96ea16d99d4a2fed07082304410efa6b2943111b6a4e0aaa7b7db55a07e9861d1fb3cb1f421044a5',\n      ),\n    ),\n  );\n  assert(\n    ecc.verify(\n      h('5e9f0a0d593efdcf78ac923bc3313e4e7d408d574354ee2b3288c0da9fbba6ed'),\n      h('0379be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798'),\n      h(\n        '54c4a33c6423d689378f160a7ff8b61330444abb58fb470f96ea16d99d4a2fed07082304410efa6b2943111b6a4e0aaa7b7db55a07e9861d1fb3cb1f421044a5',\n      ),\n    ),\n  );\n  if (ecc.signSchnorr) {\n    assert(\n      Buffer.from(\n        ecc.signSchnorr(\n          h('7e2d58d8b3bcdf1abadec7829054f90dda9805aab56c77333024b9d0a508b75c'),\n          h('c90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b14e5c9'),\n          h('c87aa53824b4d7ae2eb035a2b5bbbccc080e76cdc6d1692c4b0b62d798e6d906'),\n        ),\n      ).equals(\n        h(\n          '5831aaeed7b44bb74e5eab94ba9d4294c49bcf2a60728d8b4c200f50dd313c1bab745879a5ad954a72c45a91c3a51d3c7adea98d82f8481e0e1e03674a6f3fb7',\n        ),\n      ),\n    );\n  }\n  if (ecc.verifySchnorr) {\n    assert(\n      ecc.verifySchnorr(\n        h('7e2d58d8b3bcdf1abadec7829054f90dda9805aab56c77333024b9d0a508b75c'),\n        h('dd308afec5777e13121fa72b9cc1b7cc0139715309b086c960e18fd969774eb8'),\n        h(\n          '5831aaeed7b44bb74e5eab94ba9d4294c49bcf2a60728d8b4c200f50dd313c1bab745879a5ad954a72c45a91c3a51d3c7adea98d82f8481e0e1e03674a6f3fb7',\n        ),\n      ),\n    );\n  }\n}\nfunction assert(bool) {\n  if (!bool) throw new Error('ecc library invalid');\n}\n", "const HEX_STRINGS = \"0123456789abcdefABCDEF\";\nconst HEX_CODES = HEX_STRINGS.split(\"\").map((c) => c.codePointAt(0));\nconst HEX_CODEPOINTS = Array(256)\n    .fill(true)\n    .map((_, i) => {\n    const s = String.fromCodePoint(i);\n    const index = HEX_STRINGS.indexOf(s);\n    // ABCDEF will use 10 - 15\n    return index < 0 ? undefined : index < 16 ? index : index - 6;\n});\nconst ENCODER = new TextEncoder();\nconst DECODER = new TextDecoder();\nexport function toUtf8(bytes) {\n    return DECODER.decode(bytes);\n}\nexport function fromUtf8(s) {\n    return ENCODER.encode(s);\n}\nexport function concat(arrays) {\n    const totalLength = arrays.reduce((a, b) => a + b.length, 0);\n    const result = new Uint8Array(totalLength);\n    let offset = 0;\n    for (const array of arrays) {\n        result.set(array, offset);\n        offset += array.length;\n    }\n    return result;\n}\n// There are two implementations.\n// One optimizes for length of the bytes, and uses TextDecoder.\n// One optimizes for iteration count, and appends strings.\n// This removes the overhead of TextDecoder.\nexport function toHex(bytes) {\n    const b = bytes || new Uint8Array();\n    return b.length > 512 ? _toHexLengthPerf(b) : _toHexIterPerf(b);\n}\nfunction _toHexIterPerf(bytes) {\n    let s = \"\";\n    for (let i = 0; i < bytes.length; ++i) {\n        s += HEX_STRINGS[HEX_CODEPOINTS[HEX_CODES[bytes[i] >> 4]]];\n        s += HEX_STRINGS[HEX_CODEPOINTS[HEX_CODES[bytes[i] & 15]]];\n    }\n    return s;\n}\nfunction _toHexLengthPerf(bytes) {\n    const hexBytes = new Uint8Array(bytes.length * 2);\n    for (let i = 0; i < bytes.length; ++i) {\n        hexBytes[i * 2] = HEX_CODES[bytes[i] >> 4];\n        hexBytes[i * 2 + 1] = HEX_CODES[bytes[i] & 15];\n    }\n    return DECODER.decode(hexBytes);\n}\n// Mimics Buffer.from(x, 'hex') logic\n// Stops on first non-hex string and returns\n// https://github.com/nodejs/node/blob/v14.18.1/src/string_bytes.cc#L246-L261\nexport function fromHex(hexString) {\n    const hexBytes = ENCODER.encode(hexString || \"\");\n    const resultBytes = new Uint8Array(Math.floor(hexBytes.length / 2));\n    let i;\n    for (i = 0; i < resultBytes.length; i++) {\n        const a = HEX_CODEPOINTS[hexBytes[i * 2]];\n        const b = HEX_CODEPOINTS[hexBytes[i * 2 + 1]];\n        if (a === undefined || b === undefined) {\n            break;\n        }\n        resultBytes[i] = (a << 4) | b;\n    }\n    return i === resultBytes.length ? resultBytes : resultBytes.slice(0, i);\n}\nexport function toBase64(bytes) {\n    return btoa(String.fromCharCode(...bytes));\n}\nexport function fromBase64(base64) {\n    const binaryString = atob(base64);\n    const bytes = new Uint8Array(binaryString.length);\n    for (let i = 0; i < binaryString.length; i++) {\n        bytes[i] = binaryString.charCodeAt(i);\n    }\n    return bytes;\n}\n// Same behavior as Buffer.compare()\nexport function compare(v1, v2) {\n    const minLength = Math.min(v1.length, v2.length);\n    for (let i = 0; i < minLength; ++i) {\n        if (v1[i] !== v2[i]) {\n            return v1[i] < v2[i] ? -1 : 1;\n        }\n    }\n    return v1.length === v2.length ? 0 : v1.length > v2.length ? 1 : -1;\n}\nexport function writeUInt8(buffer, offset, value) {\n    if (offset + 1 > buffer.length) {\n        throw new Error(\"Offset is outside the bounds of Uint8Array\");\n    }\n    if (value > 0xff) {\n        throw new Error(`The value of \"value\" is out of range. It must be >= 0 and <= ${0xff}. Received ${value}`);\n    }\n    buffer[offset] = value;\n}\nexport function writeUInt16(buffer, offset, value, littleEndian) {\n    if (offset + 2 > buffer.length) {\n        throw new Error(\"Offset is outside the bounds of Uint8Array\");\n    }\n    littleEndian = littleEndian.toUpperCase();\n    if (value > 0xffff) {\n        throw new Error(`The value of \"value\" is out of range. It must be >= 0 and <= ${0xffff}. Received ${value}`);\n    }\n    if (littleEndian === \"LE\") {\n        buffer[offset] = value & 0xff;\n        buffer[offset + 1] = (value >> 8) & 0xff;\n    }\n    else {\n        buffer[offset] = (value >> 8) & 0xff;\n        buffer[offset + 1] = value & 0xff;\n    }\n}\nexport function writeUInt32(buffer, offset, value, littleEndian) {\n    if (offset + 4 > buffer.length) {\n        throw new Error(\"Offset is outside the bounds of Uint8Array\");\n    }\n    littleEndian = littleEndian.toUpperCase();\n    if (value > 0xffffffff) {\n        throw new Error(`The value of \"value\" is out of range. It must be >= 0 and <= ${0xffffffff}. Received ${value}`);\n    }\n    if (littleEndian === \"LE\") {\n        buffer[offset] = value & 0xff;\n        buffer[offset + 1] = (value >> 8) & 0xff;\n        buffer[offset + 2] = (value >> 16) & 0xff;\n        buffer[offset + 3] = (value >> 24) & 0xff;\n    }\n    else {\n        buffer[offset] = (value >> 24) & 0xff;\n        buffer[offset + 1] = (value >> 16) & 0xff;\n        buffer[offset + 2] = (value >> 8) & 0xff;\n        buffer[offset + 3] = value & 0xff;\n    }\n}\nexport function writeUInt64(buffer, offset, value, littleEndian) {\n    if (offset + 8 > buffer.length) {\n        throw new Error(\"Offset is outside the bounds of Uint8Array\");\n    }\n    littleEndian = littleEndian.toUpperCase();\n    if (value > 0xffffffffffffffffn) {\n        throw new Error(`The value of \"value\" is out of range. It must be >= 0 and <= ${0xffffffffffffffffn}. Received ${value}`);\n    }\n    if (littleEndian === \"LE\") {\n        buffer[offset] = Number(value & 0xffn);\n        buffer[offset + 1] = Number((value >> 8n) & 0xffn);\n        buffer[offset + 2] = Number((value >> 16n) & 0xffn);\n        buffer[offset + 3] = Number((value >> 24n) & 0xffn);\n        buffer[offset + 4] = Number((value >> 32n) & 0xffn);\n        buffer[offset + 5] = Number((value >> 40n) & 0xffn);\n        buffer[offset + 6] = Number((value >> 48n) & 0xffn);\n        buffer[offset + 7] = Number((value >> 56n) & 0xffn);\n    }\n    else {\n        buffer[offset] = Number((value >> 56n) & 0xffn);\n        buffer[offset + 1] = Number((value >> 48n) & 0xffn);\n        buffer[offset + 2] = Number((value >> 40n) & 0xffn);\n        buffer[offset + 3] = Number((value >> 32n) & 0xffn);\n        buffer[offset + 4] = Number((value >> 24n) & 0xffn);\n        buffer[offset + 5] = Number((value >> 16n) & 0xffn);\n        buffer[offset + 6] = Number((value >> 8n) & 0xffn);\n        buffer[offset + 7] = Number(value & 0xffn);\n    }\n}\nexport function readUInt8(buffer, offset) {\n    if (offset + 1 > buffer.length) {\n        throw new Error(\"Offset is outside the bounds of Uint8Array\");\n    }\n    return buffer[offset];\n}\nexport function readUInt16(buffer, offset, littleEndian) {\n    if (offset + 2 > buffer.length) {\n        throw new Error(\"Offset is outside the bounds of Uint8Array\");\n    }\n    littleEndian = littleEndian.toUpperCase();\n    if (littleEndian === \"LE\") {\n        let num = 0;\n        num = (num << 8) + buffer[offset + 1];\n        num = (num << 8) + buffer[offset];\n        return num;\n    }\n    else {\n        let num = 0;\n        num = (num << 8) + buffer[offset];\n        num = (num << 8) + buffer[offset + 1];\n        return num;\n    }\n}\nexport function readUInt32(buffer, offset, littleEndian) {\n    if (offset + 4 > buffer.length) {\n        throw new Error(\"Offset is outside the bounds of Uint8Array\");\n    }\n    littleEndian = littleEndian.toUpperCase();\n    if (littleEndian === \"LE\") {\n        let num = 0;\n        num = ((num << 8) + buffer[offset + 3]) >>> 0;\n        num = ((num << 8) + buffer[offset + 2]) >>> 0;\n        num = ((num << 8) + buffer[offset + 1]) >>> 0;\n        num = ((num << 8) + buffer[offset]) >>> 0;\n        return num;\n    }\n    else {\n        let num = 0;\n        num = ((num << 8) + buffer[offset]) >>> 0;\n        num = ((num << 8) + buffer[offset + 1]) >>> 0;\n        num = ((num << 8) + buffer[offset + 2]) >>> 0;\n        num = ((num << 8) + buffer[offset + 3]) >>> 0;\n        return num;\n    }\n}\nexport function readUInt64(buffer, offset, littleEndian) {\n    if (offset + 8 > buffer.length) {\n        throw new Error(\"Offset is outside the bounds of Uint8Array\");\n    }\n    littleEndian = littleEndian.toUpperCase();\n    if (littleEndian === \"LE\") {\n        let num = 0n;\n        num = (num << 8n) + BigInt(buffer[offset + 7]);\n        num = (num << 8n) + BigInt(buffer[offset + 6]);\n        num = (num << 8n) + BigInt(buffer[offset + 5]);\n        num = (num << 8n) + BigInt(buffer[offset + 4]);\n        num = (num << 8n) + BigInt(buffer[offset + 3]);\n        num = (num << 8n) + BigInt(buffer[offset + 2]);\n        num = (num << 8n) + BigInt(buffer[offset + 1]);\n        num = (num << 8n) + BigInt(buffer[offset]);\n        return num;\n    }\n    else {\n        let num = 0n;\n        num = (num << 8n) + BigInt(buffer[offset]);\n        num = (num << 8n) + BigInt(buffer[offset + 1]);\n        num = (num << 8n) + BigInt(buffer[offset + 2]);\n        num = (num << 8n) + BigInt(buffer[offset + 3]);\n        num = (num << 8n) + BigInt(buffer[offset + 4]);\n        num = (num << 8n) + BigInt(buffer[offset + 5]);\n        num = (num << 8n) + BigInt(buffer[offset + 6]);\n        num = (num << 8n) + BigInt(buffer[offset + 7]);\n        return num;\n    }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAO,IAAM,UAAU;AAAA,EACrB,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,KAAK;AACP;AACO,IAAM,UAAU;AAAA,EACrB,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,QAAQ;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,KAAK;AACP;;;ACrBA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;;;ACAA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AA+CA,IAAI;AAIJ,SAAS,gBAAgB,SAAS;AAChC,SAAO;AAAA,IACL,MAAM,SAAS,QAAQ,OAAO;AAAA,IAC9B,SAAS,SAAS;AAAA,IAClB,YAAY,SAAS,cAAc,OAAO;AAAA,IAC1C,gBAAgB,SAAS,kBAAkB,OAAO;AAAA,EACpD;AACF;AAMA,IAAI;AAKJ,SAAS,iBAAiB,MAAM;AAC9B,SAAO,QAAQ,IAAI,IAAI;AACzB;AAMA,IAAI;AAKJ,SAAS,iBAAiB,MAAM;AAC9B,SAAO,QAAQ,IAAI,IAAI;AACzB;AAMA,IAAI;AAMJ,SAAS,mBAAmB,WAAW,MAAM;AAC3C,SAAO,QAAQ,IAAI,SAAS,GAAG,IAAI,IAAI;AACzC;AAMA,SAAS,WAAW,OAAO;AACzB,QAAM,OAAO,OAAO;AACpB,MAAI,SAAS,UAAU;AACrB,WAAO,IAAI,KAAK;AAAA,EAClB;AACA,MAAI,SAAS,YAAY,SAAS,YAAY,SAAS,WAAW;AAChE,WAAO,GAAG,KAAK;AAAA,EACjB;AACA,MAAI,SAAS,YAAY,SAAS,YAAY;AAC5C,YAAQ,SAAS,OAAO,eAAe,KAAK,GAAG,aAAa,SAAS;AAAA,EACvE;AACA,SAAO;AACT;AAGA,SAAS,UAAU,SAAS,OAAO,SAAS,SAAS,OAAO;AAC1D,QAAM,QAAQ,SAAS,WAAW,QAAQ,MAAM,QAAQ,QAAQ;AAChE,QAAM,WAAW,OAAO,YAAY,QAAQ,WAAW;AACvD,QAAM,WAAW,OAAO,YAAY,WAAW,KAAK;AACpD,QAAM,QAAQ;AAAA,IACZ,MAAM,QAAQ;AAAA,IACd,MAAM,QAAQ;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS,WAAW,KAAK,KAAK,WAAW,YAAY,QAAQ,WAAW,GAAG,WAAW,QAAQ;AAAA;AAAA,IAE9F,aAAa,QAAQ;AAAA,IACrB,MAAM,OAAO;AAAA,IACb,QAAQ,OAAO;AAAA,IACf,MAAM,QAAQ;AAAA,IACd,YAAY,QAAQ;AAAA,IACpB,gBAAgB,QAAQ;AAAA,EAC1B;AACA,QAAM,WAAW,QAAQ,SAAS;AAClC,QAAM,UAAU,OAAO;AAAA,EACvB,QAAQ,WAAW,mBAAmB,QAAQ,WAAW,MAAM,IAAI,MAAM,WAAW,iBAAiB,MAAM,IAAI,IAAI,SAAS,QAAQ,WAAW,iBAAiB,MAAM,IAAI;AAC1K,MAAI,SAAS;AACX,UAAM,UAAU,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AAAA,EACnE;AACA,MAAI,UAAU;AACZ,YAAQ,QAAQ;AAAA,EAClB;AACA,MAAI,QAAQ,QAAQ;AAClB,YAAQ,OAAO,KAAK,KAAK;AAAA,EAC3B,OAAO;AACL,YAAQ,SAAS,CAAC,KAAK;AAAA,EACzB;AACF;AAmEA,IAAI,YAAY,cAAc,MAAM;AAAA;AAAA;AAAA;AAAA,EAIlC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,QAAQ;AAClB,UAAM,OAAO,CAAC,EAAE,OAAO;AACvB,SAAK,OAAO;AACZ,SAAK,SAAS;AAAA,EAChB;AACF;AAihBA,SAAS,QAAQ,SAAS;AACxB,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,IACP,SAAS;AAAA,IACT,aAAa,OAAO;AAAA,IACpB;AAAA,IACA,KAAK,SAAS,SAAS;AACrB,UAAI,QAAQ,SAAS,CAAC,KAAK,YAAY,QAAQ,KAAK,GAAG;AACrD,kBAAU,MAAM,WAAW,SAAS,OAAO;AAAA,MAC7C;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AA8KA,SAAS,OAAO,aAAa,SAAS;AACpC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,IACP,SAAS,GAAG,WAAW;AAAA,IACvB;AAAA,IACA;AAAA,IACA,KAAK,SAAS,SAAS;AACrB,UAAI,QAAQ,SAAS,QAAQ,MAAM,WAAW,KAAK,aAAa;AAC9D,kBAAU,MAAM,UAAU,SAAS,SAAS;AAAA,UAC1C,UAAU,GAAG,QAAQ,MAAM,MAAM;AAAA,QACnC,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AA6IA,SAAS,SAAS,aAAa,SAAS;AACtC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,IACP,SAAS,KAAK,uBAAuB,OAAO,YAAY,OAAO,IAAI,WAAW,WAAW,CAAC;AAAA,IAC1F;AAAA,IACA;AAAA,IACA,KAAK,SAAS,SAAS;AACrB,UAAI,QAAQ,SAAS,QAAQ,QAAQ,KAAK,aAAa;AACrD,kBAAU,MAAM,SAAS,SAAS,SAAS;AAAA,UACzC,UAAU,QAAQ,iBAAiB,OAAO,QAAQ,MAAM,OAAO,IAAI,WAAW,QAAQ,KAAK;AAAA,QAC7F,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AA0FA,SAAS,SAAS,aAAa,SAAS;AACtC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,IACP,SAAS,KAAK,uBAAuB,OAAO,YAAY,OAAO,IAAI,WAAW,WAAW,CAAC;AAAA,IAC1F;AAAA,IACA;AAAA,IACA,KAAK,SAAS,SAAS;AACrB,UAAI,QAAQ,SAAS,QAAQ,QAAQ,KAAK,aAAa;AACrD,kBAAU,MAAM,SAAS,SAAS,SAAS;AAAA,UACzC,UAAU,QAAQ,iBAAiB,OAAO,QAAQ,MAAM,OAAO,IAAI,WAAW,QAAQ,KAAK;AAAA,QAC7F,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAggBA,SAAS,UAAU,WAAW;AAC5B,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,IACP;AAAA,IACA,KAAK,SAAS;AACZ,cAAQ,QAAQ,KAAK,UAAU,QAAQ,KAAK;AAC5C,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAgTA,SAAS,WAAW,QAAQ,SAAS,SAAS;AAC5C,SAAO,OAAO,OAAO,YAAY;AAAA;AAAA,IAE/B,OAAO,QAAQ,SAAS,OAAO;AAAA;AAAA;AAAA,IAG/B,OAAO;AAAA;AAEX;AAiPA,SAAS,QAAQ,SAAS;AACxB,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,IACP;AAAA,IACA,KAAK,SAAS,SAAS;AACrB,UAAI,OAAO,QAAQ,UAAU,WAAW;AACtC,gBAAQ,QAAQ;AAAA,MAClB,OAAO;AACL,kBAAU,MAAM,QAAQ,SAAS,OAAO;AAAA,MAC1C;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAsIA,SAAS,SAAS,QAAQ,SAAS;AACjC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,SAAS,OAAO;AAAA,IAChB,OAAO;AAAA,IACP,OAAO;AAAA,IACP;AAAA,IACA,KAAK,SAAS,SAAS;AACrB,UAAI,QAAQ,iBAAiB,KAAK,OAAO;AACvC,gBAAQ,QAAQ;AAAA,MAClB,OAAO;AACL,kBAAU,MAAM,QAAQ,SAAS,OAAO;AAAA,MAC1C;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AA08BA,SAAS,OAAO,SAAS;AACvB,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,IACP;AAAA,IACA,KAAK,SAAS,SAAS;AACrB,UAAI,OAAO,QAAQ,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK,GAAG;AAC9D,gBAAQ,QAAQ;AAAA,MAClB,OAAO;AACL,kBAAU,MAAM,QAAQ,SAAS,OAAO;AAAA,MAC1C;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAGA,SAAS,OAAO,SAAS,SAAS;AAChC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA,KAAK,SAAS,SAAS;AACrB,YAAM,QAAQ,QAAQ;AACtB,UAAI,SAAS,OAAO,UAAU,UAAU;AACtC,gBAAQ,QAAQ;AAChB,gBAAQ,QAAQ,CAAC;AACjB,mBAAW,OAAO,KAAK,SAAS;AAC9B,gBAAM,SAAS,MAAM,GAAG;AACxB,gBAAM,eAAe,KAAK,QAAQ,GAAG,EAAE;AAAA,YACrC,EAAE,OAAO,OAAO,OAAO,OAAO;AAAA,YAC9B;AAAA,UACF;AACA,cAAI,aAAa,QAAQ;AACvB,kBAAM,WAAW;AAAA,cACf,MAAM;AAAA,cACN,QAAQ;AAAA,cACR;AAAA,cACA;AAAA,cACA,OAAO;AAAA,YACT;AACA,uBAAW,SAAS,aAAa,QAAQ;AACvC,kBAAI,MAAM,MAAM;AACd,sBAAM,KAAK,QAAQ,QAAQ;AAAA,cAC7B,OAAO;AACL,sBAAM,OAAO,CAAC,QAAQ;AAAA,cACxB;AACA,sBAAQ,QAAQ,KAAK,KAAK;AAAA,YAC5B;AACA,gBAAI,CAAC,QAAQ,QAAQ;AACnB,sBAAQ,SAAS,aAAa;AAAA,YAChC;AACA,gBAAI,QAAQ,YAAY;AACtB,sBAAQ,QAAQ;AAChB;AAAA,YACF;AAAA,UACF;AACA,cAAI,CAAC,aAAa,OAAO;AACvB,oBAAQ,QAAQ;AAAA,UAClB;AACA,cAAI,aAAa,UAAU,UAAU,OAAO,OAAO;AACjD,oBAAQ,MAAM,GAAG,IAAI,aAAa;AAAA,UACpC;AAAA,QACF;AAAA,MACF,OAAO;AACL,kBAAU,MAAM,QAAQ,SAAS,OAAO;AAAA,MAC1C;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AA6RA,SAAS,SAAS,YAAY,MAAM;AAClC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,SAAS,GAAG,QAAQ,OAAO;AAAA,IAC3B,OAAO;AAAA,IACP;AAAA,IACA,KAAK,SAAS,SAAS;AACrB,UAAI,QAAQ,UAAU,QAAQ;AAC5B,YAAI,aAAa,MAAM;AACrB,kBAAQ,QAAQ;AAAA,YACd;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACA,YAAI,QAAQ,UAAU,QAAQ;AAC5B,kBAAQ,QAAQ;AAChB,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO,KAAK,QAAQ,KAAK,SAAS,OAAO;AAAA,IAC3C;AAAA,EACF;AACA,MAAI,KAAK,MAAM;AACb,WAAO,UAAU,KAAK,CAAC;AAAA,EACzB;AACA,SAAO;AACT;AAurBA,SAAS,OAAO,SAAS;AACvB,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,IACP;AAAA,IACA,KAAK,SAAS,SAAS;AACrB,UAAI,OAAO,QAAQ,UAAU,UAAU;AACrC,gBAAQ,QAAQ;AAAA,MAClB,OAAO;AACL,kBAAU,MAAM,QAAQ,SAAS,OAAO;AAAA,MAC1C;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AA8WA,SAAS,WAAW,UAAU;AAC5B,MAAI;AACJ,MAAI,UAAU;AACZ,eAAW,WAAW,UAAU;AAC9B,UAAI,QAAQ;AACV,eAAO,KAAK,GAAG,QAAQ,MAAM;AAAA,MAC/B,OAAO;AACL,iBAAS,QAAQ;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,MAAM,SAAS,SAAS;AAC/B,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,SAAS,CAAC,GAAG,IAAI,IAAI,QAAQ,IAAI,CAAC,WAAW,OAAO,OAAO,CAAC,CAAC,EAAE,KAAK,KAAK,KAAK;AAAA,IAC9E,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA,KAAK,SAAS,SAAS;AACrB,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,iBAAW,UAAU,KAAK,SAAS;AACjC,cAAM,gBAAgB,OAAO;AAAA,UAC3B,EAAE,OAAO,OAAO,OAAO,QAAQ,MAAM;AAAA,UACrC;AAAA,QACF;AACA,YAAI,cAAc,OAAO;AACvB,cAAI,cAAc,QAAQ;AACxB,gBAAI,eAAe;AACjB,4BAAc,KAAK,aAAa;AAAA,YAClC,OAAO;AACL,8BAAgB,CAAC,aAAa;AAAA,YAChC;AAAA,UACF,OAAO;AACL,2BAAe;AACf;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAI,iBAAiB;AACnB,4BAAgB,KAAK,aAAa;AAAA,UACpC,OAAO;AACL,8BAAkB,CAAC,aAAa;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AACA,UAAI,cAAc;AAChB,eAAO;AAAA,MACT;AACA,UAAI,eAAe;AACjB,YAAI,cAAc,WAAW,GAAG;AAC9B,iBAAO,cAAc,CAAC;AAAA,QACxB;AACA,kBAAU,MAAM,QAAQ,SAAS,SAAS;AAAA,UACxC,QAAQ,WAAW,aAAa;AAAA,QAClC,CAAC;AACD,gBAAQ,QAAQ;AAAA,MAClB,WAAW,iBAAiB,WAAW,GAAG;AACxC,eAAO,gBAAgB,CAAC;AAAA,MAC1B,OAAO;AACL,kBAAU,MAAM,QAAQ,SAAS,SAAS;AAAA,UACxC,QAAQ,WAAW,eAAe;AAAA,QACpC,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AA6PA,SAAS,MAAM,QAAQ,OAAO,SAAS;AACrC,QAAM,UAAU,OAAO;AAAA,IACrB,EAAE,OAAO,OAAO,OAAO,MAAM;AAAA,IAC7B,gBAAgB,OAAO;AAAA,EACzB;AACA,MAAI,QAAQ,QAAQ;AAClB,UAAM,IAAI,UAAU,QAAQ,MAAM;AAAA,EACpC;AACA,SAAO,QAAQ;AACjB;AA0DA,SAAS,QAAQ,OAAO;AACtB,SAAO;AAAA,IACL,GAAG,MAAM,CAAC;AAAA,IACV,MAAM;AAAA,IACN,KAAK,SAAS,SAAS;AACrB,iBAAW,QAAQ,OAAO;AACxB,YAAI,KAAK,SAAS,YAAY;AAC5B,cAAI,QAAQ,WAAW,KAAK,SAAS,YAAY,KAAK,SAAS,mBAAmB;AAChF,oBAAQ,QAAQ;AAChB;AAAA,UACF;AACA,cAAI,CAAC,QAAQ,UAAU,CAAC,QAAQ,cAAc,CAAC,QAAQ,gBAAgB;AACrE,sBAAU,KAAK,KAAK,SAAS,OAAO;AAAA,UACtC;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;ADttKA,IAAM,eAAiB;AAAA,EACnB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS,CAAC;AAAA,EACV,SAAS,UAAU;AACvB;AACA,IAAM,cAAgB;AAAA,EAClB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS,CAAC;AAAA,EACV,SAAS,GAAI;AACjB;AACO,IAAM,gBAAkB,OAAO;AAAA,EACpC,eAAiB,MAAM,CAAG,OAAO,GAAK,SAAS,UAAU,CAAC,CAAC;AAAA,EAC3D,QAAU,OAAO;AAAA,EACjB,OAAS,OAAO;AAAA,IACd,QAAQ;AAAA,IACR,SAAS;AAAA,EACX,CAAC;AAAA,EACD,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,KAAK;AACP,CAAC;AACM,IAAM,eAAiB,KAAO,SAAS,UAAU,GAAK,OAAO,EAAE,CAAC;;;AExBvE,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;AASM,SAAU,QAAQ,GAAU;AAChC,SACE,aAAa,cACZ,KAAK,QAAQ,OAAO,MAAM,YAAY,EAAE,YAAY,SAAS;AAElE;AAEA,SAAS,MAAM,MAA8B,SAAiB;AAC5D,MAAI,CAAC,QAAQ,CAAC;AAAG,UAAM,IAAI,MAAM,qBAAqB;AACtD,MAAI,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,MAAM;AAClD,UAAM,IAAI,MAAM,iCAAiC,OAAO,mBAAmB,EAAE,MAAM,EAAE;AACzF;AAeA,SAAS,OAAOC,WAAe,gBAAgB,MAAI;AACjD,MAAIA,UAAS;AAAW,UAAM,IAAI,MAAM,kCAAkC;AAC1E,MAAI,iBAAiBA,UAAS;AAAU,UAAM,IAAI,MAAM,uCAAuC;AACjG;AACA,SAAS,OAAO,KAAUA,WAAa;AACrC,QAAM,GAAG;AACT,QAAM,MAAMA,UAAS;AACrB,MAAI,IAAI,SAAS,KAAK;AACpB,UAAM,IAAI,MAAM,yDAAyD,GAAG,EAAE;EAChF;AACF;;;AC7CA,IAAAC,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;;;ACGA,IAAAC,gBAAA;IAAAA,gBAAA;IAAAA,gBAAA;AAAO,IAAMC,UACX,OAAO,eAAe,YAAY,YAAY,aAAa,WAAW,SAAS;;;ADyB1E,IAAM,aAAa,CAAC,QACzB,IAAI,SAAS,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;AAGlD,IAAM,OAAO,CAAC,MAAc,UAAmB,QAAS,KAAK,QAAW,SAAS;AAKjF,IAAM,OAAO,IAAI,WAAW,IAAI,YAAY,CAAC,SAAU,CAAC,EAAE,MAAM,EAAE,CAAC,MAAM;AAkBhF,IAAM,QAAwB,MAAM,KAAK,EAAE,QAAQ,IAAG,GAAI,CAAC,GAAG,MAC5D,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC;AAsE3B,SAAU,YAAY,KAAW;AACrC,MAAI,OAAO,QAAQ;AAAU,UAAM,IAAI,MAAM,oCAAoC,OAAO,GAAG,EAAE;AAC7F,SAAO,IAAI,WAAW,IAAI,YAAW,EAAG,OAAO,GAAG,CAAC;AACrD;AAQM,SAAU,QAAQ,MAAW;AACjC,MAAI,OAAO,SAAS;AAAU,WAAO,YAAY,IAAI;AACrD,QAAO,IAAI;AACX,SAAO;AACT;AAsBM,IAAgB,OAAhB,MAAoB;;EAsBxB,QAAK;AACH,WAAO,KAAK,WAAU;EACxB;;AAcF,IAAM,QAAQ,CAAA,EAAG;AAcX,SAAU,gBAAmC,UAAuB;AACxE,QAAM,QAAQ,CAAC,QAA2B,SAAQ,EAAG,OAAO,QAAQ,GAAG,CAAC,EAAE,OAAM;AAChF,QAAM,MAAM,SAAQ;AACpB,QAAM,YAAY,IAAI;AACtB,QAAM,WAAW,IAAI;AACrB,QAAM,SAAS,MAAM,SAAQ;AAC7B,SAAO;AACT;;;AF3NA,SAAS,aAAa,MAAgB,YAAoB,OAAeC,OAAa;AACpF,MAAI,OAAO,KAAK,iBAAiB;AAAY,WAAO,KAAK,aAAa,YAAY,OAAOA,KAAI;AAC7F,QAAM,OAAO,OAAO,EAAE;AACtB,QAAM,WAAW,OAAO,UAAU;AAClC,QAAM,KAAK,OAAQ,SAAS,OAAQ,QAAQ;AAC5C,QAAM,KAAK,OAAO,QAAQ,QAAQ;AAClC,QAAMC,KAAID,QAAO,IAAI;AACrB,QAAM,IAAIA,QAAO,IAAI;AACrB,OAAK,UAAU,aAAaC,IAAG,IAAID,KAAI;AACvC,OAAK,UAAU,aAAa,GAAG,IAAIA,KAAI;AACzC;AAGO,IAAM,MAAM,CAAC,GAAW,GAAW,MAAe,IAAI,IAAM,CAAC,IAAI;AAEjE,IAAM,MAAM,CAAC,GAAW,GAAW,MAAe,IAAI,IAAM,IAAI,IAAM,IAAI;AAM3E,IAAgB,SAAhB,cAAoD,KAAO;EAc/D,YACW,UACF,WACE,WACAA,OAAa;AAEtB,UAAK;AALI,SAAA,WAAA;AACF,SAAA,YAAA;AACE,SAAA,YAAA;AACA,SAAA,OAAAA;AATD,SAAA,WAAW;AACX,SAAA,SAAS;AACT,SAAA,MAAM;AACN,SAAA,YAAY;AASpB,SAAK,SAAS,IAAI,WAAW,QAAQ;AACrC,SAAK,OAAO,WAAW,KAAK,MAAM;EACpC;EACA,OAAO,MAAW;AAChB,WAAO,IAAI;AACX,UAAM,EAAE,MAAM,QAAQ,SAAQ,IAAK;AACnC,WAAO,QAAQ,IAAI;AACnB,UAAM,MAAM,KAAK;AACjB,aAAS,MAAM,GAAG,MAAM,OAAO;AAC7B,YAAM,OAAO,KAAK,IAAI,WAAW,KAAK,KAAK,MAAM,GAAG;AAEpD,UAAI,SAAS,UAAU;AACrB,cAAM,WAAW,WAAW,IAAI;AAChC,eAAO,YAAY,MAAM,KAAK,OAAO;AAAU,eAAK,QAAQ,UAAU,GAAG;AACzE;MACF;AACA,aAAO,IAAI,KAAK,SAAS,KAAK,MAAM,IAAI,GAAG,KAAK,GAAG;AACnD,WAAK,OAAO;AACZ,aAAO;AACP,UAAI,KAAK,QAAQ,UAAU;AACzB,aAAK,QAAQ,MAAM,CAAC;AACpB,aAAK,MAAM;MACb;IACF;AACA,SAAK,UAAU,KAAK;AACpB,SAAK,WAAU;AACf,WAAO;EACT;EACA,WAAW,KAAe;AACxB,WAAO,IAAI;AACX,WAAO,KAAK,IAAI;AAChB,SAAK,WAAW;AAIhB,UAAM,EAAE,QAAQ,MAAM,UAAU,MAAAA,MAAI,IAAK;AACzC,QAAI,EAAE,IAAG,IAAK;AAEd,WAAO,KAAK,IAAI;AAChB,SAAK,OAAO,SAAS,GAAG,EAAE,KAAK,CAAC;AAGhC,QAAI,KAAK,YAAY,WAAW,KAAK;AACnC,WAAK,QAAQ,MAAM,CAAC;AACpB,YAAM;IACR;AAEA,aAAS,IAAI,KAAK,IAAI,UAAU;AAAK,aAAO,CAAC,IAAI;AAIjD,iBAAa,MAAM,WAAW,GAAG,OAAO,KAAK,SAAS,CAAC,GAAGA,KAAI;AAC9D,SAAK,QAAQ,MAAM,CAAC;AACpB,UAAM,QAAQ,WAAW,GAAG;AAC5B,UAAM,MAAM,KAAK;AAEjB,QAAI,MAAM;AAAG,YAAM,IAAI,MAAM,6CAA6C;AAC1E,UAAM,SAAS,MAAM;AACrB,UAAM,QAAQ,KAAK,IAAG;AACtB,QAAI,SAAS,MAAM;AAAQ,YAAM,IAAI,MAAM,oCAAoC;AAC/E,aAAS,IAAI,GAAG,IAAI,QAAQ;AAAK,YAAM,UAAU,IAAI,GAAG,MAAM,CAAC,GAAGA,KAAI;EACxE;EACA,SAAM;AACJ,UAAM,EAAE,QAAQ,UAAS,IAAK;AAC9B,SAAK,WAAW,MAAM;AACtB,UAAM,MAAM,OAAO,MAAM,GAAG,SAAS;AACrC,SAAK,QAAO;AACZ,WAAO;EACT;EACA,WAAW,IAAM;AACf,WAAA,KAAO,IAAK,KAAK,YAAmB;AACpC,OAAG,IAAI,GAAG,KAAK,IAAG,CAAE;AACpB,UAAM,EAAE,UAAU,QAAQ,QAAAE,SAAQ,UAAU,WAAW,IAAG,IAAK;AAC/D,OAAG,SAASA;AACZ,OAAG,MAAM;AACT,OAAG,WAAW;AACd,OAAG,YAAY;AACf,QAAIA,UAAS;AAAU,SAAG,OAAO,IAAI,MAAM;AAC3C,WAAO;EACT;;;;ADpHF,IAAM,WAA2B,IAAI,YAAY;EAC/C;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;CACrF;AAKD,IAAM,YAA4B,IAAI,YAAY;EAChD;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;CACrF;AAID,IAAM,WAA2B,IAAI,YAAY,EAAE;AACnD,IAAM,SAAN,cAAqB,OAAc;EAYjC,cAAA;AACE,UAAM,IAAI,IAAI,GAAG,KAAK;AAVxB,SAAA,IAAI,UAAU,CAAC,IAAI;AACnB,SAAA,IAAI,UAAU,CAAC,IAAI;AACnB,SAAA,IAAI,UAAU,CAAC,IAAI;AACnB,SAAA,IAAI,UAAU,CAAC,IAAI;AACnB,SAAA,IAAI,UAAU,CAAC,IAAI;AACnB,SAAA,IAAI,UAAU,CAAC,IAAI;AACnB,SAAA,IAAI,UAAU,CAAC,IAAI;AACnB,SAAA,IAAI,UAAU,CAAC,IAAI;EAInB;EACU,MAAG;AACX,UAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAC,IAAK;AACnC,WAAO,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EAChC;;EAEU,IACR,GAAW,GAAW,GAAW,GAAW,GAAW,GAAW,GAAW,GAAS;AAEtF,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;EACf;EACU,QAAQ,MAAgB,QAAc;AAE9C,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,UAAU;AAAG,eAAS,CAAC,IAAI,KAAK,UAAU,QAAQ,KAAK;AACpF,aAAS,IAAI,IAAI,IAAI,IAAI,KAAK;AAC5B,YAAM,MAAM,SAAS,IAAI,EAAE;AAC3B,YAAM,KAAK,SAAS,IAAI,CAAC;AACzB,YAAM,KAAK,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE,IAAK,QAAQ;AACnD,YAAM,KAAK,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAK,OAAO;AACjD,eAAS,CAAC,IAAK,KAAK,SAAS,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,EAAE,IAAK;IACjE;AAEA,QAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAC,IAAK;AACjC,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,YAAM,SAAS,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,IAAI,KAAK,GAAG,EAAE;AACpD,YAAM,KAAM,IAAI,SAAS,IAAI,GAAG,GAAG,CAAC,IAAI,SAAS,CAAC,IAAI,SAAS,CAAC,IAAK;AACrE,YAAM,SAAS,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,IAAI,KAAK,GAAG,EAAE;AACpD,YAAM,KAAM,SAAS,IAAI,GAAG,GAAG,CAAC,IAAK;AACrC,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAK,IAAI,KAAM;AACf,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAK,KAAK,KAAM;IAClB;AAEA,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,SAAK,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EACjC;EACU,aAAU;AAClB,aAAS,KAAK,CAAC;EACjB;EACA,UAAO;AACL,SAAK,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC/B,SAAK,OAAO,KAAK,CAAC;EACpB;;AAGF,IAAM,SAAN,cAAqB,OAAM;EASzB,cAAA;AACE,UAAK;AATP,SAAA,IAAI,aAAa;AACjB,SAAA,IAAI,YAAa;AACjB,SAAA,IAAI,YAAa;AACjB,SAAA,IAAI,aAAa;AACjB,SAAA,IAAI,aAAa;AACjB,SAAA,IAAI,aAAa;AACjB,SAAA,IAAI,aAAa;AACjB,SAAA,IAAI,aAAa;AAGf,SAAK,YAAY;EACnB;;AAOK,IAAM,SAAyB,gBAAgB,MAAM,IAAI,OAAM,CAAE;AACjE,IAAM,SAAyB,gBAAgB,MAAM,IAAI,OAAM,CAAE;;;AKhIxE,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAKA,SAAS,KAAMC,WAAU;AACvB,MAAIA,UAAS,UAAU,KAAK;AAAE,UAAM,IAAI,UAAU,mBAAmB;AAAA,EAAE;AACvE,QAAM,WAAW,IAAI,WAAW,GAAG;AACnC,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,aAAS,CAAC,IAAI;AAAA,EAChB;AACA,WAAS,IAAI,GAAG,IAAIA,UAAS,QAAQ,KAAK;AACxC,UAAM,IAAIA,UAAS,OAAO,CAAC;AAC3B,UAAM,KAAK,EAAE,WAAW,CAAC;AACzB,QAAI,SAAS,EAAE,MAAM,KAAK;AAAE,YAAM,IAAI,UAAU,IAAI,eAAe;AAAA,IAAE;AACrE,aAAS,EAAE,IAAI;AAAA,EACjB;AACA,QAAM,OAAOA,UAAS;AACtB,QAAM,SAASA,UAAS,OAAO,CAAC;AAChC,QAAM,SAAS,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG;AAC5C,QAAM,UAAU,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;AAC7C,WAASC,QAAQ,QAAQ;AAEvB,QAAI,kBAAkB,YAAY;AAAA,IAAE,WAAW,YAAY,OAAO,MAAM,GAAG;AACzE,eAAS,IAAI,WAAW,OAAO,QAAQ,OAAO,YAAY,OAAO,UAAU;AAAA,IAC7E,WAAW,MAAM,QAAQ,MAAM,GAAG;AAChC,eAAS,WAAW,KAAK,MAAM;AAAA,IACjC;AACA,QAAI,EAAE,kBAAkB,aAAa;AAAE,YAAM,IAAI,UAAU,qBAAqB;AAAA,IAAE;AAClF,QAAI,OAAO,WAAW,GAAG;AAAE,aAAO;AAAA,IAAG;AAErC,QAAI,SAAS;AACb,QAAIC,UAAS;AACb,QAAI,SAAS;AACb,UAAM,OAAO,OAAO;AACpB,WAAO,WAAW,QAAQ,OAAO,MAAM,MAAM,GAAG;AAC9C;AACA;AAAA,IACF;AAEA,UAAM,QAAS,OAAO,UAAU,UAAU,MAAO;AACjD,UAAM,MAAM,IAAI,WAAW,IAAI;AAE/B,WAAO,WAAW,MAAM;AACtB,UAAI,QAAQ,OAAO,MAAM;AAEzB,UAAI,IAAI;AACR,eAAS,MAAM,OAAO,IAAI,UAAU,KAAK,IAAIA,YAAY,QAAQ,IAAK,OAAO,KAAK;AAChF,iBAAU,MAAM,IAAI,GAAG,MAAO;AAC9B,YAAI,GAAG,IAAK,QAAQ,SAAU;AAC9B,gBAAS,QAAQ,SAAU;AAAA,MAC7B;AACA,UAAI,UAAU,GAAG;AAAE,cAAM,IAAI,MAAM,gBAAgB;AAAA,MAAE;AACrD,MAAAA,UAAS;AACT;AAAA,IACF;AAEA,QAAI,MAAM,OAAOA;AACjB,WAAO,QAAQ,QAAQ,IAAI,GAAG,MAAM,GAAG;AACrC;AAAA,IACF;AAEA,QAAI,MAAM,OAAO,OAAO,MAAM;AAC9B,WAAO,MAAM,MAAM,EAAE,KAAK;AAAE,aAAOF,UAAS,OAAO,IAAI,GAAG,CAAC;AAAA,IAAE;AAC7D,WAAO;AAAA,EACT;AACA,WAAS,aAAc,QAAQ;AAC7B,QAAI,OAAO,WAAW,UAAU;AAAE,YAAM,IAAI,UAAU,iBAAiB;AAAA,IAAE;AACzE,QAAI,OAAO,WAAW,GAAG;AAAE,aAAO,IAAI,WAAW;AAAA,IAAE;AACnD,QAAI,MAAM;AAEV,QAAI,SAAS;AACb,QAAIE,UAAS;AACb,WAAO,OAAO,GAAG,MAAM,QAAQ;AAC7B;AACA;AAAA,IACF;AAEA,UAAM,QAAU,OAAO,SAAS,OAAO,SAAU,MAAO;AACxD,UAAM,OAAO,IAAI,WAAW,IAAI;AAEhC,WAAO,OAAO,GAAG,GAAG;AAElB,UAAI,QAAQ,SAAS,OAAO,WAAW,GAAG,CAAC;AAE3C,UAAI,UAAU,KAAK;AAAE;AAAA,MAAO;AAC5B,UAAI,IAAI;AACR,eAAS,MAAM,OAAO,IAAI,UAAU,KAAK,IAAIA,YAAY,QAAQ,IAAK,OAAO,KAAK;AAChF,iBAAU,OAAO,KAAK,GAAG,MAAO;AAChC,aAAK,GAAG,IAAK,QAAQ,QAAS;AAC9B,gBAAS,QAAQ,QAAS;AAAA,MAC5B;AACA,UAAI,UAAU,GAAG;AAAE,cAAM,IAAI,MAAM,gBAAgB;AAAA,MAAE;AACrD,MAAAA,UAAS;AACT;AAAA,IACF;AAEA,QAAI,MAAM,OAAOA;AACjB,WAAO,QAAQ,QAAQ,KAAK,GAAG,MAAM,GAAG;AACtC;AAAA,IACF;AACA,UAAM,MAAM,IAAI,WAAW,UAAU,OAAO,IAAI;AAChD,QAAI,IAAI;AACR,WAAO,QAAQ,MAAM;AACnB,UAAI,GAAG,IAAI,KAAK,KAAK;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AACA,WAASC,QAAQC,SAAQ;AACvB,UAAM,SAAS,aAAaA,OAAM;AAClC,QAAI,QAAQ;AAAE,aAAO;AAAA,IAAO;AAC5B,UAAM,IAAI,MAAM,aAAa,OAAO,YAAY;AAAA,EAClD;AACA,SAAO;AAAA,IACL,QAAAH;AAAA,IACA;AAAA,IACA,QAAAE;AAAA,EACF;AACF;AACA,IAAO,cAAQ;;;ADtHf,IAAI,WAAW;AACf,IAAOE,eAAQ,YAAM,QAAQ;;;ADAd,SAAR,aAAkB,YAAY;AAEjC,WAASC,QAAO,SAAS;AACrB,QAAI,YAAY,WAAW,KAAK,OAAO;AACvC,QAAI,WAAW,WAAW,SAAS;AACnC,QAAIC,UAAS,UAAU,SAAS;AAChC,QAAI,OAAO,IAAI,WAAWA,OAAM;AAChC,SAAK,IAAI,WAAW,CAAC;AACrB,SAAK,IAAI,SAAS,SAAS,GAAG,CAAC,GAAG,UAAU,MAAM;AAClD,WAAOC,aAAO,OAAO,IAAI;AAAA,EAC7B;AACA,WAASC,WAAU,QAAQ;AACvB,QAAI,UAAU,OAAO,MAAM,GAAG,EAAE;AAChC,QAAI,WAAW,OAAO,MAAM,EAAE;AAC9B,QAAI,cAAc,WAAW,OAAO;AAEpC,QAAI,SAAS,CAAC,IAAI,YAAY,CAAC,IAC3B,SAAS,CAAC,IAAI,YAAY,CAAC,IAC3B,SAAS,CAAC,IAAI,YAAY,CAAC,IAC3B,SAAS,CAAC,IAAI,YAAY,CAAC;AAC3B;AACJ,WAAO;AAAA,EACX;AAEA,WAAS,aAAa,KAAK;AACvB,QAAI,SAASD,aAAO,aAAa,GAAG;AACpC,QAAI,UAAU;AACV;AACJ,WAAOC,WAAU,MAAM;AAAA,EAC3B;AACA,WAASC,QAAO,KAAK;AACjB,QAAI,SAASF,aAAO,OAAO,GAAG;AAC9B,QAAI,UAAUC,WAAU,MAAM;AAC9B,QAAI,WAAW;AACX,YAAM,IAAI,MAAM,kBAAkB;AACtC,WAAO;AAAA,EACX;AACA,SAAO;AAAA,IACH,QAAQH;AAAA,IACR,QAAQI;AAAA,IACR;AAAA,EACJ;AACJ;;;ANxCA,SAAS,SAAS,QAAQ;AACtB,SAAO,OAAO,OAAO,MAAM,CAAC;AAChC;AACA,IAAOC,eAAQ,aAAc,QAAQ;;;ADN9B,SAAS,UAAU,QAAQ,SAAS;AAEvC,MAAI,YAAY,UAAa,OAAO,CAAC,MAAM;AACvC,UAAM,IAAI,MAAM,yBAAyB;AAE7C,MAAI,OAAO,WAAW,IAAI;AACtB,WAAO;AAAA,MACH,SAAS,OAAO,CAAC;AAAA,MACjB,YAAY,OAAO,MAAM,GAAG,EAAE;AAAA,MAC9B,YAAY;AAAA,IAChB;AAAA,EACJ;AAEA,MAAI,OAAO,WAAW;AAClB,UAAM,IAAI,MAAM,oBAAoB;AAExC,MAAI,OAAO,EAAE,MAAM;AACf,UAAM,IAAI,MAAM,0BAA0B;AAC9C,SAAO;AAAA,IACH,SAAS,OAAO,CAAC;AAAA,IACjB,YAAY,OAAO,MAAM,GAAG,EAAE;AAAA,IAC9B,YAAY;AAAA,EAChB;AACJ;AACO,SAAS,UAAU,SAAS,YAAY,YAAY;AACvD,MAAI,WAAW,WAAW;AACtB,UAAM,IAAI,UAAU,2BAA2B;AACnD,MAAI,SAAS,IAAI,WAAW,aAAa,KAAK,EAAE;AAChD,MAAI,OAAO,IAAI,SAAS,OAAO,MAAM;AACrC,OAAK,SAAS,GAAG,OAAO;AACxB,SAAO,IAAI,YAAY,CAAC;AACxB,MAAI,YAAY;AACZ,WAAO,EAAE,IAAI;AAAA,EACjB;AACA,SAAO;AACX;AACO,SAAS,OAAO,KAAK,SAAS;AACjC,SAAO,UAAUC,aAAU,OAAO,GAAG,GAAG,OAAO;AACnD;AACO,SAAS,OAAO,KAAK;AACxB,SAAOA,aAAU,OAAO,UAAU,IAAI,SAAS,IAAI,YAAY,IAAI,UAAU,CAAC;AAClF;;;AU1CA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAM,IAAI,CAAC,QAAQ,OAAO,KAAK,KAAK,KAAK;AAClC,SAAS,QAAQ,KAAK;AAC3B;AAAA,IACE,IAAI;AAAA,MACF,EAAE,oEAAoE;AAAA,IACxE;AAAA,EACF;AACA;AAAA,IACE,CAAC,IAAI;AAAA,MACH,EAAE,oEAAoE;AAAA,IACxE;AAAA,EACF;AACA;AAAA,IACE,IAAI;AAAA,MACF,EAAE,kEAAkE;AAAA,IACtE;AAAA,EACF;AAEA;AAAA,IACE,IAAI;AAAA,MACF,EAAE,kEAAkE;AAAA,IACtE;AAAA,EACF;AAEA;AAAA,IACE,CAAC,IAAI;AAAA,MACH,EAAE,kEAAkE;AAAA,IACtE;AAAA,EACF;AAEA;AAAA,IACE,CAAC,IAAI;AAAA,MACH,EAAE,kEAAkE;AAAA,IACtE;AAAA,EACF;AAEA;AAAA,IACE,CAAC,IAAI;AAAA,MACH,EAAE,kEAAkE;AAAA,IACtE;AAAA,EACF;AAEA;AAAA,IACE,OAAO;AAAA,MACL,IAAI;AAAA,QACF,EAAE,kEAAkE;AAAA,QACpE,EAAE,kEAAkE;AAAA,MACtE;AAAA,IACF,EAAE;AAAA,MACA,EAAE,kEAAkE;AAAA,IACtE;AAAA,EACF;AAEA;AAAA,IACE,IAAI;AAAA,MACF,EAAE,kEAAkE;AAAA,MACpE,EAAE,kEAAkE;AAAA,IACtE,MAAM;AAAA,EACR;AACA;AAAA,IACE,OAAO;AAAA,MACL,IAAI;AAAA,QACF,EAAE,kEAAkE;AAAA,QACpE,EAAE,kEAAkE;AAAA,MACtE;AAAA,IACF,EAAE;AAAA,MACA,EAAE,kEAAkE;AAAA,IACtE;AAAA,EACF;AACA;AAAA,IACE,OAAO;AAAA,MACL,IAAI;AAAA,QACF,EAAE,kEAAkE;AAAA,MACtE;AAAA,IACF,EAAE;AAAA,MACA,EAAE,kEAAkE;AAAA,IACtE;AAAA,EACF;AACA;AAAA,IACE,OAAO;AAAA,MACL,IAAI;AAAA,QACF,EAAE,kEAAkE;AAAA,MACtE;AAAA,IACF,EAAE;AAAA,MACA,EAAE,kEAAkE;AAAA,IACtE;AAAA,EACF;AACA;AAAA,IACE,OAAO;AAAA,MACL,IAAI;AAAA,QACF,EAAE,kEAAkE;AAAA,MACtE;AAAA,IACF,EAAE;AAAA,MACA,EAAE,kEAAkE;AAAA,IACtE;AAAA,EACF;AACA;AAAA,IACE,OAAO;AAAA,MACL,IAAI;AAAA,QACF;AAAA,UACE;AAAA,QACF;AAAA,QACA;AAAA,MACF;AAAA,IACF,EAAE;AAAA,MACA,EAAE,oEAAoE;AAAA,IACxE;AAAA,EACF;AACA;AAAA,IACE,OAAO;AAAA,MACL,IAAI;AAAA,QACF;AAAA,UACE;AAAA,QACF;AAAA,QACA;AAAA,MACF;AAAA,IACF,EAAE;AAAA,MACA;AAAA,QACE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA;AAAA,IACE,OAAO;AAAA,MACL,IAAI;AAAA,QACF,EAAE,oEAAoE;AAAA,QACtE;AAAA,MACF;AAAA,IACF,EAAE;AAAA,MACA,EAAE,oEAAoE;AAAA,IACxE;AAAA,EACF;AACA;AAAA,IACE,OAAO;AAAA,MACL,IAAI;AAAA,QACF,EAAE,oEAAoE;AAAA,QACtE;AAAA,MACF;AAAA,IACF,EAAE;AAAA,MACA;AAAA,QACE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA;AAAA,IACE,OAAO;AAAA,MACL,IAAI;AAAA,QACF,EAAE,kEAAkE;AAAA,MACtE;AAAA,IACF,EAAE;AAAA,MACA,EAAE,oEAAoE;AAAA,IACxE;AAAA,EACF;AACA;AAAA,IACE,IAAI;AAAA,MACF,EAAE,kEAAkE;AAAA,MACpE,EAAE,kEAAkE;AAAA,IACtE,MAAM;AAAA,EACR;AACA,MAAI,WAAW,IAAI;AAAA,IACjB,EAAE,kEAAkE;AAAA,IACpE,EAAE,kEAAkE;AAAA,EACtE;AACA;AAAA,IACE,OAAO,KAAK,SAAS,WAAW,EAAE;AAAA,MAChC,EAAE,kEAAkE;AAAA,IACtE,KAAK,SAAS,WAAW;AAAA,EAC3B;AACA,aAAW,IAAI;AAAA,IACb,EAAE,kEAAkE;AAAA,IACpE,EAAE,kEAAkE;AAAA,EACtE;AACA;AAAA,IACE,OAAO,KAAK,SAAS,WAAW,EAAE;AAAA,MAChC,EAAE,kEAAkE;AAAA,IACtE,KAAK,SAAS,WAAW;AAAA,EAC3B;AACA;AAAA,IACE,OAAO;AAAA,MACL,IAAI;AAAA,QACF,EAAE,kEAAkE;AAAA,QACpE,EAAE,kEAAkE;AAAA,MACtE;AAAA,IACF,EAAE;AAAA,MACA;AAAA,QACE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA;AAAA,IACE,IAAI;AAAA,MACF,EAAE,kEAAkE;AAAA,MACpE,EAAE,oEAAoE;AAAA,MACtE;AAAA,QACE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,IAAI,aAAa;AACnB;AAAA,MACE,OAAO;AAAA,QACL,IAAI;AAAA,UACF,EAAE,kEAAkE;AAAA,UACpE,EAAE,kEAAkE;AAAA,UACpE,EAAE,kEAAkE;AAAA,QACtE;AAAA,MACF,EAAE;AAAA,QACA;AAAA,UACE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,IAAI,eAAe;AACrB;AAAA,MACE,IAAI;AAAA,QACF,EAAE,kEAAkE;AAAA,QACpE,EAAE,kEAAkE;AAAA,QACpE;AAAA,UACE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,OAAO,MAAM;AACpB,MAAI,CAAC,KAAM,OAAM,IAAI,MAAM,qBAAqB;AAClD;;;ACnOA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAM,cAAc;AACpB,IAAM,YAAY,YAAY,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;AACnE,IAAM,iBAAiB,MAAM,GAAG,EAC3B,KAAK,IAAI,EACT,IAAI,CAAC,GAAG,MAAM;AACf,QAAM,IAAI,OAAO,cAAc,CAAC;AAChC,QAAM,QAAQ,YAAY,QAAQ,CAAC;AAEnC,SAAO,QAAQ,IAAI,SAAY,QAAQ,KAAK,QAAQ,QAAQ;AAChE,CAAC;AACD,IAAM,UAAU,IAAI,YAAY;AAChC,IAAM,UAAU,IAAI,YAAY;AAOzB,SAAS,OAAO,QAAQ;AAC3B,QAAM,cAAc,OAAO,OAAO,CAAC,GAAG,MAAM,IAAI,EAAE,QAAQ,CAAC;AAC3D,QAAM,SAAS,IAAI,WAAW,WAAW;AACzC,MAAI,SAAS;AACb,aAAW,SAAS,QAAQ;AACxB,WAAO,IAAI,OAAO,MAAM;AACxB,cAAU,MAAM;AAAA,EACpB;AACA,SAAO;AACX;AAyFO,SAAS,YAAY,QAAQ,QAAQ,OAAO,cAAc;AAC7D,MAAI,SAAS,IAAI,OAAO,QAAQ;AAC5B,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAChE;AACA,iBAAe,aAAa,YAAY;AACxC,MAAI,QAAQ,YAAY;AACpB,UAAM,IAAI,MAAM,gEAAgE,UAAU,cAAc,KAAK,EAAE;AAAA,EACnH;AACA,MAAI,iBAAiB,MAAM;AACvB,WAAO,MAAM,IAAI,QAAQ;AACzB,WAAO,SAAS,CAAC,IAAK,SAAS,IAAK;AACpC,WAAO,SAAS,CAAC,IAAK,SAAS,KAAM;AACrC,WAAO,SAAS,CAAC,IAAK,SAAS,KAAM;AAAA,EACzC,OACK;AACD,WAAO,MAAM,IAAK,SAAS,KAAM;AACjC,WAAO,SAAS,CAAC,IAAK,SAAS,KAAM;AACrC,WAAO,SAAS,CAAC,IAAK,SAAS,IAAK;AACpC,WAAO,SAAS,CAAC,IAAI,QAAQ;AAAA,EACjC;AACJ;;;AfjIA,IAAM,sBAAwB;AAAA,EAC1B,OAAO;AAAA,IACP,YAAc,SAAW,QAAQ,CAAC;AAAA,IAClC,SAAW,SAAe,aAAa;AAAA;AAAA,IAEvC,KAAO;AAAA,MACH;AAAA,QACE,SAAS,QAAQ;AAAA,QACjB,UAAU,CAAC,SAAS;AACpB,iBAAO,CAAC,QAAQ;AACd,kBAAM,YAAc,MAAQ,SAAW,OAAO,CAAC,GAAG,GAAG;AACrD,kBAAM,gBAAgB,KAAK,SAAS;AACpC,kBAAM,eAAiB,MAAQ,SAAS,UAAU,GAAG,aAAa;AAClE,mBAAO;AAAA,UACT;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,CAAC;AACH;AACA,IAAM,UAAU,CAAC,WACf,OAAO,WAAW,KAAK,SAAS,OAAO,SAAS,GAAG,EAAE;AAChD,SAAS,cAAc,KAAK;AACjC,UAAQ,GAAG;AACX,WAAS,QAAQ,YAAY;AAC3B,WAAO,IAAI,QAAQ,UAAU;AAAA,EAC/B;AACA,WAAS,eAAe,QAAQ,SAAS;AACvC,IAAE,MAAY,cAAc,MAAM;AAClC,QAAI,CAAC,IAAI,UAAU,MAAM;AACvB,YAAM,IAAI,UAAU,iCAAiC;AACvD,IAAE,MAAM,qBAAqB,OAAO;AACpC,WAAO,IAAI,OAAO,QAAQ,QAAW,OAAO;AAAA,EAC9C;AACA,WAAS,cAAc,QAAQ,SAAS;AACtC,QAAI,CAAC,IAAI,QAAQ,MAAM,GAAG;AACxB,YAAM,IAAI,MAAM,wBAAwB;AAAA,IAC1C;AACA,IAAE,MAAM,qBAAqB,OAAO;AACpC,WAAO,IAAI,OAAO,QAAW,QAAQ,OAAO;AAAA,EAC9C;AACA,WAAS,QAAQ,WAAW,SAAS;AACnC,UAAM,UAAc,OAAO,SAAS;AACpC,UAAM,UAAU,QAAQ;AAExB,QAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,gBAAU,QACP,OAAO,CAAC,MAAM;AACb,eAAO,YAAY,EAAE;AAAA,MACvB,CAAC,EACA,IAAI;AACP,UAAI,CAAC,QAAS,OAAM,IAAI,MAAM,yBAAyB;AAAA,IAEzD,OAAO;AACL,gBAAU,WAAoB;AAC9B,UAAI,YAAY,QAAQ,IAAK,OAAM,IAAI,MAAM,yBAAyB;AAAA,IACxE;AACA,WAAO,eAAe,QAAQ,YAAY;AAAA,MACxC,YAAY,QAAQ;AAAA,MACpB;AAAA,IACF,CAAC;AAAA,EACH;AACA,WAAS,WAAW,SAAS;AAC3B,IAAE,MAAM,qBAAqB,OAAO;AACpC,QAAI,YAAY,OAAW,WAAU,CAAC;AACtC,UAAM,MACJ,QAAQ,QAAQ,CAAC,SAAS,OAAO,gBAAgB,IAAI,WAAW,IAAI,CAAC;AACvE,QAAI;AACJ,OAAG;AACD,UAAI,IAAI,EAAE;AACV,MAAE,MAAY,cAAc,CAAC;AAAA,IAC/B,SAAS,CAAC,IAAI,UAAU,CAAC;AACzB,WAAO,eAAe,GAAG,OAAO;AAAA,EAClC;AAAA,EACA,MAAM,OAAO;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY,KAAK,KAAK,SAAS;AAC7B,WAAK,MAAM;AACX,WAAK,MAAM;AACX,WAAK,OAAO;AACZ,UAAI,YAAY,OAAW,WAAU,CAAC;AACtC,WAAK,aACH,QAAQ,eAAe,SAAY,OAAO,QAAQ;AACpD,WAAK,UAAU,QAAQ,WAAoB;AAC3C,UAAI,QAAQ,OAAW,MAAK,MAAM,IAAI,cAAc,KAAK,KAAK,UAAU;AAAA,IAC1E;AAAA,IACA,IAAI,aAAa;AACf,aAAO,KAAK;AAAA,IACd;AAAA,IACA,IAAI,YAAY;AACd,UAAI,CAAC,KAAK,KAAK;AAGb,cAAM,IAAI,IAAI,gBAAgB,KAAK,KAAK,KAAK,UAAU;AAGvD,aAAK,MAAM;AAAA,MACb;AACA,aAAO,KAAK;AAAA,IACd;AAAA,IACA,QAAQ;AACN,UAAI,CAAC,KAAK,IAAK,OAAM,IAAI,MAAM,qBAAqB;AACpD,aAAW,OAAO;AAAA,QAChB,YAAY,KAAK;AAAA,QACjB,YAAY,KAAK;AAAA,QACjB,SAAS,KAAK,QAAQ;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,IACA,MAAM,GAAG;AACP,UAAI,KAAK,WAAY,QAAO,KAAK,oBAAoB,CAAC;AACtD,aAAO,KAAK,mBAAmB,CAAC;AAAA,IAClC;AAAA,IACA,KAAK,MAAM,MAAM;AACf,UAAI,CAAC,KAAK,IAAK,OAAM,IAAI,MAAM,qBAAqB;AACpD,UAAI,SAAS,OAAW,QAAO,KAAK;AACpC,UAAI,SAAS,OAAO;AAClB,eAAO,IAAI,KAAK,MAAM,KAAK,GAAG;AAAA,MAChC,OAAO;AACL,YAAI,MAAM,IAAI,KAAK,MAAM,KAAK,GAAG;AACjC,cAAM,YAAY,IAAI,WAAW,EAAE;AACnC,YAAI,UAAU;AAGd,eAAO,IAAI,CAAC,IAAI,KAAM;AACpB;AACA,UAAM,YAAY,WAAW,GAAG,SAAS,IAAI;AAC7C,gBAAM,IAAI,KAAK,MAAM,KAAK,KAAK,SAAS;AAAA,QAC1C;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,YAAY,MAAM;AAChB,UAAI,CAAC,KAAK,WAAY,OAAM,IAAI,MAAM,qBAAqB;AAC3D,UAAI,CAAC,IAAI;AACP,cAAM,IAAI,MAAM,0CAA0C;AAC5D,aAAO,IAAI,YAAY,MAAM,KAAK,UAAU;AAAA,IAC9C;AAAA,IACA,OAAO,MAAM,WAAW;AACtB,aAAO,IAAI,OAAO,MAAM,KAAK,WAAW,SAAS;AAAA,IACnD;AAAA,IACA,cAAc,MAAM,WAAW;AAC7B,UAAI,CAAC,IAAI;AACP,cAAM,IAAI,MAAM,4CAA4C;AAC9D,aAAO,IAAI,cAAc,MAAM,KAAK,UAAU,SAAS,GAAG,EAAE,GAAG,SAAS;AAAA,IAC1E;AAAA,IACA,mBAAmB,GAAG;AACpB,YAAM,cAAc,QAAQ,KAAK,SAAS;AAC1C,YAAM,mBAAmB,IAAI,mBAAmB,aAAa,CAAC;AAC9D,UAAI,CAAC,oBAAoB,iBAAiB,gBAAgB;AACxD,cAAM,IAAI,MAAM,0BAA0B;AAC5C,YAAM,aAAa,WAAW,KAAK;AAAA,QACjC,iBAAiB,WAAW,IAAI,IAAO;AAAA,MACzC,CAAC;AACD,aAAO;AAAA,QACC,OAAO,CAAC,YAAY,iBAAiB,WAAW,CAAC;AAAA,QACvD;AAAA,UACE,SAAS,KAAK;AAAA,UACd,YAAY,KAAK;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAAA,IACA,oBAAoB,GAAG;AACrB,YAAM,UACJ,KAAK,UAAU,CAAC,MAAM,KACrB,KAAK,UAAU,CAAC,MAAM,MAAM,KAAK,UAAU,EAAE,IAAI,OAAO;AAC3D,YAAM,aAAa,UACf,IAAI,cAAc,KAAK,UAAU,IACjC,KAAK;AACT,YAAM,oBAAoB,IAAI,WAAW,YAAY,CAAC;AACtD,UAAI,CAAC,kBAAmB,OAAM,IAAI,MAAM,8BAA8B;AACtE,aAAO,eAAe,mBAAmB;AAAA,QACvC,SAAS,KAAK;AAAA,QACd,YAAY,KAAK;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": ["import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "import_dist", "instance", "import_dist", "import_dist", "crypto", "isLE", "h", "length", "import_dist", "import_dist", "import_dist", "ALPHABET", "encode", "length", "decode", "string", "esm_default", "encode", "length", "esm_default", "decodeRaw", "decode", "esm_default", "esm_default", "import_dist", "import_dist"]}