{"version": 3, "sources": ["../../vite-plugin-node-polyfills/node_modules/.pnpm/base64-js@1.5.1/node_modules/base64-js/index.js", "../../vite-plugin-node-polyfills/node_modules/.pnpm/ieee754@1.2.1/node_modules/ieee754/index.js", "../../vite-plugin-node-polyfills/node_modules/.pnpm/buffer@6.0.3_patch_hash=zkkuxompt5d553skpnegwi5wuy/node_modules/buffer/index.js", "../../vite-plugin-node-polyfills/shims/buffer/index.ts", "../../vite-plugin-node-polyfills/shims/global/index.ts", "../../vite-plugin-node-polyfills/node_modules/.pnpm/process@0.11.10/node_modules/process/browser.js"], "sourcesContent": ["'use strict'\n\nexports.byteLength = byteLength\nexports.toByteArray = toByteArray\nexports.fromByteArray = fromByteArray\n\nvar lookup = []\nvar revLookup = []\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array\n\nvar code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i]\n  revLookup[code.charCodeAt(i)] = i\n}\n\n// Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\nrevLookup['-'.charCodeAt(0)] = 62\nrevLookup['_'.charCodeAt(0)] = 63\n\nfunction getLens (b64) {\n  var len = b64.length\n\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4')\n  }\n\n  // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n  var validLen = b64.indexOf('=')\n  if (validLen === -1) validLen = len\n\n  var placeHoldersLen = validLen === len\n    ? 0\n    : 4 - (validLen % 4)\n\n  return [validLen, placeHoldersLen]\n}\n\n// base64 is 4/3 + up to two characters of the original data\nfunction byteLength (b64) {\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction _byteLength (b64, validLen, placeHoldersLen) {\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction toByteArray (b64) {\n  var tmp\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen))\n\n  var curByte = 0\n\n  // if there are placeholders, only get up to the last complete 4 chars\n  var len = placeHoldersLen > 0\n    ? validLen - 4\n    : validLen\n\n  var i\n  for (i = 0; i < len; i += 4) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 18) |\n      (revLookup[b64.charCodeAt(i + 1)] << 12) |\n      (revLookup[b64.charCodeAt(i + 2)] << 6) |\n      revLookup[b64.charCodeAt(i + 3)]\n    arr[curByte++] = (tmp >> 16) & 0xFF\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 2) |\n      (revLookup[b64.charCodeAt(i + 1)] >> 4)\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 10) |\n      (revLookup[b64.charCodeAt(i + 1)] << 4) |\n      (revLookup[b64.charCodeAt(i + 2)] >> 2)\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  return arr\n}\n\nfunction tripletToBase64 (num) {\n  return lookup[num >> 18 & 0x3F] +\n    lookup[num >> 12 & 0x3F] +\n    lookup[num >> 6 & 0x3F] +\n    lookup[num & 0x3F]\n}\n\nfunction encodeChunk (uint8, start, end) {\n  var tmp\n  var output = []\n  for (var i = start; i < end; i += 3) {\n    tmp =\n      ((uint8[i] << 16) & 0xFF0000) +\n      ((uint8[i + 1] << 8) & 0xFF00) +\n      (uint8[i + 2] & 0xFF)\n    output.push(tripletToBase64(tmp))\n  }\n  return output.join('')\n}\n\nfunction fromByteArray (uint8) {\n  var tmp\n  var len = uint8.length\n  var extraBytes = len % 3 // if we have 1 byte left, pad 2 bytes\n  var parts = []\n  var maxChunkLength = 16383 // must be multiple of 3\n\n  // go through the array every three bytes, we'll deal with trailing stuff later\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)))\n  }\n\n  // pad the end with zeros, but make sure to not forget the extra bytes\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 2] +\n      lookup[(tmp << 4) & 0x3F] +\n      '=='\n    )\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 10] +\n      lookup[(tmp >> 4) & 0x3F] +\n      lookup[(tmp << 2) & 0x3F] +\n      '='\n    )\n  }\n\n  return parts.join('')\n}\n", "/*! ieee754. BSD-3-Clause License. Feross A<PERSON> <https://feross.org/opensource> */\nexports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var nBits = -7\n  var i = isLE ? (nBytes - 1) : 0\n  var d = isLE ? -1 : 1\n  var s = buffer[offset + i]\n\n  i += d\n\n  e = s & ((1 << (-nBits)) - 1)\n  s >>= (-nBits)\n  nBits += eLen\n  for (; nBits > 0; e = (e * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & ((1 << (-nBits)) - 1)\n  e >>= (-nBits)\n  nBits += mLen\n  for (; nBits > 0; m = (m * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias\n  } else if (e === eMax) {\n    return m ? NaN : ((s ? -1 : 1) * Infinity)\n  } else {\n    m = m + Math.pow(2, mLen)\n    e = e - eBias\n  }\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)\n}\n\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0)\n  var i = isLE ? 0 : (nBytes - 1)\n  var d = isLE ? 1 : -1\n  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0\n\n  value = Math.abs(value)\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0\n    e = eMax\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2)\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--\n      c *= 2\n    }\n    if (e + eBias >= 1) {\n      value += rt / c\n    } else {\n      value += rt * Math.pow(2, 1 - eBias)\n    }\n    if (value * c >= 2) {\n      e++\n      c /= 2\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0\n      e = eMax\n    } else if (e + eBias >= 1) {\n      m = ((value * c) - 1) * Math.pow(2, mLen)\n      e = e + eBias\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen)\n      e = 0\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = (e << mLen) | m\n  eLen += mLen\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128\n}\n", "/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> <https://feross.org>\n * @license  MIT\n */\n/* eslint-disable no-proto */\n\n'use strict'\n\nconst base64 = require('base64-js')\nconst ieee754 = require('ieee754')\nconst customInspectSymbol =\n  (typeof Symbol === 'function' && typeof Symbol['for'] === 'function') // eslint-disable-line dot-notation\n    ? Symbol['for']('nodejs.util.inspect.custom') // eslint-disable-line dot-notation\n    : null\n\nexports.Buffer = Buffer\nexports.SlowBuffer = SlowBuffer\nexports.INSPECT_MAX_BYTES = 50\n\nconst K_MAX_LENGTH = 0x7fffffff\nexports.kMaxLength = K_MAX_LENGTH\nconst { Uint8Array: GlobalUint8Array, ArrayBuffer: GlobalArrayBuffer, SharedArrayBuffer: GlobalSharedArrayBuffer } = globalThis\n\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Print warning and recommend using `buffer` v4.x which has an Object\n *               implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * We report that the browser does not support typed arrays if the are not subclassable\n * using __proto__. Firefox 4-29 lacks support for adding new properties to `Uint8Array`\n * (See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438). IE 10 lacks support\n * for __proto__ and has a buggy typed array implementation.\n */\nBuffer.TYPED_ARRAY_SUPPORT = typedArraySupport()\n\nif (!Buffer.TYPED_ARRAY_SUPPORT && typeof console !== 'undefined' &&\n    typeof console.error === 'function') {\n  console.error(\n    'This browser lacks typed array (Uint8Array) support which is required by ' +\n    '`buffer` v5.x. Use `buffer` v4.x if you require old browser support.'\n  )\n}\n\nfunction typedArraySupport () {\n  // Can typed array instances can be augmented?\n  try {\n    const arr = new GlobalUint8Array(1)\n    const proto = { foo: function () { return 42 } }\n    Object.setPrototypeOf(proto, GlobalUint8Array.prototype)\n    Object.setPrototypeOf(arr, proto)\n    return arr.foo() === 42\n  } catch (e) {\n    return false\n  }\n}\n\nObject.defineProperty(Buffer.prototype, 'parent', {\n  enumerable: true,\n  get: function () {\n    if (!Buffer.isBuffer(this)) return undefined\n    return this.buffer\n  }\n})\n\nObject.defineProperty(Buffer.prototype, 'offset', {\n  enumerable: true,\n  get: function () {\n    if (!Buffer.isBuffer(this)) return undefined\n    return this.byteOffset\n  }\n})\n\nfunction createBuffer (length) {\n  if (length > K_MAX_LENGTH) {\n    throw new RangeError('The value \"' + length + '\" is invalid for option \"size\"')\n  }\n  // Return an augmented `Uint8Array` instance\n  const buf = new GlobalUint8Array(length)\n  Object.setPrototypeOf(buf, Buffer.prototype)\n  return buf\n}\n\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\nfunction Buffer (arg, encodingOrOffset, length) {\n  // Common case.\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new TypeError(\n        'The \"string\" argument must be of type string. Received type number'\n      )\n    }\n    return allocUnsafe(arg)\n  }\n  return from(arg, encodingOrOffset, length)\n}\n\nBuffer.poolSize = 8192 // not used by this implementation\n\nfunction from (value, encodingOrOffset, length) {\n  if (typeof value === 'string') {\n    return fromString(value, encodingOrOffset)\n  }\n\n  if (GlobalArrayBuffer.isView(value)) {\n    return fromArrayView(value)\n  }\n\n  if (value == null) {\n    throw new TypeError(\n      'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +\n      'or Array-like Object. Received type ' + (typeof value)\n    )\n  }\n\n  if (isInstance(value, GlobalArrayBuffer) ||\n      (value && isInstance(value.buffer, GlobalArrayBuffer))) {\n    return fromArrayBuffer(value, encodingOrOffset, length)\n  }\n\n  if (typeof GlobalSharedArrayBuffer !== 'undefined' &&\n      (isInstance(value, GlobalSharedArrayBuffer) ||\n      (value && isInstance(value.buffer, GlobalSharedArrayBuffer)))) {\n    return fromArrayBuffer(value, encodingOrOffset, length)\n  }\n\n  if (typeof value === 'number') {\n    throw new TypeError(\n      'The \"value\" argument must not be of type number. Received type number'\n    )\n  }\n\n  const valueOf = value.valueOf && value.valueOf()\n  if (valueOf != null && valueOf !== value) {\n    return Buffer.from(valueOf, encodingOrOffset, length)\n  }\n\n  const b = fromObject(value)\n  if (b) return b\n\n  if (typeof Symbol !== 'undefined' && Symbol.toPrimitive != null &&\n      typeof value[Symbol.toPrimitive] === 'function') {\n    return Buffer.from(value[Symbol.toPrimitive]('string'), encodingOrOffset, length)\n  }\n\n  throw new TypeError(\n    'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +\n    'or Array-like Object. Received type ' + (typeof value)\n  )\n}\n\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(value, encodingOrOffset, length)\n}\n\n// Note: Change prototype *after* Buffer.from is defined to workaround Chrome bug:\n// https://github.com/feross/buffer/pull/148\nObject.setPrototypeOf(Buffer.prototype, GlobalUint8Array.prototype)\nObject.setPrototypeOf(Buffer, GlobalUint8Array)\n\nfunction assertSize (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be of type number')\n  } else if (size < 0) {\n    throw new RangeError('The value \"' + size + '\" is invalid for option \"size\"')\n  }\n}\n\nfunction alloc (size, fill, encoding) {\n  assertSize(size)\n  if (size <= 0) {\n    return createBuffer(size)\n  }\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpreted as a start offset.\n    return typeof encoding === 'string'\n      ? createBuffer(size).fill(fill, encoding)\n      : createBuffer(size).fill(fill)\n  }\n  return createBuffer(size)\n}\n\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(size, fill, encoding)\n}\n\nfunction allocUnsafe (size) {\n  assertSize(size)\n  return createBuffer(size < 0 ? 0 : checked(size) | 0)\n}\n\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(size)\n}\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(size)\n}\n\nfunction fromString (string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8'\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('Unknown encoding: ' + encoding)\n  }\n\n  const length = byteLength(string, encoding) | 0\n  let buf = createBuffer(length)\n\n  const actual = buf.write(string, encoding)\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    buf = buf.slice(0, actual)\n  }\n\n  return buf\n}\n\nfunction fromArrayLike (array) {\n  const length = array.length < 0 ? 0 : checked(array.length) | 0\n  const buf = createBuffer(length)\n  for (let i = 0; i < length; i += 1) {\n    buf[i] = array[i] & 255\n  }\n  return buf\n}\n\nfunction fromArrayView (arrayView) {\n  if (isInstance(arrayView, GlobalUint8Array)) {\n    const copy = new GlobalUint8Array(arrayView)\n    return fromArrayBuffer(copy.buffer, copy.byteOffset, copy.byteLength)\n  }\n  return fromArrayLike(arrayView)\n}\n\nfunction fromArrayBuffer (array, byteOffset, length) {\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\"offset\" is outside of buffer bounds')\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\"length\" is outside of buffer bounds')\n  }\n\n  let buf\n  if (byteOffset === undefined && length === undefined) {\n    buf = new GlobalUint8Array(array)\n  } else if (length === undefined) {\n    buf = new GlobalUint8Array(array, byteOffset)\n  } else {\n    buf = new GlobalUint8Array(array, byteOffset, length)\n  }\n\n  // Return an augmented `Uint8Array` instance\n  Object.setPrototypeOf(buf, Buffer.prototype)\n\n  return buf\n}\n\nfunction fromObject (obj) {\n  if (Buffer.isBuffer(obj)) {\n    const len = checked(obj.length) | 0\n    const buf = createBuffer(len)\n\n    if (buf.length === 0) {\n      return buf\n    }\n\n    obj.copy(buf, 0, 0, len)\n    return buf\n  }\n\n  if (obj.length !== undefined) {\n    if (typeof obj.length !== 'number' || numberIsNaN(obj.length)) {\n      return createBuffer(0)\n    }\n    return fromArrayLike(obj)\n  }\n\n  if (obj.type === 'Buffer' && Array.isArray(obj.data)) {\n    return fromArrayLike(obj.data)\n  }\n}\n\nfunction checked (length) {\n  // Note: cannot use `length < K_MAX_LENGTH` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= K_MAX_LENGTH) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' +\n                         'size: 0x' + K_MAX_LENGTH.toString(16) + ' bytes')\n  }\n  return length | 0\n}\n\nfunction SlowBuffer (length) {\n  if (+length != length) { // eslint-disable-line eqeqeq\n    length = 0\n  }\n  return Buffer.alloc(+length)\n}\n\nBuffer.isBuffer = function isBuffer (b) {\n  return b != null && b._isBuffer === true &&\n    b !== Buffer.prototype // so Buffer.isBuffer(Buffer.prototype) will be false\n}\n\nBuffer.compare = function compare (a, b) {\n  if (isInstance(a, GlobalUint8Array)) a = Buffer.from(a, a.offset, a.byteLength)\n  if (isInstance(b, GlobalUint8Array)) b = Buffer.from(b, b.offset, b.byteLength)\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    throw new TypeError(\n      'The \"buf1\", \"buf2\" arguments must be one of type Buffer or Uint8Array'\n    )\n  }\n\n  if (a === b) return 0\n\n  let x = a.length\n  let y = b.length\n\n  for (let i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i]\n      y = b[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\nBuffer.isEncoding = function isEncoding (encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true\n    default:\n      return false\n  }\n}\n\nBuffer.concat = function concat (list, length) {\n  if (!Array.isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers')\n  }\n\n  if (list.length === 0) {\n    return Buffer.alloc(0)\n  }\n\n  let i\n  if (length === undefined) {\n    length = 0\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length\n    }\n  }\n\n  const buffer = Buffer.allocUnsafe(length)\n  let pos = 0\n  for (i = 0; i < list.length; ++i) {\n    let buf = list[i]\n    if (isInstance(buf, GlobalUint8Array)) {\n      if (pos + buf.length > buffer.length) {\n        if (!Buffer.isBuffer(buf)) buf = Buffer.from(buf)\n        buf.copy(buffer, pos)\n      } else {\n        GlobalUint8Array.prototype.set.call(\n          buffer,\n          buf,\n          pos\n        )\n      }\n    } else if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers')\n    } else {\n      buf.copy(buffer, pos)\n    }\n    pos += buf.length\n  }\n  return buffer\n}\n\nfunction byteLength (string, encoding) {\n  if (Buffer.isBuffer(string)) {\n    return string.length\n  }\n  if (GlobalArrayBuffer.isView(string) || isInstance(string, GlobalArrayBuffer)) {\n    return string.byteLength\n  }\n  if (typeof string !== 'string') {\n    throw new TypeError(\n      'The \"string\" argument must be one of type string, Buffer, or ArrayBuffer. ' +\n      'Received type ' + typeof string\n    )\n  }\n\n  const len = string.length\n  const mustMatch = (arguments.length > 2 && arguments[2] === true)\n  if (!mustMatch && len === 0) return 0\n\n  // Use a for loop to avoid recursion\n  let loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len\n      case 'utf8':\n      case 'utf-8':\n        return utf8ToBytes(string).length\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2\n      case 'hex':\n        return len >>> 1\n      case 'base64':\n        return base64ToBytes(string).length\n      default:\n        if (loweredCase) {\n          return mustMatch ? -1 : utf8ToBytes(string).length // assume utf8\n        }\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\nBuffer.byteLength = byteLength\n\nfunction slowToString (encoding, start, end) {\n  let loweredCase = false\n\n  // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n  if (start === undefined || start < 0) {\n    start = 0\n  }\n  // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n  if (start > this.length) {\n    return ''\n  }\n\n  if (end === undefined || end > this.length) {\n    end = this.length\n  }\n\n  if (end <= 0) {\n    return ''\n  }\n\n  // Force coercion to uint32. This will also coerce falsey/NaN values to 0.\n  end >>>= 0\n  start >>>= 0\n\n  if (end <= start) {\n    return ''\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end)\n\n      case 'ascii':\n        return asciiSlice(this, start, end)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end)\n\n      case 'base64':\n        return base64Slice(this, start, end)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = (encoding + '').toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\n// This property is used by `Buffer.isBuffer` (and the `is-buffer` npm package)\n// to detect a Buffer instance. It's not possible to use `instanceof Buffer`\n// reliably in a browserify context because there could be multiple different\n// copies of the 'buffer' package in use. This method works even for Buffer\n// instances that were created from another copy of the `buffer` package.\n// See: https://github.com/feross/buffer/issues/154\nBuffer.prototype._isBuffer = true\n\nfunction swap (b, n, m) {\n  const i = b[n]\n  b[n] = b[m]\n  b[m] = i\n}\n\nBuffer.prototype.swap16 = function swap16 () {\n  const len = this.length\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits')\n  }\n  for (let i = 0; i < len; i += 2) {\n    swap(this, i, i + 1)\n  }\n  return this\n}\n\nBuffer.prototype.swap32 = function swap32 () {\n  const len = this.length\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits')\n  }\n  for (let i = 0; i < len; i += 4) {\n    swap(this, i, i + 3)\n    swap(this, i + 1, i + 2)\n  }\n  return this\n}\n\nBuffer.prototype.swap64 = function swap64 () {\n  const len = this.length\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits')\n  }\n  for (let i = 0; i < len; i += 8) {\n    swap(this, i, i + 7)\n    swap(this, i + 1, i + 6)\n    swap(this, i + 2, i + 5)\n    swap(this, i + 3, i + 4)\n  }\n  return this\n}\n\nBuffer.prototype.toString = function toString () {\n  const length = this.length\n  if (length === 0) return ''\n  if (arguments.length === 0) return utf8Slice(this, 0, length)\n  return slowToString.apply(this, arguments)\n}\n\nBuffer.prototype.toLocaleString = Buffer.prototype.toString\n\nBuffer.prototype.equals = function equals (b) {\n  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer')\n  if (this === b) return true\n  return Buffer.compare(this, b) === 0\n}\n\nBuffer.prototype.inspect = function inspect () {\n  let str = ''\n  const max = exports.INSPECT_MAX_BYTES\n  str = this.toString('hex', 0, max).replace(/(.{2})/g, '$1 ').trim()\n  if (this.length > max) str += ' ... '\n  return '<Buffer ' + str + '>'\n}\nif (customInspectSymbol) {\n  Buffer.prototype[customInspectSymbol] = Buffer.prototype.inspect\n}\n\nBuffer.prototype.compare = function compare (target, start, end, thisStart, thisEnd) {\n  if (isInstance(target, GlobalUint8Array)) {\n    target = Buffer.from(target, target.offset, target.byteLength)\n  }\n  if (!Buffer.isBuffer(target)) {\n    throw new TypeError(\n      'The \"target\" argument must be one of type Buffer or Uint8Array. ' +\n      'Received type ' + (typeof target)\n    )\n  }\n\n  if (start === undefined) {\n    start = 0\n  }\n  if (end === undefined) {\n    end = target ? target.length : 0\n  }\n  if (thisStart === undefined) {\n    thisStart = 0\n  }\n  if (thisEnd === undefined) {\n    thisEnd = this.length\n  }\n\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index')\n  }\n\n  if (thisStart >= thisEnd && start >= end) {\n    return 0\n  }\n  if (thisStart >= thisEnd) {\n    return -1\n  }\n  if (start >= end) {\n    return 1\n  }\n\n  start >>>= 0\n  end >>>= 0\n  thisStart >>>= 0\n  thisEnd >>>= 0\n\n  if (this === target) return 0\n\n  let x = thisEnd - thisStart\n  let y = end - start\n  const len = Math.min(x, y)\n\n  const thisCopy = this.slice(thisStart, thisEnd)\n  const targetCopy = target.slice(start, end)\n\n  for (let i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i]\n      y = targetCopy[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\n// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\nfunction bidirectionalIndexOf (buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1\n\n  // Normalize byteOffset\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset\n    byteOffset = 0\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000\n  }\n  byteOffset = +byteOffset // Coerce to Number.\n  if (numberIsNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : (buffer.length - 1)\n  }\n\n  // Normalize byteOffset: negative offsets start from the end of the buffer\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1\n    else byteOffset = buffer.length - 1\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0\n    else return -1\n  }\n\n  // Normalize val\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding)\n  }\n\n  // Finally, search either indexOf (if dir is true) or lastIndexOf\n  if (Buffer.isBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1\n    }\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir)\n  } else if (typeof val === 'number') {\n    val = val & 0xFF // Search for a byte value [0-255]\n    if (typeof GlobalUint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return GlobalUint8Array.prototype.indexOf.call(buffer, val, byteOffset)\n      } else {\n        return GlobalUint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset)\n      }\n    }\n    return arrayIndexOf(buffer, [val], byteOffset, encoding, dir)\n  }\n\n  throw new TypeError('val must be string, number or Buffer')\n}\n\nfunction arrayIndexOf (arr, val, byteOffset, encoding, dir) {\n  let indexSize = 1\n  let arrLength = arr.length\n  let valLength = val.length\n\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase()\n    if (encoding === 'ucs2' || encoding === 'ucs-2' ||\n        encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1\n      }\n      indexSize = 2\n      arrLength /= 2\n      valLength /= 2\n      byteOffset /= 2\n    }\n  }\n\n  function read (buf, i) {\n    if (indexSize === 1) {\n      return buf[i]\n    } else {\n      return buf.readUInt16BE(i * indexSize)\n    }\n  }\n\n  let i\n  if (dir) {\n    let foundIndex = -1\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex\n        foundIndex = -1\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength\n    for (i = byteOffset; i >= 0; i--) {\n      let found = true\n      for (let j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false\n          break\n        }\n      }\n      if (found) return i\n    }\n  }\n\n  return -1\n}\n\nBuffer.prototype.includes = function includes (val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1\n}\n\nBuffer.prototype.indexOf = function indexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true)\n}\n\nBuffer.prototype.lastIndexOf = function lastIndexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false)\n}\n\nfunction hexWrite (buf, string, offset, length) {\n  offset = Number(offset) || 0\n  const remaining = buf.length - offset\n  if (!length) {\n    length = remaining\n  } else {\n    length = Number(length)\n    if (length > remaining) {\n      length = remaining\n    }\n  }\n\n  const strLen = string.length\n\n  if (length > strLen / 2) {\n    length = strLen / 2\n  }\n  let i\n  for (i = 0; i < length; ++i) {\n    const parsed = parseInt(string.substr(i * 2, 2), 16)\n    if (numberIsNaN(parsed)) return i\n    buf[offset + i] = parsed\n  }\n  return i\n}\n\nfunction utf8Write (buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nfunction asciiWrite (buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length)\n}\n\nfunction base64Write (buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length)\n}\n\nfunction ucs2Write (buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nBuffer.prototype.write = function write (string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8'\n    length = this.length\n    offset = 0\n  // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset\n    length = this.length\n    offset = 0\n  // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset >>> 0\n    if (isFinite(length)) {\n      length = length >>> 0\n      if (encoding === undefined) encoding = 'utf8'\n    } else {\n      encoding = length\n      length = undefined\n    }\n  } else {\n    throw new Error(\n      'Buffer.write(string, encoding, offset[, length]) is no longer supported'\n    )\n  }\n\n  const remaining = this.length - offset\n  if (length === undefined || length > remaining) length = remaining\n\n  if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds')\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  let loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length)\n\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return asciiWrite(this, string, offset, length)\n\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\nBuffer.prototype.toJSON = function toJSON () {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  }\n}\n\nfunction base64Slice (buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return base64.fromByteArray(buf)\n  } else {\n    return base64.fromByteArray(buf.slice(start, end))\n  }\n}\n\nfunction utf8Slice (buf, start, end) {\n  end = Math.min(buf.length, end)\n  const res = []\n\n  let i = start\n  while (i < end) {\n    const firstByte = buf[i]\n    let codePoint = null\n    let bytesPerSequence = (firstByte > 0xEF)\n      ? 4\n      : (firstByte > 0xDF)\n          ? 3\n          : (firstByte > 0xBF)\n              ? 2\n              : 1\n\n    if (i + bytesPerSequence <= end) {\n      let secondByte, thirdByte, fourthByte, tempCodePoint\n\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte\n          }\n          break\n        case 2:\n          secondByte = buf[i + 1]\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | (secondByte & 0x3F)\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 3:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | (thirdByte & 0x3F)\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 4:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          fourthByte = buf[i + 3]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | (fourthByte & 0x3F)\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint\n            }\n          }\n      }\n    }\n\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD\n      bytesPerSequence = 1\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800)\n      codePoint = 0xDC00 | codePoint & 0x3FF\n    }\n\n    res.push(codePoint)\n    i += bytesPerSequence\n  }\n\n  return decodeCodePointsArray(res)\n}\n\n// Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\nconst MAX_ARGUMENTS_LENGTH = 0x1000\n\nfunction decodeCodePointsArray (codePoints) {\n  const len = codePoints.length\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints) // avoid extra slice()\n  }\n\n  // Decode in chunks to avoid \"call stack size exceeded\".\n  let res = ''\n  let i = 0\n  while (i < len) {\n    res += String.fromCharCode.apply(\n      String,\n      codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH)\n    )\n  }\n  return res\n}\n\nfunction asciiSlice (buf, start, end) {\n  let ret = ''\n  end = Math.min(buf.length, end)\n\n  for (let i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F)\n  }\n  return ret\n}\n\nfunction latin1Slice (buf, start, end) {\n  let ret = ''\n  end = Math.min(buf.length, end)\n\n  for (let i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i])\n  }\n  return ret\n}\n\nfunction hexSlice (buf, start, end) {\n  const len = buf.length\n\n  if (!start || start < 0) start = 0\n  if (!end || end < 0 || end > len) end = len\n\n  let out = ''\n  for (let i = start; i < end; ++i) {\n    out += hexSliceLookupTable[buf[i]]\n  }\n  return out\n}\n\nfunction utf16leSlice (buf, start, end) {\n  const bytes = buf.slice(start, end)\n  let res = ''\n  // If bytes.length is odd, the last 8 bits must be ignored (same as node.js)\n  for (let i = 0; i < bytes.length - 1; i += 2) {\n    res += String.fromCharCode(bytes[i] + (bytes[i + 1] * 256))\n  }\n  return res\n}\n\nBuffer.prototype.slice = function slice (start, end) {\n  const len = this.length\n  start = ~~start\n  end = end === undefined ? len : ~~end\n\n  if (start < 0) {\n    start += len\n    if (start < 0) start = 0\n  } else if (start > len) {\n    start = len\n  }\n\n  if (end < 0) {\n    end += len\n    if (end < 0) end = 0\n  } else if (end > len) {\n    end = len\n  }\n\n  if (end < start) end = start\n\n  const newBuf = this.subarray(start, end)\n  // Return an augmented `Uint8Array` instance\n  Object.setPrototypeOf(newBuf, Buffer.prototype)\n\n  return newBuf\n}\n\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\nfunction checkOffset (offset, ext, length) {\n  if ((offset % 1) !== 0 || offset < 0) throw new RangeError('offset is not uint')\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length')\n}\n\nBuffer.prototype.readUintLE =\nBuffer.prototype.readUIntLE = function readUIntLE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  let val = this[offset]\n  let mul = 1\n  let i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUintBE =\nBuffer.prototype.readUIntBE = function readUIntBE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length)\n  }\n\n  let val = this[offset + --byteLength]\n  let mul = 1\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUint8 =\nBuffer.prototype.readUInt8 = function readUInt8 (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  return this[offset]\n}\n\nBuffer.prototype.readUint16LE =\nBuffer.prototype.readUInt16LE = function readUInt16LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return this[offset] | (this[offset + 1] << 8)\n}\n\nBuffer.prototype.readUint16BE =\nBuffer.prototype.readUInt16BE = function readUInt16BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return (this[offset] << 8) | this[offset + 1]\n}\n\nBuffer.prototype.readUint32LE =\nBuffer.prototype.readUInt32LE = function readUInt32LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return ((this[offset]) |\n      (this[offset + 1] << 8) |\n      (this[offset + 2] << 16)) +\n      (this[offset + 3] * 0x1000000)\n}\n\nBuffer.prototype.readUint32BE =\nBuffer.prototype.readUInt32BE = function readUInt32BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] * 0x1000000) +\n    ((this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    this[offset + 3])\n}\n\nBuffer.prototype.readBigUInt64LE = defineBigIntMethod(function readBigUInt64LE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const lo = first +\n    this[++offset] * 2 ** 8 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 24\n\n  const hi = this[++offset] +\n    this[++offset] * 2 ** 8 +\n    this[++offset] * 2 ** 16 +\n    last * 2 ** 24\n\n  return BigInt(lo) + (BigInt(hi) << BigInt(32))\n})\n\nBuffer.prototype.readBigUInt64BE = defineBigIntMethod(function readBigUInt64BE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const hi = first * 2 ** 24 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    this[++offset]\n\n  const lo = this[++offset] * 2 ** 24 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    last\n\n  return (BigInt(hi) << BigInt(32)) + BigInt(lo)\n})\n\nBuffer.prototype.readIntLE = function readIntLE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  let val = this[offset]\n  let mul = 1\n  let i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readIntBE = function readIntBE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  let i = byteLength\n  let mul = 1\n  let val = this[offset + --i]\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readInt8 = function readInt8 (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  if (!(this[offset] & 0x80)) return (this[offset])\n  return ((0xff - this[offset] + 1) * -1)\n}\n\nBuffer.prototype.readInt16LE = function readInt16LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  const val = this[offset] | (this[offset + 1] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt16BE = function readInt16BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  const val = this[offset + 1] | (this[offset] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt32LE = function readInt32LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset]) |\n    (this[offset + 1] << 8) |\n    (this[offset + 2] << 16) |\n    (this[offset + 3] << 24)\n}\n\nBuffer.prototype.readInt32BE = function readInt32BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] << 24) |\n    (this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    (this[offset + 3])\n}\n\nBuffer.prototype.readBigInt64LE = defineBigIntMethod(function readBigInt64LE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const val = this[offset + 4] +\n    this[offset + 5] * 2 ** 8 +\n    this[offset + 6] * 2 ** 16 +\n    (last << 24) // Overflow\n\n  return (BigInt(val) << BigInt(32)) +\n    BigInt(first +\n    this[++offset] * 2 ** 8 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 24)\n})\n\nBuffer.prototype.readBigInt64BE = defineBigIntMethod(function readBigInt64BE (offset) {\n  offset = offset >>> 0\n  validateNumber(offset, 'offset')\n  const first = this[offset]\n  const last = this[offset + 7]\n  if (first === undefined || last === undefined) {\n    boundsError(offset, this.length - 8)\n  }\n\n  const val = (first << 24) + // Overflow\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    this[++offset]\n\n  return (BigInt(val) << BigInt(32)) +\n    BigInt(this[++offset] * 2 ** 24 +\n    this[++offset] * 2 ** 16 +\n    this[++offset] * 2 ** 8 +\n    last)\n})\n\nBuffer.prototype.readFloatLE = function readFloatLE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, true, 23, 4)\n}\n\nBuffer.prototype.readFloatBE = function readFloatBE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, false, 23, 4)\n}\n\nBuffer.prototype.readDoubleLE = function readDoubleLE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, true, 52, 8)\n}\n\nBuffer.prototype.readDoubleBE = function readDoubleBE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, false, 52, 8)\n}\n\nfunction checkInt (buf, value, offset, ext, max, min) {\n  if (!Buffer.isBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance')\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds')\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n}\n\nBuffer.prototype.writeUintLE =\nBuffer.prototype.writeUIntLE = function writeUIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) {\n    const maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  let mul = 1\n  let i = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUintBE =\nBuffer.prototype.writeUIntBE = function writeUIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) {\n    const maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  let i = byteLength - 1\n  let mul = 1\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUint8 =\nBuffer.prototype.writeUInt8 = function writeUInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0)\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nBuffer.prototype.writeUint16LE =\nBuffer.prototype.writeUInt16LE = function writeUInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  this[offset] = (value & 0xff)\n  this[offset + 1] = (value >>> 8)\n  return offset + 2\n}\n\nBuffer.prototype.writeUint16BE =\nBuffer.prototype.writeUInt16BE = function writeUInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  this[offset] = (value >>> 8)\n  this[offset + 1] = (value & 0xff)\n  return offset + 2\n}\n\nBuffer.prototype.writeUint32LE =\nBuffer.prototype.writeUInt32LE = function writeUInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  this[offset + 3] = (value >>> 24)\n  this[offset + 2] = (value >>> 16)\n  this[offset + 1] = (value >>> 8)\n  this[offset] = (value & 0xff)\n  return offset + 4\n}\n\nBuffer.prototype.writeUint32BE =\nBuffer.prototype.writeUInt32BE = function writeUInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  this[offset] = (value >>> 24)\n  this[offset + 1] = (value >>> 16)\n  this[offset + 2] = (value >>> 8)\n  this[offset + 3] = (value & 0xff)\n  return offset + 4\n}\n\nfunction wrtBigUInt64LE (buf, value, offset, min, max) {\n  checkIntBI(value, min, max, buf, offset, 7)\n\n  let lo = Number(value & BigInt(0xffffffff))\n  buf[offset++] = lo\n  lo = lo >> 8\n  buf[offset++] = lo\n  lo = lo >> 8\n  buf[offset++] = lo\n  lo = lo >> 8\n  buf[offset++] = lo\n  let hi = Number(value >> BigInt(32) & BigInt(0xffffffff))\n  buf[offset++] = hi\n  hi = hi >> 8\n  buf[offset++] = hi\n  hi = hi >> 8\n  buf[offset++] = hi\n  hi = hi >> 8\n  buf[offset++] = hi\n  return offset\n}\n\nfunction wrtBigUInt64BE (buf, value, offset, min, max) {\n  checkIntBI(value, min, max, buf, offset, 7)\n\n  let lo = Number(value & BigInt(0xffffffff))\n  buf[offset + 7] = lo\n  lo = lo >> 8\n  buf[offset + 6] = lo\n  lo = lo >> 8\n  buf[offset + 5] = lo\n  lo = lo >> 8\n  buf[offset + 4] = lo\n  let hi = Number(value >> BigInt(32) & BigInt(0xffffffff))\n  buf[offset + 3] = hi\n  hi = hi >> 8\n  buf[offset + 2] = hi\n  hi = hi >> 8\n  buf[offset + 1] = hi\n  hi = hi >> 8\n  buf[offset] = hi\n  return offset + 8\n}\n\nBuffer.prototype.writeBigUInt64LE = defineBigIntMethod(function writeBigUInt64LE (value, offset = 0) {\n  return wrtBigUInt64LE(this, value, offset, BigInt(0), BigInt('0xffffffffffffffff'))\n})\n\nBuffer.prototype.writeBigUInt64BE = defineBigIntMethod(function writeBigUInt64BE (value, offset = 0) {\n  return wrtBigUInt64BE(this, value, offset, BigInt(0), BigInt('0xffffffffffffffff'))\n})\n\nBuffer.prototype.writeIntLE = function writeIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    const limit = Math.pow(2, (8 * byteLength) - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  let i = 0\n  let mul = 1\n  let sub = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeIntBE = function writeIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    const limit = Math.pow(2, (8 * byteLength) - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  let i = byteLength - 1\n  let mul = 1\n  let sub = 0\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeInt8 = function writeInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80)\n  if (value < 0) value = 0xff + value + 1\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nBuffer.prototype.writeInt16LE = function writeInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  this[offset] = (value & 0xff)\n  this[offset + 1] = (value >>> 8)\n  return offset + 2\n}\n\nBuffer.prototype.writeInt16BE = function writeInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  this[offset] = (value >>> 8)\n  this[offset + 1] = (value & 0xff)\n  return offset + 2\n}\n\nBuffer.prototype.writeInt32LE = function writeInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  this[offset] = (value & 0xff)\n  this[offset + 1] = (value >>> 8)\n  this[offset + 2] = (value >>> 16)\n  this[offset + 3] = (value >>> 24)\n  return offset + 4\n}\n\nBuffer.prototype.writeInt32BE = function writeInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  if (value < 0) value = 0xffffffff + value + 1\n  this[offset] = (value >>> 24)\n  this[offset + 1] = (value >>> 16)\n  this[offset + 2] = (value >>> 8)\n  this[offset + 3] = (value & 0xff)\n  return offset + 4\n}\n\nBuffer.prototype.writeBigInt64LE = defineBigIntMethod(function writeBigInt64LE (value, offset = 0) {\n  return wrtBigUInt64LE(this, value, offset, -BigInt('0x8000000000000000'), BigInt('0x7fffffffffffffff'))\n})\n\nBuffer.prototype.writeBigInt64BE = defineBigIntMethod(function writeBigInt64BE (value, offset = 0) {\n  return wrtBigUInt64BE(this, value, offset, -BigInt('0x8000000000000000'), BigInt('0x7fffffffffffffff'))\n})\n\nfunction checkIEEE754 (buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n  if (offset < 0) throw new RangeError('Index out of range')\n}\n\nfunction writeFloat (buf, value, offset, littleEndian, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 23, 4)\n  return offset + 4\n}\n\nBuffer.prototype.writeFloatLE = function writeFloatLE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeFloatBE = function writeFloatBE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert)\n}\n\nfunction writeDouble (buf, value, offset, littleEndian, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 52, 8)\n  return offset + 8\n}\n\nBuffer.prototype.writeDoubleLE = function writeDoubleLE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeDoubleBE = function writeDoubleBE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert)\n}\n\n// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\nBuffer.prototype.copy = function copy (target, targetStart, start, end) {\n  if (!Buffer.isBuffer(target)) throw new TypeError('argument should be a Buffer')\n  if (!start) start = 0\n  if (!end && end !== 0) end = this.length\n  if (targetStart >= target.length) targetStart = target.length\n  if (!targetStart) targetStart = 0\n  if (end > 0 && end < start) end = start\n\n  // Copy 0 bytes; we're done\n  if (end === start) return 0\n  if (target.length === 0 || this.length === 0) return 0\n\n  // Fatal error conditions\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds')\n  }\n  if (start < 0 || start >= this.length) throw new RangeError('Index out of range')\n  if (end < 0) throw new RangeError('sourceEnd out of bounds')\n\n  // Are we oob?\n  if (end > this.length) end = this.length\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start\n  }\n\n  const len = end - start\n\n  if (this === target && typeof GlobalUint8Array.prototype.copyWithin === 'function') {\n    // Use built-in when available, missing from IE11\n    this.copyWithin(targetStart, start, end)\n  } else {\n    GlobalUint8Array.prototype.set.call(\n      target,\n      this.subarray(start, end),\n      targetStart\n    )\n  }\n\n  return len\n}\n\n// Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\nBuffer.prototype.fill = function fill (val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start\n      start = 0\n      end = this.length\n    } else if (typeof end === 'string') {\n      encoding = end\n      end = this.length\n    }\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string')\n    }\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding)\n    }\n    if (val.length === 1) {\n      const code = val.charCodeAt(0)\n      if ((encoding === 'utf8' && code < 128) ||\n          encoding === 'latin1') {\n        // Fast path: If `val` fits into a single byte, use that numeric value.\n        val = code\n      }\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255\n  } else if (typeof val === 'boolean') {\n    val = Number(val)\n  }\n\n  // Invalid ranges are not set to a default, so can range check early.\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index')\n  }\n\n  if (end <= start) {\n    return this\n  }\n\n  start = start >>> 0\n  end = end === undefined ? this.length : end >>> 0\n\n  if (!val) val = 0\n\n  let i\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val\n    }\n  } else {\n    const bytes = Buffer.isBuffer(val)\n      ? val\n      : Buffer.from(val, encoding)\n    const len = bytes.length\n    if (len === 0) {\n      throw new TypeError('The value \"' + val +\n        '\" is invalid for argument \"value\"')\n    }\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len]\n    }\n  }\n\n  return this\n}\n\n// CUSTOM ERRORS\n// =============\n\n// Simplified versions from Node, changed for Buffer-only usage\nconst errors = {}\nfunction E (sym, getMessage, Base) {\n  errors[sym] = class NodeError extends Base {\n    constructor () {\n      super()\n\n      Object.defineProperty(this, 'message', {\n        value: getMessage.apply(this, arguments),\n        writable: true,\n        configurable: true\n      })\n\n      // Add the error code to the name to include it in the stack trace.\n      this.name = `${this.name} [${sym}]`\n      // Access the stack to generate the error message including the error code\n      // from the name.\n      this.stack // eslint-disable-line no-unused-expressions\n      // Reset the name to the actual name.\n      delete this.name\n    }\n\n    get code () {\n      return sym\n    }\n\n    set code (value) {\n      Object.defineProperty(this, 'code', {\n        configurable: true,\n        enumerable: true,\n        value,\n        writable: true\n      })\n    }\n\n    toString () {\n      return `${this.name} [${sym}]: ${this.message}`\n    }\n  }\n}\n\nE('ERR_BUFFER_OUT_OF_BOUNDS',\n  function (name) {\n    if (name) {\n      return `${name} is outside of buffer bounds`\n    }\n\n    return 'Attempt to access memory outside buffer bounds'\n  }, RangeError)\nE('ERR_INVALID_ARG_TYPE',\n  function (name, actual) {\n    return `The \"${name}\" argument must be of type number. Received type ${typeof actual}`\n  }, TypeError)\nE('ERR_OUT_OF_RANGE',\n  function (str, range, input) {\n    let msg = `The value of \"${str}\" is out of range.`\n    let received = input\n    if (Number.isInteger(input) && Math.abs(input) > 2 ** 32) {\n      received = addNumericalSeparator(String(input))\n    } else if (typeof input === 'bigint') {\n      received = String(input)\n      if (input > BigInt(2) ** BigInt(32) || input < -(BigInt(2) ** BigInt(32))) {\n        received = addNumericalSeparator(received)\n      }\n      received += 'n'\n    }\n    msg += ` It must be ${range}. Received ${received}`\n    return msg\n  }, RangeError)\n\nfunction addNumericalSeparator (val) {\n  let res = ''\n  let i = val.length\n  const start = val[0] === '-' ? 1 : 0\n  for (; i >= start + 4; i -= 3) {\n    res = `_${val.slice(i - 3, i)}${res}`\n  }\n  return `${val.slice(0, i)}${res}`\n}\n\n// CHECK FUNCTIONS\n// ===============\n\nfunction checkBounds (buf, offset, byteLength) {\n  validateNumber(offset, 'offset')\n  if (buf[offset] === undefined || buf[offset + byteLength] === undefined) {\n    boundsError(offset, buf.length - (byteLength + 1))\n  }\n}\n\nfunction checkIntBI (value, min, max, buf, offset, byteLength) {\n  if (value > max || value < min) {\n    const n = typeof min === 'bigint' ? 'n' : ''\n    let range\n    if (byteLength > 3) {\n      if (min === 0 || min === BigInt(0)) {\n        range = `>= 0${n} and < 2${n} ** ${(byteLength + 1) * 8}${n}`\n      } else {\n        range = `>= -(2${n} ** ${(byteLength + 1) * 8 - 1}${n}) and < 2 ** ` +\n                `${(byteLength + 1) * 8 - 1}${n}`\n      }\n    } else {\n      range = `>= ${min}${n} and <= ${max}${n}`\n    }\n    throw new errors.ERR_OUT_OF_RANGE('value', range, value)\n  }\n  checkBounds(buf, offset, byteLength)\n}\n\nfunction validateNumber (value, name) {\n  if (typeof value !== 'number') {\n    throw new errors.ERR_INVALID_ARG_TYPE(name, 'number', value)\n  }\n}\n\nfunction boundsError (value, length, type) {\n  if (Math.floor(value) !== value) {\n    validateNumber(value, type)\n    throw new errors.ERR_OUT_OF_RANGE(type || 'offset', 'an integer', value)\n  }\n\n  if (length < 0) {\n    throw new errors.ERR_BUFFER_OUT_OF_BOUNDS()\n  }\n\n  throw new errors.ERR_OUT_OF_RANGE(type || 'offset',\n                                    `>= ${type ? 1 : 0} and <= ${length}`,\n                                    value)\n}\n\n// HELPER FUNCTIONS\n// ================\n\nconst INVALID_BASE64_RE = /[^+/0-9A-Za-z-_]/g\n\nfunction base64clean (str) {\n  // Node takes equal signs as end of the Base64 encoding\n  str = str.split('=')[0]\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = str.trim().replace(INVALID_BASE64_RE, '')\n  // Node converts strings with length < 2 to ''\n  if (str.length < 2) return ''\n  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n  while (str.length % 4 !== 0) {\n    str = str + '='\n  }\n  return str\n}\n\nfunction utf8ToBytes (string, units) {\n  units = units || Infinity\n  let codePoint\n  const length = string.length\n  let leadSurrogate = null\n  const bytes = []\n\n  for (let i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i)\n\n    // is surrogate component\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        }\n\n        // valid lead\n        leadSurrogate = codePoint\n\n        continue\n      }\n\n      // 2 leads in a row\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n        leadSurrogate = codePoint\n        continue\n      }\n\n      // valid surrogate pair\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n    }\n\n    leadSurrogate = null\n\n    // encode utf8\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break\n      bytes.push(codePoint)\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break\n      bytes.push(\n        codePoint >> 0x6 | 0xC0,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break\n      bytes.push(\n        codePoint >> 0xC | 0xE0,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break\n      bytes.push(\n        codePoint >> 0x12 | 0xF0,\n        codePoint >> 0xC & 0x3F | 0x80,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else {\n      throw new Error('Invalid code point')\n    }\n  }\n\n  return bytes\n}\n\nfunction asciiToBytes (str) {\n  const byteArray = []\n  for (let i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF)\n  }\n  return byteArray\n}\n\nfunction utf16leToBytes (str, units) {\n  let c, hi, lo\n  const byteArray = []\n  for (let i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break\n\n    c = str.charCodeAt(i)\n    hi = c >> 8\n    lo = c % 256\n    byteArray.push(lo)\n    byteArray.push(hi)\n  }\n\n  return byteArray\n}\n\nfunction base64ToBytes (str) {\n  return base64.toByteArray(base64clean(str))\n}\n\nfunction blitBuffer (src, dst, offset, length) {\n  let i\n  for (i = 0; i < length; ++i) {\n    if ((i + offset >= dst.length) || (i >= src.length)) break\n    dst[i + offset] = src[i]\n  }\n  return i\n}\n\n// ArrayBuffer or Uint8Array objects from other contexts (i.e. iframes) do not pass\n// the `instanceof` check but they should be treated as of that type.\n// See: https://github.com/feross/buffer/issues/166\nfunction isInstance (obj, type) {\n  return obj instanceof type ||\n    (obj != null && obj.constructor != null && obj.constructor.name != null &&\n      obj.constructor.name === type.name)\n}\nfunction numberIsNaN (obj) {\n  // For IE11 support\n  return obj !== obj // eslint-disable-line no-self-compare\n}\n\n// Create lookup table for `toString('hex')`\n// See: https://github.com/feross/buffer/issues/219\nconst hexSliceLookupTable = (function () {\n  const alphabet = '0123456789abcdef'\n  const table = new Array(256)\n  for (let i = 0; i < 16; ++i) {\n    const i16 = i * 16\n    for (let j = 0; j < 16; ++j) {\n      table[i16 + j] = alphabet[i] + alphabet[j]\n    }\n  }\n  return table\n})()\n\n// Return not function with Error if BigInt not supported\nfunction defineBigIntMethod (fn) {\n  return typeof BigInt === 'undefined' ? BufferBigIntNotDefined : fn\n}\n\nfunction BufferBigIntNotDefined () {\n  throw new Error('BigInt not supported')\n}\n", "import {\n  Blob,\n  BlobOptions,\n  Buffer,\n  File,\n  FileOptions,\n  INSPECT_MAX_BYTES,\n  // eslint-disable-next-line n/no-deprecated-api\n  SlowBuffer,\n  TranscodeEncoding,\n  atob,\n  btoa,\n  constants,\n  isAscii,\n  isUtf8,\n  kMaxLength,\n  kStringMaxLength,\n  resolveObjectURL,\n  transcode,\n// eslint-disable-next-line unicorn/prefer-node-protocol\n} from 'buffer'\n\nexport {\n  Blob,\n  BlobOptions,\n  Buffer,\n  File,\n  FileOptions,\n  INSPECT_MAX_BYTES,\n  SlowBuffer,\n  TranscodeEncoding,\n  atob,\n  btoa,\n  constants,\n  isAscii,\n  isUtf8,\n  kMaxLength,\n  kStringMaxLength,\n  resolveObjectURL,\n  transcode,\n}\n\nexport default Buffer\n", "// eslint-disable-next-line @typescript-eslint/no-invalid-this\nconst global = globalThis || this || self\n\nexport { global }\nexport default global\n", "// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,aAAA,aAAqB;AACrB,aAAA,cAAsB;AACtB,aAAA,gBAAwB;AAExB,QAAI,SAAS,CAAA;AACb,QAAI,YAAY,CAAA;AAChB,QAAI,MAAM,OAAO,eAAe,cAAc,aAAa;AAE3D,QAAI,OAAO;AACX,SAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC/C,aAAO,CAAC,IAAI,KAAK,CAAC;AAClB,gBAAU,KAAK,WAAW,CAAC,CAAC,IAAI;IAClC;AAHS;AAAO;AAOhB,cAAU,IAAI,WAAW,CAAC,CAAC,IAAI;AAC/B,cAAU,IAAI,WAAW,CAAC,CAAC,IAAI;AAE/B,aAAS,QAAS,KAAK;AACrB,UAAIA,OAAM,IAAI;AAEd,UAAIA,OAAM,IAAI,GAAG;AACf,cAAM,IAAI,MAAM,gDAAgD;MACpE;AAIE,UAAI,WAAW,IAAI,QAAQ,GAAG;AAC9B,UAAI,aAAa,GAAI,YAAWA;AAEhC,UAAI,kBAAkB,aAAaA,OAC/B,IACA,IAAK,WAAW;AAEpB,aAAO,CAAC,UAAU,eAAe;IACnC;AAGA,aAAS,WAAY,KAAK;AACxB,UAAI,OAAO,QAAQ,GAAG;AACtB,UAAI,WAAW,KAAK,CAAC;AACrB,UAAI,kBAAkB,KAAK,CAAC;AAC5B,cAAS,WAAW,mBAAmB,IAAI,IAAK;IAClD;AAEA,aAAS,YAAa,KAAK,UAAU,iBAAiB;AACpD,cAAS,WAAW,mBAAmB,IAAI,IAAK;IAClD;AAEA,aAAS,YAAa,KAAK;AACzB,UAAI;AACJ,UAAI,OAAO,QAAQ,GAAG;AACtB,UAAI,WAAW,KAAK,CAAC;AACrB,UAAI,kBAAkB,KAAK,CAAC;AAE5B,UAAI,MAAM,IAAI,IAAI,YAAY,KAAK,UAAU,eAAe,CAAC;AAE7D,UAAI,UAAU;AAGd,UAAIA,OAAM,kBAAkB,IACxB,WAAW,IACX;AAEJ,UAAIC;AACJ,WAAKA,KAAI,GAAGA,KAAID,MAAKC,MAAK,GAAG;AAC3B,cACG,UAAU,IAAI,WAAWA,EAAC,CAAC,KAAK,KAChC,UAAU,IAAI,WAAWA,KAAI,CAAC,CAAC,KAAK,KACpC,UAAU,IAAI,WAAWA,KAAI,CAAC,CAAC,KAAK,IACrC,UAAU,IAAI,WAAWA,KAAI,CAAC,CAAC;AACjC,YAAI,SAAS,IAAK,OAAO,KAAM;AAC/B,YAAI,SAAS,IAAK,OAAO,IAAK;AAC9B,YAAI,SAAS,IAAI,MAAM;MAC3B;AAEE,UAAI,oBAAoB,GAAG;AACzB,cACG,UAAU,IAAI,WAAWA,EAAC,CAAC,KAAK,IAChC,UAAU,IAAI,WAAWA,KAAI,CAAC,CAAC,KAAK;AACvC,YAAI,SAAS,IAAI,MAAM;MAC3B;AAEE,UAAI,oBAAoB,GAAG;AACzB,cACG,UAAU,IAAI,WAAWA,EAAC,CAAC,KAAK,KAChC,UAAU,IAAI,WAAWA,KAAI,CAAC,CAAC,KAAK,IACpC,UAAU,IAAI,WAAWA,KAAI,CAAC,CAAC,KAAK;AACvC,YAAI,SAAS,IAAK,OAAO,IAAK;AAC9B,YAAI,SAAS,IAAI,MAAM;MAC3B;AAEE,aAAO;IACT;AAEA,aAAS,gBAAiB,KAAK;AAC7B,aAAO,OAAO,OAAO,KAAK,EAAI,IAC5B,OAAO,OAAO,KAAK,EAAI,IACvB,OAAO,OAAO,IAAI,EAAI,IACtB,OAAO,MAAM,EAAI;IACrB;AAEA,aAAS,YAAa,OAAO,OAAO,KAAK;AACvC,UAAI;AACJ,UAAI,SAAS,CAAA;AACb,eAASA,KAAI,OAAOA,KAAI,KAAKA,MAAK,GAAG;AACnC,eACI,MAAMA,EAAC,KAAK,KAAM,aAClB,MAAMA,KAAI,CAAC,KAAK,IAAK,UACtB,MAAMA,KAAI,CAAC,IAAI;AAClB,eAAO,KAAK,gBAAgB,GAAG,CAAC;MACpC;AACE,aAAO,OAAO,KAAK,EAAE;IACvB;AAEA,aAAS,cAAe,OAAO;AAC7B,UAAI;AACJ,UAAID,OAAM,MAAM;AAChB,UAAI,aAAaA,OAAM;AACvB,UAAI,QAAQ,CAAA;AACZ,UAAI,iBAAiB;AAGrB,eAASC,KAAI,GAAGC,QAAOF,OAAM,YAAYC,KAAIC,OAAMD,MAAK,gBAAgB;AACtE,cAAM,KAAK,YAAY,OAAOA,IAAIA,KAAI,iBAAkBC,QAAOA,QAAQD,KAAI,cAAe,CAAC;MAC/F;AAGE,UAAI,eAAe,GAAG;AACpB,cAAM,MAAMD,OAAM,CAAC;AACnB,cAAM;UACJ,OAAO,OAAO,CAAC,IACf,OAAQ,OAAO,IAAK,EAAI,IACxB;QACN;MACA,WAAa,eAAe,GAAG;AAC3B,eAAO,MAAMA,OAAM,CAAC,KAAK,KAAK,MAAMA,OAAM,CAAC;AAC3C,cAAM;UACJ,OAAO,OAAO,EAAE,IAChB,OAAQ,OAAO,IAAK,EAAI,IACxB,OAAQ,OAAO,IAAK,EAAI,IACxB;QACN;MACA;AAEE,aAAO,MAAM,KAAK,EAAE;IACtB;;ACpJY,YAAA,OAAG,SAAUG,SAAQ,QAAQ,MAAM,MAAM,QAAQ;AAC3D,UAAI,GAAG;AACP,UAAI,OAAQ,SAAS,IAAK,OAAO;AACjC,UAAI,QAAQ,KAAK,QAAQ;AACzB,UAAI,QAAQ,QAAQ;AACpB,UAAI,QAAQ;AACZ,UAAIF,KAAI,OAAQ,SAAS,IAAK;AAC9B,UAAI,IAAI,OAAO,KAAK;AACpB,UAAI,IAAIE,QAAO,SAASF,EAAC;AAEzB,MAAAA,MAAK;AAEL,UAAI,KAAM,KAAM,CAAC,SAAU;AAC3B,YAAO,CAAC;AACR,eAAS;AACT,aAAO,QAAQ,GAAG,IAAK,IAAI,MAAOE,QAAO,SAASF,EAAC,GAAGA,MAAK,GAAG,SAAS,GAAG;MAAA;AAE1E,UAAI,KAAM,KAAM,CAAC,SAAU;AAC3B,YAAO,CAAC;AACR,eAAS;AACT,aAAO,QAAQ,GAAG,IAAK,IAAI,MAAOE,QAAO,SAASF,EAAC,GAAGA,MAAK,GAAG,SAAS,GAAG;MAAA;AAE1E,UAAI,MAAM,GAAG;AACX,YAAI,IAAI;MACZ,WAAa,MAAM,MAAM;AACrB,eAAO,IAAI,OAAQ,IAAI,KAAK,KAAK;MACrC,OAAS;AACL,YAAI,IAAI,KAAK,IAAI,GAAG,IAAI;AACxB,YAAI,IAAI;MACZ;AACE,cAAQ,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI;IAChD;AAEA,YAAA,QAAgB,SAAUE,SAAQ,OAAO,QAAQ,MAAM,MAAM,QAAQ;AACnE,UAAI,GAAG,GAAG;AACV,UAAI,OAAQ,SAAS,IAAK,OAAO;AACjC,UAAI,QAAQ,KAAK,QAAQ;AACzB,UAAI,QAAQ,QAAQ;AACpB,UAAI,KAAM,SAAS,KAAK,KAAK,IAAI,GAAG,GAAG,IAAI,KAAK,IAAI,GAAG,GAAG,IAAI;AAC9D,UAAIF,KAAI,OAAO,IAAK,SAAS;AAC7B,UAAI,IAAI,OAAO,IAAI;AACnB,UAAI,IAAI,QAAQ,KAAM,UAAU,KAAK,IAAI,QAAQ,IAAK,IAAI;AAE1D,cAAQ,KAAK,IAAI,KAAK;AAEtB,UAAI,MAAM,KAAK,KAAK,UAAU,UAAU;AACtC,YAAI,MAAM,KAAK,IAAI,IAAI;AACvB,YAAI;MACR,OAAS;AACL,YAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG;AACzC,YAAI,SAAS,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG;AACrC;AACA,eAAK;QACX;AACI,YAAI,IAAI,SAAS,GAAG;AAClB,mBAAS,KAAK;QACpB,OAAW;AACL,mBAAS,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK;QACzC;AACI,YAAI,QAAQ,KAAK,GAAG;AAClB;AACA,eAAK;QACX;AAEI,YAAI,IAAI,SAAS,MAAM;AACrB,cAAI;AACJ,cAAI;QACV,WAAe,IAAI,SAAS,GAAG;AACzB,eAAM,QAAQ,IAAK,KAAK,KAAK,IAAI,GAAG,IAAI;AACxC,cAAI,IAAI;QACd,OAAW;AACL,cAAI,QAAQ,KAAK,IAAI,GAAG,QAAQ,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI;AACrD,cAAI;QACV;MACA;AAEE,aAAO,QAAQ,GAAGE,QAAO,SAASF,EAAC,IAAI,IAAI,KAAMA,MAAK,GAAG,KAAK,KAAK,QAAQ,GAAG;MAAA;AAE9E,UAAK,KAAK,OAAQ;AAClB,cAAQ;AACR,aAAO,OAAO,GAAGE,QAAO,SAASF,EAAC,IAAI,IAAI,KAAMA,MAAK,GAAG,KAAK,KAAK,QAAQ,GAAG;MAAA;AAE7E,MAAAE,QAAO,SAASF,KAAI,CAAC,KAAK,IAAI;IAChC;;AC1EA,YAAM,SAASG;AACf,YAAMC,YAAUC;AAChB,YAAM,sBACH,OAAO,WAAW,cAAc,OAAO,OAAO,KAAK,MAAM,aACtD,OAAO,KAAK,EAAE,4BAA4B,IAC1C;AAEN,MAAAC,SAAA,SAAiBC;AACjB,MAAAD,SAAA,aAAqB;AACrB,MAAAA,SAAA,oBAA4B;AAE5B,YAAM,eAAe;AACrB,MAAAA,SAAA,aAAqB;AACrB,YAAM,EAAE,YAAY,kBAAkB,aAAa,mBAAmB,mBAAmB,wBAAuB,IAAK;AAgBrH,MAAAC,QAAO,sBAAsB,kBAAiB;AAE9C,UAAI,CAACA,QAAO,uBAAuB,OAAO,YAAY,eAClD,OAAO,QAAQ,UAAU,YAAY;AACvC,gBAAQ;UACN;;;AAKJ,eAAS,oBAAqB;AAE5B,YAAI;AACF,gBAAM,MAAM,IAAI,iBAAiB,CAAC;AAClC,gBAAM,QAAQ,EAAE,KAAK,WAAY;AAAE,mBAAO;UAAE,EAAE;AAC9C,iBAAO,eAAe,OAAO,iBAAiB,SAAS;AACvD,iBAAO,eAAe,KAAK,KAAK;AAChC,iBAAO,IAAI,IAAG,MAAO;iBACd,GAAG;AACV,iBAAO;;;AAIX,aAAO,eAAeA,QAAO,WAAW,UAAU;QAChD,YAAY;QACZ,KAAK,WAAY;AACf,cAAI,CAACA,QAAO,SAAS,IAAI,EAAG,QAAO;AACnC,iBAAO,KAAK;;MAEhB,CAAC;AAED,aAAO,eAAeA,QAAO,WAAW,UAAU;QAChD,YAAY;QACZ,KAAK,WAAY;AACf,cAAI,CAACA,QAAO,SAAS,IAAI,EAAG,QAAO;AACnC,iBAAO,KAAK;;MAEhB,CAAC;AAED,eAAS,aAAc,QAAQ;AAC7B,YAAI,SAAS,cAAc;AACzB,gBAAM,IAAI,WAAW,gBAAgB,SAAS,gCAAgC;;AAGhF,cAAM,MAAM,IAAI,iBAAiB,MAAM;AACvC,eAAO,eAAe,KAAKA,QAAO,SAAS;AAC3C,eAAO;;AAaT,eAASA,QAAQ,KAAK,kBAAkB,QAAQ;AAE9C,YAAI,OAAO,QAAQ,UAAU;AAC3B,cAAI,OAAO,qBAAqB,UAAU;AACxC,kBAAM,IAAI;cACR;;;AAGJ,iBAAO,YAAY,GAAG;;AAExB,eAAO,KAAK,KAAK,kBAAkB,MAAM;;AAG3C,MAAAA,QAAO,WAAW;AAElB,eAAS,KAAM,OAAO,kBAAkB,QAAQ;AAC9C,YAAI,OAAO,UAAU,UAAU;AAC7B,iBAAO,WAAW,OAAO,gBAAgB;;AAG3C,YAAI,kBAAkB,OAAO,KAAK,GAAG;AACnC,iBAAO,cAAc,KAAK;;AAG5B,YAAI,SAAS,MAAM;AACjB,gBAAM,IAAI;YACR,oHAC0C,OAAO;;;AAIrD,YAAI,WAAW,OAAO,iBAAiB,KAClC,SAAS,WAAW,MAAM,QAAQ,iBAAiB,GAAI;AAC1D,iBAAO,gBAAgB,OAAO,kBAAkB,MAAM;;AAGxD,YAAI,OAAO,4BAA4B,gBAClC,WAAW,OAAO,uBAAuB,KACzC,SAAS,WAAW,MAAM,QAAQ,uBAAuB,IAAK;AACjE,iBAAO,gBAAgB,OAAO,kBAAkB,MAAM;;AAGxD,YAAI,OAAO,UAAU,UAAU;AAC7B,gBAAM,IAAI;YACR;;;AAIJ,cAAM,UAAU,MAAM,WAAW,MAAM,QAAO;AAC9C,YAAI,WAAW,QAAQ,YAAY,OAAO;AACxC,iBAAOA,QAAO,KAAK,SAAS,kBAAkB,MAAM;;AAGtD,cAAM,IAAI,WAAW,KAAK;AAC1B,YAAI,EAAG,QAAO;AAEd,YAAI,OAAO,WAAW,eAAe,OAAO,eAAe,QACvD,OAAO,MAAM,OAAO,WAAW,MAAM,YAAY;AACnD,iBAAOA,QAAO,KAAK,MAAM,OAAO,WAAW,EAAE,QAAQ,GAAG,kBAAkB,MAAM;;AAGlF,cAAM,IAAI;UACR,oHAC0C,OAAO;;;AAYrD,MAAAA,QAAO,OAAO,SAAU,OAAO,kBAAkB,QAAQ;AACvD,eAAO,KAAK,OAAO,kBAAkB,MAAM;;AAK7C,aAAO,eAAeA,QAAO,WAAW,iBAAiB,SAAS;AAClE,aAAO,eAAeA,SAAQ,gBAAgB;AAE9C,eAAS,WAAY,MAAM;AACzB,YAAI,OAAO,SAAS,UAAU;AAC5B,gBAAM,IAAI,UAAU,wCAAwC;QAChE,WAAa,OAAO,GAAG;AACnB,gBAAM,IAAI,WAAW,gBAAgB,OAAO,gCAAgC;;;AAIhF,eAAS,MAAO,MAAM,MAAM,UAAU;AACpC,mBAAW,IAAI;AACf,YAAI,QAAQ,GAAG;AACb,iBAAO,aAAa,IAAI;;AAE1B,YAAI,SAAS,QAAW;AAItB,iBAAO,OAAO,aAAa,WACvB,aAAa,IAAI,EAAE,KAAK,MAAM,QAAQ,IACtC,aAAa,IAAI,EAAE,KAAK,IAAI;;AAElC,eAAO,aAAa,IAAI;;AAO1B,MAAAA,QAAO,QAAQ,SAAU,MAAM,MAAM,UAAU;AAC7C,eAAO,MAAM,MAAM,MAAM,QAAQ;;AAGnC,eAAS,YAAa,MAAM;AAC1B,mBAAW,IAAI;AACf,eAAO,aAAa,OAAO,IAAI,IAAI,QAAQ,IAAI,IAAI,CAAC;;AAMtD,MAAAA,QAAO,cAAc,SAAU,MAAM;AACnC,eAAO,YAAY,IAAI;;AAKzB,MAAAA,QAAO,kBAAkB,SAAU,MAAM;AACvC,eAAO,YAAY,IAAI;;AAGzB,eAAS,WAAY,QAAQ,UAAU;AACrC,YAAI,OAAO,aAAa,YAAY,aAAa,IAAI;AACnD,qBAAW;;AAGb,YAAI,CAACA,QAAO,WAAW,QAAQ,GAAG;AAChC,gBAAM,IAAI,UAAU,uBAAuB,QAAQ;;AAGrD,cAAM,SAASC,YAAW,QAAQ,QAAQ,IAAI;AAC9C,YAAI,MAAM,aAAa,MAAM;AAE7B,cAAM,SAAS,IAAI,MAAM,QAAQ,QAAQ;AAEzC,YAAI,WAAW,QAAQ;AAIrB,gBAAM,IAAI,MAAM,GAAG,MAAM;;AAG3B,eAAO;;AAGT,eAAS,cAAe,OAAO;AAC7B,cAAM,SAAS,MAAM,SAAS,IAAI,IAAI,QAAQ,MAAM,MAAM,IAAI;AAC9D,cAAM,MAAM,aAAa,MAAM;AAC/B,iBAASR,KAAI,GAAGA,KAAI,QAAQA,MAAK,GAAG;AAClC,cAAIA,EAAC,IAAI,MAAMA,EAAC,IAAI;;AAEtB,eAAO;;AAGT,eAAS,cAAe,WAAW;AACjC,YAAI,WAAW,WAAW,gBAAgB,GAAG;AAC3C,gBAAM,OAAO,IAAI,iBAAiB,SAAS;AAC3C,iBAAO,gBAAgB,KAAK,QAAQ,KAAK,YAAY,KAAK,UAAU;;AAEtE,eAAO,cAAc,SAAS;;AAGhC,eAAS,gBAAiB,OAAO,YAAY,QAAQ;AACnD,YAAI,aAAa,KAAK,MAAM,aAAa,YAAY;AACnD,gBAAM,IAAI,WAAW,sCAAsC;;AAG7D,YAAI,MAAM,aAAa,cAAc,UAAU,IAAI;AACjD,gBAAM,IAAI,WAAW,sCAAsC;;AAG7D,YAAI;AACJ,YAAI,eAAe,UAAa,WAAW,QAAW;AACpD,gBAAM,IAAI,iBAAiB,KAAK;QACpC,WAAa,WAAW,QAAW;AAC/B,gBAAM,IAAI,iBAAiB,OAAO,UAAU;QAChD,OAAS;AACL,gBAAM,IAAI,iBAAiB,OAAO,YAAY,MAAM;;AAItD,eAAO,eAAe,KAAKO,QAAO,SAAS;AAE3C,eAAO;;AAGT,eAAS,WAAY,KAAK;AACxB,YAAIA,QAAO,SAAS,GAAG,GAAG;AACxB,gBAAMR,OAAM,QAAQ,IAAI,MAAM,IAAI;AAClC,gBAAM,MAAM,aAAaA,IAAG;AAE5B,cAAI,IAAI,WAAW,GAAG;AACpB,mBAAO;;AAGT,cAAI,KAAK,KAAK,GAAG,GAAGA,IAAG;AACvB,iBAAO;;AAGT,YAAI,IAAI,WAAW,QAAW;AAC5B,cAAI,OAAO,IAAI,WAAW,YAAY,YAAY,IAAI,MAAM,GAAG;AAC7D,mBAAO,aAAa,CAAC;;AAEvB,iBAAO,cAAc,GAAG;;AAG1B,YAAI,IAAI,SAAS,YAAY,MAAM,QAAQ,IAAI,IAAI,GAAG;AACpD,iBAAO,cAAc,IAAI,IAAI;;;AAIjC,eAAS,QAAS,QAAQ;AAGxB,YAAI,UAAU,cAAc;AAC1B,gBAAM,IAAI,WAAW,4DACa,aAAa,SAAS,EAAE,IAAI,QAAQ;;AAExE,eAAO,SAAS;;AAGlB,eAAS,WAAY,QAAQ;AAC3B,YAAI,CAAC,UAAU,QAAQ;AACrB,mBAAS;;AAEX,eAAOQ,QAAO,MAAM,CAAC,MAAM;;AAG7B,MAAAA,QAAO,WAAW,SAAS,SAAU,GAAG;AACtC,eAAO,KAAK,QAAQ,EAAE,cAAc,QAClC,MAAMA,QAAO;;AAGjB,MAAAA,QAAO,UAAU,SAAS,QAAS,GAAG,GAAG;AACvC,YAAI,WAAW,GAAG,gBAAgB,EAAG,KAAIA,QAAO,KAAK,GAAG,EAAE,QAAQ,EAAE,UAAU;AAC9E,YAAI,WAAW,GAAG,gBAAgB,EAAG,KAAIA,QAAO,KAAK,GAAG,EAAE,QAAQ,EAAE,UAAU;AAC9E,YAAI,CAACA,QAAO,SAAS,CAAC,KAAK,CAACA,QAAO,SAAS,CAAC,GAAG;AAC9C,gBAAM,IAAI;YACR;;;AAIJ,YAAI,MAAM,EAAG,QAAO;AAEpB,YAAI,IAAI,EAAE;AACV,YAAI,IAAI,EAAE;AAEV,iBAASP,KAAI,GAAGD,OAAM,KAAK,IAAI,GAAG,CAAC,GAAGC,KAAID,MAAK,EAAEC,IAAG;AAClD,cAAI,EAAEA,EAAC,MAAM,EAAEA,EAAC,GAAG;AACjB,gBAAI,EAAEA,EAAC;AACP,gBAAI,EAAEA,EAAC;AACP;;;AAIJ,YAAI,IAAI,EAAG,QAAO;AAClB,YAAI,IAAI,EAAG,QAAO;AAClB,eAAO;;AAGT,MAAAO,QAAO,aAAa,SAAS,WAAY,UAAU;AACjD,gBAAQ,OAAO,QAAQ,EAAE,YAAW,GAAE;UACpC,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;AACH,mBAAO;UACT;AACE,mBAAO;;;AAIb,MAAAA,QAAO,SAAS,SAAS,OAAQ,MAAM,QAAQ;AAC7C,YAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACxB,gBAAM,IAAI,UAAU,6CAA6C;;AAGnE,YAAI,KAAK,WAAW,GAAG;AACrB,iBAAOA,QAAO,MAAM,CAAC;;AAGvB,YAAIP;AACJ,YAAI,WAAW,QAAW;AACxB,mBAAS;AACT,eAAKA,KAAI,GAAGA,KAAI,KAAK,QAAQ,EAAEA,IAAG;AAChC,sBAAU,KAAKA,EAAC,EAAE;;;AAItB,cAAME,UAASK,QAAO,YAAY,MAAM;AACxC,YAAI,MAAM;AACV,aAAKP,KAAI,GAAGA,KAAI,KAAK,QAAQ,EAAEA,IAAG;AAChC,cAAI,MAAM,KAAKA,EAAC;AAChB,cAAI,WAAW,KAAK,gBAAgB,GAAG;AACrC,gBAAI,MAAM,IAAI,SAASE,QAAO,QAAQ;AACpC,kBAAI,CAACK,QAAO,SAAS,GAAG,EAAG,OAAMA,QAAO,KAAK,GAAG;AAChD,kBAAI,KAAKL,SAAQ,GAAG;YAC5B,OAAa;AACL,+BAAiB,UAAU,IAAI;gBAC7BA;gBACA;gBACA;;;qBAGK,CAACK,QAAO,SAAS,GAAG,GAAG;AAChC,kBAAM,IAAI,UAAU,6CAA6C;UACvE,OAAW;AACL,gBAAI,KAAKL,SAAQ,GAAG;;AAEtB,iBAAO,IAAI;;AAEb,eAAOA;;AAGT,eAASM,YAAY,QAAQ,UAAU;AACrC,YAAID,QAAO,SAAS,MAAM,GAAG;AAC3B,iBAAO,OAAO;;AAEhB,YAAI,kBAAkB,OAAO,MAAM,KAAK,WAAW,QAAQ,iBAAiB,GAAG;AAC7E,iBAAO,OAAO;;AAEhB,YAAI,OAAO,WAAW,UAAU;AAC9B,gBAAM,IAAI;YACR,6FACmB,OAAO;;;AAI9B,cAAMR,OAAM,OAAO;AACnB,cAAM,YAAa,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM;AAC5D,YAAI,CAAC,aAAaA,SAAQ,EAAG,QAAO;AAGpC,YAAI,cAAc;AAClB,mBAAS;AACP,kBAAQ,UAAQ;YACd,KAAK;YACL,KAAK;YACL,KAAK;AACH,qBAAOA;YACT,KAAK;YACL,KAAK;AACH,qBAAO,YAAY,MAAM,EAAE;YAC7B,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;AACH,qBAAOA,OAAM;YACf,KAAK;AACH,qBAAOA,SAAQ;YACjB,KAAK;AACH,qBAAO,cAAc,MAAM,EAAE;YAC/B;AACE,kBAAI,aAAa;AACf,uBAAO,YAAY,KAAK,YAAY,MAAM,EAAE;;AAE9C,0BAAY,KAAK,UAAU,YAAW;AACtC,4BAAc;;;;AAItB,MAAAQ,QAAO,aAAaC;AAEpB,eAAS,aAAc,UAAU,OAAO,KAAK;AAC3C,YAAI,cAAc;AASlB,YAAI,UAAU,UAAa,QAAQ,GAAG;AACpC,kBAAQ;;AAIV,YAAI,QAAQ,KAAK,QAAQ;AACvB,iBAAO;;AAGT,YAAI,QAAQ,UAAa,MAAM,KAAK,QAAQ;AAC1C,gBAAM,KAAK;;AAGb,YAAI,OAAO,GAAG;AACZ,iBAAO;;AAIT,iBAAS;AACT,mBAAW;AAEX,YAAI,OAAO,OAAO;AAChB,iBAAO;;AAGT,YAAI,CAAC,SAAU,YAAW;AAE1B,eAAO,MAAM;AACX,kBAAQ,UAAQ;YACd,KAAK;AACH,qBAAO,SAAS,MAAM,OAAO,GAAG;YAElC,KAAK;YACL,KAAK;AACH,qBAAO,UAAU,MAAM,OAAO,GAAG;YAEnC,KAAK;AACH,qBAAO,WAAW,MAAM,OAAO,GAAG;YAEpC,KAAK;YACL,KAAK;AACH,qBAAO,YAAY,MAAM,OAAO,GAAG;YAErC,KAAK;AACH,qBAAO,YAAY,MAAM,OAAO,GAAG;YAErC,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;AACH,qBAAO,aAAa,MAAM,OAAO,GAAG;YAEtC;AACE,kBAAI,YAAa,OAAM,IAAI,UAAU,uBAAuB,QAAQ;AACpE,0BAAY,WAAW,IAAI,YAAW;AACtC,4BAAc;;;;AAWtB,MAAAD,QAAO,UAAU,YAAY;AAE7B,eAAS,KAAM,GAAG,GAAG,GAAG;AACtB,cAAMP,KAAI,EAAE,CAAC;AACb,UAAE,CAAC,IAAI,EAAE,CAAC;AACV,UAAE,CAAC,IAAIA;;AAGT,MAAAO,QAAO,UAAU,SAAS,SAAS,SAAU;AAC3C,cAAMR,OAAM,KAAK;AACjB,YAAIA,OAAM,MAAM,GAAG;AACjB,gBAAM,IAAI,WAAW,2CAA2C;;AAElE,iBAASC,KAAI,GAAGA,KAAID,MAAKC,MAAK,GAAG;AAC/B,eAAK,MAAMA,IAAGA,KAAI,CAAC;;AAErB,eAAO;;AAGT,MAAAO,QAAO,UAAU,SAAS,SAAS,SAAU;AAC3C,cAAMR,OAAM,KAAK;AACjB,YAAIA,OAAM,MAAM,GAAG;AACjB,gBAAM,IAAI,WAAW,2CAA2C;;AAElE,iBAASC,KAAI,GAAGA,KAAID,MAAKC,MAAK,GAAG;AAC/B,eAAK,MAAMA,IAAGA,KAAI,CAAC;AACnB,eAAK,MAAMA,KAAI,GAAGA,KAAI,CAAC;;AAEzB,eAAO;;AAGT,MAAAO,QAAO,UAAU,SAAS,SAAS,SAAU;AAC3C,cAAMR,OAAM,KAAK;AACjB,YAAIA,OAAM,MAAM,GAAG;AACjB,gBAAM,IAAI,WAAW,2CAA2C;;AAElE,iBAASC,KAAI,GAAGA,KAAID,MAAKC,MAAK,GAAG;AAC/B,eAAK,MAAMA,IAAGA,KAAI,CAAC;AACnB,eAAK,MAAMA,KAAI,GAAGA,KAAI,CAAC;AACvB,eAAK,MAAMA,KAAI,GAAGA,KAAI,CAAC;AACvB,eAAK,MAAMA,KAAI,GAAGA,KAAI,CAAC;;AAEzB,eAAO;;AAGT,MAAAO,QAAO,UAAU,WAAW,SAAS,WAAY;AAC/C,cAAM,SAAS,KAAK;AACpB,YAAI,WAAW,EAAG,QAAO;AACzB,YAAI,UAAU,WAAW,EAAG,QAAO,UAAU,MAAM,GAAG,MAAM;AAC5D,eAAO,aAAa,MAAM,MAAM,SAAS;;AAG3C,MAAAA,QAAO,UAAU,iBAAiBA,QAAO,UAAU;AAEnD,MAAAA,QAAO,UAAU,SAAS,SAAS,OAAQ,GAAG;AAC5C,YAAI,CAACA,QAAO,SAAS,CAAC,EAAG,OAAM,IAAI,UAAU,2BAA2B;AACxE,YAAI,SAAS,EAAG,QAAO;AACvB,eAAOA,QAAO,QAAQ,MAAM,CAAC,MAAM;;AAGrC,MAAAA,QAAO,UAAU,UAAU,SAAS,UAAW;AAC7C,YAAI,MAAM;AACV,cAAM,MAAMD,SAAQ;AACpB,cAAM,KAAK,SAAS,OAAO,GAAG,GAAG,EAAE,QAAQ,WAAW,KAAK,EAAE,KAAI;AACjE,YAAI,KAAK,SAAS,IAAK,QAAO;AAC9B,eAAO,aAAa,MAAM;;AAE5B,UAAI,qBAAqB;AACvB,QAAAC,QAAO,UAAU,mBAAmB,IAAIA,QAAO,UAAU;;AAG3D,MAAAA,QAAO,UAAU,UAAU,SAAS,QAAS,QAAQ,OAAO,KAAK,WAAW,SAAS;AACnF,YAAI,WAAW,QAAQ,gBAAgB,GAAG;AACxC,mBAASA,QAAO,KAAK,QAAQ,OAAO,QAAQ,OAAO,UAAU;;AAE/D,YAAI,CAACA,QAAO,SAAS,MAAM,GAAG;AAC5B,gBAAM,IAAI;YACR,mFACoB,OAAO;;;AAI/B,YAAI,UAAU,QAAW;AACvB,kBAAQ;;AAEV,YAAI,QAAQ,QAAW;AACrB,gBAAM,SAAS,OAAO,SAAS;;AAEjC,YAAI,cAAc,QAAW;AAC3B,sBAAY;;AAEd,YAAI,YAAY,QAAW;AACzB,oBAAU,KAAK;;AAGjB,YAAI,QAAQ,KAAK,MAAM,OAAO,UAAU,YAAY,KAAK,UAAU,KAAK,QAAQ;AAC9E,gBAAM,IAAI,WAAW,oBAAoB;;AAG3C,YAAI,aAAa,WAAW,SAAS,KAAK;AACxC,iBAAO;;AAET,YAAI,aAAa,SAAS;AACxB,iBAAO;;AAET,YAAI,SAAS,KAAK;AAChB,iBAAO;;AAGT,mBAAW;AACX,iBAAS;AACT,uBAAe;AACf,qBAAa;AAEb,YAAI,SAAS,OAAQ,QAAO;AAE5B,YAAI,IAAI,UAAU;AAClB,YAAI,IAAI,MAAM;AACd,cAAMR,OAAM,KAAK,IAAI,GAAG,CAAC;AAEzB,cAAM,WAAW,KAAK,MAAM,WAAW,OAAO;AAC9C,cAAM,aAAa,OAAO,MAAM,OAAO,GAAG;AAE1C,iBAASC,KAAI,GAAGA,KAAID,MAAK,EAAEC,IAAG;AAC5B,cAAI,SAASA,EAAC,MAAM,WAAWA,EAAC,GAAG;AACjC,gBAAI,SAASA,EAAC;AACd,gBAAI,WAAWA,EAAC;AAChB;;;AAIJ,YAAI,IAAI,EAAG,QAAO;AAClB,YAAI,IAAI,EAAG,QAAO;AAClB,eAAO;;AAYT,eAAS,qBAAsBE,SAAQ,KAAK,YAAY,UAAU,KAAK;AAErE,YAAIA,QAAO,WAAW,EAAG,QAAO;AAGhC,YAAI,OAAO,eAAe,UAAU;AAClC,qBAAW;AACX,uBAAa;QACjB,WAAa,aAAa,YAAY;AAClC,uBAAa;QACjB,WAAa,aAAa,aAAa;AACnC,uBAAa;;AAEf,qBAAa,CAAC;AACd,YAAI,YAAY,UAAU,GAAG;AAE3B,uBAAa,MAAM,IAAKA,QAAO,SAAS;;AAI1C,YAAI,aAAa,EAAG,cAAaA,QAAO,SAAS;AACjD,YAAI,cAAcA,QAAO,QAAQ;AAC/B,cAAI,IAAK,QAAO;cACX,cAAaA,QAAO,SAAS;QACtC,WAAa,aAAa,GAAG;AACzB,cAAI,IAAK,cAAa;cACjB,QAAO;;AAId,YAAI,OAAO,QAAQ,UAAU;AAC3B,gBAAMK,QAAO,KAAK,KAAK,QAAQ;;AAIjC,YAAIA,QAAO,SAAS,GAAG,GAAG;AAExB,cAAI,IAAI,WAAW,GAAG;AACpB,mBAAO;;AAET,iBAAO,aAAaL,SAAQ,KAAK,YAAY,UAAU,GAAG;QAC9D,WAAa,OAAO,QAAQ,UAAU;AAClC,gBAAM,MAAM;AACZ,cAAI,OAAO,iBAAiB,UAAU,YAAY,YAAY;AAC5D,gBAAI,KAAK;AACP,qBAAO,iBAAiB,UAAU,QAAQ,KAAKA,SAAQ,KAAK,UAAU;YAC9E,OAAa;AACL,qBAAO,iBAAiB,UAAU,YAAY,KAAKA,SAAQ,KAAK,UAAU;;;AAG9E,iBAAO,aAAaA,SAAQ,CAAC,GAAG,GAAG,YAAY,UAAU,GAAG;;AAG9D,cAAM,IAAI,UAAU,sCAAsC;;AAG5D,eAAS,aAAc,KAAK,KAAK,YAAY,UAAU,KAAK;AAC1D,YAAI,YAAY;AAChB,YAAI,YAAY,IAAI;AACpB,YAAI,YAAY,IAAI;AAEpB,YAAI,aAAa,QAAW;AAC1B,qBAAW,OAAO,QAAQ,EAAE,YAAW;AACvC,cAAI,aAAa,UAAU,aAAa,WACpC,aAAa,aAAa,aAAa,YAAY;AACrD,gBAAI,IAAI,SAAS,KAAK,IAAI,SAAS,GAAG;AACpC,qBAAO;;AAET,wBAAY;AACZ,yBAAa;AACb,yBAAa;AACb,0BAAc;;;AAIlB,iBAAS,KAAM,KAAKF,IAAG;AACrB,cAAI,cAAc,GAAG;AACnB,mBAAO,IAAIA,EAAC;UAClB,OAAW;AACL,mBAAO,IAAI,aAAaA,KAAI,SAAS;;;AAIzC,YAAIA;AACJ,YAAI,KAAK;AACP,cAAI,aAAa;AACjB,eAAKA,KAAI,YAAYA,KAAI,WAAWA,MAAK;AACvC,gBAAI,KAAK,KAAKA,EAAC,MAAM,KAAK,KAAK,eAAe,KAAK,IAAIA,KAAI,UAAU,GAAG;AACtE,kBAAI,eAAe,GAAI,cAAaA;AACpC,kBAAIA,KAAI,aAAa,MAAM,UAAW,QAAO,aAAa;YAClE,OAAa;AACL,kBAAI,eAAe,GAAI,CAAAA,MAAKA,KAAI;AAChC,2BAAa;;;QAGrB,OAAS;AACL,cAAI,aAAa,YAAY,UAAW,cAAa,YAAY;AACjE,eAAKA,KAAI,YAAYA,MAAK,GAAGA,MAAK;AAChC,gBAAI,QAAQ;AACZ,qBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,kBAAI,KAAK,KAAKA,KAAI,CAAC,MAAM,KAAK,KAAK,CAAC,GAAG;AACrC,wBAAQ;AACR;;;AAGJ,gBAAI,MAAO,QAAOA;;;AAItB,eAAO;;AAGT,MAAAO,QAAO,UAAU,WAAW,SAAS,SAAU,KAAK,YAAY,UAAU;AACxE,eAAO,KAAK,QAAQ,KAAK,YAAY,QAAQ,MAAM;;AAGrD,MAAAA,QAAO,UAAU,UAAU,SAAS,QAAS,KAAK,YAAY,UAAU;AACtE,eAAO,qBAAqB,MAAM,KAAK,YAAY,UAAU,IAAI;;AAGnE,MAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,KAAK,YAAY,UAAU;AAC9E,eAAO,qBAAqB,MAAM,KAAK,YAAY,UAAU,KAAK;;AAGpE,eAAS,SAAU,KAAK,QAAQ,QAAQ,QAAQ;AAC9C,iBAAS,OAAO,MAAM,KAAK;AAC3B,cAAM,YAAY,IAAI,SAAS;AAC/B,YAAI,CAAC,QAAQ;AACX,mBAAS;QACb,OAAS;AACL,mBAAS,OAAO,MAAM;AACtB,cAAI,SAAS,WAAW;AACtB,qBAAS;;;AAIb,cAAM,SAAS,OAAO;AAEtB,YAAI,SAAS,SAAS,GAAG;AACvB,mBAAS,SAAS;;AAEpB,YAAIP;AACJ,aAAKA,KAAI,GAAGA,KAAI,QAAQ,EAAEA,IAAG;AAC3B,gBAAM,SAAS,SAAS,OAAO,OAAOA,KAAI,GAAG,CAAC,GAAG,EAAE;AACnD,cAAI,YAAY,MAAM,EAAG,QAAOA;AAChC,cAAI,SAASA,EAAC,IAAI;;AAEpB,eAAOA;;AAGT,eAAS,UAAW,KAAK,QAAQ,QAAQ,QAAQ;AAC/C,eAAO,WAAW,YAAY,QAAQ,IAAI,SAAS,MAAM,GAAG,KAAK,QAAQ,MAAM;;AAGjF,eAAS,WAAY,KAAK,QAAQ,QAAQ,QAAQ;AAChD,eAAO,WAAW,aAAa,MAAM,GAAG,KAAK,QAAQ,MAAM;;AAG7D,eAAS,YAAa,KAAK,QAAQ,QAAQ,QAAQ;AACjD,eAAO,WAAW,cAAc,MAAM,GAAG,KAAK,QAAQ,MAAM;;AAG9D,eAAS,UAAW,KAAK,QAAQ,QAAQ,QAAQ;AAC/C,eAAO,WAAW,eAAe,QAAQ,IAAI,SAAS,MAAM,GAAG,KAAK,QAAQ,MAAM;;AAGpF,MAAAO,QAAO,UAAU,QAAQ,SAAS,MAAO,QAAQ,QAAQ,QAAQ,UAAU;AAEzE,YAAI,WAAW,QAAW;AACxB,qBAAW;AACX,mBAAS,KAAK;AACd,mBAAS;mBAEA,WAAW,UAAa,OAAO,WAAW,UAAU;AAC7D,qBAAW;AACX,mBAAS,KAAK;AACd,mBAAS;QAEb,WAAa,SAAS,MAAM,GAAG;AAC3B,mBAAS,WAAW;AACpB,cAAI,SAAS,MAAM,GAAG;AACpB,qBAAS,WAAW;AACpB,gBAAI,aAAa,OAAW,YAAW;UAC7C,OAAW;AACL,uBAAW;AACX,qBAAS;;QAEf,OAAS;AACL,gBAAM,IAAI;YACR;;;AAIJ,cAAM,YAAY,KAAK,SAAS;AAChC,YAAI,WAAW,UAAa,SAAS,UAAW,UAAS;AAEzD,YAAK,OAAO,SAAS,MAAM,SAAS,KAAK,SAAS,MAAO,SAAS,KAAK,QAAQ;AAC7E,gBAAM,IAAI,WAAW,wCAAwC;;AAG/D,YAAI,CAAC,SAAU,YAAW;AAE1B,YAAI,cAAc;AAClB,mBAAS;AACP,kBAAQ,UAAQ;YACd,KAAK;AACH,qBAAO,SAAS,MAAM,QAAQ,QAAQ,MAAM;YAE9C,KAAK;YACL,KAAK;AACH,qBAAO,UAAU,MAAM,QAAQ,QAAQ,MAAM;YAE/C,KAAK;YACL,KAAK;YACL,KAAK;AACH,qBAAO,WAAW,MAAM,QAAQ,QAAQ,MAAM;YAEhD,KAAK;AAEH,qBAAO,YAAY,MAAM,QAAQ,QAAQ,MAAM;YAEjD,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;AACH,qBAAO,UAAU,MAAM,QAAQ,QAAQ,MAAM;YAE/C;AACE,kBAAI,YAAa,OAAM,IAAI,UAAU,uBAAuB,QAAQ;AACpE,0BAAY,KAAK,UAAU,YAAW;AACtC,4BAAc;;;;AAKtB,MAAAA,QAAO,UAAU,SAAS,SAAS,SAAU;AAC3C,eAAO;UACL,MAAM;UACN,MAAM,MAAM,UAAU,MAAM,KAAK,KAAK,QAAQ,MAAM,CAAC;;;AAIzD,eAAS,YAAa,KAAK,OAAO,KAAK;AACrC,YAAI,UAAU,KAAK,QAAQ,IAAI,QAAQ;AACrC,iBAAO,OAAO,cAAc,GAAG;QACnC,OAAS;AACL,iBAAO,OAAO,cAAc,IAAI,MAAM,OAAO,GAAG,CAAC;;;AAIrD,eAAS,UAAW,KAAK,OAAO,KAAK;AACnC,cAAM,KAAK,IAAI,IAAI,QAAQ,GAAG;AAC9B,cAAM,MAAM,CAAA;AAEZ,YAAIP,KAAI;AACR,eAAOA,KAAI,KAAK;AACd,gBAAM,YAAY,IAAIA,EAAC;AACvB,cAAI,YAAY;AAChB,cAAI,mBAAoB,YAAY,MAChC,IACC,YAAY,MACT,IACC,YAAY,MACT,IACA;AAEZ,cAAIA,KAAI,oBAAoB,KAAK;AAC/B,gBAAI,YAAY,WAAW,YAAY;AAEvC,oBAAQ,kBAAgB;cACtB,KAAK;AACH,oBAAI,YAAY,KAAM;AACpB,8BAAY;;AAEd;cACF,KAAK;AACH,6BAAa,IAAIA,KAAI,CAAC;AACtB,qBAAK,aAAa,SAAU,KAAM;AAChC,mCAAiB,YAAY,OAAS,IAAO,aAAa;AAC1D,sBAAI,gBAAgB,KAAM;AACxB,gCAAY;;;AAGhB;cACF,KAAK;AACH,6BAAa,IAAIA,KAAI,CAAC;AACtB,4BAAY,IAAIA,KAAI,CAAC;AACrB,qBAAK,aAAa,SAAU,QAAS,YAAY,SAAU,KAAM;AAC/D,mCAAiB,YAAY,OAAQ,MAAO,aAAa,OAAS,IAAO,YAAY;AACrF,sBAAI,gBAAgB,SAAU,gBAAgB,SAAU,gBAAgB,QAAS;AAC/E,gCAAY;;;AAGhB;cACF,KAAK;AACH,6BAAa,IAAIA,KAAI,CAAC;AACtB,4BAAY,IAAIA,KAAI,CAAC;AACrB,6BAAa,IAAIA,KAAI,CAAC;AACtB,qBAAK,aAAa,SAAU,QAAS,YAAY,SAAU,QAAS,aAAa,SAAU,KAAM;AAC/F,mCAAiB,YAAY,OAAQ,MAAQ,aAAa,OAAS,MAAO,YAAY,OAAS,IAAO,aAAa;AACnH,sBAAI,gBAAgB,SAAU,gBAAgB,SAAU;AACtD,gCAAY;;;;;AAMtB,cAAI,cAAc,MAAM;AAGtB,wBAAY;AACZ,+BAAmB;UACzB,WAAe,YAAY,OAAQ;AAE7B,yBAAa;AACb,gBAAI,KAAK,cAAc,KAAK,OAAQ,KAAM;AAC1C,wBAAY,QAAS,YAAY;;AAGnC,cAAI,KAAK,SAAS;AAClB,UAAAA,MAAK;;AAGP,eAAO,sBAAsB,GAAG;;AAMlC,YAAM,uBAAuB;AAE7B,eAAS,sBAAuB,YAAY;AAC1C,cAAMD,OAAM,WAAW;AACvB,YAAIA,QAAO,sBAAsB;AAC/B,iBAAO,OAAO,aAAa,MAAM,QAAQ,UAAU;;AAIrD,YAAI,MAAM;AACV,YAAIC,KAAI;AACR,eAAOA,KAAID,MAAK;AACd,iBAAO,OAAO,aAAa;YACzB;YACA,WAAW,MAAMC,IAAGA,MAAK,oBAAoB;;;AAGjD,eAAO;;AAGT,eAAS,WAAY,KAAK,OAAO,KAAK;AACpC,YAAI,MAAM;AACV,cAAM,KAAK,IAAI,IAAI,QAAQ,GAAG;AAE9B,iBAASA,KAAI,OAAOA,KAAI,KAAK,EAAEA,IAAG;AAChC,iBAAO,OAAO,aAAa,IAAIA,EAAC,IAAI,GAAI;;AAE1C,eAAO;;AAGT,eAAS,YAAa,KAAK,OAAO,KAAK;AACrC,YAAI,MAAM;AACV,cAAM,KAAK,IAAI,IAAI,QAAQ,GAAG;AAE9B,iBAASA,KAAI,OAAOA,KAAI,KAAK,EAAEA,IAAG;AAChC,iBAAO,OAAO,aAAa,IAAIA,EAAC,CAAC;;AAEnC,eAAO;;AAGT,eAAS,SAAU,KAAK,OAAO,KAAK;AAClC,cAAMD,OAAM,IAAI;AAEhB,YAAI,CAAC,SAAS,QAAQ,EAAG,SAAQ;AACjC,YAAI,CAAC,OAAO,MAAM,KAAK,MAAMA,KAAK,OAAMA;AAExC,YAAI,MAAM;AACV,iBAASC,KAAI,OAAOA,KAAI,KAAK,EAAEA,IAAG;AAChC,iBAAO,oBAAoB,IAAIA,EAAC,CAAC;;AAEnC,eAAO;;AAGT,eAAS,aAAc,KAAK,OAAO,KAAK;AACtC,cAAM,QAAQ,IAAI,MAAM,OAAO,GAAG;AAClC,YAAI,MAAM;AAEV,iBAASA,KAAI,GAAGA,KAAI,MAAM,SAAS,GAAGA,MAAK,GAAG;AAC5C,iBAAO,OAAO,aAAa,MAAMA,EAAC,IAAK,MAAMA,KAAI,CAAC,IAAI,GAAI;;AAE5D,eAAO;;AAGT,MAAAO,QAAO,UAAU,QAAQ,SAAS,MAAO,OAAO,KAAK;AACnD,cAAMR,OAAM,KAAK;AACjB,gBAAQ,CAAC,CAAC;AACV,cAAM,QAAQ,SAAYA,OAAM,CAAC,CAAC;AAElC,YAAI,QAAQ,GAAG;AACb,mBAASA;AACT,cAAI,QAAQ,EAAG,SAAQ;QAC3B,WAAa,QAAQA,MAAK;AACtB,kBAAQA;;AAGV,YAAI,MAAM,GAAG;AACX,iBAAOA;AACP,cAAI,MAAM,EAAG,OAAM;QACvB,WAAa,MAAMA,MAAK;AACpB,gBAAMA;;AAGR,YAAI,MAAM,MAAO,OAAM;AAEvB,cAAM,SAAS,KAAK,SAAS,OAAO,GAAG;AAEvC,eAAO,eAAe,QAAQQ,QAAO,SAAS;AAE9C,eAAO;;AAMT,eAAS,YAAa,QAAQ,KAAK,QAAQ;AACzC,YAAK,SAAS,MAAO,KAAK,SAAS,EAAG,OAAM,IAAI,WAAW,oBAAoB;AAC/E,YAAI,SAAS,MAAM,OAAQ,OAAM,IAAI,WAAW,uCAAuC;;AAGzF,MAAAA,QAAO,UAAU,aACjBA,QAAO,UAAU,aAAa,SAAS,WAAY,QAAQC,aAAY,UAAU;AAC/E,iBAAS,WAAW;AACpB,QAAAA,cAAaA,gBAAe;AAC5B,YAAI,CAAC,SAAU,aAAY,QAAQA,aAAY,KAAK,MAAM;AAE1D,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,MAAM;AACV,YAAIR,KAAI;AACR,eAAO,EAAEA,KAAIQ,gBAAe,OAAO,MAAQ;AACzC,iBAAO,KAAK,SAASR,EAAC,IAAI;;AAG5B,eAAO;;AAGT,MAAAO,QAAO,UAAU,aACjBA,QAAO,UAAU,aAAa,SAAS,WAAY,QAAQC,aAAY,UAAU;AAC/E,iBAAS,WAAW;AACpB,QAAAA,cAAaA,gBAAe;AAC5B,YAAI,CAAC,UAAU;AACb,sBAAY,QAAQA,aAAY,KAAK,MAAM;;AAG7C,YAAI,MAAM,KAAK,SAAS,EAAEA,WAAU;AACpC,YAAI,MAAM;AACV,eAAOA,cAAa,MAAM,OAAO,MAAQ;AACvC,iBAAO,KAAK,SAAS,EAAEA,WAAU,IAAI;;AAGvC,eAAO;;AAGT,MAAAD,QAAO,UAAU,YACjBA,QAAO,UAAU,YAAY,SAAS,UAAW,QAAQ,UAAU;AACjE,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,eAAO,KAAK,MAAM;;AAGpB,MAAAA,QAAO,UAAU,eACjBA,QAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,eAAO,KAAK,MAAM,IAAK,KAAK,SAAS,CAAC,KAAK;;AAG7C,MAAAA,QAAO,UAAU,eACjBA,QAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,eAAQ,KAAK,MAAM,KAAK,IAAK,KAAK,SAAS,CAAC;;AAG9C,MAAAA,QAAO,UAAU,eACjBA,QAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AAEjD,gBAAS,KAAK,MAAM,IACf,KAAK,SAAS,CAAC,KAAK,IACpB,KAAK,SAAS,CAAC,KAAK,MACpB,KAAK,SAAS,CAAC,IAAI;;AAG1B,MAAAA,QAAO,UAAU,eACjBA,QAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AAEjD,eAAQ,KAAK,MAAM,IAAI,YACnB,KAAK,SAAS,CAAC,KAAK,KACrB,KAAK,SAAS,CAAC,KAAK,IACrB,KAAK,SAAS,CAAC;;AAGnB,MAAAA,QAAO,UAAU,kBAAkB,mBAAmB,SAAS,gBAAiB,QAAQ;AACtF,iBAAS,WAAW;AACpB,uBAAe,QAAQ,QAAQ;AAC/B,cAAM,QAAQ,KAAK,MAAM;AACzB,cAAM,OAAO,KAAK,SAAS,CAAC;AAC5B,YAAI,UAAU,UAAa,SAAS,QAAW;AAC7C,sBAAY,QAAQ,KAAK,SAAS,CAAC;;AAGrC,cAAM,KAAK,QACT,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK;AAExB,cAAM,KAAK,KAAK,EAAE,MAAM,IACtB,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,OAAO,KAAK;AAEd,eAAO,OAAO,EAAE,KAAK,OAAO,EAAE,KAAK,OAAO,EAAE;MAC9C,CAAC;AAED,MAAAA,QAAO,UAAU,kBAAkB,mBAAmB,SAAS,gBAAiB,QAAQ;AACtF,iBAAS,WAAW;AACpB,uBAAe,QAAQ,QAAQ;AAC/B,cAAM,QAAQ,KAAK,MAAM;AACzB,cAAM,OAAO,KAAK,SAAS,CAAC;AAC5B,YAAI,UAAU,UAAa,SAAS,QAAW;AAC7C,sBAAY,QAAQ,KAAK,SAAS,CAAC;;AAGrC,cAAM,KAAK,QAAQ,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB,KAAK,EAAE,MAAM;AAEf,cAAM,KAAK,KAAK,EAAE,MAAM,IAAI,KAAK,KAC/B,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB;AAEF,gBAAQ,OAAO,EAAE,KAAK,OAAO,EAAE,KAAK,OAAO,EAAE;MAC/C,CAAC;AAED,MAAAA,QAAO,UAAU,YAAY,SAAS,UAAW,QAAQC,aAAY,UAAU;AAC7E,iBAAS,WAAW;AACpB,QAAAA,cAAaA,gBAAe;AAC5B,YAAI,CAAC,SAAU,aAAY,QAAQA,aAAY,KAAK,MAAM;AAE1D,YAAI,MAAM,KAAK,MAAM;AACrB,YAAI,MAAM;AACV,YAAIR,KAAI;AACR,eAAO,EAAEA,KAAIQ,gBAAe,OAAO,MAAQ;AACzC,iBAAO,KAAK,SAASR,EAAC,IAAI;;AAE5B,eAAO;AAEP,YAAI,OAAO,IAAK,QAAO,KAAK,IAAI,GAAG,IAAIQ,WAAU;AAEjD,eAAO;;AAGT,MAAAD,QAAO,UAAU,YAAY,SAAS,UAAW,QAAQC,aAAY,UAAU;AAC7E,iBAAS,WAAW;AACpB,QAAAA,cAAaA,gBAAe;AAC5B,YAAI,CAAC,SAAU,aAAY,QAAQA,aAAY,KAAK,MAAM;AAE1D,YAAIR,KAAIQ;AACR,YAAI,MAAM;AACV,YAAI,MAAM,KAAK,SAAS,EAAER,EAAC;AAC3B,eAAOA,KAAI,MAAM,OAAO,MAAQ;AAC9B,iBAAO,KAAK,SAAS,EAAEA,EAAC,IAAI;;AAE9B,eAAO;AAEP,YAAI,OAAO,IAAK,QAAO,KAAK,IAAI,GAAG,IAAIQ,WAAU;AAEjD,eAAO;;AAGT,MAAAD,QAAO,UAAU,WAAW,SAAS,SAAU,QAAQ,UAAU;AAC/D,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,YAAI,EAAE,KAAK,MAAM,IAAI,KAAO,QAAQ,KAAK,MAAM;AAC/C,gBAAS,MAAO,KAAK,MAAM,IAAI,KAAK;;AAGtC,MAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,cAAM,MAAM,KAAK,MAAM,IAAK,KAAK,SAAS,CAAC,KAAK;AAChD,eAAQ,MAAM,QAAU,MAAM,aAAa;;AAG7C,MAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,cAAM,MAAM,KAAK,SAAS,CAAC,IAAK,KAAK,MAAM,KAAK;AAChD,eAAQ,MAAM,QAAU,MAAM,aAAa;;AAG7C,MAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AAEjD,eAAQ,KAAK,MAAM,IAChB,KAAK,SAAS,CAAC,KAAK,IACpB,KAAK,SAAS,CAAC,KAAK,KACpB,KAAK,SAAS,CAAC,KAAK;;AAGzB,MAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AAEjD,eAAQ,KAAK,MAAM,KAAK,KACrB,KAAK,SAAS,CAAC,KAAK,KACpB,KAAK,SAAS,CAAC,KAAK,IACpB,KAAK,SAAS,CAAC;;AAGpB,MAAAA,QAAO,UAAU,iBAAiB,mBAAmB,SAAS,eAAgB,QAAQ;AACpF,iBAAS,WAAW;AACpB,uBAAe,QAAQ,QAAQ;AAC/B,cAAM,QAAQ,KAAK,MAAM;AACzB,cAAM,OAAO,KAAK,SAAS,CAAC;AAC5B,YAAI,UAAU,UAAa,SAAS,QAAW;AAC7C,sBAAY,QAAQ,KAAK,SAAS,CAAC;;AAGrC,cAAM,MAAM,KAAK,SAAS,CAAC,IACzB,KAAK,SAAS,CAAC,IAAI,KAAK,IACxB,KAAK,SAAS,CAAC,IAAI,KAAK,MACvB,QAAQ;AAEX,gBAAQ,OAAO,GAAG,KAAK,OAAO,EAAE,KAC9B,OAAO,QACP,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK,EAAE;MAC5B,CAAC;AAED,MAAAA,QAAO,UAAU,iBAAiB,mBAAmB,SAAS,eAAgB,QAAQ;AACpF,iBAAS,WAAW;AACpB,uBAAe,QAAQ,QAAQ;AAC/B,cAAM,QAAQ,KAAK,MAAM;AACzB,cAAM,OAAO,KAAK,SAAS,CAAC;AAC5B,YAAI,UAAU,UAAa,SAAS,QAAW;AAC7C,sBAAY,QAAQ,KAAK,SAAS,CAAC;;AAGrC,cAAM,OAAO,SAAS;QACpB,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB,KAAK,EAAE,MAAM;AAEf,gBAAQ,OAAO,GAAG,KAAK,OAAO,EAAE,KAC9B,OAAO,KAAK,EAAE,MAAM,IAAI,KAAK,KAC7B,KAAK,EAAE,MAAM,IAAI,KAAK,KACtB,KAAK,EAAE,MAAM,IAAI,KAAK,IACtB,IAAI;MACR,CAAC;AAED,MAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,eAAOH,UAAQ,KAAK,MAAM,QAAQ,MAAM,IAAI,CAAC;;AAG/C,MAAAG,QAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,eAAOH,UAAQ,KAAK,MAAM,QAAQ,OAAO,IAAI,CAAC;;AAGhD,MAAAG,QAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,eAAOH,UAAQ,KAAK,MAAM,QAAQ,MAAM,IAAI,CAAC;;AAG/C,MAAAG,QAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,eAAOH,UAAQ,KAAK,MAAM,QAAQ,OAAO,IAAI,CAAC;;AAGhD,eAAS,SAAU,KAAK,OAAO,QAAQ,KAAK,KAAK,KAAK;AACpD,YAAI,CAACG,QAAO,SAAS,GAAG,EAAG,OAAM,IAAI,UAAU,6CAA6C;AAC5F,YAAI,QAAQ,OAAO,QAAQ,IAAK,OAAM,IAAI,WAAW,mCAAmC;AACxF,YAAI,SAAS,MAAM,IAAI,OAAQ,OAAM,IAAI,WAAW,oBAAoB;;AAG1E,MAAAA,QAAO,UAAU,cACjBA,QAAO,UAAU,cAAc,SAAS,YAAa,OAAO,QAAQC,aAAY,UAAU;AACxF,gBAAQ,CAAC;AACT,iBAAS,WAAW;AACpB,QAAAA,cAAaA,gBAAe;AAC5B,YAAI,CAAC,UAAU;AACb,gBAAM,WAAW,KAAK,IAAI,GAAG,IAAIA,WAAU,IAAI;AAC/C,mBAAS,MAAM,OAAO,QAAQA,aAAY,UAAU,CAAC;;AAGvD,YAAI,MAAM;AACV,YAAIR,KAAI;AACR,aAAK,MAAM,IAAI,QAAQ;AACvB,eAAO,EAAEA,KAAIQ,gBAAe,OAAO,MAAQ;AACzC,eAAK,SAASR,EAAC,IAAK,QAAQ,MAAO;;AAGrC,eAAO,SAASQ;;AAGlB,MAAAD,QAAO,UAAU,cACjBA,QAAO,UAAU,cAAc,SAAS,YAAa,OAAO,QAAQC,aAAY,UAAU;AACxF,gBAAQ,CAAC;AACT,iBAAS,WAAW;AACpB,QAAAA,cAAaA,gBAAe;AAC5B,YAAI,CAAC,UAAU;AACb,gBAAM,WAAW,KAAK,IAAI,GAAG,IAAIA,WAAU,IAAI;AAC/C,mBAAS,MAAM,OAAO,QAAQA,aAAY,UAAU,CAAC;;AAGvD,YAAIR,KAAIQ,cAAa;AACrB,YAAI,MAAM;AACV,aAAK,SAASR,EAAC,IAAI,QAAQ;AAC3B,eAAO,EAAEA,MAAK,MAAM,OAAO,MAAQ;AACjC,eAAK,SAASA,EAAC,IAAK,QAAQ,MAAO;;AAGrC,eAAO,SAASQ;;AAGlB,MAAAD,QAAO,UAAU,aACjBA,QAAO,UAAU,aAAa,SAAS,WAAY,OAAO,QAAQ,UAAU;AAC1E,gBAAQ,CAAC;AACT,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,KAAM,CAAC;AACvD,aAAK,MAAM,IAAK,QAAQ;AACxB,eAAO,SAAS;;AAGlB,MAAAA,QAAO,UAAU,gBACjBA,QAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,gBAAQ,CAAC;AACT,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,OAAQ,CAAC;AACzD,aAAK,MAAM,IAAK,QAAQ;AACxB,aAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,eAAO,SAAS;;AAGlB,MAAAA,QAAO,UAAU,gBACjBA,QAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,gBAAQ,CAAC;AACT,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,OAAQ,CAAC;AACzD,aAAK,MAAM,IAAK,UAAU;AAC1B,aAAK,SAAS,CAAC,IAAK,QAAQ;AAC5B,eAAO,SAAS;;AAGlB,MAAAA,QAAO,UAAU,gBACjBA,QAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,gBAAQ,CAAC;AACT,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,YAAY,CAAC;AAC7D,aAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,aAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,aAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,aAAK,MAAM,IAAK,QAAQ;AACxB,eAAO,SAAS;;AAGlB,MAAAA,QAAO,UAAU,gBACjBA,QAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,gBAAQ,CAAC;AACT,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,YAAY,CAAC;AAC7D,aAAK,MAAM,IAAK,UAAU;AAC1B,aAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,aAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,aAAK,SAAS,CAAC,IAAK,QAAQ;AAC5B,eAAO,SAAS;;AAGlB,eAAS,eAAgB,KAAK,OAAO,QAAQ,KAAK,KAAK;AACrD,mBAAW,OAAO,KAAK,KAAK,KAAK,QAAQ,CAAC;AAE1C,YAAI,KAAK,OAAO,QAAQ,OAAO,UAAU,CAAC;AAC1C,YAAI,QAAQ,IAAI;AAChB,aAAK,MAAM;AACX,YAAI,QAAQ,IAAI;AAChB,aAAK,MAAM;AACX,YAAI,QAAQ,IAAI;AAChB,aAAK,MAAM;AACX,YAAI,QAAQ,IAAI;AAChB,YAAI,KAAK,OAAO,SAAS,OAAO,EAAE,IAAI,OAAO,UAAU,CAAC;AACxD,YAAI,QAAQ,IAAI;AAChB,aAAK,MAAM;AACX,YAAI,QAAQ,IAAI;AAChB,aAAK,MAAM;AACX,YAAI,QAAQ,IAAI;AAChB,aAAK,MAAM;AACX,YAAI,QAAQ,IAAI;AAChB,eAAO;;AAGT,eAAS,eAAgB,KAAK,OAAO,QAAQ,KAAK,KAAK;AACrD,mBAAW,OAAO,KAAK,KAAK,KAAK,QAAQ,CAAC;AAE1C,YAAI,KAAK,OAAO,QAAQ,OAAO,UAAU,CAAC;AAC1C,YAAI,SAAS,CAAC,IAAI;AAClB,aAAK,MAAM;AACX,YAAI,SAAS,CAAC,IAAI;AAClB,aAAK,MAAM;AACX,YAAI,SAAS,CAAC,IAAI;AAClB,aAAK,MAAM;AACX,YAAI,SAAS,CAAC,IAAI;AAClB,YAAI,KAAK,OAAO,SAAS,OAAO,EAAE,IAAI,OAAO,UAAU,CAAC;AACxD,YAAI,SAAS,CAAC,IAAI;AAClB,aAAK,MAAM;AACX,YAAI,SAAS,CAAC,IAAI;AAClB,aAAK,MAAM;AACX,YAAI,SAAS,CAAC,IAAI;AAClB,aAAK,MAAM;AACX,YAAI,MAAM,IAAI;AACd,eAAO,SAAS;;AAGlB,MAAAA,QAAO,UAAU,mBAAmB,mBAAmB,SAAS,iBAAkB,OAAO,SAAS,GAAG;AACnG,eAAO,eAAe,MAAM,OAAO,QAAQ,OAAO,CAAC,GAAG,OAAO,oBAAoB,CAAC;MACpF,CAAC;AAED,MAAAA,QAAO,UAAU,mBAAmB,mBAAmB,SAAS,iBAAkB,OAAO,SAAS,GAAG;AACnG,eAAO,eAAe,MAAM,OAAO,QAAQ,OAAO,CAAC,GAAG,OAAO,oBAAoB,CAAC;MACpF,CAAC;AAED,MAAAA,QAAO,UAAU,aAAa,SAAS,WAAY,OAAO,QAAQC,aAAY,UAAU;AACtF,gBAAQ,CAAC;AACT,iBAAS,WAAW;AACpB,YAAI,CAAC,UAAU;AACb,gBAAM,QAAQ,KAAK,IAAI,GAAI,IAAIA,cAAc,CAAC;AAE9C,mBAAS,MAAM,OAAO,QAAQA,aAAY,QAAQ,GAAG,CAAC,KAAK;;AAG7D,YAAIR,KAAI;AACR,YAAI,MAAM;AACV,YAAI,MAAM;AACV,aAAK,MAAM,IAAI,QAAQ;AACvB,eAAO,EAAEA,KAAIQ,gBAAe,OAAO,MAAQ;AACzC,cAAI,QAAQ,KAAK,QAAQ,KAAK,KAAK,SAASR,KAAI,CAAC,MAAM,GAAG;AACxD,kBAAM;;AAER,eAAK,SAASA,EAAC,KAAM,QAAQ,OAAQ,KAAK,MAAM;;AAGlD,eAAO,SAASQ;;AAGlB,MAAAD,QAAO,UAAU,aAAa,SAAS,WAAY,OAAO,QAAQC,aAAY,UAAU;AACtF,gBAAQ,CAAC;AACT,iBAAS,WAAW;AACpB,YAAI,CAAC,UAAU;AACb,gBAAM,QAAQ,KAAK,IAAI,GAAI,IAAIA,cAAc,CAAC;AAE9C,mBAAS,MAAM,OAAO,QAAQA,aAAY,QAAQ,GAAG,CAAC,KAAK;;AAG7D,YAAIR,KAAIQ,cAAa;AACrB,YAAI,MAAM;AACV,YAAI,MAAM;AACV,aAAK,SAASR,EAAC,IAAI,QAAQ;AAC3B,eAAO,EAAEA,MAAK,MAAM,OAAO,MAAQ;AACjC,cAAI,QAAQ,KAAK,QAAQ,KAAK,KAAK,SAASA,KAAI,CAAC,MAAM,GAAG;AACxD,kBAAM;;AAER,eAAK,SAASA,EAAC,KAAM,QAAQ,OAAQ,KAAK,MAAM;;AAGlD,eAAO,SAASQ;;AAGlB,MAAAD,QAAO,UAAU,YAAY,SAAS,UAAW,OAAO,QAAQ,UAAU;AACxE,gBAAQ,CAAC;AACT,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,KAAM,IAAK;AAC3D,YAAI,QAAQ,EAAG,SAAQ,MAAO,QAAQ;AACtC,aAAK,MAAM,IAAK,QAAQ;AACxB,eAAO,SAAS;;AAGlB,MAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,gBAAQ,CAAC;AACT,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,OAAQ,MAAO;AAC/D,aAAK,MAAM,IAAK,QAAQ;AACxB,aAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,eAAO,SAAS;;AAGlB,MAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,gBAAQ,CAAC;AACT,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,OAAQ,MAAO;AAC/D,aAAK,MAAM,IAAK,UAAU;AAC1B,aAAK,SAAS,CAAC,IAAK,QAAQ;AAC5B,eAAO,SAAS;;AAGlB,MAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,gBAAQ,CAAC;AACT,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,YAAY,WAAW;AACvE,aAAK,MAAM,IAAK,QAAQ;AACxB,aAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,aAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,aAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,eAAO,SAAS;;AAGlB,MAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,gBAAQ,CAAC;AACT,iBAAS,WAAW;AACpB,YAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,YAAY,WAAW;AACvE,YAAI,QAAQ,EAAG,SAAQ,aAAa,QAAQ;AAC5C,aAAK,MAAM,IAAK,UAAU;AAC1B,aAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,aAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,aAAK,SAAS,CAAC,IAAK,QAAQ;AAC5B,eAAO,SAAS;;AAGlB,MAAAA,QAAO,UAAU,kBAAkB,mBAAmB,SAAS,gBAAiB,OAAO,SAAS,GAAG;AACjG,eAAO,eAAe,MAAM,OAAO,QAAQ,CAAC,OAAO,oBAAoB,GAAG,OAAO,oBAAoB,CAAC;MACxG,CAAC;AAED,MAAAA,QAAO,UAAU,kBAAkB,mBAAmB,SAAS,gBAAiB,OAAO,SAAS,GAAG;AACjG,eAAO,eAAe,MAAM,OAAO,QAAQ,CAAC,OAAO,oBAAoB,GAAG,OAAO,oBAAoB,CAAC;MACxG,CAAC;AAED,eAAS,aAAc,KAAK,OAAO,QAAQ,KAAK,KAAK,KAAK;AACxD,YAAI,SAAS,MAAM,IAAI,OAAQ,OAAM,IAAI,WAAW,oBAAoB;AACxE,YAAI,SAAS,EAAG,OAAM,IAAI,WAAW,oBAAoB;;AAG3D,eAAS,WAAY,KAAK,OAAO,QAAQ,cAAc,UAAU;AAC/D,gBAAQ,CAAC;AACT,iBAAS,WAAW;AACpB,YAAI,CAAC,UAAU;AACb,uBAAa,KAAK,OAAO,QAAQ,CAAkD;;AAErFH,kBAAQ,MAAM,KAAK,OAAO,QAAQ,cAAc,IAAI,CAAC;AACrD,eAAO,SAAS;;AAGlB,MAAAG,QAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,eAAO,WAAW,MAAM,OAAO,QAAQ,MAAM,QAAQ;;AAGvD,MAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,eAAO,WAAW,MAAM,OAAO,QAAQ,OAAO,QAAQ;;AAGxD,eAAS,YAAa,KAAK,OAAO,QAAQ,cAAc,UAAU;AAChE,gBAAQ,CAAC;AACT,iBAAS,WAAW;AACpB,YAAI,CAAC,UAAU;AACb,uBAAa,KAAK,OAAO,QAAQ,CAAoD;;AAEvFH,kBAAQ,MAAM,KAAK,OAAO,QAAQ,cAAc,IAAI,CAAC;AACrD,eAAO,SAAS;;AAGlB,MAAAG,QAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,eAAO,YAAY,MAAM,OAAO,QAAQ,MAAM,QAAQ;;AAGxD,MAAAA,QAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,eAAO,YAAY,MAAM,OAAO,QAAQ,OAAO,QAAQ;;AAIzD,MAAAA,QAAO,UAAU,OAAO,SAAS,KAAM,QAAQ,aAAa,OAAO,KAAK;AACtE,YAAI,CAACA,QAAO,SAAS,MAAM,EAAG,OAAM,IAAI,UAAU,6BAA6B;AAC/E,YAAI,CAAC,MAAO,SAAQ;AACpB,YAAI,CAAC,OAAO,QAAQ,EAAG,OAAM,KAAK;AAClC,YAAI,eAAe,OAAO,OAAQ,eAAc,OAAO;AACvD,YAAI,CAAC,YAAa,eAAc;AAChC,YAAI,MAAM,KAAK,MAAM,MAAO,OAAM;AAGlC,YAAI,QAAQ,MAAO,QAAO;AAC1B,YAAI,OAAO,WAAW,KAAK,KAAK,WAAW,EAAG,QAAO;AAGrD,YAAI,cAAc,GAAG;AACnB,gBAAM,IAAI,WAAW,2BAA2B;;AAElD,YAAI,QAAQ,KAAK,SAAS,KAAK,OAAQ,OAAM,IAAI,WAAW,oBAAoB;AAChF,YAAI,MAAM,EAAG,OAAM,IAAI,WAAW,yBAAyB;AAG3D,YAAI,MAAM,KAAK,OAAQ,OAAM,KAAK;AAClC,YAAI,OAAO,SAAS,cAAc,MAAM,OAAO;AAC7C,gBAAM,OAAO,SAAS,cAAc;;AAGtC,cAAMR,OAAM,MAAM;AAElB,YAAI,SAAS,UAAU,OAAO,iBAAiB,UAAU,eAAe,YAAY;AAElF,eAAK,WAAW,aAAa,OAAO,GAAG;QAC3C,OAAS;AACL,2BAAiB,UAAU,IAAI;YAC7B;YACA,KAAK,SAAS,OAAO,GAAG;YACxB;;;AAIJ,eAAOA;;AAOT,MAAAQ,QAAO,UAAU,OAAO,SAAS,KAAM,KAAK,OAAO,KAAK,UAAU;AAEhE,YAAI,OAAO,QAAQ,UAAU;AAC3B,cAAI,OAAO,UAAU,UAAU;AAC7B,uBAAW;AACX,oBAAQ;AACR,kBAAM,KAAK;UACjB,WAAe,OAAO,QAAQ,UAAU;AAClC,uBAAW;AACX,kBAAM,KAAK;;AAEb,cAAI,aAAa,UAAa,OAAO,aAAa,UAAU;AAC1D,kBAAM,IAAI,UAAU,2BAA2B;;AAEjD,cAAI,OAAO,aAAa,YAAY,CAACA,QAAO,WAAW,QAAQ,GAAG;AAChE,kBAAM,IAAI,UAAU,uBAAuB,QAAQ;;AAErD,cAAI,IAAI,WAAW,GAAG;AACpB,kBAAME,QAAO,IAAI,WAAW,CAAC;AAC7B,gBAAK,aAAa,UAAUA,QAAO,OAC/B,aAAa,UAAU;AAEzB,oBAAMA;;;QAGd,WAAa,OAAO,QAAQ,UAAU;AAClC,gBAAM,MAAM;QAChB,WAAa,OAAO,QAAQ,WAAW;AACnC,gBAAM,OAAO,GAAG;;AAIlB,YAAI,QAAQ,KAAK,KAAK,SAAS,SAAS,KAAK,SAAS,KAAK;AACzD,gBAAM,IAAI,WAAW,oBAAoB;;AAG3C,YAAI,OAAO,OAAO;AAChB,iBAAO;;AAGT,gBAAQ,UAAU;AAClB,cAAM,QAAQ,SAAY,KAAK,SAAS,QAAQ;AAEhD,YAAI,CAAC,IAAK,OAAM;AAEhB,YAAIT;AACJ,YAAI,OAAO,QAAQ,UAAU;AAC3B,eAAKA,KAAI,OAAOA,KAAI,KAAK,EAAEA,IAAG;AAC5B,iBAAKA,EAAC,IAAI;;QAEhB,OAAS;AACL,gBAAM,QAAQO,QAAO,SAAS,GAAG,IAC7B,MACAA,QAAO,KAAK,KAAK,QAAQ;AAC7B,gBAAMR,OAAM,MAAM;AAClB,cAAIA,SAAQ,GAAG;AACb,kBAAM,IAAI,UAAU,gBAAgB,MAClC,mCAAmC;;AAEvC,eAAKC,KAAI,GAAGA,KAAI,MAAM,OAAO,EAAEA,IAAG;AAChC,iBAAKA,KAAI,KAAK,IAAI,MAAMA,KAAID,IAAG;;;AAInC,eAAO;;AAOT,YAAM,SAAS,CAAA;AACf,eAAS,EAAG,KAAK,YAAY,MAAM;AACjC,eAAO,GAAG,IAAI,MAAM,kBAAkB,KAAK;UACzC,cAAe;AACb,kBAAK;AAEL,mBAAO,eAAe,MAAM,WAAW;cACrC,OAAO,WAAW,MAAM,MAAM,SAAS;cACvC,UAAU;cACV,cAAc;YACtB,CAAO;AAGD,iBAAK,OAAO,GAAG,KAAK,IAAI,KAAK,GAAG;AAGhC,iBAAK;AAEL,mBAAO,KAAK;;UAGd,IAAI,OAAQ;AACV,mBAAO;;UAGT,IAAI,KAAM,OAAO;AACf,mBAAO,eAAe,MAAM,QAAQ;cAClC,cAAc;cACd,YAAY;cACZ;cACA,UAAU;YAClB,CAAO;;UAGH,WAAY;AACV,mBAAO,GAAG,KAAK,IAAI,KAAK,GAAG,MAAM,KAAK,OAAO;;;;AAKnD;QAAE;QACA,SAAU,MAAM;AACd,cAAI,MAAM;AACR,mBAAO,GAAG,IAAI;;AAGhB,iBAAO;;QACN;MAAU;AACf;QAAE;QACA,SAAU,MAAM,QAAQ;AACtB,iBAAO,QAAQ,IAAI,oDAAoD,OAAO,MAAM;;QACnF;MAAS;AACd;QAAE;QACA,SAAU,KAAK,OAAO,OAAO;AAC3B,cAAI,MAAM,iBAAiB,GAAG;AAC9B,cAAI,WAAW;AACf,cAAI,OAAO,UAAU,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI;AACxD,uBAAW,sBAAsB,OAAO,KAAK,CAAC;UACpD,WAAe,OAAO,UAAU,UAAU;AACpC,uBAAW,OAAO,KAAK;AACvB,gBAAI,QAAQ,OAAO,CAAC,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,OAAO,CAAC,KAAK,OAAO,EAAE,IAAI;AACzE,yBAAW,sBAAsB,QAAQ;;AAE3C,wBAAY;;AAEd,iBAAO,eAAe,KAAK,cAAc,QAAQ;AACjD,iBAAO;;QACN;MAAU;AAEf,eAAS,sBAAuB,KAAK;AACnC,YAAI,MAAM;AACV,YAAIC,KAAI,IAAI;AACZ,cAAM,QAAQ,IAAI,CAAC,MAAM,MAAM,IAAI;AACnC,eAAOA,MAAK,QAAQ,GAAGA,MAAK,GAAG;AAC7B,gBAAM,IAAI,IAAI,MAAMA,KAAI,GAAGA,EAAC,CAAC,GAAG,GAAG;;AAErC,eAAO,GAAG,IAAI,MAAM,GAAGA,EAAC,CAAC,GAAG,GAAG;;AAMjC,eAAS,YAAa,KAAK,QAAQQ,aAAY;AAC7C,uBAAe,QAAQ,QAAQ;AAC/B,YAAI,IAAI,MAAM,MAAM,UAAa,IAAI,SAASA,WAAU,MAAM,QAAW;AACvE,sBAAY,QAAQ,IAAI,UAAUA,cAAa,EAAE;;;AAIrD,eAAS,WAAY,OAAO,KAAK,KAAK,KAAK,QAAQA,aAAY;AAC7D,YAAI,QAAQ,OAAO,QAAQ,KAAK;AAC9B,gBAAM,IAAI,OAAO,QAAQ,WAAW,MAAM;AAC1C,cAAI;AACJ,cAAIA,cAAa,GAAG;AAClB,gBAAI,QAAQ,KAAK,QAAQ,OAAO,CAAC,GAAG;AAClC,sBAAQ,OAAO,CAAC,WAAW,CAAC,QAAQA,cAAa,KAAK,CAAC,GAAG,CAAC;YACnE,OAAa;AACL,sBAAQ,SAAS,CAAC,QAAQA,cAAa,KAAK,IAAI,CAAC,GAAG,CAAC,iBACzCA,cAAa,KAAK,IAAI,CAAC,GAAG,CAAC;;UAE/C,OAAW;AACL,oBAAQ,MAAM,GAAG,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC;;AAEzC,gBAAM,IAAI,OAAO,iBAAiB,SAAS,OAAO,KAAK;;AAEzD,oBAAY,KAAK,QAAQA,WAAU;;AAGrC,eAAS,eAAgB,OAAO,MAAM;AACpC,YAAI,OAAO,UAAU,UAAU;AAC7B,gBAAM,IAAI,OAAO,qBAAqB,MAAM,UAAU,KAAK;;;AAI/D,eAAS,YAAa,OAAO,QAAQ,MAAM;AACzC,YAAI,KAAK,MAAM,KAAK,MAAM,OAAO;AAC/B,yBAAe,OAAO,IAAI;AAC1B,gBAAM,IAAI,OAAO,iBAAiB,QAAQ,UAAU,cAAc,KAAK;;AAGzE,YAAI,SAAS,GAAG;AACd,gBAAM,IAAI,OAAO,yBAAwB;;AAG3C,cAAM,IAAI,OAAO;UAAiB,QAAQ;UACR,MAAM,OAAO,IAAI,CAAC,WAAW,MAAM;UACnC;QAAK;;AAMzC,YAAM,oBAAoB;AAE1B,eAAS,YAAa,KAAK;AAEzB,cAAM,IAAI,MAAM,GAAG,EAAE,CAAC;AAEtB,cAAM,IAAI,KAAI,EAAG,QAAQ,mBAAmB,EAAE;AAE9C,YAAI,IAAI,SAAS,EAAG,QAAO;AAE3B,eAAO,IAAI,SAAS,MAAM,GAAG;AAC3B,gBAAM,MAAM;;AAEd,eAAO;;AAGT,eAAS,YAAa,QAAQ,OAAO;AACnC,gBAAQ,SAAS;AACjB,YAAI;AACJ,cAAM,SAAS,OAAO;AACtB,YAAI,gBAAgB;AACpB,cAAM,QAAQ,CAAA;AAEd,iBAASR,KAAI,GAAGA,KAAI,QAAQ,EAAEA,IAAG;AAC/B,sBAAY,OAAO,WAAWA,EAAC;AAG/B,cAAI,YAAY,SAAU,YAAY,OAAQ;AAE5C,gBAAI,CAAC,eAAe;AAElB,kBAAI,YAAY,OAAQ;AAEtB,qBAAK,SAAS,KAAK,GAAI,OAAM,KAAK,KAAM,KAAM,GAAI;AAClD;cACV,WAAmBA,KAAI,MAAM,QAAQ;AAE3B,qBAAK,SAAS,KAAK,GAAI,OAAM,KAAK,KAAM,KAAM,GAAI;AAClD;;AAIF,8BAAgB;AAEhB;;AAIF,gBAAI,YAAY,OAAQ;AACtB,mBAAK,SAAS,KAAK,GAAI,OAAM,KAAK,KAAM,KAAM,GAAI;AAClD,8BAAgB;AAChB;;AAIF,yBAAa,gBAAgB,SAAU,KAAK,YAAY,SAAU;qBACzD,eAAe;AAExB,iBAAK,SAAS,KAAK,GAAI,OAAM,KAAK,KAAM,KAAM,GAAI;;AAGpD,0BAAgB;AAGhB,cAAI,YAAY,KAAM;AACpB,iBAAK,SAAS,KAAK,EAAG;AACtB,kBAAM,KAAK,SAAS;UAC1B,WAAe,YAAY,MAAO;AAC5B,iBAAK,SAAS,KAAK,EAAG;AACtB,kBAAM;cACJ,aAAa,IAAM;cACnB,YAAY,KAAO;;UAE3B,WAAe,YAAY,OAAS;AAC9B,iBAAK,SAAS,KAAK,EAAG;AACtB,kBAAM;cACJ,aAAa,KAAM;cACnB,aAAa,IAAM,KAAO;cAC1B,YAAY,KAAO;;UAE3B,WAAe,YAAY,SAAU;AAC/B,iBAAK,SAAS,KAAK,EAAG;AACtB,kBAAM;cACJ,aAAa,KAAO;cACpB,aAAa,KAAM,KAAO;cAC1B,aAAa,IAAM,KAAO;cAC1B,YAAY,KAAO;;UAE3B,OAAW;AACL,kBAAM,IAAI,MAAM,oBAAoB;;;AAIxC,eAAO;;AAGT,eAAS,aAAc,KAAK;AAC1B,cAAM,YAAY,CAAA;AAClB,iBAASA,KAAI,GAAGA,KAAI,IAAI,QAAQ,EAAEA,IAAG;AAEnC,oBAAU,KAAK,IAAI,WAAWA,EAAC,IAAI,GAAI;;AAEzC,eAAO;;AAGT,eAAS,eAAgB,KAAK,OAAO;AACnC,YAAI,GAAG,IAAI;AACX,cAAM,YAAY,CAAA;AAClB,iBAASA,KAAI,GAAGA,KAAI,IAAI,QAAQ,EAAEA,IAAG;AACnC,eAAK,SAAS,KAAK,EAAG;AAEtB,cAAI,IAAI,WAAWA,EAAC;AACpB,eAAK,KAAK;AACV,eAAK,IAAI;AACT,oBAAU,KAAK,EAAE;AACjB,oBAAU,KAAK,EAAE;;AAGnB,eAAO;;AAGT,eAAS,cAAe,KAAK;AAC3B,eAAO,OAAO,YAAY,YAAY,GAAG,CAAC;;AAG5C,eAAS,WAAY,KAAK,KAAK,QAAQ,QAAQ;AAC7C,YAAIA;AACJ,aAAKA,KAAI,GAAGA,KAAI,QAAQ,EAAEA,IAAG;AAC3B,cAAKA,KAAI,UAAU,IAAI,UAAYA,MAAK,IAAI,OAAS;AACrD,cAAIA,KAAI,MAAM,IAAI,IAAIA,EAAC;;AAEzB,eAAOA;;AAMT,eAAS,WAAY,KAAK,MAAM;AAC9B,eAAO,eAAe,QACnB,OAAO,QAAQ,IAAI,eAAe,QAAQ,IAAI,YAAY,QAAQ,QACjE,IAAI,YAAY,SAAS,KAAK;;AAEpC,eAAS,YAAa,KAAK;AAEzB,eAAO,QAAQ;;AAKjB,YAAM,sBAAuB,WAAY;AACvC,cAAM,WAAW;AACjB,cAAM,QAAQ,IAAI,MAAM,GAAG;AAC3B,iBAASA,KAAI,GAAGA,KAAI,IAAI,EAAEA,IAAG;AAC3B,gBAAM,MAAMA,KAAI;AAChB,mBAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AAC3B,kBAAM,MAAM,CAAC,IAAI,SAASA,EAAC,IAAI,SAAS,CAAC;;;AAG7C,eAAO;MACT,EAAC;AAGD,eAAS,mBAAoB,IAAI;AAC/B,eAAO,OAAO,WAAW,cAAc,yBAAyB;;AAGlE,eAAS,yBAA0B;AACjC,cAAM,IAAI,MAAM,sBAAsB;MACxC;;AChhEA,QAAAO,UAAeA,OAAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzCT,QAAA,SAAS,cAAc,UAAQ;;;;;;;;;;;;;;;ACArC,QAAI,UAAUG,QAAc,UAAG,CAAA;AAO/B,QAAI;AACJ,QAAI;AAEJ,aAAS,mBAAmB;AACxB,YAAM,IAAI,MAAM,iCAAiC;IACrD;AACA,aAAS,sBAAuB;AAC5B,YAAM,IAAI,MAAM,mCAAmC;IACvD;AACA,KAAC,WAAY;AACT,UAAI;AACA,YAAI,OAAO,eAAe,YAAY;AAClC,6BAAmB;QAC/B,OAAe;AACH,6BAAmB;QAC/B;MACA,SAAa,GAAG;AACR,2BAAmB;MAC3B;AACI,UAAI;AACA,YAAI,OAAO,iBAAiB,YAAY;AACpC,+BAAqB;QACjC,OAAe;AACH,+BAAqB;QACjC;MACA,SAAa,GAAG;AACR,6BAAqB;MAC7B;IACA,GAAC;AACD,aAAS,WAAW,KAAK;AACrB,UAAI,qBAAqB,YAAY;AAEjC,eAAO,WAAW,KAAK,CAAC;MAChC;AAEI,WAAK,qBAAqB,oBAAoB,CAAC,qBAAqB,YAAY;AAC5E,2BAAmB;AACnB,eAAO,WAAW,KAAK,CAAC;MAChC;AACI,UAAI;AAEA,eAAO,iBAAiB,KAAK,CAAC;MACtC,SAAY,GAAE;AACN,YAAI;AAEA,iBAAO,iBAAiB,KAAK,MAAM,KAAK,CAAC;QACrD,SAAgBC,IAAE;AAEN,iBAAO,iBAAiB,KAAK,MAAM,KAAK,CAAC;QACrD;MACA;IAGA;AACA,aAAS,gBAAgB,QAAQ;AAC7B,UAAI,uBAAuB,cAAc;AAErC,eAAO,aAAa,MAAM;MAClC;AAEI,WAAK,uBAAuB,uBAAuB,CAAC,uBAAuB,cAAc;AACrF,6BAAqB;AACrB,eAAO,aAAa,MAAM;MAClC;AACI,UAAI;AAEA,eAAO,mBAAmB,MAAM;MACxC,SAAa,GAAE;AACP,YAAI;AAEA,iBAAO,mBAAmB,KAAK,MAAM,MAAM;QACvD,SAAiBA,IAAE;AAGP,iBAAO,mBAAmB,KAAK,MAAM,MAAM;QACvD;MACA;IAIA;AACA,QAAI,QAAQ,CAAA;AACZ,QAAI,WAAW;AACf,QAAI;AACJ,QAAI,aAAa;AAEjB,aAAS,kBAAkB;AACvB,UAAI,CAAC,YAAY,CAAC,cAAc;AAC5B;MACR;AACI,iBAAW;AACX,UAAI,aAAa,QAAQ;AACrB,gBAAQ,aAAa,OAAO,KAAK;MACzC,OAAW;AACH,qBAAa;MACrB;AACI,UAAI,MAAM,QAAQ;AACd,mBAAU;MAClB;IACA;AAEA,aAAS,aAAa;AAClB,UAAI,UAAU;AACV;MACR;AACI,UAAI,UAAU,WAAW,eAAe;AACxC,iBAAW;AAEX,UAAI,MAAM,MAAM;AAChB,aAAM,KAAK;AACP,uBAAe;AACf,gBAAQ,CAAA;AACR,eAAO,EAAE,aAAa,KAAK;AACvB,cAAI,cAAc;AACd,yBAAa,UAAU,EAAE,IAAG;UAC5C;QACA;AACQ,qBAAa;AACb,cAAM,MAAM;MACpB;AACI,qBAAe;AACf,iBAAW;AACX,sBAAgB,OAAO;IAC3B;AAEA,YAAQ,WAAW,SAAU,KAAK;AAC9B,UAAI,OAAO,IAAI,MAAM,UAAU,SAAS,CAAC;AACzC,UAAI,UAAU,SAAS,GAAG;AACtB,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,eAAK,IAAI,CAAC,IAAI,UAAU,CAAC;QACrC;MACA;AACI,YAAM,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC;AAC9B,UAAI,MAAM,WAAW,KAAK,CAAC,UAAU;AACjC,mBAAW,UAAU;MAC7B;IACA;AAGA,aAAS,KAAK,KAAK,OAAO;AACtB,WAAK,MAAM;AACX,WAAK,QAAQ;IACjB;AACA,SAAK,UAAU,MAAM,WAAY;AAC7B,WAAK,IAAI,MAAM,MAAM,KAAK,KAAK;IACnC;AACA,YAAQ,QAAQ;AAChB,YAAQ,UAAU;AAClB,YAAQ,MAAM,CAAA;AACd,YAAQ,OAAO,CAAA;AACf,YAAQ,UAAU;AAClB,YAAQ,WAAW,CAAA;AAEnB,aAAS,OAAO;IAAA;AAEhB,YAAQ,KAAK;AACb,YAAQ,cAAc;AACtB,YAAQ,OAAO;AACf,YAAQ,MAAM;AACd,YAAQ,iBAAiB;AACzB,YAAQ,qBAAqB;AAC7B,YAAQ,OAAO;AACf,YAAQ,kBAAkB;AAC1B,YAAQ,sBAAsB;AAE9B,YAAQ,YAAY,SAAU,MAAM;AAAE,aAAO,CAAA;IAAE;AAE/C,YAAQ,UAAU,SAAU,MAAM;AAC9B,YAAM,IAAI,MAAM,kCAAkC;IACtD;AAEA,YAAQ,MAAM,WAAY;AAAE,aAAO;IAAG;AACtC,YAAQ,QAAQ,SAAU,KAAK;AAC3B,YAAM,IAAI,MAAM,gCAAgC;IACpD;AACA,YAAQ,QAAQ,WAAW;AAAE,aAAO;IAAE;;;;;;;", "names": ["len", "i", "len2", "buffer", "require$$0", "ieee754", "require$$1", "exports", "<PERSON><PERSON><PERSON>", "byteLength", "code", "browserModule", "e"]}