<!-- 客户端训练监控页面 -->
<template>
  <div class="training-page">
    <div class="page-header">
      <h1>训练监控</h1>
      <div class="task-info" v-if="task">
        <span class="task-name">{{ task.name }}</span>
        <el-tag :type="getStatusType(task.status)">{{ getStatusText(task.status) }}</el-tag>
      </div>
    </div>
    
    <Breadcrumb />
    
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>加载中...</p>
    </div>
    
    <div v-else-if="!task" class="empty-state">
      <i class="fas fa-exclamation-circle"></i>
      <h3>未找到任务</h3>
      <p>无法找到指定的训练任务，请返回任务列表重新选择。</p>
      <el-button type="primary" @click="goToMyTasks">返回我的任务</el-button>
    </div>
    
    <template v-else>
      <!-- 训练状态概览 -->
      <el-card class="status-card">
        <template #header>
          <div class="card-header">
            <i class="fas fa-info-circle"></i>
            <span>训练状态</span>
          </div>
        </template>
        
        <div class="training-status">
          <div class="status-item">
            <span class="status-label">当前状态</span>
            <span class="status-value">
              <el-tag :type="trainingStatus.isTraining ? 'success' : 'info'">
                {{ trainingStatus.isTraining ? '训练中' : '未训练' }}
              </el-tag>
            </span>
          </div>
          
          <div class="status-item">
            <span class="status-label">当前轮次</span>
            <span class="status-value">{{ trainingStatus.currentRound || 0 }}/{{ task.totalRounds }}</span>
          </div>
          
          <div class="status-item">
            <span class="status-label">开始时间</span>
            <span class="status-value">{{ trainingStatus.startedAt ? formatDateTime(trainingStatus.startedAt) : '未开始' }}</span>
          </div>
          
          <div class="status-item">
            <span class="status-label">上次更新</span>
            <span class="status-value">{{ trainingStatus.lastUpdated ? formatDateTime(trainingStatus.lastUpdated) : '无' }}</span>
          </div>
        </div>
        
        <div class="action-buttons">
          <el-button 
            type="primary" 
            :disabled="trainingStatus.isTraining || task.status !== 'in_progress'" 
            @click="startTraining"
            :loading="actionLoading"
          >
            开始训练
          </el-button>
          
          <el-button 
            type="warning" 
            :disabled="!trainingStatus.isTraining"
            @click="pauseTraining"
            :loading="actionLoading"
          >
            暂停训练
          </el-button>
          
          <el-button 
            type="info" 
            @click="refreshStatus"
            :loading="refreshing"
          >
            刷新状态
          </el-button>
        </div>
      </el-card>
      
      <!-- 训练指标和图表 -->
      <div class="metrics-container">
        <el-card class="metrics-card">
          <template #header>
            <div class="card-header">
              <i class="fas fa-chart-bar"></i>
              <span>本地模型指标</span>
            </div>
          </template>
          
          <LocalMetrics 
            :metrics="{
              loss: trainingStatus.loss || 0,
              accuracy: trainingStatus.accuracy || 0,
              epochs: trainingStatus.currentRound || 0,
              trainingTime: calculateTrainingTime()
            }"
            :loading="loading"
          />
        </el-card>
        
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <i class="fas fa-chart-line"></i>
              <span>训练进度</span>
            </div>
          </template>
          
          <LocalTrainingChart 
            :history="trainingHistory"
            :loading="loading"
          />
        </el-card>
      </div>
      
      <!-- 资源使用情况 -->
      <el-card class="resources-card">
        <template #header>
          <div class="card-header">
            <i class="fas fa-microchip"></i>
            <span>资源使用</span>
          </div>
        </template>
        
        <ResourceUsage 
          :usage="{
            cpu: Math.random() * 0.7, // 模拟CPU使用率
            memory: Math.random() * 0.6, // 模拟内存使用率
            network: Math.random() * 1000, // 模拟网络使用率 (KB/s)
            storage: Math.random() * 0.4 // 模拟存储使用率
          }"
          :loading="loading"
        />
      </el-card>
    </template>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useStore } from 'vuex';
import { apiService, TaskAPI, ParticipantAPI } from '@/services/api';
import Breadcrumb from '@/components/Breadcrumb.vue';
import LocalMetrics from '@/components/LocalMetrics.vue';
import LocalTrainingChart from '@/components/LocalTrainingChart.vue';
import ResourceUsage from '@/components/ResourceUsage.vue';
import axios from 'axios';

export default {
  name: 'ClientTraining',
  components: {
    Breadcrumb,
    LocalMetrics,
    LocalTrainingChart,
    ResourceUsage
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const store = useStore();
    
    const loading = ref(false);
    const refreshing = ref(false);
    const actionLoading = ref(false);
    const task = ref(null);
    const trainingStatus = ref({
      isTraining: false,
      currentRound: 0,
      totalRounds: 0,
      startedAt: null,
      lastUpdated: null,
      loss: 0,
      accuracy: 0
    });
    const trainingHistory = ref([]);
    const statusInterval = ref(null);
    
    // 获取当前用户地址
    const userAddress = computed(() => store.state.user.address);
    
    // 获取任务详情
    const loadTask = async () => {
      const taskId = route.query.task;
      if (!taskId) {
        ElMessage.error('未指定任务ID');
        return;
      }
      
      loading.value = true;
      try {
        // 获取所有任务
        const allTasks = await TaskAPI.getAll();
        // 找到当前任务
        const foundTask = allTasks.find(t => t.id.toString() === taskId.toString());
        
        if (!foundTask) {
          ElMessage.error('未找到指定任务');
          return;
        }
        
        task.value = foundTask;
        
        // 获取训练状态
        await loadTrainingStatus();
        
        // 如果没有获取到实际历史数据，再考虑生成模拟数据
        if (trainingHistory.value.length === 0) {
          console.log('未获取到实际历史数据，将生成模拟数据');
          generateMockTrainingHistory();
        }
      } catch (error) {
        console.error('加载任务失败:', error);
        ElMessage.error('加载任务失败: ' + error.message);
      } finally {
        loading.value = false;
      }
    };
    
    // 获取训练状态
    const loadTrainingStatus = async () => {
      if (!task.value || !userAddress.value) return;
      
      try {
        refreshing.value = true;
        
        // 获取训练状态
        const response = await axios.get(`http://localhost:5000/api/client/status/${userAddress.value}`);
        const status = response.data;
        
        console.log('获取到客户端训练状态:', status);
        
        // 如果客户端返回了实际指标，使用它
        const metrics = status.metrics || {};
        
        // 更新状态
        trainingStatus.value = {
          isTraining: status.isTraining || false,
          currentRound: status.currentRound || 0,
          totalRounds: status.totalRounds || task.value.totalRounds,
          startedAt: status.startTime ? new Date(status.startTime * 1000) : null,
          lastUpdated: new Date(),
          loss: metrics.loss || 0,
          accuracy: metrics.accuracy || 0
        };
        
        console.log('更新后的训练状态:', trainingStatus.value);
        
        // 如果有训练历史，更新历史数据
        if (status.history && status.history.length > 0) {
          trainingHistory.value = status.history.map(item => ({
            round: item.round,
            loss: item.loss,
            accuracy: item.accuracy,
            timestamp: new Date(item.timestamp)
          }));
          
          console.log('更新训练历史数据:', trainingHistory.value);
        } else if (trainingStatus.value.isTraining && trainingHistory.value.length === 0) {
          // 如果没有历史数据但正在训练，初始化历史数据
          updateTrainingHistory();
        }
      } catch (error) {
        console.error('获取训练状态失败:', error);
      } finally {
        refreshing.value = false;
      }
    };
    
    // 刷新状态
    const refreshStatus = async () => {
      await loadTrainingStatus();
      ElMessage.success('状态已刷新');
    };
    
    // 开始训练
    const startTraining = async () => {
      if (!task.value || !userAddress.value) return;
      
      try {
        actionLoading.value = true;
        
        // 确认开始训练
        await ElMessageBox.confirm(
          '确定要开始参与此任务的训练吗？\n这将连接到联邦学习服务器参与训练过程。',
          '开始训练',
          {
            confirmButtonText: '开始训练',
            cancelButtonText: '取消',
            type: 'info'
          }
        );
        
        console.log(`开始客户端训练，用户地址: ${userAddress.value}, 任务ID: ${task.value.id}`);
        
        try {
          // 首先检查任务状态
          const taskResponse = await TaskAPI.getById(task.value.id);
          if (taskResponse.status !== 'in_progress') {
            ElMessage.warning(`任务当前状态为${getStatusText(taskResponse.status)}，只有进行中的任务才能参与训练`);
            return;
          }
          
          // 检查参与者状态
          const participants = await ParticipantAPI.getByTaskId(task.value.id);
          const userParticipant = participants.find(p => p.client_address === userAddress.value);
          
          if (!userParticipant) {
            ElMessage.error('您不是此任务的参与者，无法参与训练');
            return;
          }
          
          if (userParticipant.status === 'registered') {
            ElMessage.warning('您的申请尚未被批准，请等待审核通过后再参与训练');
            return;
          }
          
          if (userParticipant.status === 'rejected') {
            ElMessage.error('您的申请已被拒绝，无法参与训练');
            return;
          }
          
          // 显示启动中的提示
          ElMessage({
            type: 'info',
            message: '正在连接到联邦学习服务器...',
            duration: 0,
            showClose: true,
          });
          
          // 首先通知服务器加入训练
          const joinResponse = await axios.post('http://localhost:5000/api/client/join', {
            address: userAddress.value,
            taskId: task.value.id,
            dataset: {
              name: "MNIST",
              size: userParticipant.data_size || 1000
            }
          });
          
          console.log('服务器加入响应:', joinResponse.data);
          
          // 确定客户端端口
          const clientPort = userAddress.value === "0xe56B1329f32D9c289C40054Fb20239DE52D6BbC0" ? 5001 :
                            userAddress.value === "0x8D506914151782957A0627c18be93c0b7E770DFA" ? 5002 : 5003;
          
          // 调用客户端训练控制API
          const trainResponse = await axios.post(`http://localhost:${clientPort}/api/client/train`, {
            action: 'start',
            taskId: task.value.id,
            batchSize: task.value.batchSize || 32,
            learningRate: task.value.learningRate || 0.01,
            epochs: task.value.epochs || 1,
            timeout: task.value.timeout || 60
          });
          
          console.log('客户端训练响应:', trainResponse.data);
          
          if (trainResponse.data.success) {
            // 更新状态
            trainingStatus.value = {
              ...trainingStatus.value,
              isTraining: true,
              startedAt: new Date(),
              lastUpdated: new Date(),
              currentRound: 1,
              totalRounds: task.value.totalRounds,
              loss: 1.0,
              accuracy: 0.5
            };
            
            // 更新历史记录
            updateTrainingHistory();
            
            // 关闭所有提示
            ElMessage.closeAll();
            
            ElMessage.success('已成功开始训练');
          } else {
            throw new Error(trainResponse.data.error || '启动训练失败');
          }
          
        } catch (error) {
          // 关闭所有提示
          ElMessage.closeAll();
          
          console.error('启动训练失败:', error);
          if (error.response) {
            ElMessage.error(`启动训练失败: ${error.response.data.error || '未知错误'}`);
          } else {
            ElMessage.error(`启动训练失败: ${error.message}`);
          }
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('开始训练失败:', error);
          ElMessage.error('开始训练失败: ' + error.message);
        }
      } finally {
        actionLoading.value = false;
      }
    };
    
    // 暂停训练
    const pauseTraining = async () => {
      if (!task.value || !userAddress.value) return;
      
      try {
        actionLoading.value = true;
        
        // 确认暂停训练
        await ElMessageBox.confirm(
          '确定要暂停当前训练吗？',
          '暂停训练',
          {
            confirmButtonText: '暂停',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );
        
        // 使用实际的API调用
        const clientPort = userAddress.value === "0xe56B1329f32D9c289C40054Fb20239DE52D6BbC0" ? 5001 :
                          userAddress.value === "0x8D506914151782957A0627c18be93c0b7E770DFA" ? 5002 : 5003;
                          
        // 调用客户端API
        const response = await axios.post(`http://localhost:${clientPort}/api/client/train`, {
          action: 'pause',
          taskId: task.value.id
        }, {
          headers: { 'Content-Type': 'application/json' }
        });
        
        if (!response.data.success) {
          throw new Error(response.data.message || '暂停训练失败');
        }
        
        // 更新状态
        trainingStatus.value = {
          ...trainingStatus.value,
          isTraining: false,
          lastUpdated: new Date()
        };
        
        ElMessage.success('训练已暂停');
      } catch (error) {
        if (error !== 'cancel') {
          console.error('暂停训练失败:', error);
          ElMessage.error('暂停训练失败: ' + error.message);
        }
      } finally {
        actionLoading.value = false;
      }
    };
    
    // 生成模拟训练历史数据（仅在无实际数据时使用）
    const generateMockTrainingHistory = () => {
      if (!task.value) return;
      
      // 如果已经有历史数据，则不生成模拟数据
      if (trainingHistory.value && trainingHistory.value.length > 0) {
        console.log('已有历史数据，不生成模拟数据');
        return;
      }
      
      console.log('生成模拟训练历史数据');
      const history = [];
      const totalRounds = task.value.totalRounds || 10;
      const currentRound = trainingStatus.value.currentRound || 0;
      
      // 只生成到当前轮次的模拟数据
      const roundsToGenerate = Math.min(currentRound, totalRounds);
      
      // 生成随机训练历史
      for (let i = 1; i <= roundsToGenerate; i++) {
        // 随机生成损失和准确率，使其随着轮次的增加呈现一定趋势
        const progress = i / totalRounds;
        const loss = Math.max(0.1, 1.0 - progress * 0.8 - Math.random() * 0.1);
        const accuracy = Math.min(0.99, 0.5 + progress * 0.4 + Math.random() * 0.1);
        
        history.push({
          round: i,
          loss,
          accuracy,
          timestamp: new Date(Date.now() - (roundsToGenerate - i) * 600000) // 模拟时间戳
        });
      }
      
      trainingHistory.value = history;
    };
    
    // 更新训练历史（添加当前轮次的数据点）
    const updateTrainingHistory = () => {
      if (!trainingStatus.value.isTraining || !task.value) return;
      
      const currentRound = trainingStatus.value.currentRound || 0;
      
      // 如果历史记录长度小于当前轮次，添加新记录
      if (trainingHistory.value.length < currentRound) {
        const newEntry = {
          round: currentRound,
          loss: trainingStatus.value.loss,
          accuracy: trainingStatus.value.accuracy,
          timestamp: new Date()
        };
        
        console.log('添加新的训练历史数据点:', newEntry);
        trainingHistory.value.push(newEntry);
      }
    };
    
    // 计算训练时间（秒）
    const calculateTrainingTime = () => {
      if (!trainingStatus.value.startedAt) return 0;
      
      const startTime = new Date(trainingStatus.value.startedAt).getTime();
      const endTime = trainingStatus.value.isTraining ? Date.now() : new Date(trainingStatus.value.lastUpdated || Date.now()).getTime();
      
      return Math.floor((endTime - startTime) / 1000);
    };
    
    // 格式化日期时间
    const formatDateTime = (date) => {
      if (!date) return '';
      
      const d = new Date(date);
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}:${String(d.getSeconds()).padStart(2, '0')}`;
    };
    
    // 获取状态样式类型
    const getStatusType = (status) => {
      const typeMap = {
        'draft': 'info',
        'published': 'warning',
        'in_progress': 'success',
        'completed': 'success',
        'cancelled': 'danger'
      };
      
      return typeMap[status] || 'info';
    };
    
    // 获取状态文本
    const getStatusText = (status) => {
      const textMap = {
        'draft': '草稿',
        'published': '已发布',
        'in_progress': '进行中',
        'completed': '已完成',
        'cancelled': '已取消'
      };
      
      return textMap[status] || status;
    };
    
    // 返回任务列表
    const goToMyTasks = () => {
      router.push('/client/my-tasks');
    };
    
    // 开启定时刷新
    const startStatusRefresh = () => {
      // 清除已有的定时器
      if (statusInterval.value) {
        clearInterval(statusInterval.value);
      }
      
      // 每5秒刷新一次状态
      statusInterval.value = setInterval(async () => {
        await loadTrainingStatus();
      }, 5000);
    };
    
    // 组件挂载时加载数据
    onMounted(async () => {
      await loadTask();
      startStatusRefresh();
    });
    
    // 组件卸载时清除定时器
    onUnmounted(() => {
      if (statusInterval.value) {
        clearInterval(statusInterval.value);
      }
    });
    
    return {
      loading,
      refreshing,
      actionLoading,
      task,
      trainingStatus,
      trainingHistory,
      loadTask,
      loadTrainingStatus,
      refreshStatus,
      startTraining,
      pauseTraining,
      formatDateTime,
      getStatusType,
      getStatusText,
      goToMyTasks,
      calculateTrainingTime
    };
  }
};
</script>

<style scoped>
.training-page {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.task-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-name {
  font-size: 16px;
  font-weight: 500;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1976D2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  text-align: center;
}

.empty-state i {
  font-size: 48px;
  color: #bbb;
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 20px;
  margin-bottom: 8px;
}

.empty-state p {
  color: #666;
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-header i {
  color: #409EFF;
}

.status-card {
  margin-bottom: 24px;
}

.training-status {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.status-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-label {
  font-size: 14px;
  color: #666;
}

.status-value {
  font-size: 16px;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.metrics-container {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 24px;
  margin-bottom: 24px;
}

.resources-card {
  margin-bottom: 24px;
}

@media (max-width: 992px) {
  .metrics-container {
    grid-template-columns: 1fr;
  }
  
  .training-status {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .action-buttons {
    flex-direction: column;
    width: 100%;
  }
  
  .training-status {
    grid-template-columns: 1fr;
  }
}
</style> 