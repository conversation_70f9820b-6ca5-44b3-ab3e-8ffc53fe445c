const mysql = require('mysql2/promise');
const env = require('../config/env');

// 数据库配置
const dbConfig = {
  host: env.DB.HOST,
  port: env.DB.PORT,
  user: env.DB.USER,
  password: env.DB.PASSWORD,
  database: env.DB.DATABASE,
  connectionLimit: env.DB.CONNECTION_LIMIT,
  waitForConnections: true,
  queueLimit: 0,
  namedPlaceholders: true,
  // MySQL默认强制将类型转换为字符串，这里明确禁用类型转换
  typeCast: function (field, next) {
    if (field.type === 'TINY' && field.length === 1) {
      return (field.string() === '1'); // 1 = true, 0 = false
    }
    return next();
  },
  // 添加以下参数解决认证问题
  authPlugins: {
    mysql_native_password: () => ({ password: env.DB.PASSWORD })
  }
};

// 创建数据库连接池
const pool = mysql.createPool(dbConfig);

/**
 * 测试数据库连接
 */
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('数据库连接成功');
    connection.release();
    return true;
  } catch (error) {
    console.error('数据库连接失败:', error);
    return false;
  }
}

/**
 * 确保数据库存在
 */
async function ensureDatabase() {
  try {
    // 创建不含数据库名的连接配置
    const tempConfig = {
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.user,
      password: dbConfig.password,
      // 添加以下参数解决认证问题
      authPlugins: {
        mysql_native_password: () => ({ password: dbConfig.password })
      }
    };

    console.log('尝试创建MySQL连接:', {
      host: tempConfig.host,
      port: tempConfig.port,
      user: tempConfig.user
    });

    // 尝试不同的连接方式
    let tempConnection;
    try {
      tempConnection = await mysql.createConnection(tempConfig);
    } catch (connError) {
      console.log('第一种连接方式失败，尝试备用连接方式...');
      // 尝试第二种连接方式，使用遗留认证
      try {
        tempConnection = await mysql.createConnection({
          ...tempConfig,
          insecureAuth: true
        });
      } catch (secondError) {
        console.log('第二种连接方式失败，尝试直接连接到mysql数据库...');
        // 尝试直接连接到mysql数据库
        try {
          tempConnection = await mysql.createConnection({
            ...tempConfig,
            database: 'mysql'
          });
        } catch (thirdError) {
          // 如果所有尝试都失败，抛出原始错误
          throw connError;
        }
      }
    }
    
    console.log('MySQL临时连接成功');
    
    // 尝试创建数据库（如果不存在）
    console.log(`尝试创建数据库: ${dbConfig.database}`);
    await tempConnection.query(
      `CREATE DATABASE IF NOT EXISTS ${dbConfig.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`
    );
    
    // 关闭临时连接
    await tempConnection.end();
    
    console.log(`确保数据库 ${dbConfig.database} 存在`);
    return true;
  } catch (error) {
    console.error('确保数据库存在时出错:', error);
    
    // 尝试跳过创建数据库直接返回true，假设数据库已存在
    console.log('尝试跳过创建数据库步骤...');
    return true; // 修改为直接返回true
  }
}

/**
 * 执行SQL查询
 * @param {string} sql SQL语句
 * @param {Array|Object} params 查询参数
 * @returns {Promise<*>} 查询结果
 */
async function query(sql, params = []) {
  try {
    // 预处理参数，将undefined替换为null，并确保数值型参数正确转换
    let processedParams;
    
    if (Array.isArray(params)) {
      processedParams = params.map(param => {
        // 处理undefined参数
        if (param === undefined) return null;
        
        // 处理数值参数，确保是数字类型
        if (typeof param === 'string') {
          // 如果是纯数字字符串，尝试转换为数字
          if (/^\d+$/.test(param)) {
            return parseInt(param, 10);
          }
          // 如果是浮点数字符串，尝试转换为浮点数
          if (/^\d+\.\d+$/.test(param)) {
            return parseFloat(param);
          }
        }
        
        return param;
      });
      console.log('SQL查询参数(数组):', processedParams);
    } else if (typeof params === 'object' && params !== null) {
      processedParams = {};
      for (const key in params) {
        let value = params[key];
        
        // 处理undefined值
        if (value === undefined) {
          processedParams[key] = null;
          continue;
        }
        
        // 处理数值参数
        if (typeof value === 'string') {
          // 如果是纯数字字符串，尝试转换为数字
          if (/^\d+$/.test(value)) {
            value = parseInt(value, 10);
          }
          // 如果是浮点数字符串，尝试转换为浮点数
          else if (/^\d+\.\d+$/.test(value)) {
            value = parseFloat(value);
          }
        }
        
        processedParams[key] = value;
      }
      console.log('SQL查询参数(对象):', processedParams);
    } else {
      processedParams = params;
      console.log('SQL查询参数(其他):', processedParams);
    }
    
    console.log('执行SQL查询:', sql);
    
    const [rows] = await pool.execute(sql, processedParams);
    return rows;
  } catch (error) {
    console.error('执行SQL查询失败:', error);
    console.error('SQL语句:', sql);
    console.error('参数:', JSON.stringify(params));
    throw error;
  }
}

// 导出方法
module.exports = {
  pool,
  testConnection,
  ensureDatabase,
  query
};