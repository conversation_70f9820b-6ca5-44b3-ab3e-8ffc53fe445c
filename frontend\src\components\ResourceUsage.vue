<template>
  <div class="resource-component">
    <div class="component-header">
      <h3>资源使用</h3>
    </div>
    
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
    </div>
    
    <div v-else class="resource-content">
      <!-- CPU 使用率 -->
      <div class="resource-item">
        <div class="resource-info">
          <div class="resource-label">
            <i class="fas fa-microchip"></i>
            <span>CPU</span>
          </div>
          <div class="resource-value">{{ formatPercentage(usage.cpu) }}</div>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${usage.cpu * 100}%`, backgroundColor: getCpuColor(usage.cpu) }"></div>
        </div>
      </div>
      
      <!-- 内存使用率 -->
      <div class="resource-item">
        <div class="resource-info">
          <div class="resource-label">
            <i class="fas fa-memory"></i>
            <span>内存</span>
          </div>
          <div class="resource-value">{{ formatPercentage(usage.memory) }}</div>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${usage.memory * 100}%`, backgroundColor: getMemoryColor(usage.memory) }"></div>
        </div>
      </div>
      
      <!-- 网络使用率 -->
      <div class="resource-item">
        <div class="resource-info">
          <div class="resource-label">
            <i class="fas fa-network-wired"></i>
            <span>网络</span>
          </div>
          <div class="resource-value">{{ formatNetworkSpeed(usage.network) }}</div>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${getNetworkPercentage(usage.network)}%`, backgroundColor: getNetworkColor(usage.network) }"></div>
        </div>
      </div>
      
      <!-- 存储使用率 -->
      <div class="resource-item">
        <div class="resource-info">
          <div class="resource-label">
            <i class="fas fa-hdd"></i>
            <span>存储</span>
          </div>
          <div class="resource-value">{{ formatPercentage(usage.storage) }}</div>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${usage.storage * 100}%`, backgroundColor: getStorageColor(usage.storage) }"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ResourceUsage',
  props: {
    usage: {
      type: Object,
      default: () => ({
        cpu: 0,
        memory: 0,
        network: 0,
        storage: 0
      })
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    // 格式化百分比
    const formatPercentage = (value) => {
      if (typeof value !== 'number') return '0%';
      return Math.round(value * 100) + '%';
    };
    
    // 格式化网络速度
    const formatNetworkSpeed = (value) => {
      if (!value || typeof value !== 'number') return '0 KB/s';
      
      if (value < 1024) {
        return Math.round(value) + ' KB/s';
      } else if (value < 1024 * 1024) {
        return (value / 1024).toFixed(1) + ' MB/s';
      } else {
        return (value / (1024 * 1024)).toFixed(1) + ' GB/s';
      }
    };
    
    // 获取网络使用率百分比（基于经验值）
    const getNetworkPercentage = (value) => {
      if (!value || typeof value !== 'number') return 0;
      
      // 假设10MB/s是100%
      const maxSpeed = 10 * 1024; // 10MB/s in KB/s
      return Math.min(value / maxSpeed * 100, 100);
    };
    
    // 根据CPU使用率获取颜色
    const getCpuColor = (value) => {
      if (value < 0.5) return '#4CAF50'; // 绿色
      if (value < 0.8) return '#FF9800'; // 橙色
      return '#F44336'; // 红色
    };
    
    // 根据内存使用率获取颜色
    const getMemoryColor = (value) => {
      if (value < 0.6) return '#2196F3'; // 蓝色
      if (value < 0.85) return '#FF9800'; // 橙色
      return '#F44336'; // 红色
    };
    
    // 根据网络使用率获取颜色
    const getNetworkColor = (value) => {
      const percentage = getNetworkPercentage(value);
      if (percentage < 50) return '#2196F3'; // 蓝色
      if (percentage < 80) return '#FF9800'; // 橙色
      return '#F44336'; // 红色
    };
    
    // 根据存储使用率获取颜色
    const getStorageColor = (value) => {
      if (value < 0.7) return '#9C27B0'; // 紫色
      if (value < 0.9) return '#FF9800'; // 橙色
      return '#F44336'; // 红色
    };
    
    return {
      formatPercentage,
      formatNetworkSpeed,
      getNetworkPercentage,
      getCpuColor,
      getMemoryColor,
      getNetworkColor,
      getStorageColor
    };
  }
};
</script>

<style scoped>
.resource-component {
  background: white;
  border-radius: 16px;
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.component-header {
  margin-bottom: 24px;
}

.component-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
  font-weight: 600;
}

.resource-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.resource-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.resource-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.resource-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.resource-label {
  font-size: 14px;
  color: #5f6368;
  display: flex;
  align-items: center;
  gap: 8px;
}

.resource-label i {
  font-size: 16px;
  color: #2c3e50;
}

.resource-value {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.progress-bar {
  height: 8px;
  background-color: #f1f3f4;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.loading-container {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1976D2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 