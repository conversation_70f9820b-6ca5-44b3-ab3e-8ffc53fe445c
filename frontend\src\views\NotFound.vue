<template>
  <div class="not-found">
    <div class="error-container">
      <h1 class="error-code">404</h1>
      <h2 class="error-title">页面未找到</h2>
      <p class="error-message">您访问的页面不存在或已被移除</p>
      <button class="return-btn" @click="goHome">返回首页</button>
      <p class="redirect-message" v-if="countdown > 0">{{ countdown }}秒后自动跳转...</p>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';

export default {
  name: 'NotFound',
  setup() {
    const router = useRouter();
    const store = useStore();
    const countdown = ref(5); // 5秒倒计时
    let timer = null;

    const goHome = () => {
      const userRole = store.state.user.role;
      if (userRole) {
        router.push(`/${userRole}`);
      } else {
        router.push('/');
      }
    };

    onMounted(() => {
      // 开始倒计时
      timer = setInterval(() => {
        countdown.value--;
        if (countdown.value <= 0) {
          clearInterval(timer);
          goHome();
        }
      }, 1000);
    });

    onUnmounted(() => {
      // 清除定时器
      if (timer) {
        clearInterval(timer);
      }
    });

    return {
      countdown,
      goHome
    };
  }
};
</script>

<style scoped>
.not-found {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.error-container {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 500px;
}

.error-code {
  font-size: 6rem;
  color: #f44336;
  margin: 0;
}

.error-title {
  font-size: 2rem;
  margin: 1rem 0;
  color: #333;
}

.error-message {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 2rem;
}

.return-btn {
  padding: 0.75rem 2rem;
  background-color: #1976D2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.return-btn:hover {
  background-color: #1565C0;
}

.redirect-message {
  margin-top: 1.5rem;
  color: #666;
  font-size: 0.9rem;
}
</style>
