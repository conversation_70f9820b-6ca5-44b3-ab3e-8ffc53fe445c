import Web3Service from '@/services/web3';

const state = {
  address: null,
  role: null,
  isRegistered: false,
  profile: null
};

const mutations = {
  SET_USER(state, { address, role }) {
    state.address = address;
    state.role = role;
  },
  CLEAR_USER(state) {
    state.address = null;
    state.role = null;
    state.isRegistered = false;
    state.profile = null;
  },
  SET_PROFILE(state, profile) {
    state.profile = profile;
    state.isRegistered = true;
  }
};

const actions = {
  async login({ commit }, { address, role }) {
    try {
      // 检查是否已注册
      const isRegistered = await Web3Service.isUserRegistered(address);
      if (!isRegistered) {
        throw new Error('该钱包地址尚未注册');
      }

      // 获取用户角色
      const userRole = await Web3Service.getUserRole(address);
      const expectedRoleNumber = Web3Service.convertRoleToNumber(role);
      console.log('Store login - User role from contract (raw):', userRole);
      console.log('Store login - User role from contract (type):', typeof userRole);
      console.log('Store login - Expected role number:', expectedRoleNumber);
      console.log('Store login - Expected role number (type):', typeof expectedRoleNumber);
      
      // 确保进行数字比较
      const userRoleNumber = parseInt(userRole);
      
      console.log('Store login - Comparing:', userRoleNumber, 'vs', expectedRoleNumber);
      
      if (userRoleNumber !== expectedRoleNumber) {
        console.log('Store login - Role mismatch!');
        throw new Error('用户角色不匹配');
      }

      commit('SET_USER', { address, role });
      localStorage.setItem('user', JSON.stringify({ address, role }));
      
      return true;
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  },
  
  logout({ commit }) {
    localStorage.removeItem('user');
    commit('CLEAR_USER');
  },

  async register({ commit }, { role }) {
    try {
      const address = await Web3Service.connectWallet();
      if (!address) {
        throw new Error('请先连接钱包');
      }

      // 检查是否已注册
      const isRegistered = await Web3Service.isUserRegistered(address);
      if (isRegistered) {
        throw new Error('该钱包地址已注册');
      }

      // 注册用户
      await Web3Service.registerUser(role);

      // 更新状态
      commit('SET_USER', { address, role });
      localStorage.setItem('user', JSON.stringify({ address, role }));

      return true;
    } catch (error) {
      console.error('注册失败:', error);
      throw error;
    }
  },

  async checkRegistrationStatus({ state, commit }) {
    try {
      if (!state.address) return false;
      
      const isRegistered = await Web3Service.isUserRegistered(state.address);
      if (isRegistered) {
        const role = await Web3Service.getUserRole(state.address);
        commit('SET_PROFILE', { registered: true, role });
      }
      return isRegistered;
    } catch (error) {
      console.error('检查注册状态失败:', error);
      return false;
    }
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
