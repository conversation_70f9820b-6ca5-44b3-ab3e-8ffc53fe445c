<!-- 客户端首页组件 -->
<template>
  <div class="client-home">
    <div class="page-header">
      <h1>客户端控制台</h1>
      <div class="page-actions">
        <button class="app-btn primary" @click="browseAvailableTasks">
          <i class="fas fa-list"></i> 浏览可用任务
        </button>
        <button class="app-btn secondary" @click="refreshData">
          <i class="fas fa-sync"></i> 刷新数据
        </button>
      </div>
    </div>
    
    <!-- 训练参与配置面板 -->
    <div class="panel">
      <div class="panel-header">
        <h2>训练参与配置</h2>
      </div>
      <div class="panel-content">
        <div class="config-form">
          <div class="form-group">
            <label>数据集大小</label>
            <input 
              type="number" 
              v-model="participationConfig.datasetSize" 
              min="100" 
              :disabled="isParticipating"
              class="form-control"
            >
          </div>
          <div class="form-group">
            <label>计算资源分配</label>
            <select v-model="participationConfig.computeResource" :disabled="isParticipating" class="form-control">
              <option value="low">低 (25%)</option>
              <option value="medium">中 (50%)</option>
              <option value="high">高 (75%)</option>
              <option value="max">最大 (100%)</option>
            </select>
          </div>
          <div class="form-group">
            <label>批处理大小</label>
            <select v-model="participationConfig.batchSize" :disabled="isParticipating" class="form-control">
              <option :value="16">16</option>
              <option :value="32">32</option>
              <option :value="64">64</option>
              <option :value="128">128</option>
            </select>
          </div>
        </div>
        <div class="form-actions">
          <button 
            class="app-btn large"
            :class="{ 'danger': isParticipating, 'primary': !isParticipating }"
            @click="toggleParticipation"
            :disabled="loading"
          >
            <i :class="[isParticipating ? 'fas fa-sign-out-alt' : 'fas fa-sign-in-alt']"></i>
            {{ isParticipating ? '退出训练' : '加入训练' }}
          </button>
          <button 
            class="app-btn large info"
            @click="refreshDatasets"
            :disabled="isParticipating || loading"
          >
            <i class="fas fa-database"></i>
            刷新数据集信息
          </button>
        </div>
      </div>
    </div>

    <!-- 客户端训练状态仪表盘 -->
    <div class="dashboard-grid">
      <!-- 本地训练进度图表 -->
      <div class="dashboard-card span-2">
        <div class="card-header">
          <h3>本地训练进度</h3>
        </div>
        <div class="loading-container" v-if="loading">
          <div class="loading-spinner"></div>
        </div>
        <div class="empty-state" v-else-if="localTrainingHistory.length === 0">
          <i class="fas fa-chart-line"></i>
          <p>暂无训练数据</p>
        </div>
        <div class="chart-container" v-else>
          <div class="chart-placeholder">
            训练数据图表将在此显示
          </div>
        </div>
      </div>

      <!-- 本地模型指标 -->
      <div class="dashboard-card">
        <div class="card-header">
          <h3>本地模型指标</h3>
        </div>
        <div class="loading-container" v-if="loading">
          <div class="loading-spinner"></div>
        </div>
        <div class="grid grid-2" v-else>
          <div class="metric-item">
            <div class="metric-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <div class="metric-info">
              <div class="metric-label">损失值</div>
              <div class="metric-value">{{ localMetrics.loss.toFixed(4) }}</div>
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-icon">
              <i class="fas fa-bullseye"></i>
            </div>
            <div class="metric-info">
              <div class="metric-label">准确率</div>
              <div class="metric-value">{{ (localMetrics.accuracy * 100).toFixed(2) }}%</div>
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-icon">
              <i class="fas fa-redo"></i>
            </div>
            <div class="metric-info">
              <div class="metric-label">完成轮次</div>
              <div class="metric-value">{{ localMetrics.epochs }}</div>
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="metric-info">
              <div class="metric-label">训练时长</div>
              <div class="metric-value">{{ formatTime(localMetrics.trainingTime) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 贡献统计 -->
      <div class="dashboard-card">
        <div class="card-header">
          <h3>贡献统计</h3>
        </div>
        <div class="loading-container" v-if="loading">
          <div class="loading-spinner"></div>
        </div>
        <div class="grid grid-2" v-else>
          <div class="metric-item">
            <div class="metric-icon">
              <i class="fas fa-tasks"></i>
            </div>
            <div class="metric-info">
              <div class="metric-label">参与轮次</div>
              <div class="metric-value">{{ contributionStats.roundsParticipated }}</div>
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-icon">
              <i class="fas fa-database"></i>
            </div>
            <div class="metric-info">
              <div class="metric-label">数据贡献</div>
              <div class="metric-value">{{ formatData(contributionStats.dataContributed) }}</div>
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-icon">
              <i class="fas fa-percentage"></i>
            </div>
            <div class="metric-info">
              <div class="metric-label">平均准确率</div>
              <div class="metric-value">{{ (contributionStats.avgAccuracy * 100).toFixed(2) }}%</div>
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-icon">
              <i class="fas fa-coins"></i>
            </div>
            <div class="metric-info">
              <div class="metric-label">累计奖励</div>
              <div class="metric-value">{{ contributionStats.totalReward.toFixed(2) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 资源使用 -->
      <div class="dashboard-card">
        <div class="card-header">
          <h3>资源使用</h3>
        </div>
        <div class="loading-container" v-if="loading">
          <div class="loading-spinner"></div>
        </div>
        <div class="resource-list" v-else>
          <div class="resource-item">
            <div class="resource-info">
              <div class="resource-label">CPU使用率</div>
              <div class="resource-value">{{ (resourceUsage.cpu * 100).toFixed(0) }}%</div>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: `${resourceUsage.cpu * 100}%`, backgroundColor: getResourceColor(resourceUsage.cpu) }"></div>
            </div>
          </div>
          <div class="resource-item">
            <div class="resource-info">
              <div class="resource-label">内存使用率</div>
              <div class="resource-value">{{ (resourceUsage.memory * 100).toFixed(0) }}%</div>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: `${resourceUsage.memory * 100}%`, backgroundColor: getResourceColor(resourceUsage.memory) }"></div>
            </div>
          </div>
          <div class="resource-item">
            <div class="resource-info">
              <div class="resource-label">网络使用率</div>
              <div class="resource-value">{{ formatNetworkSpeed(resourceUsage.network) }}</div>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: `${Math.min(resourceUsage.network / 1024 * 10, 100)}%`, backgroundColor: getResourceColor(resourceUsage.network / 1024 / 10) }"></div>
            </div>
          </div>
          <div class="resource-item">
            <div class="resource-info">
              <div class="resource-label">存储使用率</div>
              <div class="resource-value">{{ (resourceUsage.storage * 100).toFixed(0) }}%</div>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: `${resourceUsage.storage * 100}%`, backgroundColor: getResourceColor(resourceUsage.storage) }"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import web3Service from '../../services/web3';
import apiService from '../../services/api';
import { useStore } from 'vuex';

export default {
  name: 'ClientHome',
  setup() {
    const router = useRouter();
    const store = useStore();
    const loading = ref(false);
    const isParticipating = ref(false);
    const localTrainingHistory = ref([]);
    const updateInterval = ref(null);

    const participationConfig = ref({
      datasetSize: 1000,
      computeResource: 'medium',
      batchSize: 32
    });

    const localMetrics = ref({
      loss: 0,
      accuracy: 0,
      epochs: 0,
      trainingTime: 0
    });

    const contributionStats = ref({
      roundsParticipated: 0,
      dataContributed: 0,
      avgAccuracy: 0,
      totalReward: 0
    });

    const resourceUsage = ref({
      cpu: 0,
      memory: 0,
      network: 0,
      storage: 0
    });

    // 加入/退出训练
    const toggleParticipation = async () => {
      try {
        loading.value = true;
        
        // 确保Web3已初始化
        try {
          await web3Service.init();
          console.log('Web3已初始化，准备操作合约');
        } catch (error) {
          console.error('Web3初始化失败:', error);
          store.dispatch('notification/showError', '区块链连接失败，无法操作训练');
          loading.value = false;
          return;
        }
        
        if (!isParticipating.value) {
          // 检查训练是否活跃
          const contract = web3Service.getContract();
          const isTrainingActive = await contract.methods.isTrainingActive().call();
          
          if (!isTrainingActive) {
            store.dispatch('notification/showError', '没有活跃的训练可以加入');
            return;
          }
          
          // 加入训练
          await contract.methods.joinTraining().send({
            from: store.state.user.address,
            gas: 500000
          });
          
          // 调用API加入训练
          await apiService.joinTraining(participationConfig.value);
          
          isParticipating.value = true;
          startUpdateLoop();
          store.dispatch('notification/showSuccess', '已成功加入训练');
        } else {
          // 退出训练
          const contract = web3Service.getContract();
          await contract.methods.leaveTraining().send({
            from: store.state.user.address,
            gas: 500000
          });
          
          // 调用API退出训练
          await apiService.leaveTraining();
          
          isParticipating.value = false;
          stopUpdateLoop();
          store.dispatch('notification/showSuccess', '已成功退出训练');
        }
      } catch (error) {
        console.error('参与操作失败:', error);
        let errorMessage = error.message || '参与操作失败';
        
        // 处理MetaMask错误
        if (error.code) {
          if (error.code === 4001) {
            errorMessage = '用户拒绝了交易';
          }
        }
        
        store.dispatch('notification/showError', errorMessage);
      } finally {
        loading.value = false;
      }
    };

    // 刷新数据集信息
    const refreshDatasets = async () => {
      try {
        loading.value = true;
        
        // 获取可用数据集信息
        const datasets = await apiService.getDatasets();
        
        // 计算总数据量
        const totalSize = datasets.reduce((total, dataset) => total + dataset.size, 0);
        
        participationConfig.value.datasetSize = totalSize;
        
        store.dispatch('notification/showSuccess', '数据集信息已更新');
      } catch (error) {
        console.error('刷新数据集信息失败:', error);
        store.dispatch('notification/showError', error.message || '刷新数据集信息失败');
      } finally {
        loading.value = false;
      }
    };

    // 更新本地状态
    const updateLocalStatus = async () => {
      try {
        // 获取本地训练状态
        const status = await apiService.getLocalTrainingStatus();
        
        // 更新状态
        localMetrics.value = status.metrics || { loss: 0, accuracy: 0, epochs: 0, trainingTime: 0 };
        contributionStats.value = status.contribution || { roundsParticipated: 0, dataContributed: 0, avgAccuracy: 0, totalReward: 0 };
        resourceUsage.value = status.resources || { cpu: 0, memory: 0, network: 0, storage: 0 };
        
        // 更新历史记录
        if (status.history) {
          localTrainingHistory.value = status.history;
        }
        
        // 检查参与状态
        isParticipating.value = status.isParticipating || false;
        
        // 如果不再参与，停止更新
        if (!isParticipating.value) {
          stopUpdateLoop();
        }
      } catch (error) {
        console.error('获取本地状态失败:', error);
      }
    };

    // 开始定期更新
    const startUpdateLoop = () => {
      updateInterval.value = setInterval(updateLocalStatus, 5000);
    };

    // 停止定期更新
    const stopUpdateLoop = () => {
      if (updateInterval.value) {
        clearInterval(updateInterval.value);
        updateInterval.value = null;
      }
    };
    
    // 格式化日期
    const formatDate = (date) => {
      if (!date) return '';
      const d = new Date(date);
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
    };
    
    // 格式化时间
    const formatTime = (seconds) => {
      if (!seconds) return '0秒';
      
      if (seconds < 60) {
        return Math.floor(seconds) + '秒';
      } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}分${remainingSeconds}秒`;
      } else {
        const hours = Math.floor(seconds / 3600);
        const remainingMinutes = Math.floor((seconds % 3600) / 60);
        return `${hours}小时${remainingMinutes}分`;
      }
    };
    
    // 格式化数据量
    const formatData = (value) => {
      if (!value) return '0';
      
      if (value < 1000) {
        return value.toString();
      } else if (value < 1000000) {
        return (value / 1000).toFixed(1) + 'K';
      } else {
        return (value / 1000000).toFixed(1) + 'M';
      }
    };
    
    // 格式化网络速度
    const formatNetworkSpeed = (value) => {
      if (!value) return '0 KB/s';
      
      if (value < 1024) {
        return Math.round(value) + ' KB/s';
      } else if (value < 1024 * 1024) {
        return (value / 1024).toFixed(1) + ' MB/s';
      } else {
        return (value / (1024 * 1024)).toFixed(1) + ' GB/s';
      }
    };
    
    // 获取资源使用颜色
    const getResourceColor = (value) => {
      if (value < 0.5) return 'var(--success-color)';
      if (value < 0.8) return 'var(--warning-color)';
      return 'var(--error-color)';
    };
    
    // 浏览可用任务
    const browseAvailableTasks = () => {
      router.push('/client/available-tasks');
    };

    // 刷新数据
    const refreshData = async () => {
      try {
        loading.value = true;
        await updateLocalStatus();
        store.dispatch('notification/showSuccess', '数据已刷新');
      } catch (error) {
        console.error('刷新数据失败:', error);
        store.dispatch('notification/showError', '刷新数据失败: ' + error.message);
      } finally {
        loading.value = false;
      }
    };

    onMounted(async () => {
      try {
        loading.value = true;
        
        // 尝试初始化Web3
        try {
          await web3Service.init();
          console.log('Web3初始化成功');
        } catch (error) {
          console.warn('Web3初始化失败，部分功能可能不可用:', error);
          store.dispatch('notification/showWarning', '区块链连接失败，部分功能可能不可用');
        }
        
        await updateLocalStatus();
        if (isParticipating.value) {
          startUpdateLoop();
        }
      } finally {
        loading.value = false;
      }
    });

    onUnmounted(() => {
      stopUpdateLoop();
    });

    return {
      isParticipating,
      loading,
      participationConfig,
      localTrainingHistory,
      localMetrics,
      contributionStats,
      resourceUsage,
      toggleParticipation,
      refreshDatasets,
      formatDate,
      formatTime,
      formatData,
      formatNetworkSpeed,
      getResourceColor,
      browseAvailableTasks,
      refreshData
    };
  }
};
</script>

<style scoped>
.client-home {
  width: 100%;
  padding: var(--spacing-md);
  margin: 0;
  box-sizing: border-box;
  overflow-x: hidden;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  width: 100%;
  box-sizing: border-box;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: var(--text-primary);
  font-weight: 600;
}

.page-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.panel {
  width: 100%;
  margin-bottom: var(--spacing-md);
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
  width: 100%;
  margin-bottom: var(--spacing-md);
}

.span-2 {
  grid-column: span 2;
}

/* 表单布局 */
.config-form {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  width: 100%;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.form-group label {
  font-weight: 500;
  color: var(--text-secondary);
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 16px;
  transition: all var(--transition-fast);
}

.form-control:hover {
  border-color: var(--primary-color);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.form-control:disabled {
  background-color: var(--background-color);
  cursor: not-allowed;
}

/* 仪表盘组件 */
.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: var(--spacing-md);
}

.chart-placeholder {
  font-size: 16px;
  color: var(--text-hint);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: var(--text-hint);
}

.empty-state i {
  font-size: 48px;
  margin-bottom: var(--spacing-md);
}

/* 指标项 */
.metric-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .config-form {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .span-2 {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .client-home {
    padding: var(--spacing-sm);
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
  
  .config-form {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style> 