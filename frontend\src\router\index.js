import { createRouter, createWebHistory } from 'vue-router';
import Login from '@/views/Login.vue';
import Register from '@/views/Register.vue';
import ServerDashboard from '../views/ServerDashboard.vue';
import ClientDashboard from '../views/ClientDashboard.vue';
import web3Service from '../services/web3';
import store from '../store';

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue'),
    meta: { requiresAuth: true },
    beforeEnter: (to, from, next) => {
      const userRole = store.state.user.role;
      if (userRole) {
        // 根据用户角色重定向到对应页面
        next(`/${userRole}`);
      } else {
        // 如果没有角色信息，重定向到登录页
        next('/login');
      }
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresGuest: true, title: '登录' }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: { requiresGuest: true, title: '注册' }
  },
  {
    path: '/server',
    name: 'server',
    component: ServerDashboard,
    meta: { requiresAuth: true, role: 'server', title: '服务端控制台' },
    children: [
      {
        path: '',
        name: 'ServerHome',
        component: () => import('../views/server/ServerHome.vue'),
        meta: { requiresAuth: true, role: 'server', title: '控制台首页' }
      },
      {
        path: 'tasks',
        name: 'TaskManagement',
        component: () => import('../views/server/TaskManagement.vue'),
        meta: { requiresAuth: true, role: 'server', title: '训练任务管理' }
      },
      {
        path: 'tasks/create',
        name: 'CreateTask',
        component: () => import('../views/server/CreateTask.vue'),
        meta: { requiresAuth: true, role: 'server', title: '创建训练任务' }
      },
      {
        path: 'tasks/:id',
        name: 'TaskDetail',
        component: () => import('../views/server/TaskDetail.vue'),
        meta: { requiresAuth: true, role: 'server', title: '任务详情' }
      },
      {
        path: 'tasks/:id/applications',
        name: 'TaskApplications',
        component: () => import('../views/server/TaskApplications.vue'),
        meta: { requiresAuth: true, role: 'server', title: '任务申请审核' }
      },
      {
        path: 'tasks/:id/results',
        name: 'TaskResults',
        component: () => import('../views/server/TaskResults.vue'),
        meta: { requiresAuth: true, role: 'server', title: '任务结果' }
      },
      {
        path: 'client-management',
        name: 'ClientManagement',
        component: () => import('../views/server/ClientManagement.vue'),
        meta: { requiresAuth: true, role: 'server', title: '客户端管理' }
      },
      {
        path: 'training',
        name: 'TrainingMonitor',
        component: () => import('../views/server/TrainingMonitor.vue'),
        meta: { requiresAuth: true, role: 'server', title: '训练监控' }
      },
      {
        path: 'transactions',
        name: 'ServerTransactions',
        component: () => import('../views/server/ServerTransactions.vue'),
        meta: { requiresAuth: true, role: 'server', title: '交易记录' }
      },
      {
        path: 'models',
        name: 'ModelManagement',
        component: () => import('../views/server/ModelManagement.vue'),
        meta: { requiresAuth: true, role: 'server', title: '模型管理' }
      }
    ]
  },
  {
    path: '/client',
    name: 'client',
    component: ClientDashboard,
    meta: { requiresAuth: true, role: 'client', title: '客户端主页' },
    children: [
      {
        path: '',
        name: 'ClientHome',
        component: () => import('../views/client/ClientHome.vue'),
        meta: { requiresAuth: true, role: 'client', title: '客户端首页' }
      },
      {
        path: 'available-tasks',
        name: 'AvailableTasks',
        component: () => import('../views/client/AvailableTasks.vue'),
        meta: { requiresAuth: true, role: 'client', title: '可用任务' }
      },
      {
        path: 'my-applications',
        name: 'MyApplications',
        component: () => import('../views/client/MyApplications.vue'),
        meta: { requiresAuth: true, role: 'client', title: '我的申请' }
      },
      {
        path: 'my-tasks',
        name: 'MyTasks',
        component: () => import('../views/client/MyTasks.vue'),
        meta: { requiresAuth: true, role: 'client', title: '我的任务' }
      },
      {
        path: 'training',
        name: 'ClientTraining',
        component: () => import('../views/client/Training.vue'),
        meta: { requiresAuth: true, role: 'client', title: '训练监控' }
      },
      {
        path: 'data-management',
        name: 'DataManagement',
        component: () => import('../views/client/DataManagement.vue'),
        meta: { requiresAuth: true, role: 'client', title: '数据管理' }
      },
      {
        path: 'transactions',
        name: 'ClientTransactions',
        component: () => import('../views/client/ClientTransactions.vue'),
        meta: { requiresAuth: true, role: 'client', title: '交易记录' }
      },
      {
        path: 'contribution',
        name: 'MyContribution',
        component: () => import('../views/client/MyContribution.vue'),
        meta: { requiresAuth: true, role: 'client', title: '我的贡献' }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue'),
    meta: { title: '页面未找到' }
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 简化导航守卫逻辑
router.beforeEach(async (to, from, next) => {
  console.log('路由守卫触发:', to.path);
  
  // 检查Vuex状态和localStorage，确保两者一致
  const userInStore = store.state.user.address !== null;
  const userInStorage = localStorage.getItem('user') !== null;
  
  // 如果localStorage中没有用户信息，但Vuex中有，则清除Vuex中的用户信息
  if (!userInStorage && userInStore) {
    console.log('检测到状态不一致：localStorage中无用户但Vuex中有，正在清除Vuex状态');
    store.commit('user/CLEAR_USER');
  }
  
  // 重新获取认证状态
  const isAuthenticated = store.state.user.address !== null;
  console.log('当前认证状态:', isAuthenticated);
  
  const userRole = store.state.user.role;

  // 如果是访问登录或注册页面，但已经登录，则重定向到对应角色页面
  if (to.meta.requiresGuest && isAuthenticated) {
    console.log('已登录用户访问游客页面，重定向到:', `/${userRole}`);
    next(`/${userRole}`);
    return;
  }

  // 如果是访问需要认证的页面，但未登录，则重定向到登录页面
  if (to.meta.requiresAuth && !isAuthenticated) {
    console.log('未登录用户访问认证页面，重定向到登录页面');
    next('/login');
    return;
  }

  // 如果是访问需要特定角色的页面，但角色不匹配，则重定向到对应角色页面
  if (to.meta.requiresAuth && to.meta.role && to.meta.role !== userRole) {
    console.log('角色不匹配，重定向到:', `/${userRole}`);
    next(`/${userRole}`);
    return;
  }

  // 其他情况正常导航
  console.log('正常导航到:', to.path);
  next();
});

export default router;
