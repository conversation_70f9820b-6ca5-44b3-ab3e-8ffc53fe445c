<template>
  <div class="metrics-container">
    <h3>模型指标</h3>
    <div class="metrics-grid" v-if="!loading">
      <div class="metric-card">
        <div class="metric-icon">
          <i class="fas fa-chart-line"></i>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ formatNumber(metrics.loss) }}</div>
          <div class="metric-label">损失值</div>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-icon">
          <i class="fas fa-bullseye"></i>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ formatPercentage(metrics.accuracy) }}</div>
          <div class="metric-label">准确率</div>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-icon">
          <i class="fas fa-users"></i>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ metrics.participants }}</div>
          <div class="metric-label">参与节点</div>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-icon">
          <i class="fas fa-clock"></i>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ formatTime(metrics.trainingTime) }}</div>
          <div class="metric-label">训练时间</div>
        </div>
      </div>
    </div>
    <div v-else class="loading-container">
      <div class="loading-spinner"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModelMetrics',
  props: {
    metrics: {
      type: Object,
      required: true,
      default: () => ({
        loss: 0,
        accuracy: 0,
        participants: 0,
        trainingTime: 0
      })
    },
    loading: {
      type: Boolean,
      default: false
    }
  },

  setup() {
    const formatNumber = (value) => {
      return typeof value === 'number' ? value.toFixed(4) : '0';
    };

    const formatPercentage = (value) => {
      return typeof value === 'number' ? `${(value * 100).toFixed(2)}%` : '0%';
    };

    const formatTime = (seconds) => {
      if (!seconds) return '0:00';
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    };

    return {
      formatNumber,
      formatPercentage,
      formatTime
    };
  }
};
</script>

<style scoped>
.metrics-container {
  height: 100%;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.metric-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: transform 0.2s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
}

.metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #e3f2fd;
  color: #1976D2;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2em;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 1.2em;
  font-weight: 600;
  color: #2196F3;
}

.metric-label {
  font-size: 0.9em;
  color: #666;
  margin-top: 4px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 