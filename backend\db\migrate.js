/**
 * 数据库迁移管理脚本
 * 用于管理数据库结构的变更
 */
const fs = require('fs');
const path = require('path');
const { query, ensureDatabase } = require('./config');
const env = require('../config/env');

// 迁移文件目录
const MIGRATIONS_DIR = path.join(__dirname, 'migrations');
const MIGRATIONS_TABLE = 'migrations';

/**
 * 确保迁移表存在
 */
async function ensureMigrationsTable() {
  try {
    await query(`
      CREATE TABLE IF NOT EXISTS ${MIGRATIONS_TABLE} (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        batch INT NOT NULL,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_migration_name (name)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    `);
    console.log('迁移表已创建或已存在');
    return true;
  } catch (error) {
    console.error('创建迁移表失败:', error);
    return false;
  }
}

/**
 * 获取已执行的迁移
 */
async function getExecutedMigrations() {
  try {
    return await query(`SELECT * FROM ${MIGRATIONS_TABLE} ORDER BY id`);
  } catch (error) {
    console.error('获取已执行迁移失败:', error);
    return [];
  }
}

/**
 * 获取所有迁移文件
 */
async function getMigrationFiles() {
  // 确保迁移目录存在
  if (!fs.existsSync(MIGRATIONS_DIR)) {
    fs.mkdirSync(MIGRATIONS_DIR, { recursive: true });
  }

  // 读取所有迁移文件
  const files = fs.readdirSync(MIGRATIONS_DIR)
    .filter(file => file.endsWith('.js'))
    .sort(); // 按文件名排序

  return files;
}

/**
 * 获取待执行的迁移
 */
async function getPendingMigrations() {
  const executedMigrations = await getExecutedMigrations();
  const executedNames = executedMigrations.map(m => m.name);
  
  const allFiles = await getMigrationFiles();
  
  return allFiles.filter(file => !executedNames.includes(file));
}

/**
 * 执行迁移
 */
async function runMigrations() {
  try {
    // 确保数据库和迁移表存在
    await ensureDatabase();
    await ensureMigrationsTable();

    // 获取待执行的迁移
    const pendingMigrations = await getPendingMigrations();
    
    if (pendingMigrations.length === 0) {
      console.log('没有待执行的迁移');
      return true;
    }

    console.log(`发现 ${pendingMigrations.length} 个待执行的迁移`);

    // 获取当前批次号
    const batches = await query(`SELECT MAX(batch) as max_batch FROM ${MIGRATIONS_TABLE}`);
    const currentBatch = (batches[0].max_batch || 0) + 1;

    // 执行每个迁移
    for (const migrationFile of pendingMigrations) {
      const migrationPath = path.join(MIGRATIONS_DIR, migrationFile);
      const migration = require(migrationPath);

      console.log(`执行迁移: ${migrationFile}`);
      
      try {
        // 执行迁移的up方法
        await migration.up(query);
        
        // 记录迁移已执行
        await query(
          `INSERT INTO ${MIGRATIONS_TABLE} (name, batch) VALUES (?, ?)`,
          [migrationFile, currentBatch]
        );
        
        console.log(`迁移 ${migrationFile} 执行成功`);
      } catch (error) {
        console.error(`迁移 ${migrationFile} 执行失败:`, error);
        throw error; // 中断后续迁移
      }
    }

    console.log('所有迁移执行完成');
    return true;
  } catch (error) {
    console.error('执行迁移失败:', error);
    return false;
  }
}

/**
 * 回滚迁移
 * @param {number} steps 回滚步数，默认为1批次
 */
async function rollbackMigrations(steps = 1) {
  try {
    // 确保迁移表存在
    await ensureMigrationsTable();

    // 获取最近的n个批次
    const batches = await query(
      `SELECT DISTINCT batch FROM ${MIGRATIONS_TABLE} ORDER BY batch DESC LIMIT ?`,
      [steps]
    );

    if (batches.length === 0) {
      console.log('没有可回滚的迁移');
      return true;
    }

    const batchesToRollback = batches.map(b => b.batch);
    console.log(`准备回滚批次: ${batchesToRollback.join(', ')}`);

    // 获取需要回滚的迁移，按ID倒序回滚
    const migrationsToRollback = await query(
      `SELECT * FROM ${MIGRATIONS_TABLE} WHERE batch IN (?) ORDER BY id DESC`,
      [batchesToRollback]
    );

    if (migrationsToRollback.length === 0) {
      console.log('没有可回滚的迁移');
      return true;
    }

    console.log(`将回滚 ${migrationsToRollback.length} 个迁移`);

    // 执行每个回滚
    for (const migration of migrationsToRollback) {
      const migrationPath = path.join(MIGRATIONS_DIR, migration.name);
      
      try {
        const migrationModule = require(migrationPath);
        
        console.log(`回滚迁移: ${migration.name}`);
        
        // 执行迁移的down方法
        await migrationModule.down(query);
        
        // 从迁移表中删除记录
        await query(`DELETE FROM ${MIGRATIONS_TABLE} WHERE id = ?`, [migration.id]);
        
        console.log(`迁移 ${migration.name} 回滚成功`);
      } catch (error) {
        console.error(`迁移 ${migration.name} 回滚失败:`, error);
        throw error; // 中断后续回滚
      }
    }

    console.log('迁移回滚完成');
    return true;
  } catch (error) {
    console.error('回滚迁移失败:', error);
    return false;
  }
}

/**
 * 创建新的迁移文件
 * @param {string} name 迁移名称
 */
async function createMigration(name) {
  if (!name) {
    console.error('请提供迁移名称');
    return false;
  }

  // 确保迁移目录存在
  if (!fs.existsSync(MIGRATIONS_DIR)) {
    fs.mkdirSync(MIGRATIONS_DIR, { recursive: true });
  }

  // 生成时间戳
  const timestamp = new Date().toISOString().replace(/[-:]/g, '').split('.')[0];
  const fileName = `${timestamp}_${name}.js`;
  const filePath = path.join(MIGRATIONS_DIR, fileName);

  // 迁移文件模板
  const template = `/**
 * 迁移: ${name}
 * 创建于: ${new Date().toISOString()}
 */

/**
 * 向上迁移 - 应用更改
 * @param {Function} query 数据库查询函数
 */
exports.up = async function(query) {
  // 实现向上迁移逻辑
  // 例如: await query('CREATE TABLE ...');
};

/**
 * 向下迁移 - 撤销更改
 * @param {Function} query 数据库查询函数
 */
exports.down = async function(query) {
  // 实现向下迁移逻辑
  // 例如: await query('DROP TABLE ...');
};
`;

  // 写入文件
  fs.writeFileSync(filePath, template);
  console.log(`已创建迁移文件: ${fileName}`);
  return true;
}

/**
 * 显示迁移状态
 */
async function getMigrationStatus() {
  try {
    // 确保迁移表存在
    await ensureMigrationsTable();

    // 获取所有迁移文件
    const allFiles = await getMigrationFiles();
    
    // 获取已执行的迁移
    const executedMigrations = await getExecutedMigrations();
    const executedNames = executedMigrations.map(m => m.name);

    // 构建状态报告
    const status = allFiles.map(file => ({
      file,
      executed: executedNames.includes(file),
      batch: executedNames.includes(file) ? 
        executedMigrations.find(m => m.name === file).batch : null,
      executedAt: executedNames.includes(file) ? 
        executedMigrations.find(m => m.name === file).executed_at : null
    }));

    return status;
  } catch (error) {
    console.error('获取迁移状态失败:', error);
    return [];
  }
}

/**
 * 显示迁移帮助信息
 */
function showHelp() {
  console.log(`
数据库迁移工具使用方法:

  node migrate.js <命令> [参数]

可用命令:
  run           执行所有待执行的迁移
  rollback [n]  回滚最近的n批迁移（默认为1）
  create <name> 创建新的迁移文件
  status        显示迁移状态
  help          显示此帮助信息

示例:
  node migrate.js run
  node migrate.js rollback 2
  node migrate.js create add_users_table
  node migrate.js status
  `);
}

/**
 * 命令行入口
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'help';

  switch (command) {
    case 'run':
      await runMigrations();
      break;
    case 'rollback':
      const steps = parseInt(args[1]) || 1;
      await rollbackMigrations(steps);
      break;
    case 'create':
      const name = args[1];
      await createMigration(name);
      break;
    case 'status':
      const status = await getMigrationStatus();
      console.log('迁移状态:');
      console.table(status);
      break;
    case 'help':
    default:
      showHelp();
      break;
  }

  // 完成后退出
  process.exit(0);
}

// 如果直接运行此文件，执行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('迁移脚本执行失败:', error);
    process.exit(1);
  });
}

// 导出功能函数
module.exports = {
  runMigrations,
  rollbackMigrations,
  createMigration,
  getMigrationStatus
}; 