/**
 * 数据库初始化脚本
 * 创建所有需要的表结构
 */
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });
const { pool, query, ensureDatabase } = require('./config');
const env = require('../config/env');


// 添加调试日志
console.log('数据库初始化脚本启动');
console.log('数据库配置:', {
  host: env.DB.HOST,
  port: env.DB.PORT,
  user: env.DB.USER,
  password: '123456',
  database: env.DB.DATABASE,
  connectionLimit: env.DB.CONNECTION_LIMIT
});

/**
 * 创建任务表
 */
async function createTasksTable() {
  try {
    console.log('开始创建任务表...');
    await query(`
      CREATE TABLE IF NOT EXISTS tasks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        status ENUM('pending', 'in_progress', 'completed', 'stopped', 'failed') NOT NULL DEFAULT 'pending',
        data_type VARCHAR(50) NOT NULL,
        current_round INT DEFAULT 0,
        total_rounds INT NOT NULL,
        required_participants INT NOT NULL,
        learning_rate FLOAT NOT NULL,
        batch_size INT NOT NULL,
        timeout INT NOT NULL COMMENT '超时时间（秒）',
        blockchain_tx_hash VARCHAR(255),
        start_time DATETIME,
        end_time DATETIME,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    `);
    console.log('✅ 任务表创建成功');
    return true;
  } catch (error) {
    console.error('❌ 创建任务表失败:', error);
    return false;
  }
}

/**
 * 创建参与者表
 */
async function createParticipantsTable() {
  try {
    await query(`
      CREATE TABLE IF NOT EXISTS participants (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id INT NOT NULL,
        client_address VARCHAR(255) NOT NULL,
        status ENUM('registered', 'training', 'completed', 'failed', 'disconnected') NOT NULL DEFAULT 'registered',
        data_size INT NOT NULL COMMENT '数据量大小',
        compute_resource VARCHAR(100) COMMENT '计算资源描述',
        contribution FLOAT DEFAULT 0 COMMENT '贡献度',
        current_round_status ENUM('pending', 'training', 'submitted', 'verified', 'rejected') DEFAULT 'pending',
        last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
        UNIQUE KEY unique_task_client (task_id, client_address),
        INDEX idx_task_id (task_id),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    `);
    console.log('参与者表创建成功');
    return true;
  } catch (error) {
    console.error('创建参与者表失败:', error);
    return false;
  }
}

/**
 * 创建模型表
 */
async function createModelsTable() {
  try {
    await query(`
      CREATE TABLE IF NOT EXISTS models (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(50) NOT NULL,
        version VARCHAR(50) NOT NULL,
        description TEXT,
        task_id INT,
        task_name VARCHAR(255),
        status ENUM('draft', 'published', 'archived') NOT NULL DEFAULT 'draft',
        size INT COMMENT '模型大小（KB）',
        performance JSON COMMENT '性能指标',
        downloads INT DEFAULT 0,
        is_latest BOOLEAN DEFAULT FALSE,
        from_training BOOLEAN DEFAULT FALSE COMMENT '是否从训练任务创建',
        blockchain_tx_hash VARCHAR(255),
        file_path VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE SET NULL,
        INDEX idx_status (status),
        INDEX idx_task_id (task_id),
        INDEX idx_name_version (name, version)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    `);
    console.log('模型表创建成功');
    return true;
  } catch (error) {
    console.error('创建模型表失败:', error);
    return false;
  }
}

/**
 * 创建指标表
 */
async function createMetricsTable() {
  try {
    await query(`
      CREATE TABLE IF NOT EXISTS metrics (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id INT NOT NULL,
        round INT NOT NULL,
        loss FLOAT,
        accuracy FLOAT,
        participants_count INT DEFAULT 0,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
        UNIQUE KEY unique_task_round (task_id, round),
        INDEX idx_task_id (task_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    `);
    console.log('指标表创建成功');
    return true;
  } catch (error) {
    console.error('创建指标表失败:', error);
    return false;
  }
}

/**
 * 创建参与者指标表
 */
async function createParticipantMetricsTable() {
  try {
    await query(`
      CREATE TABLE IF NOT EXISTS participant_metrics (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id INT NOT NULL,
        participant_id INT NOT NULL,
        round INT NOT NULL,
        loss FLOAT,
        accuracy FLOAT,
        training_time INT COMMENT '训练时间（秒）',
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
        FOREIGN KEY (participant_id) REFERENCES participants(id) ON DELETE CASCADE,
        UNIQUE KEY unique_participant_round (participant_id, round),
        INDEX idx_task_id (task_id),
        INDEX idx_participant_id (participant_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    `);
    console.log('参与者指标表创建成功');
    return true;
  } catch (error) {
    console.error('创建参与者指标表失败:', error);
    return false;
  }
}

/**
 * 创建日志表
 */
async function createLogsTable() {
  try {
    await query(`
      CREATE TABLE IF NOT EXISTS logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id INT,
        type VARCHAR(50) NOT NULL,
        message TEXT NOT NULL,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
        INDEX idx_task_id (task_id),
        INDEX idx_type (type),
        INDEX idx_timestamp (timestamp)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    `);
    console.log('日志表创建成功');
    return true;
  } catch (error) {
    console.error('创建日志表失败:', error);
    return false;
  }
}

/**
 * 初始化数据库
 * 创建所有必要的表
 */
async function initializeDatabase() {
  try {
    console.log('初始化数据库开始...');
    
    // 确保数据库存在
    const dbCreated = await ensureDatabase();
    if (!dbCreated) {
      console.error('❌ 确保数据库存在失败，请检查数据库连接配置');
      return false;
    }
    console.log('✅ 数据库已就绪');
    
    // 测试连接池连接
    try {
      const testConn = await pool.getConnection();
      console.log('✅ 连接池连接测试成功');
      testConn.release();
    } catch (poolErr) {
      console.error('❌ 连接池连接测试失败:', poolErr);
      // 我们继续尝试创建表，可能会失败
    }
    
    // 创建表结构
    let result = true;
    
    console.log('开始创建数据表...');
    try {
      result = await createTasksTable() && result;
      result = await createParticipantsTable() && result;
      result = await createModelsTable() && result;
      result = await createMetricsTable() && result;
      result = await createParticipantMetricsTable() && result;
      result = await createLogsTable() && result;
    } catch (tableErr) {
      console.error('❌ 创建表时发生错误:', tableErr);
      return false;
    }

    if (result) {
      console.log('✅ 所有数据表创建成功');
      console.log('✅ 数据库初始化完成');
    } else {
      console.error('⚠️ 部分数据表创建失败');
    }
    
    return result;
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    return false;
  }
}

// 如果直接运行此文件，则初始化数据库
if (require.main === module) {
  console.log('正在执行数据库初始化脚本...');
  initializeDatabase()
    .then((result) => {
      if (result) {
        console.log('✅ 数据库初始化脚本执行完成');
        process.exit(0);
      } else {
        console.error('❌ 数据库初始化部分失败');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('❌ 数据库初始化脚本执行失败:', error);
      process.exit(1);
    });
}

// 导出
module.exports = {
  query,
  initializeDatabase
}; 