<template>
  <div class="notifications-container">
    <transition-group name="notification">
      <div 
        v-for="notification in notifications" 
        :key="notification.id" 
        class="notification"
        :class="[`notification-${notification.type}`]"
      >
        <div class="notification-icon">
          <i :class="getIconClass(notification.type)"></i>
        </div>
        <div class="notification-content">
          <div class="notification-title" v-if="notification.title">{{ notification.title }}</div>
          <div class="notification-message">{{ typeof notification.message === 'string' ? notification.message : '系统通知' }}</div>
        </div>
        <button class="notification-close" @click="removeNotification(notification.id)">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </transition-group>
  </div>
</template>

<script>
import { computed } from 'vue';
import { useStore } from 'vuex';

export default {
  name: 'Notifications',
  setup() {
    const store = useStore();
    
    const notifications = computed(() => store.getters['notification/getNotifications']);
    
    const getIconClass = (type) => {
      const iconMap = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-exclamation-circle',
        'warning': 'fas fa-exclamation-triangle',
        'info': 'fas fa-info-circle'
      };
      
      return iconMap[type] || 'fas fa-bell';
    };
    
    const removeNotification = (id) => {
      store.dispatch('notification/removeNotification', id);
    };
    
    return {
      notifications,
      getIconClass,
      removeNotification
    };
  }
};
</script>

<style scoped>
.notifications-container {
  position: fixed;
  top: 24px;
  right: 24px;
  z-index: 9999;
  width: 360px;
  max-width: calc(100vw - 48px);
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
}

.notification {
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  padding: 16px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  animation: slide-in 0.3s ease;
  overflow: hidden;
  position: relative;
}

.notification::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
}

.notification-success {
  border-left: 4px solid var(--success-color);
}

.notification-error {
  border-left: 4px solid var(--error-color);
}

.notification-warning {
  border-left: 4px solid var(--warning-color);
}

.notification-info {
  border-left: 4px solid var(--info-color);
}

.notification-icon {
  font-size: 20px;
  margin-top: 2px;
}

.notification-success .notification-icon {
  color: var(--success-color);
}

.notification-error .notification-icon {
  color: var(--error-color);
}

.notification-warning .notification-icon {
  color: var(--warning-color);
}

.notification-info .notification-icon {
  color: var(--info-color);
}

.notification-content {
  flex: 1;
  max-width: 280px;
  overflow: hidden;
}

.notification-title {
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--text-primary);
}

.notification-message {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.notification-close {
  background: transparent;
  border: none;
  color: var(--text-hint);
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.notification-close:hover {
  color: var(--text-primary);
}

.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.notification-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

@keyframes slide-in {
  from {
    transform: translateX(30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .notifications-container {
    width: calc(100% - 48px);
    max-width: 100%;
  }
}
</style> 