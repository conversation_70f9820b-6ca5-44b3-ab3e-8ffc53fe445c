{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "ant-design-vue": "^4.2.6", "axios": "^1.7.9", "chart.js": "^4.4.7", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "echarts": "^5.6.0", "ecpair": "^3.0.0-rc.0", "element-plus": "^2.9.6", "js-sha3": "^0.9.3", "tiny-secp256k1": "^2.2.3", "vite-plugin-node-polyfills": "^0.23.0", "vite-plugin-wasm": "^3.4.1", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-router": "^4.5.0", "vuex": "^4.1.0", "web3": "^1.10.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "vite": "^6.0.5"}}