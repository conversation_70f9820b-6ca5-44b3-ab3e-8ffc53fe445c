
> api
> node backend/app.js

数据库初始化脚本启动
数据库配置: {
  host: 'localhost',
  port: '3306',
  user: 'root',
  password: '123456',
  database: 'federated_learning',
  connectionLimit: 10
}
初始化数据库开始...
尝试创建MySQL连接: { host: 'localhost', port: '3306', user: 'root' }
MySQL临时连接成功
尝试创建数据库: federated_learning
确保数据库 federated_learning 存在
✅ 数据库已就绪
✅ 连接池连接测试成功
开始创建数据表...
开始创建任务表...
SQL查询参数(数组): []
执行SQL查询: 
      CREATE TABLE IF NOT EXISTS tasks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        status ENUM('pending', 'in_progress', 'completed', 'stopped', 'failed') NOT NULL DEFAULT 'pending',
        data_type VARCHAR(50) NOT NULL,
        current_round INT DEFAULT 0,
        total_rounds INT NOT NULL,
        required_participants INT NOT NULL,
        learning_rate FLOAT NOT NULL,
        batch_size INT NOT NULL,
        timeout INT NOT NULL COMMENT '超时时间（秒）',
        blockchain_tx_hash VARCHAR(255),
        start_time DATETIME,
        end_time DATETIME,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    
✅ 任务表创建成功
SQL查询参数(数组): []
执行SQL查询: 
      CREATE TABLE IF NOT EXISTS participants (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id INT NOT NULL,
        client_address VARCHAR(255) NOT NULL,
        status ENUM('registered', 'training', 'completed', 'failed', 'disconnected') NOT NULL DEFAULT 'registered',
        data_size INT NOT NULL COMMENT '数据量大小',
        compute_resource VARCHAR(100) COMMENT '计算资源描述',
        contribution FLOAT DEFAULT 0 COMMENT '贡献度',
        current_round_status ENUM('pending', 'training', 'submitted', 'verified', 'rejected') DEFAULT 'pending',
        last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
        UNIQUE KEY unique_task_client (task_id, client_address),
        INDEX idx_task_id (task_id),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    
参与者表创建成功
SQL查询参数(数组): []
执行SQL查询: 
      CREATE TABLE IF NOT EXISTS models (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        type VARCHAR(50) NOT NULL,
        version VARCHAR(50) NOT NULL,
        description TEXT,
        task_id INT,
        task_name VARCHAR(255),
        status ENUM('draft', 'published', 'archived') NOT NULL DEFAULT 'draft',
        size INT COMMENT '模型大小（KB）',
        performance JSON COMMENT '性能指标',
        downloads INT DEFAULT 0,
        is_latest BOOLEAN DEFAULT FALSE,
        from_training BOOLEAN DEFAULT FALSE COMMENT '是否从训练任务创建',
        blockchain_tx_hash VARCHAR(255),
        file_path VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE SET NULL,
        INDEX idx_status (status),
        INDEX idx_task_id (task_id),
        INDEX idx_name_version (name, version)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    
模型表创建成功
SQL查询参数(数组): []
执行SQL查询: 
      CREATE TABLE IF NOT EXISTS metrics (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id INT NOT NULL,
        round INT NOT NULL,
        loss FLOAT,
        accuracy FLOAT,
        participants_count INT DEFAULT 0,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
        UNIQUE KEY unique_task_round (task_id, round),
        INDEX idx_task_id (task_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    
指标表创建成功
SQL查询参数(数组): []
执行SQL查询: 
      CREATE TABLE IF NOT EXISTS participant_metrics (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id INT NOT NULL,
        participant_id INT NOT NULL,
        round INT NOT NULL,
        loss FLOAT,
        accuracy FLOAT,
        training_time INT COMMENT '训练时间（秒）',
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
        FOREIGN KEY (participant_id) REFERENCES participants(id) ON DELETE CASCADE,
        UNIQUE KEY unique_participant_round (participant_id, round),
        INDEX idx_task_id (task_id),
        INDEX idx_participant_id (participant_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    
参与者指标表创建成功
SQL查询参数(数组): []
执行SQL查询: 
      CREATE TABLE IF NOT EXISTS logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id INT,
        type VARCHAR(50) NOT NULL,
        message TEXT NOT NULL,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
        INDEX idx_task_id (task_id),
        INDEX idx_type (type),
        INDEX idx_timestamp (timestamp)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    
日志表创建成功
✅ 所有数据表创建成功
✅ 数据库初始化完成
数据库初始化成功
注册的路由:
GET /api/tasks
GET /api/tasks/:id
POST /api/tasks
PUT /api/tasks/:id
DELETE /api/tasks/:id
POST /api/tasks/:id/start
POST /api/tasks/:id/complete
GET /api/tasks/:id/participants
GET /api/participants
GET /api/participants/:id
GET /api/tasks/:taskId/participants
PUT /api/participants/:id/status
POST /api/participants
DELETE /api/participants/:id
GET /api/applications/client/:clientAddress
GET /api/models
GET /api/models/:id
POST /api/models
PUT /api/models/:id/publish
PUT /api/models/:id
PUT /api/models/:id/downloads/increment
GET /api/models/:id/download
GET /api/tasks/:taskId/metrics
POST /api/metrics
GET /api/metrics
GET /api/metrics/task/:taskId
GET /api/metrics/task/:taskId/round/:round
GET /api/metrics/task/:taskId/latest
GET /api/tasks/:taskId/logs
GET /api/logs
POST /api/logs
POST /api/auth/login
POST /api/tasks/:id/complete-download
POST /api/tasks/:id/distribute-rewards
API服务器运行在 http://localhost:3001
SQL查询参数(数组): []
执行SQL查询: SELECT * FROM tasks ORDER BY created_at DESC
获取所有任务，返回数据: [{"id":112,"name":"图像识别模型","description":"训练识别数字图像等。。。","status":"in_progress","data_type":"image","current_round":5,"total_rounds":10,"required_participants":3,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-08-27T00:48:39.000Z","end_time":null,"created_at":"2025-08-27T00:44:43.000Z","updated_at":"2025-08-27T01:00:09.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"first_payment_completed","payment_method":"balance","wallet_type":null,"blockchain_second_payment_tx":null},{"id":111,"name":"的萨芬萨地方撒","description":" 法撒旦富士达发撒发  发撒发士大夫","status":"pending","data_type":"image","current_round":0,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":null,"end_time":null,"created_at":"2025-05-15T12:09:16.000Z","updated_at":"2025-05-15T12:09:16.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"CNY","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"first_payment_completed","payment_method":"wechat","wallet_type":null,"blockchain_second_payment_tx":null},{"id":110,"name":"发顺丰士大夫sa","description":"的幅度萨芬士大夫 的撒发撒","status":"completed","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-05-15T07:52:21.000Z","end_time":"2025-05-15T07:56:58.000Z","created_at":"2025-05-15T07:50:14.000Z","updated_at":"2025-05-15T12:09:52.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"second_payment_completed","payment_method":"wallet","wallet_type":null,"blockchain_second_payment_tx":"0x5d5d74f0b5899107917bc0bd6456665e98b162e1a61663d22a0ef3277534c087"},{"id":109,"name":"啊发生的萨芬","description":" 发士大夫的撒地方撒地方","status":"completed","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-05-15T07:30:25.000Z","end_time":"2025-05-15T07:34:57.000Z","created_at":"2025-05-15T07:28:30.000Z","updated_at":"2025-05-15T07:34:56.000Z","reward":"500.00","reward_distribution":"contribution","payment_currency":"CNY","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"first_payment_completed","payment_method":"wechat","wallet_type":null,"blockchain_second_payment_tx":null},{"id":108,"name":"的士大夫的撒fsd","description":" 飞洒地方萨的地方的撒法撒旦法撒旦ff","status":"completed","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-05-14T11:10:12.000Z","end_time":"2025-05-14T11:14:53.000Z","created_at":"2025-05-14T11:09:06.000Z","updated_at":"2025-05-14T11:16:37.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"second_payment_completed","payment_method":"wallet","wallet_type":null,"blockchain_second_payment_tx":"0x27e0392b472ac9a6bee26d9723558b74d3b912146e3e10b4a61dcf556d2e2992"},{"id":107,"name":"飞洒地方是f","description":"啊飞洒地方萨芬撒地方按时范德萨","status":"completed","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-05-14T11:01:54.000Z","end_time":"2025-05-14T11:07:27.000Z","created_at":"2025-05-14T10:59:27.000Z","updated_at":"2025-05-14T11:08:37.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"CNY","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"second_payment_completed","payment_method":"wechat","wallet_type":null,"blockchain_second_payment_tx":null},{"id":106,"name":"法大师傅十大fdsd","description":" 发撒富士达富士达发撒发撒","status":"completed","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-05-14T09:38:34.000Z","end_time":"2025-05-14T09:44:30.000Z","created_at":"2025-05-14T09:37:34.000Z","updated_at":"2025-05-14T10:57:33.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"second_payment_completed","payment_method":"wallet","wallet_type":null,"blockchain_second_payment_tx":"0x0037e8c506fb70f00ebf8968652da1becf6482cbc8cb004b57296572f2f3d66a"},{"id":105,"name":"发萨芬sa","description":"发撒地方是飞洒发萨芬撒","status":"pending","data_type":"image","current_round":0,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":null,"end_time":null,"created_at":"2025-05-14T09:35:28.000Z","updated_at":"2025-05-14T09:35:28.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"CNY","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"first_payment_completed","payment_method":"wechat","wallet_type":null,"blockchain_second_payment_tx":null},{"id":87,"name":"发士大夫sad","description":" 发士大夫是地方大师傅士大夫撒旦fsa","status":"in_progress","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-04-21T14:56:22.000Z","end_time":null,"created_at":"2025-04-21T14:22:56.000Z","updated_at":"2025-04-21T15:02:32.000Z","reward":"0.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"pending","payment_method":null,"wallet_type":null,"blockchain_second_payment_tx":null},{"id":86,"name":"发生发撒","description":"发萨芬萨芬萨芬撒旦发大水","status":"in_progress","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-04-21T13:49:02.000Z","end_time":null,"created_at":"2025-04-21T13:46:49.000Z","updated_at":"2025-04-21T13:56:39.000Z","reward":"0.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"pending","payment_method":null,"wallet_type":null,"blockchain_second_payment_tx":null},{"id":85,"name":"发发撒","description":"发啊撒飞洒地方但是发撒 阿斯顿富士达","status":"pending","data_type":"image","current_round":0,"total_rounds":5,"required_participants":4,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":null,"end_time":null,"created_at":"2025-04-21T13:03:05.000Z","updated_at":"2025-04-21T13:03:05.000Z","reward":"0.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"pending","payment_method":null,"wallet_type":null,"blockchain_second_payment_tx":null},{"id":84,"name":"发顺丰","description":"发阿斯弗萨芬十大发大水","status":"in_progress","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-04-20T15:40:24.000Z","end_time":null,"created_at":"2025-04-20T15:39:11.000Z","updated_at":"2025-04-20T15:45:49.000Z","reward":"0.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"pending","payment_method":null,"wallet_type":null,"blockchain_second_payment_tx":null},{"id":83,"name":"飞洒发","description":"发撒飞洒地方撒地方是 ","status":"in_progress","data_type":"image","current_round":0,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-04-20T15:26:07.000Z","end_time":null,"created_at":"2025-04-20T15:24:43.000Z","updated_at":"2025-04-20T15:26:06.000Z","reward":"0.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"pending","payment_method":null,"wallet_type":null,"blockchain_second_payment_tx":null},{"id":82,"name":"几个国家宏观","description":"jkllhjkhklhkl","status":"in_progress","data_type":"image","current_round":0,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-04-20T14:40:31.000Z","end_time":null,"created_at":"2025-04-20T14:39:16.000Z","updated_at":"2025-04-20T14:40:31.000Z","reward":"0.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"pending","payment_method":null,"wallet_type":null,"blockchain_second_payment_tx":null}]
获取任务(ID: 112)的参与者
SQL查询参数(数组): [ 112 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 112 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 111)的参与者
SQL查询参数(数组): [ 111 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 111 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 110)的参与者
SQL查询参数(数组): [ 110 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 110 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 109)的参与者
SQL查询参数(数组): [ 109 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 109 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 108)的参与者
SQL查询参数(数组): [ 108 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 108 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 107)的参与者
SQL查询参数(数组): [ 107 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 107 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 106)的参与者
SQL查询参数(数组): [ 106 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 106 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 105)的参与者
SQL查询参数(数组): [ 105 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 105 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 87)的参与者
SQL查询参数(数组): [ 87 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 87 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 86)的参与者
SQL查询参数(数组): [ 86 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 86 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 85)的参与者
SQL查询参数(数组): [ 85 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 85 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 84)的参与者
SQL查询参数(数组): [ 84 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 84 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 83)的参与者
SQL查询参数(数组): [ 83 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 83 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 82)的参与者
SQL查询参数(数组): [ 82 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 82 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
SQL查询参数(数组): []
执行SQL查询: SELECT * FROM tasks ORDER BY created_at DESC
获取所有任务，返回数据: [{"id":112,"name":"图像识别模型","description":"训练识别数字图像等。。。","status":"in_progress","data_type":"image","current_round":5,"total_rounds":10,"required_participants":3,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-08-27T00:48:39.000Z","end_time":null,"created_at":"2025-08-27T00:44:43.000Z","updated_at":"2025-08-27T01:00:09.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"first_payment_completed","payment_method":"balance","wallet_type":null,"blockchain_second_payment_tx":null},{"id":111,"name":"的萨芬萨地方撒","description":" 法撒旦富士达发撒发  发撒发士大夫","status":"pending","data_type":"image","current_round":0,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":null,"end_time":null,"created_at":"2025-05-15T12:09:16.000Z","updated_at":"2025-05-15T12:09:16.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"CNY","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"first_payment_completed","payment_method":"wechat","wallet_type":null,"blockchain_second_payment_tx":null},{"id":110,"name":"发顺丰士大夫sa","description":"的幅度萨芬士大夫 的撒发撒","status":"completed","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-05-15T07:52:21.000Z","end_time":"2025-05-15T07:56:58.000Z","created_at":"2025-05-15T07:50:14.000Z","updated_at":"2025-05-15T12:09:52.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"second_payment_completed","payment_method":"wallet","wallet_type":null,"blockchain_second_payment_tx":"0x5d5d74f0b5899107917bc0bd6456665e98b162e1a61663d22a0ef3277534c087"},{"id":109,"name":"啊发生的萨芬","description":" 发士大夫的撒地方撒地方","status":"completed","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-05-15T07:30:25.000Z","end_time":"2025-05-15T07:34:57.000Z","created_at":"2025-05-15T07:28:30.000Z","updated_at":"2025-05-15T07:34:56.000Z","reward":"500.00","reward_distribution":"contribution","payment_currency":"CNY","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"first_payment_completed","payment_method":"wechat","wallet_type":null,"blockchain_second_payment_tx":null},{"id":108,"name":"的士大夫的撒fsd","description":" 飞洒地方萨的地方的撒法撒旦法撒旦ff","status":"completed","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-05-14T11:10:12.000Z","end_time":"2025-05-14T11:14:53.000Z","created_at":"2025-05-14T11:09:06.000Z","updated_at":"2025-05-14T11:16:37.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"second_payment_completed","payment_method":"wallet","wallet_type":null,"blockchain_second_payment_tx":"0x27e0392b472ac9a6bee26d9723558b74d3b912146e3e10b4a61dcf556d2e2992"},{"id":107,"name":"飞洒地方是f","description":"啊飞洒地方萨芬撒地方按时范德萨","status":"completed","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-05-14T11:01:54.000Z","end_time":"2025-05-14T11:07:27.000Z","created_at":"2025-05-14T10:59:27.000Z","updated_at":"2025-05-14T11:08:37.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"CNY","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"second_payment_completed","payment_method":"wechat","wallet_type":null,"blockchain_second_payment_tx":null},{"id":106,"name":"法大师傅十大fdsd","description":" 发撒富士达富士达发撒发撒","status":"completed","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-05-14T09:38:34.000Z","end_time":"2025-05-14T09:44:30.000Z","created_at":"2025-05-14T09:37:34.000Z","updated_at":"2025-05-14T10:57:33.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"second_payment_completed","payment_method":"wallet","wallet_type":null,"blockchain_second_payment_tx":"0x0037e8c506fb70f00ebf8968652da1becf6482cbc8cb004b57296572f2f3d66a"},{"id":105,"name":"发萨芬sa","description":"发撒地方是飞洒发萨芬撒","status":"pending","data_type":"image","current_round":0,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":null,"end_time":null,"created_at":"2025-05-14T09:35:28.000Z","updated_at":"2025-05-14T09:35:28.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"CNY","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"first_payment_completed","payment_method":"wechat","wallet_type":null,"blockchain_second_payment_tx":null},{"id":87,"name":"发士大夫sad","description":" 发士大夫是地方大师傅士大夫撒旦fsa","status":"in_progress","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-04-21T14:56:22.000Z","end_time":null,"created_at":"2025-04-21T14:22:56.000Z","updated_at":"2025-04-21T15:02:32.000Z","reward":"0.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"pending","payment_method":null,"wallet_type":null,"blockchain_second_payment_tx":null},{"id":86,"name":"发生发撒","description":"发萨芬萨芬萨芬撒旦发大水","status":"in_progress","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-04-21T13:49:02.000Z","end_time":null,"created_at":"2025-04-21T13:46:49.000Z","updated_at":"2025-04-21T13:56:39.000Z","reward":"0.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"pending","payment_method":null,"wallet_type":null,"blockchain_second_payment_tx":null},{"id":85,"name":"发发撒","description":"发啊撒飞洒地方但是发撒 阿斯顿富士达","status":"pending","data_type":"image","current_round":0,"total_rounds":5,"required_participants":4,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":null,"end_time":null,"created_at":"2025-04-21T13:03:05.000Z","updated_at":"2025-04-21T13:03:05.000Z","reward":"0.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"pending","payment_method":null,"wallet_type":null,"blockchain_second_payment_tx":null},{"id":84,"name":"发顺丰","description":"发阿斯弗萨芬十大发大水","status":"in_progress","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-04-20T15:40:24.000Z","end_time":null,"created_at":"2025-04-20T15:39:11.000Z","updated_at":"2025-04-20T15:45:49.000Z","reward":"0.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"pending","payment_method":null,"wallet_type":null,"blockchain_second_payment_tx":null},{"id":83,"name":"飞洒发","description":"发撒飞洒地方撒地方是 ","status":"in_progress","data_type":"image","current_round":0,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-04-20T15:26:07.000Z","end_time":null,"created_at":"2025-04-20T15:24:43.000Z","updated_at":"2025-04-20T15:26:06.000Z","reward":"0.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"pending","payment_method":null,"wallet_type":null,"blockchain_second_payment_tx":null},{"id":82,"name":"几个国家宏观","description":"jkllhjkhklhkl","status":"in_progress","data_type":"image","current_round":0,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-04-20T14:40:31.000Z","end_time":null,"created_at":"2025-04-20T14:39:16.000Z","updated_at":"2025-04-20T14:40:31.000Z","reward":"0.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"pending","payment_method":null,"wallet_type":null,"blockchain_second_payment_tx":null}]
获取任务(ID: 112)的参与者
SQL查询参数(数组): [ 112 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 112 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 111)的参与者
SQL查询参数(数组): [ 111 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 111 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 110)的参与者
SQL查询参数(数组): [ 110 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 110 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 109)的参与者
SQL查询参数(数组): [ 109 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 109 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 108)的参与者
SQL查询参数(数组): [ 108 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 108 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 107)的参与者
SQL查询参数(数组): [ 107 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 107 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 106)的参与者
SQL查询参数(数组): [ 106 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 106 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 105)的参与者
SQL查询参数(数组): [ 105 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 105 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 87)的参与者
SQL查询参数(数组): [ 87 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 87 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 86)的参与者
SQL查询参数(数组): [ 86 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 86 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 85)的参与者
SQL查询参数(数组): [ 85 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 85 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 84)的参与者
SQL查询参数(数组): [ 84 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 84 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 83)的参与者
SQL查询参数(数组): [ 83 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 83 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 82)的参与者
SQL查询参数(数组): [ 82 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 82 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
SQL查询参数(数组): []
执行SQL查询: SELECT * FROM tasks ORDER BY created_at DESC
获取所有任务，返回数据: [{"id":112,"name":"图像识别模型","description":"训练识别数字图像等。。。","status":"in_progress","data_type":"image","current_round":5,"total_rounds":10,"required_participants":3,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-08-27T00:48:39.000Z","end_time":null,"created_at":"2025-08-27T00:44:43.000Z","updated_at":"2025-08-27T01:00:09.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"first_payment_completed","payment_method":"balance","wallet_type":null,"blockchain_second_payment_tx":null},{"id":111,"name":"的萨芬萨地方撒","description":" 法撒旦富士达发撒发  发撒发士大夫","status":"pending","data_type":"image","current_round":0,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":null,"end_time":null,"created_at":"2025-05-15T12:09:16.000Z","updated_at":"2025-05-15T12:09:16.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"CNY","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"first_payment_completed","payment_method":"wechat","wallet_type":null,"blockchain_second_payment_tx":null},{"id":110,"name":"发顺丰士大夫sa","description":"的幅度萨芬士大夫 的撒发撒","status":"completed","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-05-15T07:52:21.000Z","end_time":"2025-05-15T07:56:58.000Z","created_at":"2025-05-15T07:50:14.000Z","updated_at":"2025-05-15T12:09:52.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"second_payment_completed","payment_method":"wallet","wallet_type":null,"blockchain_second_payment_tx":"0x5d5d74f0b5899107917bc0bd6456665e98b162e1a61663d22a0ef3277534c087"},{"id":109,"name":"啊发生的萨芬","description":" 发士大夫的撒地方撒地方","status":"completed","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-05-15T07:30:25.000Z","end_time":"2025-05-15T07:34:57.000Z","created_at":"2025-05-15T07:28:30.000Z","updated_at":"2025-05-15T07:34:56.000Z","reward":"500.00","reward_distribution":"contribution","payment_currency":"CNY","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"first_payment_completed","payment_method":"wechat","wallet_type":null,"blockchain_second_payment_tx":null},{"id":108,"name":"的士大夫的撒fsd","description":" 飞洒地方萨的地方的撒法撒旦法撒旦ff","status":"completed","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-05-14T11:10:12.000Z","end_time":"2025-05-14T11:14:53.000Z","created_at":"2025-05-14T11:09:06.000Z","updated_at":"2025-05-14T11:16:37.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"second_payment_completed","payment_method":"wallet","wallet_type":null,"blockchain_second_payment_tx":"0x27e0392b472ac9a6bee26d9723558b74d3b912146e3e10b4a61dcf556d2e2992"},{"id":107,"name":"飞洒地方是f","description":"啊飞洒地方萨芬撒地方按时范德萨","status":"completed","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-05-14T11:01:54.000Z","end_time":"2025-05-14T11:07:27.000Z","created_at":"2025-05-14T10:59:27.000Z","updated_at":"2025-05-14T11:08:37.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"CNY","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"second_payment_completed","payment_method":"wechat","wallet_type":null,"blockchain_second_payment_tx":null},{"id":106,"name":"法大师傅十大fdsd","description":" 发撒富士达富士达发撒发撒","status":"completed","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-05-14T09:38:34.000Z","end_time":"2025-05-14T09:44:30.000Z","created_at":"2025-05-14T09:37:34.000Z","updated_at":"2025-05-14T10:57:33.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"second_payment_completed","payment_method":"wallet","wallet_type":null,"blockchain_second_payment_tx":"0x0037e8c506fb70f00ebf8968652da1becf6482cbc8cb004b57296572f2f3d66a"},{"id":105,"name":"发萨芬sa","description":"发撒地方是飞洒发萨芬撒","status":"pending","data_type":"image","current_round":0,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":null,"end_time":null,"created_at":"2025-05-14T09:35:28.000Z","updated_at":"2025-05-14T09:35:28.000Z","reward":"500.00","reward_distribution":"equal","payment_currency":"CNY","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"first_payment_completed","payment_method":"wechat","wallet_type":null,"blockchain_second_payment_tx":null},{"id":87,"name":"发士大夫sad","description":" 发士大夫是地方大师傅士大夫撒旦fsa","status":"in_progress","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-04-21T14:56:22.000Z","end_time":null,"created_at":"2025-04-21T14:22:56.000Z","updated_at":"2025-04-21T15:02:32.000Z","reward":"0.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"pending","payment_method":null,"wallet_type":null,"blockchain_second_payment_tx":null},{"id":86,"name":"发生发撒","description":"发萨芬萨芬萨芬撒旦发大水","status":"in_progress","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-04-21T13:49:02.000Z","end_time":null,"created_at":"2025-04-21T13:46:49.000Z","updated_at":"2025-04-21T13:56:39.000Z","reward":"0.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"pending","payment_method":null,"wallet_type":null,"blockchain_second_payment_tx":null},{"id":85,"name":"发发撒","description":"发啊撒飞洒地方但是发撒 阿斯顿富士达","status":"pending","data_type":"image","current_round":0,"total_rounds":5,"required_participants":4,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":null,"end_time":null,"created_at":"2025-04-21T13:03:05.000Z","updated_at":"2025-04-21T13:03:05.000Z","reward":"0.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"pending","payment_method":null,"wallet_type":null,"blockchain_second_payment_tx":null},{"id":84,"name":"发顺丰","description":"发阿斯弗萨芬十大发大水","status":"in_progress","data_type":"image","current_round":5,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-04-20T15:40:24.000Z","end_time":null,"created_at":"2025-04-20T15:39:11.000Z","updated_at":"2025-04-20T15:45:49.000Z","reward":"0.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"pending","payment_method":null,"wallet_type":null,"blockchain_second_payment_tx":null},{"id":83,"name":"飞洒发","description":"发撒飞洒地方撒地方是 ","status":"in_progress","data_type":"image","current_round":0,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-04-20T15:26:07.000Z","end_time":null,"created_at":"2025-04-20T15:24:43.000Z","updated_at":"2025-04-20T15:26:06.000Z","reward":"0.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"pending","payment_method":null,"wallet_type":null,"blockchain_second_payment_tx":null},{"id":82,"name":"几个国家宏观","description":"jkllhjkhklhkl","status":"in_progress","data_type":"image","current_round":0,"total_rounds":5,"required_participants":2,"learning_rate":0.0010000000474974513,"batch_size":32,"timeout":60,"blockchain_tx_hash":null,"start_time":"2025-04-20T14:40:31.000Z","end_time":null,"created_at":"2025-04-20T14:39:16.000Z","updated_at":"2025-04-20T14:40:31.000Z","reward":"0.00","reward_distribution":"equal","payment_currency":"USD","installment_payment":true,"first_payment_rate":40,"platform_fee_rate":10,"payment_status":"pending","payment_method":null,"wallet_type":null,"blockchain_second_payment_tx":null}]
获取任务(ID: 112)的参与者
SQL查询参数(数组): [ 112 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 112 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 111)的参与者
SQL查询参数(数组): [ 111 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 111 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 110)的参与者
SQL查询参数(数组): [ 110 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 110 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 109)的参与者
SQL查询参数(数组): [ 109 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 109 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 108)的参与者
SQL查询参数(数组): [ 108 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 108 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 107)的参与者
SQL查询参数(数组): [ 107 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 107 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 106)的参与者
SQL查询参数(数组): [ 106 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 106 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 105)的参与者
SQL查询参数(数组): [ 105 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 105 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 87)的参与者
SQL查询参数(数组): [ 87 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 87 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 86)的参与者
SQL查询参数(数组): [ 86 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 86 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 85)的参与者
SQL查询参数(数组): [ 85 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 85 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 84)的参与者
SQL查询参数(数组): [ 84 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 84 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 83)的参与者
SQL查询参数(数组): [ 83 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 83 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
获取任务(ID: 82)的参与者
SQL查询参数(数组): [ 82 ]
执行SQL查询: SELECT * FROM tasks WHERE id = ?
SQL查询参数(数组): [ 82 ]
执行SQL查询: SELECT * FROM participants WHERE task_id = ?
^C