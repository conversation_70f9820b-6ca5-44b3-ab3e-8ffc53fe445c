<!-- 任务申请审核页面 -->
<template>
  <div class="task-applications">
    <div class="page-header">
      <h1>任务申请审核</h1>
      <div class="page-actions">
        <el-button @click="goBack">
          <i class="fas fa-arrow-left"></i> 返回任务管理
        </el-button>
      </div>
    </div>
    
    <Breadcrumb />
    
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="6" animated />
    </div>
    
    <div v-else>
      <!-- 任务基本信息 -->
      <el-card class="task-info-card">
        <template #header>
          <div class="card-header">
            <h2>{{ task.name }}</h2>
            <el-tag :type="getStatusTagType(task.status)">{{ getStatusText(task.status) }}</el-tag>
          </div>
        </template>
        
        <div class="task-info">
          <p class="task-description">{{ task.description }}</p>
          <div class="task-meta">
            <div class="meta-item">
              <span class="meta-label">数据类型:</span>
              <span class="meta-value">{{ task.dataType }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">所需参与者:</span>
              <span class="meta-value">{{ task.requiredParticipants }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">已申请:</span>
              <span class="meta-value">{{ applications?.length || 0 }}</span>
            </div>
            <div class="meta-item">
              <span class="meta-label">已批准:</span>
              <span class="meta-value">{{ approvedCount }}</span>
            </div>
          </div>
        </div>
      </el-card>
      
      <!-- 申请列表 -->
      <el-card class="applications-card">
        <template #header>
          <div class="card-header">
            <h3>申请列表</h3>
            <div class="card-actions">
              <el-button 
                size="small" 
                type="success" 
                :disabled="!hasApplications || task.status !== 'published'" 
                @click="approveAllApplications"
              >
                批准全部
              </el-button>
            </div>
          </div>
        </template>
        
        <div v-if="!hasApplications" class="empty-applications">
          <el-empty description="暂无申请" />
        </div>
        
        <div v-else>
          <el-table :data="applications" border style="width: 100%" row-key="id">
            <el-table-column prop="applicant" label="申请者" width="180">
              <template #default="scope">
                <div class="applicant-info">
                  <span class="applicant-address">{{ formatAddress(scope.row.applicant) }}</span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="dataDescription" label="数据描述" min-width="250">
              <template #default="scope">
                <div class="data-description">{{ scope.row.dataDescription }}</div>
              </template>
            </el-table-column>
            
            <el-table-column prop="dataSize" label="数据量" width="120">
              <template #default="scope">
                {{ formatDataSize(scope.row.dataSize) }}
              </template>
            </el-table-column>
            
            <el-table-column prop="computeResource" label="计算资源" width="120">
              <template #default="scope">
                <el-tag type="info" size="small">{{ scope.row.computeResource }}</el-tag>
              </template>
            </el-table-column>
            
            <el-table-column prop="estimatedTime" label="预计时间" width="120">
              <template #default="scope">
                {{ scope.row.estimatedTime }}
              </template>
            </el-table-column>
            
            <el-table-column prop="status" label="状态" width="120">
              <template #default="scope">
                <el-tag :type="getApplicationStatusType(scope.row.status)" size="small">
                  {{ getApplicationStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="scope">
                <div v-if="scope.row.status === 'pending' && task.status === 'published'">
                  <el-button 
                    size="small" 
                    type="primary" 
                    plain 
                    @click="approveApplication(scope.row)"
                  >
                    批准
                  </el-button>
                  <el-button 
                    size="small" 
                    type="danger" 
                    plain 
                    @click="rejectApplication(scope.row)"
                  >
                    拒绝
                  </el-button>
                </div>
                <div v-else-if="scope.row.status === 'approved'">
                  <el-button 
                    size="small" 
                    type="warning" 
                    plain 
                    @click="revokeApproval(scope.row)"
                    :disabled="task.status !== 'published'"
                  >
                    撤销批准
                  </el-button>
                </div>
                <div v-else-if="scope.row.status === 'rejected'">
                  <el-button 
                    size="small" 
                    type="primary" 
                    plain 
                    @click="approveApplication(scope.row)"
                    :disabled="task.status !== 'published'"
                  >
                    重新批准
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import Breadcrumb from '@/components/Breadcrumb.vue';
import apiService, { TaskAPI, ParticipantAPI } from '@/services/api';

export default {
  name: 'TaskApplications',
  components: {
    Breadcrumb
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const loading = ref(true);
    const task = ref({});
    const applications = ref([]);
    const taskId = route.params.id;
    
    // 获取任务和申请
    const loadTaskAndApplications = async () => {
      loading.value = true;
      try {
        console.log('加载任务ID:', taskId);
        
        // 使用TaskAPI获取任务信息
        const currentTask = await TaskAPI.getById(taskId);
        
        if (!currentTask) {
          ElMessage.error('任务不存在');
          router.push('/server/tasks');
          return;
        }
        
        console.log('获取到的任务数据:', currentTask);
        
        // 处理任务数据，转换字段名称以匹配前端期望的格式
        task.value = {
          id: currentTask.id,
          name: currentTask.name,
          description: currentTask.description,
          status: currentTask.status === 'pending' ? 'published' : currentTask.status,
          dataType: currentTask.data_type,
          requiredParticipants: currentTask.required_participants,
          totalRounds: currentTask.total_rounds,
          currentRound: currentTask.current_round || 0
        };
        
        console.log('处理后的任务数据:', task.value);
        
        // 获取任务的参与者列表（作为申请）
        const taskParticipants = await ParticipantAPI.getByTaskId(taskId);
        console.log('获取到的参与者数据:', taskParticipants);
        
        // 转换参与者数据为申请数据格式
        applications.value = taskParticipants.map(participant => {
          return {
            id: participant.id,
            applicant: participant.client_address,
            dataDescription: participant.data_description || '无数据描述',
            dataSize: participant.data_size,
            computeResource: participant.compute_resource || 'cpu',
            estimatedTime: participant.estimated_time || '1-3h',
            status: participant.status === 'registered' ? 'pending' : participant.status,
            notes: participant.notes || '',
            applyTime: new Date(participant.created_at),
            reviewTime: participant.last_update ? new Date(participant.last_update) : null
          };
        });
        
        console.log('处理后的申请数据:', applications.value);
      } catch (error) {
        console.error('加载任务和申请数据失败:', error);
        ElMessage.error('加载数据失败，请重试');
      } finally {
        loading.value = false;
      }
    };
    
    // 计算已批准的申请数量
    const approvedCount = computed(() => {
      return applications.value.filter(app => app.status === 'approved').length;
    });
    
    // 是否有申请
    const hasApplications = computed(() => {
      return applications.value.length > 0;
    });
    
    // 格式化地址
    const formatAddress = (address) => {
      if (!address) return '';
      return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
    };
    
    // 格式化数据大小
    const formatDataSize = (size) => {
      if (!size) return '0';
      if (size < 1000) return size;
      if (size < 1000000) return `${(size / 1000).toFixed(1)}K`;
      return `${(size / 1000000).toFixed(1)}M`;
    };
    
    // 获取任务状态标签类型
    const getStatusTagType = (status) => {
      const types = {
        draft: 'info',
        published: 'warning',
        in_progress: 'success',
        completed: 'primary',
        cancelled: 'danger'
      };
      return types[status] || 'info';
    };
    
    // 获取任务状态文本
    const getStatusText = (status) => {
      const texts = {
        draft: '草稿',
        published: '已发布',
        in_progress: '进行中',
        completed: '已完成',
        cancelled: '已取消'
      };
      return texts[status] || status;
    };
    
    // 获取申请状态标签类型
    const getApplicationStatusType = (status) => {
      const types = {
        pending: 'warning',
        approved: 'success',
        rejected: 'danger'
      };
      return types[status] || 'info';
    };
    
    // 获取申请状态文本
    const getApplicationStatusText = (status) => {
      const texts = {
        pending: '待审核',
        approved: '已批准',
        rejected: '已拒绝'
      };
      return texts[status] || status;
    };
    
    // 批准申请
    const approveApplication = async (application) => {
      try {
        await ElMessageBox.confirm(
          `确定要批准来自 ${formatAddress(application.applicant)} 的申请吗？`,
          '批准申请',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info',
          }
        );
        
        // 获取当前用户地址作为审核者
        const reviewerAddress = localStorage.getItem('userAddress') || '0x1234567890123456789012345678901234567890';
        
        // 使用ParticipantAPI更新参与者状态
        await ParticipantAPI.updateStatus(application.id, 'training');
        
        // 更新本地状态
        application.status = 'approved';
        application.reviewTime = new Date();
        
        // 刷新任务数据
        await loadTaskAndApplications();
        
        ElMessage.success(`已批准 ${formatAddress(application.applicant)} 的申请`);
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批准申请失败:', error);
          ElMessage.error('批准申请失败，请重试');
        }
      }
    };
    
    // 拒绝申请
    const rejectApplication = async (application) => {
      try {
        await ElMessageBox.confirm(
          `确定要拒绝来自 ${formatAddress(application.applicant)} 的申请吗？`,
          '拒绝申请',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );
        
        // 获取当前用户地址作为审核者
        const reviewerAddress = localStorage.getItem('userAddress') || '0x1234567890123456789012345678901234567890';
        
        // 使用ParticipantAPI更新参与者状态
        await ParticipantAPI.updateStatus(application.id, 'rejected');
        
        // 更新本地状态
        application.status = 'rejected';
        application.reviewTime = new Date();
        
        // 刷新任务数据
        await loadTaskAndApplications();
        
        ElMessage.success(`已拒绝 ${formatAddress(application.applicant)} 的申请`);
      } catch (error) {
        if (error !== 'cancel') {
          console.error('拒绝申请失败:', error);
          ElMessage.error('拒绝申请失败，请重试');
        }
      }
    };
    
    // 撤销批准
    const revokeApproval = async (application) => {
      try {
        await ElMessageBox.confirm(
          `确定要撤销对 ${formatAddress(application.applicant)} 的批准吗？`,
          '撤销批准',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );
        
        // 使用ParticipantAPI更新参与者状态
        await ParticipantAPI.updateStatus(application.id, 'registered');
        
        // 更新本地状态
        application.status = 'pending';
        application.reviewTime = new Date();
        
        // 刷新任务数据
        await loadTaskAndApplications();
        
        ElMessage.success(`已撤销对 ${formatAddress(application.applicant)} 的批准`);
      } catch (error) {
        if (error !== 'cancel') {
          console.error('撤销批准失败:', error);
          ElMessage.error('撤销批准失败，请重试');
        }
      }
    };
    
    // 批准所有申请
    const approveAllApplications = async () => {
      try {
        const pendingApplications = applications.value.filter(app => app.status === 'pending');
        
        if (pendingApplications.length === 0) {
          ElMessage.info('没有待审核的申请');
          return;
        }
        
        await ElMessageBox.confirm(
          `确定要批准所有 ${pendingApplications.length} 个待审核申请吗？`,
          '批准所有申请',
          {
            confirmButtonText: '确定批准所有',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );
        
        loading.value = true;
        let successCount = 0;
        
        // 逐个批准申请
        for (const app of pendingApplications) {
          try {
            await ParticipantAPI.updateStatus(app.id, 'training');
            successCount++;
          } catch (error) {
            console.error(`批准申请(ID: ${app.id})失败:`, error);
          }
        }
        
        // 刷新任务数据
        await loadTaskAndApplications();
        
        ElMessage.success(`成功批准了 ${successCount}/${pendingApplications.length} 个申请`);
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批准所有申请失败:', error);
          ElMessage.error('批准所有申请失败，请重试');
        }
      } finally {
        loading.value = false;
      }
    };
    
    // 返回任务管理页面
    const goBack = () => {
      router.push('/server/tasks');
    };
    
    onMounted(() => {
      loadTaskAndApplications();
    });
    
    return {
      loading,
      task,
      applications,
      approvedCount,
      hasApplications,
      formatAddress,
      formatDataSize,
      getStatusTagType,
      getStatusText,
      getApplicationStatusType,
      getApplicationStatusText,
      approveApplication,
      rejectApplication,
      revokeApproval,
      approveAllApplications,
      goBack
    };
  }
};
</script>

<style scoped>
.task-applications {
  width: 100%;
  padding: var(--spacing-md);
  box-sizing: border-box;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: var(--text-primary);
  font-weight: 600;
}

.task-info-card {
  margin-bottom: var(--spacing-md);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.task-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  line-height: 1.5;
}

.task-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.meta-item {
  display: flex;
  gap: var(--spacing-xs);
}

.meta-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.meta-value {
  color: var(--text-primary);
  font-weight: 600;
}

.applications-card {
  margin-top: var(--spacing-md);
}

.card-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.empty-applications {
  display: flex;
  justify-content: center;
  padding: var(--spacing-xl) 0;
}

.applicant-info {
  display: flex;
  flex-direction: column;
}

.applicant-address {
  font-weight: 500;
}

.data-description {
  color: var(--text-secondary);
  line-height: 1.5;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.loading-container {
  padding: var(--spacing-md) 0;
}

@media (max-width: 768px) {
  .task-applications {
    padding: var(--spacing-sm);
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
  
  .task-meta {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}
</style> 