/**
 * 执行更新transactions表的SQL脚本
 */
const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
const env = require('../config/env');

// 使用env中的数据库配置
const dbConfig = {
  host: env.DB.HOST,
  port: env.DB.PORT,
  user: env.DB.USER,
  password: env.DB.PASSWORD,
  database: env.DB.DATABASE
};

/**
 * 确保数据库存在
 * @param {mysql.Connection} connection MySQL连接
 * @param {string} dbName 数据库名称
 */
async function ensureDatabase(connection, dbName) {
  console.log(`确保数据库 ${dbName} 存在...`);
  await connection.query(`CREATE DATABASE IF NOT EXISTS ${dbName}`);
  await connection.query(`USE ${dbName}`);
  console.log(`现在使用数据库: ${dbName}`);
}

/**
 * 执行SQL文件
 * @param {mysql.Connection} connection MySQL连接
 * @param {string} filePath SQL文件路径
 */
async function executeSQL(connection, filePath) {
  console.log(`执行SQL文件: ${filePath}`);
  
  // 读取SQL文件
  const sql = fs.readFileSync(filePath, 'utf8');
  console.log('SQL文件内容长度:', sql.length);
  
  // 将SQL语句拆分成单独的命令
  const statements = sql.split(';')
    .map(statement => statement.trim())
    .filter(statement => statement.length > 0);
  
  console.log(`SQL文件包含 ${statements.length} 条语句`);
  
  // 逐条执行SQL语句
  for (let i = 0; i < statements.length; i++) {
    const statement = statements[i];
    console.log(`执行第 ${i+1}/${statements.length} 条SQL语句:`);
    console.log(statement.substring(0, 100) + (statement.length > 100 ? '...' : ''));
    
    try {
      const [result] = await connection.query(statement);
      console.log('执行结果:', JSON.stringify(result));
    } catch (err) {
      console.error(`执行SQL语句失败:`, err);
      console.error('错误的SQL语句:', statement);
      // 继续执行后续语句
    }
  }
}

/**
 * 主函数
 */
async function main() {
  let connection;
  
  try {
    console.log('连接到MySQL数据库...');
    console.log('数据库配置:', {
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.user,
      database: dbConfig.database
    });
    
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: dbConfig.host,
      port: dbConfig.port,
      user: dbConfig.user,
      password: dbConfig.password
    });
    
    console.log('数据库连接成功');
    
    // 确保数据库存在
    await ensureDatabase(connection, dbConfig.database);
    
    // 执行SQL文件
    const sqlFilePath = path.join(__dirname, 'update_transactions_table.sql');
    await executeSQL(connection, sqlFilePath);
    
    console.log('数据库更新完成');
  } catch (err) {
    console.error('数据库更新失败:', err);
    process.exit(1);
  } finally {
    if (connection) {
      console.log('关闭数据库连接');
      await connection.end();
    }
  }
}

// 执行主函数
main().then(() => {
  console.log('操作成功!');
  process.exit(0);
}).catch(err => {
  console.error('操作失败:', err);
  process.exit(1);
}); 