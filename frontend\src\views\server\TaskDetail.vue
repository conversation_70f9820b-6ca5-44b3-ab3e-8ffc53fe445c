<!-- 任务详情页面 -->
<template>
  <div class="task-detail">
    <div class="page-header">
      <h1>{{ task.name || '任务详情' }}</h1>
      <div class="page-actions">
        <el-button @click="goBack">
          <i class="fas fa-arrow-left"></i> 返回
        </el-button>
        <template v-if="task.status === 'draft'">
          <el-button type="primary" @click="editTask" v-if="!editMode">
            <i class="fas fa-edit"></i> 编辑
          </el-button>
          <el-button type="success" @click="publishTask">
            <i class="fas fa-paper-plane"></i> 发布
          </el-button>
        </template>
        <template v-else-if="task.status === 'published'">
          <el-button type="primary" @click="reviewApplications">
            <i class="fas fa-clipboard-check"></i> 审核申请
          </el-button>
          <el-button type="success" @click="startTraining">
            <i class="fas fa-play"></i> 开始训练
          </el-button>
        </template>
        <template v-else-if="task.status === 'in_progress'">
          <el-button type="primary" @click="monitorTask">
            <i class="fas fa-chart-line"></i> 监控训练
          </el-button>
        </template>
      </div>
    </div>
    
    <Breadcrumb />
    
    <!-- 任务信息卡片 -->
    <el-card class="detail-card" v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>任务概览</span>
          <el-tag :type="getStatusTagType(task.status)">{{ getStatusText(task.status) }}</el-tag>
        </div>
      </template>
      
      <div class="task-overview">
        <div class="overview-item">
          <div class="item-label">创建时间</div>
          <div class="item-value">{{ formatDate(task.createdAt) }}</div>
        </div>
        <div class="overview-item">
          <div class="item-label">截止日期</div>
          <div class="item-value">{{ formatDate(task.deadline) }}</div>
        </div>
        <div class="overview-item">
          <div class="item-label">数据类型</div>
          <div class="item-value">{{ task.dataType }}</div>
        </div>
        <div class="overview-item">
          <div class="item-label">训练轮次</div>
          <div class="item-value">{{ task.currentRound || 0 }}/{{ task.totalRounds }}</div>
        </div>
        <div class="overview-item">
          <div class="item-label">参与者</div>
          <div class="item-value">{{ task.participants?.length || 0 }}/{{ task.requiredParticipants }}</div>
        </div>
        <div class="overview-item">
          <div class="item-label">奖励</div>
          <div class="item-value">{{ task.reward }} 代币</div>
        </div>
      </div>
      
      <el-divider />
      
      <div class="task-description">
        <h3>任务描述</h3>
        <p>{{ task.description }}</p>
      </div>
      
      <el-divider />
      
      <div class="task-requirements">
        <h3>参与要求</h3>
        <ul class="requirements-list">
          <li v-for="(req, index) in task.requirements" :key="index">{{ req }}</li>
        </ul>
      </div>
      
      <el-divider />
      
      <div class="task-config">
        <h3>训练配置</h3>
        <div class="config-grid">
          <div class="config-item">
            <div class="item-label">学习率</div>
            <div class="item-value">{{ task.learningRate }}</div>
          </div>
          <div class="config-item">
            <div class="item-label">批次大小</div>
            <div class="item-value">{{ task.batchSize }}</div>
          </div>
          <div class="config-item">
            <div class="item-label">每轮超时时间</div>
            <div class="item-value">{{ task.timeout }} 秒</div>
          </div>
          <div class="config-item">
            <div class="item-label">奖励分配方式</div>
            <div class="item-value">{{ getRewardDistributionText(task.rewardDistribution) }}</div>
          </div>
        </div>
      </div>
    </el-card>
    
    <!-- 申请列表 -->
    <el-card class="detail-card" v-if="task.status !== 'draft'">
      <template #header>
        <div class="card-header">
          <span>申请列表</span>
          <el-button type="primary" size="small" @click="reviewApplications">管理申请</el-button>
        </div>
      </template>
      
      <el-table :data="applications" style="width: 100%">
        <el-table-column prop="client" label="客户端" width="180">
          <template #default="scope">
            <div class="client-info">
              <el-avatar :size="30" :src="getAvatarUrl(scope.row.client)" class="client-avatar"></el-avatar>
              <span>{{ shortenAddress(scope.row.client) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="dataSize" label="数据量" width="100" />
        <el-table-column prop="computeResource" label="计算资源" width="120">
          <template #default="scope">
            <el-tag size="small" :type="scope.row.computeResource === 'gpu' ? 'success' : 'info'">
              {{ scope.row.computeResource === 'gpu' ? 'GPU' : 'CPU' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="申请时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getApplicationStatusType(scope.row.status)">
              {{ getApplicationStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <template v-if="scope.row.status === 'pending'">
              <el-button size="small" type="success" @click="approveApplication(scope.row)" plain>批准</el-button>
              <el-button size="small" type="danger" @click="rejectApplication(scope.row)" plain>拒绝</el-button>
            </template>
            <el-button size="small" type="info" @click="viewApplicationDetail(scope.row)" plain>详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 参与者列表 -->
    <el-card class="detail-card" v-if="task.status === 'in_progress' || task.status === 'completed'">
      <template #header>
        <div class="card-header">
          <span>参与者列表</span>
        </div>
      </template>
      
      <el-table :data="participants" style="width: 100%">
        <el-table-column prop="client" label="客户端" width="180">
          <template #default="scope">
            <div class="client-info">
              <el-avatar :size="30" :src="getAvatarUrl(scope.row.client)" class="client-avatar"></el-avatar>
              <span>{{ shortenAddress(scope.row.client) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="dataSize" label="数据量" width="100" />
        <el-table-column prop="computeResource" label="计算资源" width="120">
          <template #default="scope">
            <el-tag size="small" :type="scope.row.computeResource === 'gpu' ? 'success' : 'info'">
              {{ scope.row.computeResource === 'gpu' ? 'GPU' : 'CPU' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="accuracy" label="准确率" width="100">
          <template #default="scope">
            {{ scope.row.accuracy ? parseFloat(scope.row.accuracy).toFixed(4) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="lastActive" label="最后活跃" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.lastActive) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getParticipantStatusType(scope.row.status)">
              {{ getParticipantStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 模型性能 -->
    <el-card class="detail-card" v-if="task.status === 'in_progress' || task.status === 'completed'">
      <template #header>
        <div class="card-header">
          <span>模型性能</span>
          <el-button type="primary" size="small" @click="monitorTask">查看详细数据</el-button>
        </div>
      </template>
      
      <div class="performance-metrics">
        <div class="metric-card">
          <div class="metric-value">{{ task.accuracy ? parseFloat(task.accuracy).toFixed(4) : '-' }}</div>
          <div class="metric-label">准确率</div>
        </div>
        <div class="metric-card">
          <div class="metric-value">{{ task.loss ? parseFloat(task.loss).toFixed(4) : '-' }}</div>
          <div class="metric-label">损失值</div>
        </div>
        <div class="metric-card">
          <div class="metric-value">{{ formatTime(task.trainingTime) }}</div>
          <div class="metric-label">训练时间</div>
        </div>
        <div class="metric-card">
          <div class="metric-value">{{ task.convergeRound || '-' }}</div>
          <div class="metric-label">收敛轮次</div>
        </div>
      </div>
      
      <div class="chart-container">
        <!-- 这里应该放置图表组件 -->
        <div class="chart-placeholder">性能趋势图表</div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import Breadcrumb from '@/components/Breadcrumb.vue';

export default {
  name: 'TaskDetail',
  components: {
    Breadcrumb
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const loading = ref(false);
    const editMode = ref(false);
    const task = reactive({
      id: null,
      name: '',
      description: '',
      status: '',
      dataType: '',
      createdAt: null,
      deadline: null,
      totalRounds: 0,
      currentRound: 0,
      requiredParticipants: 0,
      participants: [],
      applications: [],
      requirements: [],
      reward: 0,
      rewardDistribution: '',
      learningRate: 0,
      batchSize: 0,
      timeout: 0,
      accuracy: null,
      loss: null,
      trainingTime: 0,
      convergeRound: null
    });
    
    const applications = ref([]);
    const participants = ref([]);
    
    // 获取任务详情
    const loadTaskDetail = async () => {
      loading.value = true;
      try {
        const taskId = route.params.id;
        
        // 这里应该调用API获取任务详情
        // 临时数据
        const dummyTask = {
          id: taskId,
          name: 'MNIST手写数字识别训练',
          description: '基于MNIST数据集的手写数字识别模型训练，需要提供高质量的手写数字样本。通过联邦学习方法，在保护数据隐私的前提下构建高质量模型。',
          status: 'published',
          dataType: '图像',
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          deadline: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
          totalRounds: 10,
          currentRound: 0,
          requiredParticipants: 5,
          participants: [],
          applications: [
            {
              id: 1,
              client: '0x1234567890abcdef1234567890abcdef12345678',
              dataSize: 1200,
              computeResource: 'gpu',
              estimatedTime: '1-3h',
              createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
              status: 'pending'
            },
            {
              id: 2,
              client: '0x2345678901abcdef2345678901abcdef23456789',
              dataSize: 800,
              computeResource: 'cpu',
              estimatedTime: '3-6h',
              createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
              status: 'approved'
            }
          ],
          requirements: [
            '至少1000个手写数字样本',
            'GPU计算资源优先',
            '需要维持在线状态直到训练完成'
          ],
          reward: 500,
          rewardDistribution: 'equal',
          learningRate: 0.001,
          batchSize: 32,
          timeout: 60,
          accuracy: null,
          loss: null,
          trainingTime: 0,
          convergeRound: null
        };
        
        // 填充任务数据
        Object.assign(task, dummyTask);
        
        // 填充申请和参与者列表
        applications.value = task.applications || [];
        participants.value = task.participants || [];
        
      } catch (error) {
        console.error('加载任务详情失败:', error);
        ElMessage.error('加载任务详情失败，请重试');
      } finally {
        loading.value = false;
      }
    };
    
    // 格式化日期
    const formatDate = (date) => {
      if (!date) return '-';
      if (typeof date === 'string') {
        date = new Date(date);
      }
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    };
    
    // 格式化日期时间
    const formatDateTime = (date) => {
      if (!date) return '-';
      if (typeof date === 'string') {
        date = new Date(date);
      }
      return `${formatDate(date)} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    };
    
    // 格式化时间（秒）
    const formatTime = (seconds) => {
      if (!seconds) return '-';
      
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;
      
      return `${hours}h ${minutes}m ${remainingSeconds}s`;
    };
    
    // 获取状态标签类型
    const getStatusTagType = (status) => {
      const types = {
        draft: 'info',
        published: 'warning',
        in_progress: 'success',
        completed: 'primary',
        cancelled: 'danger'
      };
      return types[status] || 'info';
    };
    
    // 获取状态文本
    const getStatusText = (status) => {
      const texts = {
        draft: '草稿',
        published: '已发布',
        in_progress: '进行中',
        completed: '已完成',
        cancelled: '已取消'
      };
      return texts[status] || status;
    };
    
    // 获取申请状态标签类型
    const getApplicationStatusType = (status) => {
      const types = {
        pending: 'warning',
        approved: 'success',
        rejected: 'danger'
      };
      return types[status] || 'info';
    };
    
    // 获取申请状态文本
    const getApplicationStatusText = (status) => {
      const texts = {
        pending: '待审核',
        approved: '已批准',
        rejected: '已拒绝'
      };
      return texts[status] || status;
    };
    
    // 获取参与者状态标签类型
    const getParticipantStatusType = (status) => {
      const types = {
        active: 'success',
        inactive: 'warning',
        offline: 'info',
        failed: 'danger'
      };
      return types[status] || 'info';
    };
    
    // 获取参与者状态文本
    const getParticipantStatusText = (status) => {
      const texts = {
        active: '活跃',
        inactive: '不活跃',
        offline: '离线',
        failed: '失败'
      };
      return texts[status] || status;
    };
    
    // 获取奖励分配文本
    const getRewardDistributionText = (distribution) => {
      const texts = {
        equal: '平均分配',
        contribution: '按贡献度分配',
        data: '按数据量分配'
      };
      return texts[distribution] || distribution;
    };
    
    // 获取头像URL
    const getAvatarUrl = (address) => {
      return `https://api.dicebear.com/7.x/identicon/svg?seed=${address}`;
    };
    
    // 缩短地址显示
    const shortenAddress = (address) => {
      if (!address) return '';
      return `${address.slice(0, 6)}...${address.slice(-4)}`;
    };
    
    // 返回上一页
    const goBack = () => {
      router.push('/server/tasks');
    };
    
    // 编辑任务
    const editTask = () => {
      editMode.value = true;
    };
    
    // 发布任务
    const publishTask = async () => {
      try {
        await ElMessageBox.confirm(
          '确定要发布此任务吗？发布后将对客户端用户可见，且无法回到草稿状态。',
          '发布确认',
          {
            confirmButtonText: '确定发布',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );
        
        ElMessage.success(`任务 ${task.name} 已成功发布`);
        task.status = 'published';
      } catch (error) {
        if (error !== 'cancel') {
          console.error('发布任务失败:', error);
          ElMessage.error('发布任务失败，请重试');
        }
      }
    };
    
    // 审核申请
    const reviewApplications = () => {
      router.push(`/server/tasks/${task.id}/applications`);
    };
    
    // 查看申请详情
    const viewApplicationDetail = (application) => {
      ElMessage.info(`查看申请详情：${application.id}`);
    };
    
    // 批准申请
    const approveApplication = async (application) => {
      try {
        await ElMessageBox.confirm(
          `确定要批准来自 ${shortenAddress(application.client)} 的申请吗？`,
          '批准确认',
          {
            confirmButtonText: '确定批准',
            cancelButtonText: '取消',
            type: 'info',
          }
        );
        
        // 更新申请状态
        application.status = 'approved';
        ElMessage.success('已批准申请');
      } catch (error) {
        if (error !== 'cancel') {
          console.error('批准申请失败:', error);
          ElMessage.error('批准申请失败，请重试');
        }
      }
    };
    
    // 拒绝申请
    const rejectApplication = async (application) => {
      try {
        await ElMessageBox.confirm(
          `确定要拒绝来自 ${shortenAddress(application.client)} 的申请吗？`,
          '拒绝确认',
          {
            confirmButtonText: '确定拒绝',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );
        
        // 更新申请状态
        application.status = 'rejected';
        ElMessage.success('已拒绝申请');
      } catch (error) {
        if (error !== 'cancel') {
          console.error('拒绝申请失败:', error);
          ElMessage.error('拒绝申请失败，请重试');
        }
      }
    };
    
    // 开始训练
    const startTraining = async () => {
      try {
        await ElMessageBox.confirm(
          `确定要开始此任务的训练吗？系统将使用当前已批准的 ${task.participants?.length || 0} 个客户端参与训练。`,
          '开始训练',
          {
            confirmButtonText: '开始训练',
            cancelButtonText: '取消',
            type: 'info',
          }
        );
        
        ElMessage.success(`任务 ${task.name} 已开始训练`);
        task.status = 'in_progress';
      } catch (error) {
        if (error !== 'cancel') {
          console.error('开始训练失败:', error);
          ElMessage.error('开始训练失败，请重试');
        }
      }
    };
    
    // 监控任务
    const monitorTask = () => {
      router.push(`/server/training?task=${task.id}`);
    };
    
    onMounted(() => {
      loadTaskDetail();
    });
    
    return {
      loading,
      editMode,
      task,
      applications,
      participants,
      formatDate,
      formatDateTime,
      formatTime,
      getStatusTagType,
      getStatusText,
      getApplicationStatusType,
      getApplicationStatusText,
      getParticipantStatusType,
      getParticipantStatusText,
      getRewardDistributionText,
      getAvatarUrl,
      shortenAddress,
      goBack,
      editTask,
      publishTask,
      reviewApplications,
      viewApplicationDetail,
      approveApplication,
      rejectApplication,
      startTraining,
      monitorTask
    };
  }
};
</script>

<style scoped>
.task-detail {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.page-actions {
  display: flex;
  gap: 12px;
}

.detail-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-overview {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.overview-item,
.config-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.item-label {
  font-size: 14px;
  color: #909399;
}

.item-value {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.task-description p {
  color: #606266;
  line-height: 1.6;
}

.requirements-list {
  padding-left: 20px;
  margin: 0;
}

.requirements-list li {
  margin-bottom: 8px;
  color: #606266;
}

.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.client-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.client-avatar {
  flex-shrink: 0;
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.metric-card {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 8px;
}

.metric-label {
  font-size: 14px;
  color: #606266;
}

.chart-container {
  height: 300px;
  margin-top: 24px;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border-radius: 8px;
  color: #909399;
  font-style: italic;
}
</style> 