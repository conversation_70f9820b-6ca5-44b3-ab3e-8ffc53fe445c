<!-- 客户端我的任务页面 -->
<template>
  <div class="my-tasks">
    <div class="page-header">
      <h1>我的任务</h1>
      <div class="page-actions">
        <el-button type="primary" @click="refreshData">
          <i class="fas fa-sync"></i> 刷新数据
        </el-button>
      </div>
    </div>
    
    <Breadcrumb />
    
    <!-- 任务统计卡片 -->
    <div class="stats-cards">
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.total }}</div>
        <div class="stat-label">总任务数</div>
      </el-card>
      <el-card class="stat-card status-card-active">
        <div class="stat-value">{{ statistics.active }}</div>
        <div class="stat-label">进行中</div>
      </el-card>
      <el-card class="stat-card status-card-waiting">
        <div class="stat-value">{{ statistics.waiting }}</div>
        <div class="stat-label">待开始</div>
      </el-card>
      <el-card class="stat-card status-card-completed">
        <div class="stat-value">{{ statistics.completed }}</div>
        <div class="stat-label">已完成</div>
      </el-card>
    </div>
    
    <!-- 任务列表筛选 -->
    <div class="filter-section">
      <el-radio-group v-model="statusFilter" @change="filterTasks" class="status-filter">
        <el-radio-button value="all">全部</el-radio-button>
        <el-radio-button value="active">进行中</el-radio-button>
        <el-radio-button value="waiting">待开始</el-radio-button>
        <el-radio-button value="completed">已完成</el-radio-button>
      </el-radio-group>
      
      <div class="search-box">
        <el-input v-model="searchQuery" placeholder="搜索任务名称" clearable @input="filterTasks">
          <template #prefix>
            <i class="fas fa-search"></i>
          </template>
        </el-input>
      </div>
    </div>
    
    <!-- 任务列表 -->
    <el-table
      :data="filteredTasks"
      style="width: 100%"
      v-loading="loading"
      row-key="id"
      :empty-text="emptyText"
    >
      <el-table-column prop="name" label="任务名称" min-width="200">
        <template #default="scope">
          <div class="task-name">{{ scope.row.name }}</div>
          <div class="task-sponsor">发起方: {{ scope.row.sponsor }}</div>
        </template>
      </el-table-column>
      
      <el-table-column prop="startTime" label="开始时间" width="180">
        <template #default="scope">
          <template v-if="scope.row.startTime">
            {{ formatDateTime(scope.row.startTime) }}
          </template>
          <template v-else>
            <span class="text-muted">- -</span>
          </template>
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="120">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="progress" label="进度" width="180">
        <template #default="scope">
          <el-progress 
            :percentage="scope.row.progress" 
            :status="getProgressStatus(scope.row.progress)"
          ></el-progress>
        </template>
      </el-table-column>
      
      <el-table-column prop="reward" label="奖励" width="120">
        <template #default="scope">
          {{ scope.row.reward }} 代币
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="220" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="viewTaskDetails(scope.row)" type="primary" plain>详情</el-button>
          
          <el-button 
            size="small" 
            @click="startTraining(scope.row)" 
            type="success" 
            plain
            v-if="scope.row.status === 'waiting'"
          >开始训练</el-button>
          
          <el-button 
            size="small" 
            @click="pauseTraining(scope.row)" 
            type="warning" 
            plain
            v-if="scope.row.status === 'active'"
          >暂停</el-button>
          
          <el-button 
            size="small" 
            @click="resumeTraining(scope.row)" 
            type="info" 
            plain
            v-if="scope.row.status === 'paused'"
          >继续</el-button>
          
          <el-button 
            size="small" 
            @click="viewResults(scope.row)" 
            type="success" 
            plain
            v-if="scope.row.status === 'completed'"
          >查看结果</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalTasks"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import Breadcrumb from '@/components/Breadcrumb.vue';
import { apiService, ParticipantAPI, TaskAPI } from '@/services/api';
import store from '@/store';

export default {
  name: 'MyTasks',
  components: {
    Breadcrumb
  },
  setup() {
    const router = useRouter();
    const loading = ref(false);
    const tasks = ref([]);
    const statusFilter = ref('all');
    const searchQuery = ref('');
    const currentPage = ref(1);
    const pageSize = ref(10);
    
    // 统计数据
    const statistics = reactive({
      total: 0,
      active: 0,
      waiting: 0,
      completed: 0
    });
    
    // 加载任务列表
    const loadTasks = async () => {
      loading.value = true;
      try {
        // 获取用户地址
        const userAddress = store.state.user.address || localStorage.getItem('userAddress') || '0xe56B1329f32D9c289C40054Fb20239DE52D6BbC0';
        console.log('当前用户地址:', userAddress);
        
        // 获取用户申请列表
        console.log('正在获取用户应用列表...');
        let applications = [];
        try {
          applications = await ParticipantAPI.getByClientAddress(userAddress);
          console.log('获取到的应用列表:', applications);
        } catch (appError) {
          console.error('获取应用列表失败:', appError);
          ElMessage.warning('获取参与任务列表失败，将显示本地缓存数据');
          // 继续执行，尝试获取任务列表
        }
        
        // 筛选出已批准的申请
        const approvedApplications = Array.isArray(applications) ? 
          applications.filter(app => app.status === 'approved' || app.status === 'training') : [];
        console.log('已批准的应用:', approvedApplications);
        
        // 获取所有任务
        console.log('正在获取所有任务...');
        let allTasks = [];
        try {
          allTasks = await TaskAPI.getAll();
          console.log('获取到的任务列表:', allTasks);
        } catch (taskError) {
          console.error('获取任务列表失败:', taskError);
          ElMessage.error('获取任务列表失败，请检查服务器连接');
          loading.value = false;
          return;
        }
        
        // 筛选出用户参与的任务
        tasks.value = allTasks.filter(task => {
          // 检查任务参与者是否包含当前用户
          const isParticipant = task.participants && 
            (Array.isArray(task.participants) ? 
              task.participants.includes(userAddress) : 
              task.participants === userAddress);
          
          // 检查用户是否有已批准的申请
          const hasApprovedApplication = approvedApplications.some(
            app => app.task_id.toString() === task.id.toString() || app.taskId?.toString() === task.id.toString()
          );
          
          return isParticipant || hasApprovedApplication;
        });
        
        console.log('过滤后的用户任务:', tasks.value);
        
        // 更新统计数据
        updateStatistics();
      } catch (error) {
        console.error('加载任务列表失败:', error);
        ElMessage.error('加载任务列表失败，请重试');
      } finally {
        loading.value = false;
      }
    };
    
    // 更新统计数据
    const updateStatistics = () => {
      statistics.total = tasks.value.length;
      statistics.active = tasks.value.filter(task => task.status === 'active').length;
      statistics.waiting = tasks.value.filter(task => task.status === 'waiting').length;
      statistics.completed = tasks.value.filter(task => task.status === 'completed').length;
    };
    
    // 格式化日期
    const formatDate = (date) => {
      if (!date) return '';
      if (typeof date === 'string') {
        date = new Date(date);
      }
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    };
    
    // 格式化日期时间
    const formatDateTime = (date) => {
      if (!date) return '';
      if (typeof date === 'string') {
        date = new Date(date);
      }
      
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    };
    
    // 获取状态标签类型
    const getStatusTagType = (status) => {
      const types = {
        active: 'success',
        waiting: 'info',
        paused: 'warning',
        completed: 'primary'
      };
      return types[status] || 'info';
    };
    
    // 获取状态文本
    const getStatusText = (status) => {
      const texts = {
        active: '进行中',
        waiting: '待开始',
        paused: '已暂停',
        completed: '已完成'
      };
      return texts[status] || status;
    };
    
    // 获取进度状态
    const getProgressStatus = (progress) => {
      if (progress >= 100) return 'success';
      if (progress > 0) return '';
      return 'exception';
    };
    
    // 计算过滤后的任务
    const filteredTasks = computed(() => {
      let result = tasks.value;
      
      // 状态过滤
      if (statusFilter.value !== 'all') {
        result = result.filter(task => {
          if (statusFilter.value === 'active') {
            return task.status === 'active' || task.status === 'paused';
          }
          return task.status === statusFilter.value;
        });
      }
      
      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(task => 
          task.name.toLowerCase().includes(query) || 
          (task.sponsor && task.sponsor.toLowerCase().includes(query))
        );
      }
      
      // 分页数据
      const startIndex = (currentPage.value - 1) * pageSize.value;
      const endIndex = startIndex + pageSize.value;
      return result.slice(startIndex, endIndex);
    });
    
    // 设置空状态文本
    const emptyText = computed(() => {
      if (loading.value) return '加载中...';
      if (statusFilter.value !== 'all' || searchQuery.value) {
        return '没有符合条件的任务记录';
      }
      return '您还没有参与任何任务，浏览可用任务并申请参与吧！';
    });
    
    // 计算总记录数
    const totalTasks = computed(() => {
      let result = tasks.value;
      
      // 状态过滤
      if (statusFilter.value !== 'all') {
        result = result.filter(task => {
          if (statusFilter.value === 'active') {
            return task.status === 'active' || task.status === 'paused';
          }
          return task.status === statusFilter.value;
        });
      }
      
      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(task => 
          task.name.toLowerCase().includes(query) || 
          (task.sponsor && task.sponsor.toLowerCase().includes(query))
        );
      }
      
      return result.length;
    });
    
    // 过滤任务方法
    const filterTasks = () => {
      currentPage.value = 1;  // 重置到第一页
    };
    
    // 页面大小改变
    const handleSizeChange = (size) => {
      pageSize.value = size;
    };
    
    // 当前页改变
    const handleCurrentChange = (page) => {
      currentPage.value = page;
    };
    
    // 查看任务详情
    const viewTaskDetails = (task) => {
      router.push(`/client/tasks/${task.id}`);
    };
    
    // 查看任务结果
    const viewResults = (task) => {
      router.push(`/client/tasks/${task.id}/results`);
    };
    
    // 开始训练
    const startTraining = async (task) => {
      try {
        await ElMessageBox.confirm(
          '确定要开始参与此任务的训练吗？\n\n注意：客户端节点将由服务器自动启动，无需手动运行命令。',
          '开始训练',
          {
            confirmButtonText: '开始训练',
            cancelButtonText: '取消',
            type: 'info'
          }
        );
        
        loading.value = true;
        
        // 获取用户地址
        const userAddress = store.state.user.address;
        
        ElMessage({
          type: 'info',
          message: '正在连接到已启动的客户端节点，请稍候...',
          duration: 0,
          showClose: true,
        });
        
        // 调用API参与训练
        await apiService.startClientTraining(userAddress, task.id);
        
        ElMessage.closeAll();
        
        ElMessage({
          type: 'success',
          message: '已成功连接到客户端并开始参与训练，请在训练页面查看进度',
          duration: 3000
        });
        
        // 刷新任务列表
        await loadTasks();
        
        // 导航到训练监控页面
        router.push(`/client/training?task=${task.id}`);
      } catch (error) {
        ElMessage.closeAll();
        console.error('开始训练失败:', error);
        ElMessage({
          type: 'error',
          message: error.message || '开始训练失败，请重试',
          duration: 5000
        });
      } finally {
        loading.value = false;
      }
    };
    
    // 暂停训练
    const pauseTraining = async (task) => {
      try {
        await ElMessageBox.confirm(
          `确定要暂停"${task.name}"的训练吗？`,
          '暂停训练',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );
        
        // 更新任务状态
        task.status = 'paused';
        
        // 保存状态到localStorage
        const allTasks = await apiService.getAllTasks();
        const taskIndex = allTasks.findIndex(t => t.id === task.id);
        if (taskIndex !== -1) {
          allTasks[taskIndex].status = 'paused';
          localStorage.setItem('tasks', JSON.stringify(allTasks));
        }
        
        updateStatistics();
        ElMessage.success('任务训练已暂停');
      } catch (error) {
        if (error !== 'cancel') {
          console.error('暂停训练失败:', error);
          ElMessage.error('暂停训练失败，请重试');
        }
      }
    };
    
    // 继续训练
    const resumeTraining = async (task) => {
      try {
        await ElMessageBox.confirm(
          `确定要继续"${task.name}"的训练吗？`,
          '继续训练',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
          }
        );
        
        // 更新任务状态
        task.status = 'active';
        
        // 保存状态到localStorage
        const allTasks = await apiService.getAllTasks();
        const taskIndex = allTasks.findIndex(t => t.id === task.id);
        if (taskIndex !== -1) {
          allTasks[taskIndex].status = 'active';
          localStorage.setItem('tasks', JSON.stringify(allTasks));
        }
        
        updateStatistics();
        ElMessage.success('任务训练已继续');
      } catch (error) {
        if (error !== 'cancel') {
          console.error('继续训练失败:', error);
          ElMessage.error('继续训练失败，请重试');
        }
      }
    };
    
    // 查看训练状态
    const viewTrainingStatus = (task) => {
      router.push(`/client/training?task=${task.id}`);
    };
    
    // 刷新数据
    const refreshData = () => {
      loadTasks();
      ElMessage.success('数据已刷新');
    };
    
    onMounted(() => {
      loadTasks();
    });
    
    return {
      loading,
      tasks,
      statusFilter,
      searchQuery,
      currentPage,
      pageSize,
      statistics,
      filteredTasks,
      totalTasks,
      emptyText,
      formatDate,
      formatDateTime,
      getStatusTagType,
      getStatusText,
      getProgressStatus,
      filterTasks,
      handleSizeChange,
      handleCurrentChange,
      viewTaskDetails,
      viewResults,
      startTraining,
      pauseTraining,
      resumeTraining,
      viewTrainingStatus,
      refreshData
    };
  }
};
</script>

<style scoped>
.my-tasks {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.page-actions {
  display: flex;
  gap: 12px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.status-card-active .stat-value {
  color: #67C23A;
}

.status-card-waiting .stat-value {
  color: #409EFF;
}

.status-card-completed .stat-value {
  color: #909399;
}

.filter-section {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  align-items: center;
}

.search-box {
  width: 300px;
}

.task-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.task-sponsor {
  font-size: 12px;
  color: #999;
}

.text-muted {
  color: #999;
}

.pagination {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

@media (max-width: 1200px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }
  
  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .search-box {
    width: 100%;
  }
}
</style> 