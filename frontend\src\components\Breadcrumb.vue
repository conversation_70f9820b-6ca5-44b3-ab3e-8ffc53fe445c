<!-- 面包屑导航组件 -->
<template>
  <div class="breadcrumb-container" v-if="breadcrumbs.length > 0">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="index" :to="item.path">
        {{ item.title }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script>
import { computed, watch } from 'vue';
import { useRoute } from 'vue-router';

export default {
  name: 'BreadcrumbNav',
  setup() {
    const route = useRoute();

    // 计算当前路由的面包屑路径
    const breadcrumbs = computed(() => {
      // 获取当前路由匹配的路由记录
      const matched = route.matched.filter(item => item.meta && item.meta.title);
      
      // 创建面包屑数组
      const breadcrumbs = matched.map(item => {
        return {
          path: item.path,
          title: item.meta.title
        };
      });

      return breadcrumbs;
    });

    // 监听路由变化，更新面包屑
    watch(
      () => route.path,
      () => {
        console.log('路由发生变化，更新面包屑');
      }
    );

    return {
      breadcrumbs
    };
  }
}
</script>

<style scoped>
.breadcrumb-container {
  padding: 12px var(--spacing-md);
  background-color: transparent;
  margin: 0;
  width: 100%;
  box-sizing: border-box;
}
</style> 