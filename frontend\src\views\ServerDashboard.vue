<template>
  <div class="dashboard-container content-area-clean">
    <div class="dashboard-header">
      <h1>数据交易系统服务端</h1>
      <div class="dashboard-status">
        <span class="status-label">服务状态:</span>
        <span class="status-badge" :class="{ 'success': isActive, 'warning': !isActive }" v-if="!loading">
          {{ isActive ? '运行中' : '未启动' }}
        </span>
        <span class="status-badge info" v-else>
          <i class="fas fa-sync fa-spin"></i> 加载中
        </span>
      </div>
    </div>
    
    <!-- 嵌套路由视图 -->
    <router-view class="content-area-clean"></router-view>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import apiService from '../services/api';
import web3Service from '../services/web3';

export default {
  name: 'ServerDashboard',
  setup() {
    const store = useStore();
    const isActive = ref(false);
    const loading = ref(true);
    const error = ref(null);
    const updateInterval = ref(null);
    
    // 检查服务器状态
    const checkServerStatus = async () => {
      try {
        loading.value = true;
        error.value = null;
        
        // 检查Web3服务
        try {
          // 尝试获取合约，如果失败则初始化web3服务
          web3Service.getContract();
        } catch (error) {
          console.log('需要初始化Web3服务');
          await web3Service.init();
        }
        
        // 获取链上训练状态
        try {
          const contract = web3Service.getContract();
          const isActiveTraining = await contract.methods.isTrainingActive().call();
          isActive.value = isActiveTraining;
        } catch (err) {
          console.error('获取链上训练状态失败:', err);
          // 如果链上状态检查失败，尝试通过API获取
          try {
            const serverStatus = await apiService.getServerStatus();
            isActive.value = serverStatus.isActive || false;
          } catch (apiErr) {
            console.error('获取API训练状态失败:', apiErr);
            error.value = '无法获取训练状态';
          }
        }
      } catch (err) {
        console.error('检查服务器状态失败:', err);
        error.value = err.message || '检查状态失败';
      } finally {
        loading.value = false;
      }
    };
    
    // 开始定期更新状态
    const startStatusUpdates = () => {
      // 如果已有更新间隔，先清除
      if (updateInterval.value) {
        clearInterval(updateInterval.value);
      }
      
      // 设置为每10秒更新一次
      updateInterval.value = setInterval(checkServerStatus, 10000);
    };
    
    // 停止定期更新
    const stopStatusUpdates = () => {
      if (updateInterval.value) {
        clearInterval(updateInterval.value);
        updateInterval.value = null;
      }
    };
    
    onMounted(async () => {
      await checkServerStatus();
      startStatusUpdates();
    });
    
    onUnmounted(() => {
      stopStatusUpdates();
    });
    
    return {
      isActive,
      loading,
      error
    };
  }
};
</script>

<style scoped>
/* 使用全局CSS变量 */
.dashboard-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
  position: relative;
  overflow-x: visible; /* 确保内容可见 */
  z-index: 100; /* 确保显示在最上层 */
  background: transparent; /* 背景设为透明 */
  max-width: 100%; /* 确保不超过容器宽度 */
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-lg) 0;
  margin-bottom: var(--spacing-lg);
  background-color: transparent; /* 移除背景色 */
  border-radius: var(--border-radius);
  box-shadow: none; /* 移除阴影 */
  width: 100%;
  overflow: hidden;
  z-index: 101; /* 确保在容器上层 */
}

.dashboard-header h1 {
  color: var(--primary-color);
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  padding-left: var(--spacing-sm);
}

.dashboard-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  min-width: 80px;
  max-width: 140px;
  overflow: hidden;
}

.status-label {
  color: var(--text-secondary);
  font-weight: 500;
  white-space: nowrap;
}

.status-badge {
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 90px;
}

.status-badge.success {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
}

.status-badge.warning {
  background-color: rgba(255, 193, 7, 0.1);
  color: var(--warning-color);
}

.status-badge.error {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--error-color);
}

.status-badge.info {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  background-color: rgba(0, 123, 255, 0.1);
  color: var(--primary-color);
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
  
  .dashboard-status {
    width: 100%;
    justify-content: flex-start;
  }
}
</style> 