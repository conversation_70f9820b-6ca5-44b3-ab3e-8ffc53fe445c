<template>
  <div class="metrics-component">
    <div class="component-header">
      <h3>本地模型指标</h3>
    </div>
    
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
    </div>
    
    <div v-else class="metrics-content">
      <!-- 损失值 -->
      <div class="metric-item">
        <div class="metric-icon">
          <i class="fas fa-chart-line"></i>
        </div>
        <div class="metric-info">
          <div class="metric-label">损失值</div>
          <div class="metric-value">{{ formatNumber(metrics.loss) }}</div>
        </div>
      </div>
      
      <!-- 准确率 -->
      <div class="metric-item">
        <div class="metric-icon accuracy">
          <i class="fas fa-bullseye"></i>
        </div>
        <div class="metric-info">
          <div class="metric-label">准确率</div>
          <div class="metric-value">{{ formatPercentage(metrics.accuracy) }}</div>
        </div>
      </div>
      
      <!-- 训练轮次 -->
      <div class="metric-item">
        <div class="metric-icon epochs">
          <i class="fas fa-sync-alt"></i>
        </div>
        <div class="metric-info">
          <div class="metric-label">完成轮次</div>
          <div class="metric-value">{{ metrics.epochs || 0 }}</div>
        </div>
      </div>
      
      <!-- 训练时间 -->
      <div class="metric-item">
        <div class="metric-icon time">
          <i class="fas fa-clock"></i>
        </div>
        <div class="metric-info">
          <div class="metric-label">训练时长</div>
          <div class="metric-value">{{ formatTime(metrics.trainingTime) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LocalMetrics',
  props: {
    metrics: {
      type: Object,
      default: () => ({
        loss: 0,
        accuracy: 0,
        epochs: 0,
        trainingTime: 0
      })
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  setup() {
    // 格式化数字，保留4位小数
    const formatNumber = (value) => {
      if (typeof value !== 'number') return '0.0000';
      return value.toFixed(4);
    };
    
    // 格式化百分比
    const formatPercentage = (value) => {
      if (typeof value !== 'number') return '0.00%';
      return (value * 100).toFixed(2) + '%';
    };
    
    // 格式化时间（秒转为分钟和小时）
    const formatTime = (seconds) => {
      if (!seconds || typeof seconds !== 'number') return '0秒';
      
      if (seconds < 60) {
        return Math.floor(seconds) + '秒';
      } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}分${remainingSeconds}秒`;
      } else {
        const hours = Math.floor(seconds / 3600);
        const remainingMinutes = Math.floor((seconds % 3600) / 60);
        return `${hours}小时${remainingMinutes}分`;
      }
    };
    
    return {
      formatNumber,
      formatPercentage,
      formatTime
    };
  }
};
</script>

<style scoped>
.metrics-component {
  background: white;
  border-radius: 16px;
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.component-header {
  margin-bottom: 24px;
}

.component-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
  font-weight: 600;
}

.metrics-content {
  flex-grow: 1;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
}

.metric-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.metric-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: rgba(25, 118, 210, 0.1);
  color: #1976D2;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.metric-icon i {
  font-size: 20px;
}

.metric-icon.accuracy {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.metric-icon.epochs {
  background: rgba(255, 152, 0, 0.1);
  color: #FF9800;
}

.metric-icon.time {
  background: rgba(156, 39, 176, 0.1);
  color: #9C27B0;
}

.metric-info {
  flex-grow: 1;
}

.metric-label {
  font-size: 14px;
  color: #5f6368;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.loading-container {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1976D2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .metrics-content {
    grid-template-columns: 1fr;
  }
}
</style> 