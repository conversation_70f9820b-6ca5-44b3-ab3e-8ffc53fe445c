2025-08-27 10:31:53,837 - __main__ - INFO - Using device: cuda
2025-08-27 10:31:53,837 - __main__ - INFO - Client state initialized with port 5001 and account None
2025-08-27 10:31:53,837 - __main__ - INFO - 客户端启动中...
2025-08-27 10:31:53,841 - __main__ - INFO - Starting Flower client directly (bypassing Flask server)
2025-08-27 10:31:53,842 - __main__ - INFO - Starting Flask server on port 5001
2025-08-27 10:31:55,903 - __main__ - INFO - 成功获取服务器端口: 8080
2025-08-27 10:31:55,903 - __main__ - INFO - 连接到服务器: localhost:8080
2025-08-27 10:31:56,070 - __main__ - INFO - Model structure:
2025-08-27 10:31:56,070 - __main__ - INFO - conv1.weight: torch.Size([32, 1, 5, 5])
2025-08-27 10:31:56,081 - __main__ - INFO - conv1.bias: torch.Size([32])
2025-08-27 10:31:56,081 - __main__ - INFO - bn1.weight: torch.Size([32])
2025-08-27 10:31:56,081 - __main__ - INFO - bn1.bias: torch.Size([32])
2025-08-27 10:31:56,081 - __main__ - INFO - conv2.weight: torch.Size([64, 32, 5, 5])
2025-08-27 10:31:56,081 - __main__ - INFO - conv2.bias: torch.Size([64])
2025-08-27 10:31:56,081 - __main__ - INFO - bn2.weight: torch.Size([64])
2025-08-27 10:31:56,081 - __main__ - INFO - bn2.bias: torch.Size([64])
2025-08-27 10:31:56,081 - __main__ - INFO - conv3.weight: torch.Size([128, 64, 3, 3])
2025-08-27 10:31:56,082 - __main__ - INFO - conv3.bias: torch.Size([128])
2025-08-27 10:31:56,082 - __main__ - INFO - bn3.weight: torch.Size([128])
2025-08-27 10:31:56,082 - __main__ - INFO - bn3.bias: torch.Size([128])
2025-08-27 10:31:56,082 - __main__ - INFO - fc1.weight: torch.Size([512, 128])
2025-08-27 10:31:56,082 - __main__ - INFO - fc1.bias: torch.Size([512])
2025-08-27 10:31:56,082 - __main__ - INFO - fc2.weight: torch.Size([10, 512])
2025-08-27 10:31:56,082 - __main__ - INFO - fc2.bias: torch.Size([10])
2025-08-27 10:31:56,114 - __main__ - INFO - 开始连接到服务器 localhost:8080...
2025-08-27 10:31:58,615 - __main__ - ERROR - 启动客户端失败: <_MultiThreadedRendezvous of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "failed to connect to all addresses; last error: UNAVAILABLE: ipv4:127.0.0.1:8080: ConnectEx: Connection refused (No connection could be made because the target machine actively refused it.

 -- 10061)"
	debug_error_string = "UNKNOWN:Error received from peer  {created_time:"2025-08-27T02:31:58.4244569+00:00", grpc_status:14, grpc_message:"failed to connect to all addresses; last error: UNAVAILABLE: ipv4:127.0.0.1:8080: ConnectEx: Connection refused (No connection could be made because the target machine actively refused it.\r\n -- 10061)"}"
>
2025-08-27 10:31:59,190 - __main__ - INFO - Using device: cuda
2025-08-27 10:31:59,190 - __main__ - INFO - Client state initialized with port 5001 and account None
2025-08-27 10:31:59,190 - __main__ - INFO - 客户端启动中...
2025-08-27 10:31:59,194 - __main__ - INFO - Starting Flower client directly (bypassing Flask server)
2025-08-27 10:31:59,194 - __main__ - INFO - Starting Flask server on port 5002
2025-08-27 10:32:01,246 - __main__ - INFO - 成功获取服务器端口: 8080
2025-08-27 10:32:01,247 - __main__ - INFO - 连接到服务器: localhost:8080
2025-08-27 10:32:01,370 - __main__ - INFO - Model structure:
2025-08-27 10:32:01,370 - __main__ - INFO - conv1.weight: torch.Size([32, 1, 5, 5])
2025-08-27 10:32:01,381 - __main__ - INFO - conv1.bias: torch.Size([32])
2025-08-27 10:32:01,381 - __main__ - INFO - bn1.weight: torch.Size([32])
2025-08-27 10:32:01,381 - __main__ - INFO - bn1.bias: torch.Size([32])
2025-08-27 10:32:01,381 - __main__ - INFO - conv2.weight: torch.Size([64, 32, 5, 5])
2025-08-27 10:32:01,381 - __main__ - INFO - conv2.bias: torch.Size([64])
2025-08-27 10:32:01,381 - __main__ - INFO - bn2.weight: torch.Size([64])
2025-08-27 10:32:01,381 - __main__ - INFO - bn2.bias: torch.Size([64])
2025-08-27 10:32:01,381 - __main__ - INFO - conv3.weight: torch.Size([128, 64, 3, 3])
2025-08-27 10:32:01,381 - __main__ - INFO - conv3.bias: torch.Size([128])
2025-08-27 10:32:01,381 - __main__ - INFO - bn3.weight: torch.Size([128])
2025-08-27 10:32:01,381 - __main__ - INFO - bn3.bias: torch.Size([128])
2025-08-27 10:32:01,381 - __main__ - INFO - fc1.weight: torch.Size([512, 128])
2025-08-27 10:32:01,381 - __main__ - INFO - fc1.bias: torch.Size([512])
2025-08-27 10:32:01,381 - __main__ - INFO - fc2.weight: torch.Size([10, 512])
2025-08-27 10:32:01,381 - __main__ - INFO - fc2.bias: torch.Size([10])
2025-08-27 10:32:01,418 - __main__ - INFO - 开始连接到服务器 localhost:8080...
2025-08-27 10:32:03,952 - __main__ - ERROR - 启动客户端失败: <_MultiThreadedRendezvous of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "failed to connect to all addresses; last error: UNAVAILABLE: ipv4:127.0.0.1:8080: ConnectEx: Connection refused (No connection could be made because the target machine actively refused it.

 -- 10061)"
	debug_error_string = "UNKNOWN:Error received from peer  {created_time:"2025-08-27T02:32:03.7453092+00:00", grpc_status:14, grpc_message:"failed to connect to all addresses; last error: UNAVAILABLE: ipv4:127.0.0.1:8080: ConnectEx: Connection refused (No connection could be made because the target machine actively refused it.\r\n -- 10061)"}"
>
2025-08-27 10:43:07,373 - __main__ - INFO - Using device: cuda
2025-08-27 10:43:07,373 - __main__ - INFO - Client state initialized with port 5001 and account None
2025-08-27 10:43:07,373 - __main__ - INFO - 客户端启动中...
2025-08-27 10:43:07,376 - __main__ - INFO - Starting Flower client directly (bypassing Flask server)
2025-08-27 10:43:07,377 - __main__ - INFO - Starting Flask server on port 5001
2025-08-27 10:43:09,444 - __main__ - INFO - 成功获取服务器端口: 8080
2025-08-27 10:43:09,444 - __main__ - INFO - 连接到服务器: localhost:8080
2025-08-27 10:43:09,595 - __main__ - INFO - Model structure:
2025-08-27 10:43:09,595 - __main__ - INFO - conv1.weight: torch.Size([32, 1, 5, 5])
2025-08-27 10:43:09,605 - __main__ - INFO - conv1.bias: torch.Size([32])
2025-08-27 10:43:09,605 - __main__ - INFO - bn1.weight: torch.Size([32])
2025-08-27 10:43:09,605 - __main__ - INFO - bn1.bias: torch.Size([32])
2025-08-27 10:43:09,605 - __main__ - INFO - conv2.weight: torch.Size([64, 32, 5, 5])
2025-08-27 10:43:09,605 - __main__ - INFO - conv2.bias: torch.Size([64])
2025-08-27 10:43:09,605 - __main__ - INFO - bn2.weight: torch.Size([64])
2025-08-27 10:43:09,605 - __main__ - INFO - bn2.bias: torch.Size([64])
2025-08-27 10:43:09,605 - __main__ - INFO - conv3.weight: torch.Size([128, 64, 3, 3])
2025-08-27 10:43:09,605 - __main__ - INFO - conv3.bias: torch.Size([128])
2025-08-27 10:43:09,605 - __main__ - INFO - bn3.weight: torch.Size([128])
2025-08-27 10:43:09,605 - __main__ - INFO - bn3.bias: torch.Size([128])
2025-08-27 10:43:09,606 - __main__ - INFO - fc1.weight: torch.Size([512, 128])
2025-08-27 10:43:09,606 - __main__ - INFO - fc1.bias: torch.Size([512])
2025-08-27 10:43:09,606 - __main__ - INFO - fc2.weight: torch.Size([10, 512])
2025-08-27 10:43:09,606 - __main__ - INFO - fc2.bias: torch.Size([10])
2025-08-27 10:43:09,640 - __main__ - INFO - 开始连接到服务器 localhost:8080...
2025-08-27 10:43:12,162 - __main__ - ERROR - 启动客户端失败: <_MultiThreadedRendezvous of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "failed to connect to all addresses; last error: UNAVAILABLE: ipv4:127.0.0.1:8080: ConnectEx: Connection refused (No connection could be made because the target machine actively refused it.

 -- 10061)"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"failed to connect to all addresses; last error: UNAVAILABLE: ipv4:127.0.0.1:8080: ConnectEx: Connection refused (No connection could be made because the target machine actively refused it.\r\n -- 10061)", grpc_status:14, created_time:"2025-08-27T02:43:11.9557803+00:00"}"
>
2025-08-27 10:43:12,530 - __main__ - INFO - Using device: cuda
2025-08-27 10:43:12,531 - __main__ - INFO - Client state initialized with port 5001 and account None
2025-08-27 10:43:12,531 - __main__ - INFO - 客户端启动中...
2025-08-27 10:43:12,534 - __main__ - INFO - Starting Flower client directly (bypassing Flask server)
2025-08-27 10:43:12,534 - __main__ - INFO - Starting Flask server on port 5002
2025-08-27 10:43:14,605 - __main__ - INFO - 成功获取服务器端口: 8080
2025-08-27 10:43:14,605 - __main__ - INFO - 连接到服务器: localhost:8080
2025-08-27 10:43:14,795 - __main__ - INFO - Model structure:
2025-08-27 10:43:14,795 - __main__ - INFO - conv1.weight: torch.Size([32, 1, 5, 5])
2025-08-27 10:43:14,806 - __main__ - INFO - conv1.bias: torch.Size([32])
2025-08-27 10:43:14,806 - __main__ - INFO - bn1.weight: torch.Size([32])
2025-08-27 10:43:14,806 - __main__ - INFO - bn1.bias: torch.Size([32])
2025-08-27 10:43:14,806 - __main__ - INFO - conv2.weight: torch.Size([64, 32, 5, 5])
2025-08-27 10:43:14,806 - __main__ - INFO - conv2.bias: torch.Size([64])
2025-08-27 10:43:14,806 - __main__ - INFO - bn2.weight: torch.Size([64])
2025-08-27 10:43:14,806 - __main__ - INFO - bn2.bias: torch.Size([64])
2025-08-27 10:43:14,806 - __main__ - INFO - conv3.weight: torch.Size([128, 64, 3, 3])
2025-08-27 10:43:14,807 - __main__ - INFO - conv3.bias: torch.Size([128])
2025-08-27 10:43:14,807 - __main__ - INFO - bn3.weight: torch.Size([128])
2025-08-27 10:43:14,807 - __main__ - INFO - bn3.bias: torch.Size([128])
2025-08-27 10:43:14,807 - __main__ - INFO - fc1.weight: torch.Size([512, 128])
2025-08-27 10:43:14,807 - __main__ - INFO - fc1.bias: torch.Size([512])
2025-08-27 10:43:14,807 - __main__ - INFO - fc2.weight: torch.Size([10, 512])
2025-08-27 10:43:14,807 - __main__ - INFO - fc2.bias: torch.Size([10])
2025-08-27 10:43:14,842 - __main__ - INFO - 开始连接到服务器 localhost:8080...
2025-08-27 10:43:17,367 - __main__ - ERROR - 启动客户端失败: <_MultiThreadedRendezvous of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "failed to connect to all addresses; last error: UNAVAILABLE: ipv4:127.0.0.1:8080: ConnectEx: Connection refused (No connection could be made because the target machine actively refused it.

 -- 10061)"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"failed to connect to all addresses; last error: UNAVAILABLE: ipv4:127.0.0.1:8080: ConnectEx: Connection refused (No connection could be made because the target machine actively refused it.\r\n -- 10061)", grpc_status:14, created_time:"2025-08-27T02:43:17.168814+00:00"}"
>
2025-08-27 10:58:49,891 - __main__ - INFO - Using device: cuda
2025-08-27 10:58:49,891 - __main__ - INFO - Client state initialized with port 5001 and account None
2025-08-27 10:58:49,891 - __main__ - INFO - 客户端启动中...
2025-08-27 10:58:49,894 - __main__ - INFO - Starting Flower client directly (bypassing Flask server)
2025-08-27 10:58:49,895 - __main__ - INFO - Starting Flask server on port 5001
2025-08-27 10:58:51,969 - __main__ - INFO - 成功获取服务器端口: 8080
2025-08-27 10:58:51,969 - __main__ - INFO - 连接到服务器: localhost:8080
2025-08-27 10:58:52,117 - __main__ - INFO - Model structure:
2025-08-27 10:58:52,117 - __main__ - INFO - conv1.weight: torch.Size([32, 1, 5, 5])
2025-08-27 10:58:52,117 - __main__ - INFO - conv1.bias: torch.Size([32])
2025-08-27 10:58:52,117 - __main__ - INFO - bn1.weight: torch.Size([32])
2025-08-27 10:58:52,117 - __main__ - INFO - bn1.bias: torch.Size([32])
2025-08-27 10:58:52,117 - __main__ - INFO - conv2.weight: torch.Size([64, 32, 5, 5])
2025-08-27 10:58:52,117 - __main__ - INFO - conv2.bias: torch.Size([64])
2025-08-27 10:58:52,117 - __main__ - INFO - bn2.weight: torch.Size([64])
2025-08-27 10:58:52,117 - __main__ - INFO - bn2.bias: torch.Size([64])
2025-08-27 10:58:52,117 - __main__ - INFO - conv3.weight: torch.Size([128, 64, 3, 3])
2025-08-27 10:58:52,117 - __main__ - INFO - conv3.bias: torch.Size([128])
2025-08-27 10:58:52,117 - __main__ - INFO - bn3.weight: torch.Size([128])
2025-08-27 10:58:52,117 - __main__ - INFO - bn3.bias: torch.Size([128])
2025-08-27 10:58:52,117 - __main__ - INFO - fc1.weight: torch.Size([512, 128])
2025-08-27 10:58:52,117 - __main__ - INFO - fc1.bias: torch.Size([512])
2025-08-27 10:58:52,118 - __main__ - INFO - fc2.weight: torch.Size([10, 512])
2025-08-27 10:58:52,118 - __main__ - INFO - fc2.bias: torch.Size([10])
2025-08-27 10:58:52,152 - __main__ - INFO - 开始连接到服务器 localhost:8080...
2025-08-27 10:58:54,681 - __main__ - ERROR - 启动客户端失败: <_MultiThreadedRendezvous of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "failed to connect to all addresses; last error: UNAVAILABLE: ipv4:127.0.0.1:8080: ConnectEx: Connection refused (No connection could be made because the target machine actively refused it.

 -- 10061)"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"failed to connect to all addresses; last error: UNAVAILABLE: ipv4:127.0.0.1:8080: ConnectEx: Connection refused (No connection could be made because the target machine actively refused it.\r\n -- 10061)", grpc_status:14, created_time:"2025-08-27T02:58:54.473118+00:00"}"
>
2025-08-27 10:58:55,413 - __main__ - INFO - Using device: cuda
2025-08-27 10:58:55,413 - __main__ - INFO - Client state initialized with port 5001 and account None
2025-08-27 10:58:55,414 - __main__ - INFO - 客户端启动中...
2025-08-27 10:58:55,419 - __main__ - INFO - Starting Flower client directly (bypassing Flask server)
2025-08-27 10:58:55,420 - __main__ - INFO - Starting Flask server on port 5002
2025-08-27 10:58:57,465 - __main__ - INFO - 成功获取服务器端口: 8080
2025-08-27 10:58:57,465 - __main__ - INFO - 连接到服务器: localhost:8080
2025-08-27 10:58:57,613 - __main__ - INFO - Model structure:
2025-08-27 10:58:57,614 - __main__ - INFO - conv1.weight: torch.Size([32, 1, 5, 5])
2025-08-27 10:58:57,614 - __main__ - INFO - conv1.bias: torch.Size([32])
2025-08-27 10:58:57,614 - __main__ - INFO - bn1.weight: torch.Size([32])
2025-08-27 10:58:57,614 - __main__ - INFO - bn1.bias: torch.Size([32])
2025-08-27 10:58:57,614 - __main__ - INFO - conv2.weight: torch.Size([64, 32, 5, 5])
2025-08-27 10:58:57,614 - __main__ - INFO - conv2.bias: torch.Size([64])
2025-08-27 10:58:57,614 - __main__ - INFO - bn2.weight: torch.Size([64])
2025-08-27 10:58:57,614 - __main__ - INFO - bn2.bias: torch.Size([64])
2025-08-27 10:58:57,614 - __main__ - INFO - conv3.weight: torch.Size([128, 64, 3, 3])
2025-08-27 10:58:57,614 - __main__ - INFO - conv3.bias: torch.Size([128])
2025-08-27 10:58:57,614 - __main__ - INFO - bn3.weight: torch.Size([128])
2025-08-27 10:58:57,614 - __main__ - INFO - bn3.bias: torch.Size([128])
2025-08-27 10:58:57,614 - __main__ - INFO - fc1.weight: torch.Size([512, 128])
2025-08-27 10:58:57,614 - __main__ - INFO - fc1.bias: torch.Size([512])
2025-08-27 10:58:57,614 - __main__ - INFO - fc2.weight: torch.Size([10, 512])
2025-08-27 10:58:57,614 - __main__ - INFO - fc2.bias: torch.Size([10])
2025-08-27 10:58:57,647 - __main__ - INFO - 开始连接到服务器 localhost:8080...
2025-08-27 10:59:00,165 - __main__ - ERROR - 启动客户端失败: <_MultiThreadedRendezvous of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "failed to connect to all addresses; last error: UNAVAILABLE: ipv4:127.0.0.1:8080: ConnectEx: Connection refused (No connection could be made because the target machine actively refused it.

 -- 10061)"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"failed to connect to all addresses; last error: UNAVAILABLE: ipv4:127.0.0.1:8080: ConnectEx: Connection refused (No connection could be made because the target machine actively refused it.\r\n -- 10061)", grpc_status:14, created_time:"2025-08-27T02:58:59.9714852+00:00"}"
>
2025-08-27 12:44:11,324 - __main__ - INFO - 收到训练控制请求: action=start, task_id=113
2025-08-27 12:44:11,326 - __main__ - INFO - 正在启动训练，参数：batch_size=32, learning_rate=0.0010000000474974513, epochs=1, timeout=60
2025-08-27 12:44:11,334 - __main__ - INFO - Flower客户端启动成功
2025-08-27 12:44:13,431 - __main__ - INFO - 成功获取服务器端口: 8080
2025-08-27 12:44:13,432 - __main__ - INFO - 连接到服务器: localhost:8080
2025-08-27 12:44:13,433 - __main__ - INFO - 收到训练控制请求: action=start, task_id=113
2025-08-27 12:44:13,434 - __main__ - INFO - 正在启动训练，参数：batch_size=32, learning_rate=0.0010000000474974513, epochs=1, timeout=60
2025-08-27 12:44:13,436 - __main__ - INFO - Flower客户端启动成功
2025-08-27 12:44:13,489 - __main__ - INFO - Model structure:
2025-08-27 12:44:13,490 - __main__ - INFO - conv1.weight: torch.Size([32, 1, 5, 5])
2025-08-27 12:44:13,490 - __main__ - INFO - conv1.bias: torch.Size([32])
2025-08-27 12:44:13,491 - __main__ - INFO - bn1.weight: torch.Size([32])
2025-08-27 12:44:13,491 - __main__ - INFO - bn1.bias: torch.Size([32])
2025-08-27 12:44:13,491 - __main__ - INFO - conv2.weight: torch.Size([64, 32, 5, 5])
2025-08-27 12:44:13,492 - __main__ - INFO - conv2.bias: torch.Size([64])
2025-08-27 12:44:13,492 - __main__ - INFO - bn2.weight: torch.Size([64])
2025-08-27 12:44:13,492 - __main__ - INFO - bn2.bias: torch.Size([64])
2025-08-27 12:44:13,492 - __main__ - INFO - conv3.weight: torch.Size([128, 64, 3, 3])
2025-08-27 12:44:13,493 - __main__ - INFO - conv3.bias: torch.Size([128])
2025-08-27 12:44:13,493 - __main__ - INFO - bn3.weight: torch.Size([128])
2025-08-27 12:44:13,494 - __main__ - INFO - bn3.bias: torch.Size([128])
2025-08-27 12:44:13,494 - __main__ - INFO - fc1.weight: torch.Size([512, 128])
2025-08-27 12:44:13,494 - __main__ - INFO - fc1.bias: torch.Size([512])
2025-08-27 12:44:13,494 - __main__ - INFO - fc2.weight: torch.Size([10, 512])
2025-08-27 12:44:13,494 - __main__ - INFO - fc2.bias: torch.Size([10])
2025-08-27 12:44:13,586 - __main__ - INFO - 开始连接到服务器 localhost:8080...
2025-08-27 12:44:15,496 - __main__ - INFO - 成功获取服务器端口: 8080
2025-08-27 12:44:15,496 - __main__ - INFO - 连接到服务器: localhost:8080
2025-08-27 12:44:15,539 - __main__ - INFO - Model structure:
2025-08-27 12:44:15,541 - __main__ - INFO - conv1.weight: torch.Size([32, 1, 5, 5])
2025-08-27 12:44:15,541 - __main__ - INFO - conv1.bias: torch.Size([32])
2025-08-27 12:44:15,542 - __main__ - INFO - bn1.weight: torch.Size([32])
2025-08-27 12:44:15,542 - __main__ - INFO - bn1.bias: torch.Size([32])
2025-08-27 12:44:15,542 - __main__ - INFO - conv2.weight: torch.Size([64, 32, 5, 5])
2025-08-27 12:44:15,542 - __main__ - INFO - conv2.bias: torch.Size([64])
2025-08-27 12:44:15,543 - __main__ - INFO - bn2.weight: torch.Size([64])
2025-08-27 12:44:15,543 - __main__ - INFO - bn2.bias: torch.Size([64])
2025-08-27 12:44:15,543 - __main__ - INFO - conv3.weight: torch.Size([128, 64, 3, 3])
2025-08-27 12:44:15,544 - __main__ - INFO - conv3.bias: torch.Size([128])
2025-08-27 12:44:15,544 - __main__ - INFO - bn3.weight: torch.Size([128])
2025-08-27 12:44:15,544 - __main__ - INFO - bn3.bias: torch.Size([128])
2025-08-27 12:44:15,544 - __main__ - INFO - fc1.weight: torch.Size([512, 128])
2025-08-27 12:44:15,545 - __main__ - INFO - fc1.bias: torch.Size([512])
2025-08-27 12:44:15,545 - __main__ - INFO - fc2.weight: torch.Size([10, 512])
2025-08-27 12:44:15,545 - __main__ - INFO - fc2.bias: torch.Size([10])
2025-08-27 12:44:15,638 - __main__ - INFO - 开始连接到服务器 localhost:8080...
2025-08-27 12:44:16,298 - __main__ - INFO - 开始第 1 轮训练，本地训练 5 个epoch
2025-08-27 12:44:16,305 - __main__ - INFO - 开始第 1 轮训练，本地训练 5 个epoch
2025-08-27 12:44:18,351 - __main__ - INFO - 轮次 1, Epoch 1/5, 批次 0/1875, 损失: 2.4010
2025-08-27 12:44:18,351 - __main__ - INFO - 轮次 1, Epoch 1/5, 批次 0/1875, 损失: 2.3566
2025-08-27 12:44:18,668 - __main__ - INFO - 收到训练控制请求: action=start, task_id=113
2025-08-27 12:44:18,668 - __main__ - WARNING - 客户端已经在训练中
2025-08-27 12:44:20,721 - __main__ - INFO - 收到训练控制请求: action=start, task_id=113
2025-08-27 12:44:20,721 - __main__ - WARNING - 客户端已经在训练中
2025-08-27 12:44:40,902 - __main__ - INFO - 轮次 1, Epoch 1/5, 批次 1000/1875, 损失: 0.0257
2025-08-27 12:44:40,902 - __main__ - INFO - 轮次 1, Epoch 1/5, 批次 1000/1875, 损失: 0.1084
2025-08-27 12:45:00,259 - __main__ - INFO - 轮次 1, Epoch 1/5 完成, 损失: 0.1189
2025-08-27 12:45:00,305 - __main__ - INFO - 轮次 1, Epoch 1/5 完成, 损失: 0.1201
2025-08-27 12:45:05,302 - __main__ - INFO - 轮次 1, Epoch 2/5, 批次 0/1875, 损失: 0.0118
2025-08-27 12:45:05,302 - __main__ - INFO - 轮次 1, Epoch 2/5, 批次 0/1875, 损失: 0.1743
2025-08-27 12:45:26,328 - __main__ - INFO - 轮次 1, Epoch 2/5, 批次 1000/1875, 损失: 0.3298
2025-08-27 12:45:26,328 - __main__ - INFO - 轮次 1, Epoch 2/5, 批次 1000/1875, 损失: 0.1750
2025-08-27 12:45:43,149 - __main__ - INFO - 轮次 1, Epoch 2/5 完成, 损失: 0.0463
2025-08-27 12:45:43,150 - __main__ - INFO - 轮次 1, Epoch 2/5 完成, 损失: 0.0473
2025-08-27 12:45:44,322 - __main__ - INFO - 轮次 1, Epoch 3/5, 批次 0/1875, 损失: 0.2725
2025-08-27 12:45:44,326 - __main__ - INFO - 轮次 1, Epoch 3/5, 批次 0/1875, 损失: 0.0209
2025-08-27 12:45:50,705 - __main__ - INFO - 轮次 1, Epoch 3/5, 批次 1000/1875, 损失: 0.0165
2025-08-27 12:45:50,751 - __main__ - INFO - 轮次 1, Epoch 3/5, 批次 1000/1875, 损失: 0.1389
2025-08-27 12:45:55,828 - __main__ - INFO - 轮次 1, Epoch 3/5 完成, 损失: 0.0340
2025-08-27 12:45:55,932 - __main__ - INFO - 轮次 1, Epoch 3/5 完成, 损失: 0.0323
2025-08-27 12:45:57,013 - __main__ - INFO - 轮次 1, Epoch 4/5, 批次 0/1875, 损失: 0.0015
2025-08-27 12:45:57,128 - __main__ - INFO - 轮次 1, Epoch 4/5, 批次 0/1875, 损失: 0.0003
2025-08-27 12:46:03,068 - __main__ - INFO - 轮次 1, Epoch 4/5, 批次 1000/1875, 损失: 0.0004
2025-08-27 12:46:03,294 - __main__ - INFO - 轮次 1, Epoch 4/5, 批次 1000/1875, 损失: 0.0296
2025-08-27 12:46:08,458 - __main__ - INFO - 轮次 1, Epoch 4/5 完成, 损失: 0.0254
2025-08-27 12:46:08,701 - __main__ - INFO - 轮次 1, Epoch 4/5 完成, 损失: 0.0261
2025-08-27 12:46:09,651 - __main__ - INFO - 轮次 1, Epoch 5/5, 批次 0/1875, 损失: 0.0010
2025-08-27 12:46:09,984 - __main__ - INFO - 轮次 1, Epoch 5/5, 批次 0/1875, 损失: 0.0011
2025-08-27 12:46:15,566 - __main__ - INFO - 轮次 1, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:46:15,895 - __main__ - INFO - 轮次 1, Epoch 5/5, 批次 1000/1875, 损失: 0.0004
2025-08-27 12:46:20,791 - __main__ - INFO - 轮次 1, Epoch 5/5 完成, 损失: 0.0199
2025-08-27 12:46:21,088 - __main__ - INFO - 轮次 1, Epoch 5/5 完成, 损失: 0.0206
2025-08-27 12:46:23,082 - __main__ - INFO - 本地记录训练结果 - 轮次: 1, 损失: 0.0493, 准确率: 0.9907
2025-08-27 12:46:23,082 - __main__ - INFO - 训练完成 - 轮次: 1, 损失: 0.0493, 准确率: 0.9907, 耗时: 125.69秒
2025-08-27 12:46:23,082 - __main__ - INFO - 返回训练指标: {'loss': 0.04934195329869525, 'accuracy': 0.9907, 'num_examples': 300000, 'training_time': 125.69386029243469}
2025-08-27 12:46:23,364 - __main__ - INFO - 本地记录训练结果 - 轮次: 1, 损失: 0.0488, 准确率: 0.9907
2025-08-27 12:46:23,364 - __main__ - INFO - 训练完成 - 轮次: 1, 损失: 0.0488, 准确率: 0.9907, 耗时: 125.99秒
2025-08-27 12:46:23,364 - __main__ - INFO - 返回训练指标: {'loss': 0.04883323741862555, 'accuracy': 0.9907, 'num_examples': 300000, 'training_time': 125.98999905586243}
2025-08-27 12:46:23,406 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:46:23,407 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:46:24,711 - __main__ - INFO - 评估完成 - 轮次: 1, 损失: 0.2056, 准确率: 0.9658, 样本数: 10000
2025-08-27 12:46:24,711 - __main__ - INFO - 返回评估指标: {'loss': 0.20564549363851548, 'accuracy': 0.9658, 'num_examples': 10000, 'evaluation_time': 1.3042759895324707}
2025-08-27 12:46:24,727 - __main__ - INFO - 评估完成 - 轮次: 1, 损失: 0.2056, 准确率: 0.9658, 样本数: 10000
2025-08-27 12:46:24,727 - __main__ - INFO - 返回评估指标: {'loss': 0.20564549363851548, 'accuracy': 0.9658, 'num_examples': 10000, 'evaluation_time': 1.319856882095337}
2025-08-27 12:46:24,741 - __main__ - INFO - 开始第 2 轮训练，本地训练 5 个epoch
2025-08-27 12:46:24,743 - __main__ - INFO - 开始第 2 轮训练，本地训练 5 个epoch
2025-08-27 12:46:24,751 - __main__ - INFO - 轮次 2, Epoch 1/5, 批次 0/1875, 损失: 0.1158
2025-08-27 12:46:24,752 - __main__ - INFO - 轮次 2, Epoch 1/5, 批次 0/1875, 损失: 0.2091
2025-08-27 12:46:33,774 - __main__ - INFO - 轮次 2, Epoch 1/5, 批次 1000/1875, 损失: 0.0004
2025-08-27 12:46:33,903 - __main__ - INFO - 轮次 2, Epoch 1/5, 批次 1000/1875, 损失: 0.0048
2025-08-27 12:46:40,540 - __main__ - INFO - 轮次 2, Epoch 1/5 完成, 损失: 0.0397
2025-08-27 12:46:40,679 - __main__ - INFO - 轮次 2, Epoch 1/5 完成, 损失: 0.0382
2025-08-27 12:46:41,862 - __main__ - INFO - 轮次 2, Epoch 2/5, 批次 0/1875, 损失: 0.0062
2025-08-27 12:46:42,032 - __main__ - INFO - 轮次 2, Epoch 2/5, 批次 0/1875, 损失: 0.0024
2025-08-27 12:46:47,901 - __main__ - INFO - 轮次 2, Epoch 2/5, 批次 1000/1875, 损失: 0.0082
2025-08-27 12:46:48,076 - __main__ - INFO - 轮次 2, Epoch 2/5, 批次 1000/1875, 损失: 0.0010
2025-08-27 12:46:53,269 - __main__ - INFO - 轮次 2, Epoch 2/5 完成, 损失: 0.0183
2025-08-27 12:46:53,424 - __main__ - INFO - 轮次 2, Epoch 2/5 完成, 损失: 0.0190
2025-08-27 12:46:54,451 - __main__ - INFO - 轮次 2, Epoch 3/5, 批次 0/1875, 损失: 0.0079
2025-08-27 12:46:54,616 - __main__ - INFO - 轮次 2, Epoch 3/5, 批次 0/1875, 损失: 0.0013
2025-08-27 12:47:00,241 - __main__ - INFO - 轮次 2, Epoch 3/5, 批次 1000/1875, 损失: 0.0076
2025-08-27 12:47:00,411 - __main__ - INFO - 轮次 2, Epoch 3/5, 批次 1000/1875, 损失: 0.0008
2025-08-27 12:47:05,410 - __main__ - INFO - 轮次 2, Epoch 3/5 完成, 损失: 0.0135
2025-08-27 12:47:05,545 - __main__ - INFO - 轮次 2, Epoch 3/5 完成, 损失: 0.0141
2025-08-27 12:47:06,618 - __main__ - INFO - 轮次 2, Epoch 4/5, 批次 0/1875, 损失: 0.0001
2025-08-27 12:47:06,769 - __main__ - INFO - 轮次 2, Epoch 4/5, 批次 0/1875, 损失: 0.0020
2025-08-27 12:47:12,531 - __main__ - INFO - 轮次 2, Epoch 4/5, 批次 1000/1875, 损失: 0.0003
2025-08-27 12:47:12,730 - __main__ - INFO - 轮次 2, Epoch 4/5, 批次 1000/1875, 损失: 0.0035
2025-08-27 12:47:17,628 - __main__ - INFO - 轮次 2, Epoch 4/5 完成, 损失: 0.0109
2025-08-27 12:47:17,780 - __main__ - INFO - 轮次 2, Epoch 4/5 完成, 损失: 0.0116
2025-08-27 12:47:18,786 - __main__ - INFO - 轮次 2, Epoch 5/5, 批次 0/1875, 损失: 0.0001
2025-08-27 12:47:18,944 - __main__ - INFO - 轮次 2, Epoch 5/5, 批次 0/1875, 损失: 0.0093
2025-08-27 12:47:24,584 - __main__ - INFO - 轮次 2, Epoch 5/5, 批次 1000/1875, 损失: 0.0001
2025-08-27 12:47:24,751 - __main__ - INFO - 轮次 2, Epoch 5/5, 批次 1000/1875, 损失: 0.0011
2025-08-27 12:47:29,598 - __main__ - INFO - 轮次 2, Epoch 5/5 完成, 损失: 0.0099
2025-08-27 12:47:29,767 - __main__ - INFO - 轮次 2, Epoch 5/5 完成, 损失: 0.0103
2025-08-27 12:47:31,917 - __main__ - INFO - 本地记录训练结果 - 轮次: 2, 损失: 0.0184, 准确率: 0.9911
2025-08-27 12:47:31,917 - __main__ - INFO - 训练完成 - 轮次: 2, 损失: 0.0184, 准确率: 0.9911, 耗时: 66.04秒
2025-08-27 12:47:31,917 - __main__ - INFO - 返回训练指标: {'loss': 0.01844840921303792, 'accuracy': 0.9911, 'num_examples': 300000, 'training_time': 66.03974294662476}
2025-08-27 12:47:32,073 - __main__ - INFO - 本地记录训练结果 - 轮次: 2, 损失: 0.0187, 准确率: 0.9881
2025-08-27 12:47:32,074 - __main__ - INFO - 训练完成 - 轮次: 2, 损失: 0.0187, 准确率: 0.9881, 耗时: 66.21秒
2025-08-27 12:47:32,074 - __main__ - INFO - 返回训练指标: {'loss': 0.01865375180227103, 'accuracy': 0.9881, 'num_examples': 300000, 'training_time': 66.21347784996033}
2025-08-27 12:47:32,109 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:47:32,110 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:47:33,377 - __main__ - INFO - 评估完成 - 轮次: 2, 损失: 0.0283, 准确率: 0.9935, 样本数: 10000
2025-08-27 12:47:33,377 - __main__ - INFO - 返回评估指标: {'loss': 0.028283438022942208, 'accuracy': 0.9935, 'num_examples': 10000, 'evaluation_time': 1.26881742477417}
2025-08-27 12:47:33,380 - __main__ - INFO - 评估完成 - 轮次: 2, 损失: 0.0283, 准确率: 0.9935, 样本数: 10000
2025-08-27 12:47:33,380 - __main__ - INFO - 返回评估指标: {'loss': 0.028283438022942208, 'accuracy': 0.9935, 'num_examples': 10000, 'evaluation_time': 1.270867109298706}
2025-08-27 12:47:33,392 - __main__ - INFO - 开始第 3 轮训练，本地训练 5 个epoch
2025-08-27 12:47:33,394 - __main__ - INFO - 开始第 3 轮训练，本地训练 5 个epoch
2025-08-27 12:47:33,401 - __main__ - INFO - 轮次 3, Epoch 1/5, 批次 0/1875, 损失: 0.0025
2025-08-27 12:47:33,402 - __main__ - INFO - 轮次 3, Epoch 1/5, 批次 0/1875, 损失: 0.0019
2025-08-27 12:47:41,398 - __main__ - INFO - 轮次 3, Epoch 1/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:47:41,444 - __main__ - INFO - 轮次 3, Epoch 1/5, 批次 1000/1875, 损失: 0.0025
2025-08-27 12:47:48,449 - __main__ - INFO - 轮次 3, Epoch 1/5 完成, 损失: 0.0125
2025-08-27 12:47:48,562 - __main__ - INFO - 轮次 3, Epoch 1/5 完成, 损失: 0.0134
2025-08-27 12:47:49,872 - __main__ - INFO - 轮次 3, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:47:49,977 - __main__ - INFO - 轮次 3, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:47:56,034 - __main__ - INFO - 轮次 3, Epoch 2/5, 批次 1000/1875, 损失: 0.0001
2025-08-27 12:47:56,215 - __main__ - INFO - 轮次 3, Epoch 2/5, 批次 1000/1875, 损失: 0.0087
2025-08-27 12:48:01,056 - __main__ - INFO - 轮次 3, Epoch 2/5 完成, 损失: 0.0055
2025-08-27 12:48:01,214 - __main__ - INFO - 轮次 3, Epoch 2/5 完成, 损失: 0.0057
2025-08-27 12:48:02,270 - __main__ - INFO - 轮次 3, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:48:02,481 - __main__ - INFO - 轮次 3, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:48:08,012 - __main__ - INFO - 轮次 3, Epoch 3/5, 批次 1000/1875, 损失: 0.0001
2025-08-27 12:48:08,235 - __main__ - INFO - 轮次 3, Epoch 3/5, 批次 1000/1875, 损失: 0.0002
2025-08-27 12:48:13,222 - __main__ - INFO - 轮次 3, Epoch 3/5 完成, 损失: 0.0056
2025-08-27 12:48:13,530 - __main__ - INFO - 轮次 3, Epoch 3/5 完成, 损失: 0.0048
2025-08-27 12:48:14,396 - __main__ - INFO - 轮次 3, Epoch 4/5, 批次 0/1875, 损失: 0.0026
2025-08-27 12:48:14,667 - __main__ - INFO - 轮次 3, Epoch 4/5, 批次 0/1875, 损失: 0.0001
2025-08-27 12:48:20,159 - __main__ - INFO - 轮次 3, Epoch 4/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:48:20,470 - __main__ - INFO - 轮次 3, Epoch 4/5, 批次 1000/1875, 损失: 0.0622
2025-08-27 12:48:25,275 - __main__ - INFO - 轮次 3, Epoch 4/5 完成, 损失: 0.0048
2025-08-27 12:48:25,646 - __main__ - INFO - 轮次 3, Epoch 4/5 完成, 损失: 0.0047
2025-08-27 12:48:26,401 - __main__ - INFO - 轮次 3, Epoch 5/5, 批次 0/1875, 损失: 0.1041
2025-08-27 12:48:26,837 - __main__ - INFO - 轮次 3, Epoch 5/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:48:32,143 - __main__ - INFO - 轮次 3, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:48:32,618 - __main__ - INFO - 轮次 3, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:48:37,278 - __main__ - INFO - 轮次 3, Epoch 5/5 完成, 损失: 0.0036
2025-08-27 12:48:37,879 - __main__ - INFO - 轮次 3, Epoch 5/5 完成, 损失: 0.0040
2025-08-27 12:48:39,670 - __main__ - INFO - 本地记录训练结果 - 轮次: 3, 损失: 0.0064, 准确率: 0.9930
2025-08-27 12:48:39,670 - __main__ - INFO - 训练完成 - 轮次: 3, 损失: 0.0064, 准确率: 0.9930, 耗时: 65.16秒
2025-08-27 12:48:39,670 - __main__ - INFO - 返回训练指标: {'loss': 0.006403973445430024, 'accuracy': 0.993, 'num_examples': 300000, 'training_time': 65.16285562515259}
2025-08-27 12:48:40,084 - __main__ - INFO - 本地记录训练结果 - 轮次: 3, 损失: 0.0065, 准确率: 0.9920
2025-08-27 12:48:40,084 - __main__ - INFO - 训练完成 - 轮次: 3, 损失: 0.0065, 准确率: 0.9920, 耗时: 65.60秒
2025-08-27 12:48:40,084 - __main__ - INFO - 返回训练指标: {'loss': 0.006493621828693018, 'accuracy': 0.992, 'num_examples': 300000, 'training_time': 65.59729814529419}
2025-08-27 12:48:40,131 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:48:40,133 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:48:41,356 - __main__ - INFO - 评估完成 - 轮次: 3, 损失: 0.0288, 准确率: 0.9946, 样本数: 10000
2025-08-27 12:48:41,356 - __main__ - INFO - 返回评估指标: {'loss': 0.02876018466074146, 'accuracy': 0.9946, 'num_examples': 10000, 'evaluation_time': 1.2238984107971191}
2025-08-27 12:48:41,370 - __main__ - INFO - 评估完成 - 轮次: 3, 损失: 0.0288, 准确率: 0.9946, 样本数: 10000
2025-08-27 12:48:41,370 - __main__ - INFO - 返回评估指标: {'loss': 0.02876018466074146, 'accuracy': 0.9946, 'num_examples': 10000, 'evaluation_time': 1.2394421100616455}
2025-08-27 12:48:41,389 - __main__ - INFO - 开始第 4 轮训练，本地训练 5 个epoch
2025-08-27 12:48:41,389 - __main__ - INFO - 开始第 4 轮训练，本地训练 5 个epoch
2025-08-27 12:48:41,397 - __main__ - INFO - 轮次 4, Epoch 1/5, 批次 0/1875, 损失: 0.0007
2025-08-27 12:48:41,397 - __main__ - INFO - 轮次 4, Epoch 1/5, 批次 0/1875, 损失: 0.0004
2025-08-27 12:48:49,312 - __main__ - INFO - 轮次 4, Epoch 1/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:48:49,358 - __main__ - INFO - 轮次 4, Epoch 1/5, 批次 1000/1875, 损失: 0.0223
2025-08-27 12:48:57,194 - __main__ - INFO - 轮次 4, Epoch 1/5 完成, 损失: 0.0062
2025-08-27 12:48:57,294 - __main__ - INFO - 轮次 4, Epoch 1/5 完成, 损失: 0.0062
2025-08-27 12:48:58,611 - __main__ - INFO - 轮次 4, Epoch 2/5, 批次 0/1875, 损失: 0.0001
2025-08-27 12:48:58,719 - __main__ - INFO - 轮次 4, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:49:04,764 - __main__ - INFO - 轮次 4, Epoch 2/5, 批次 1000/1875, 损失: 0.0004
2025-08-27 12:49:04,927 - __main__ - INFO - 轮次 4, Epoch 2/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:49:10,075 - __main__ - INFO - 轮次 4, Epoch 2/5 完成, 损失: 0.0014
2025-08-27 12:49:10,306 - __main__ - INFO - 轮次 4, Epoch 2/5 完成, 损失: 0.0019
2025-08-27 12:49:11,291 - __main__ - INFO - 轮次 4, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:49:11,529 - __main__ - INFO - 轮次 4, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:49:17,218 - __main__ - INFO - 轮次 4, Epoch 3/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:49:17,526 - __main__ - INFO - 轮次 4, Epoch 3/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:49:22,287 - __main__ - INFO - 轮次 4, Epoch 3/5 完成, 损失: 0.0023
2025-08-27 12:49:22,709 - __main__ - INFO - 轮次 4, Epoch 3/5 完成, 损失: 0.0017
2025-08-27 12:49:23,488 - __main__ - INFO - 轮次 4, Epoch 4/5, 批次 0/1875, 损失: 0.0047
2025-08-27 12:49:23,888 - __main__ - INFO - 轮次 4, Epoch 4/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:49:29,236 - __main__ - INFO - 轮次 4, Epoch 4/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:49:29,700 - __main__ - INFO - 轮次 4, Epoch 4/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:49:34,140 - __main__ - INFO - 轮次 4, Epoch 4/5 完成, 损失: 0.0014
2025-08-27 12:49:34,712 - __main__ - INFO - 轮次 4, Epoch 4/5 完成, 损失: 0.0024
2025-08-27 12:49:35,245 - __main__ - INFO - 轮次 4, Epoch 5/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:49:35,868 - __main__ - INFO - 轮次 4, Epoch 5/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:49:41,012 - __main__ - INFO - 轮次 4, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:49:41,691 - __main__ - INFO - 轮次 4, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:49:46,074 - __main__ - INFO - 轮次 4, Epoch 5/5 完成, 损失: 0.0026
2025-08-27 12:49:46,724 - __main__ - INFO - 轮次 4, Epoch 5/5 完成, 损失: 0.0023
2025-08-27 12:49:48,384 - __main__ - INFO - 本地记录训练结果 - 轮次: 4, 损失: 0.0028, 准确率: 0.9923
2025-08-27 12:49:48,384 - __main__ - INFO - 训练完成 - 轮次: 4, 损失: 0.0028, 准确率: 0.9923, 耗时: 65.81秒
2025-08-27 12:49:48,384 - __main__ - INFO - 返回训练指标: {'loss': 0.0027904902932192966, 'accuracy': 0.9923, 'num_examples': 300000, 'training_time': 65.81011867523193}
2025-08-27 12:49:48,996 - __main__ - INFO - 本地记录训练结果 - 轮次: 4, 损失: 0.0029, 准确率: 0.9944
2025-08-27 12:49:48,996 - __main__ - INFO - 训练完成 - 轮次: 4, 损失: 0.0029, 准确率: 0.9944, 耗时: 66.48秒
2025-08-27 12:49:48,996 - __main__ - INFO - 返回训练指标: {'loss': 0.0029097710036022446, 'accuracy': 0.9944, 'num_examples': 300000, 'training_time': 66.48057103157043}
2025-08-27 12:49:49,044 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:49:49,044 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:49:50,346 - __main__ - INFO - 评估完成 - 轮次: 4, 损失: 0.0329, 准确率: 0.9946, 样本数: 10000
2025-08-27 12:49:50,346 - __main__ - INFO - 返回评估指标: {'loss': 0.03292133901946804, 'accuracy': 0.9946, 'num_examples': 10000, 'evaluation_time': 1.301833152770996}
2025-08-27 12:49:50,349 - __main__ - INFO - 评估完成 - 轮次: 4, 损失: 0.0329, 准确率: 0.9946, 样本数: 10000
2025-08-27 12:49:50,349 - __main__ - INFO - 返回评估指标: {'loss': 0.03292133901946804, 'accuracy': 0.9946, 'num_examples': 10000, 'evaluation_time': 1.3051955699920654}
2025-08-27 12:49:50,363 - __main__ - INFO - 开始第 5 轮训练，本地训练 5 个epoch
2025-08-27 12:49:50,365 - __main__ - INFO - 开始第 5 轮训练，本地训练 5 个epoch
2025-08-27 12:49:50,373 - __main__ - INFO - 轮次 5, Epoch 1/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:49:50,374 - __main__ - INFO - 轮次 5, Epoch 1/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:49:58,985 - __main__ - INFO - 轮次 5, Epoch 1/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:49:59,042 - __main__ - INFO - 轮次 5, Epoch 1/5, 批次 1000/1875, 损失: 0.0470
2025-08-27 12:50:05,490 - __main__ - INFO - 轮次 5, Epoch 1/5 完成, 损失: 0.0032
2025-08-27 12:50:05,566 - __main__ - INFO - 轮次 5, Epoch 1/5 完成, 损失: 0.0038
2025-08-27 12:50:06,760 - __main__ - INFO - 轮次 5, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:50:06,840 - __main__ - INFO - 轮次 5, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:50:16,756 - __main__ - INFO - 轮次 5, Epoch 2/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:50:16,816 - __main__ - INFO - 轮次 5, Epoch 2/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:50:21,826 - __main__ - INFO - 轮次 5, Epoch 2/5 完成, 损失: 0.0002
2025-08-27 12:50:21,872 - __main__ - INFO - 轮次 5, Epoch 2/5 完成, 损失: 0.0002
2025-08-27 12:50:23,003 - __main__ - INFO - 轮次 5, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:50:23,048 - __main__ - INFO - 轮次 5, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:50:28,694 - __main__ - INFO - 轮次 5, Epoch 3/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:50:28,729 - __main__ - INFO - 轮次 5, Epoch 3/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:50:33,743 - __main__ - INFO - 轮次 5, Epoch 3/5 完成, 损失: 0.0003
2025-08-27 12:50:33,750 - __main__ - INFO - 轮次 5, Epoch 3/5 完成, 损失: 0.0011
2025-08-27 12:50:34,931 - __main__ - INFO - 轮次 5, Epoch 4/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:50:34,949 - __main__ - INFO - 轮次 5, Epoch 4/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:50:40,671 - __main__ - INFO - 轮次 5, Epoch 4/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:50:40,777 - __main__ - INFO - 轮次 5, Epoch 4/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:50:45,629 - __main__ - INFO - 轮次 5, Epoch 4/5 完成, 损失: 0.0003
2025-08-27 12:50:45,750 - __main__ - INFO - 轮次 5, Epoch 4/5 完成, 损失: 0.0001
2025-08-27 12:50:46,815 - __main__ - INFO - 轮次 5, Epoch 5/5, 批次 0/1875, 损失: 0.1536
2025-08-27 12:50:46,924 - __main__ - INFO - 轮次 5, Epoch 5/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:50:52,682 - __main__ - INFO - 轮次 5, Epoch 5/5, 批次 1000/1875, 损失: 0.0170
2025-08-27 12:50:52,823 - __main__ - INFO - 轮次 5, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:50:57,803 - __main__ - INFO - 轮次 5, Epoch 5/5 完成, 损失: 0.0015
2025-08-27 12:50:57,982 - __main__ - INFO - 轮次 5, Epoch 5/5 完成, 损失: 0.0000
2025-08-27 12:51:00,132 - __main__ - INFO - 本地记录训练结果 - 轮次: 5, 损失: 0.0012, 准确率: 0.9950
2025-08-27 12:51:00,132 - __main__ - INFO - 训练完成 - 轮次: 5, 损失: 0.0012, 准确率: 0.9950, 耗时: 68.57秒
2025-08-27 12:51:00,132 - __main__ - INFO - 返回训练指标: {'loss': 0.001204669571270119, 'accuracy': 0.995, 'num_examples': 300000, 'training_time': 68.56614232063293}
2025-08-27 12:51:00,304 - __main__ - INFO - 本地记录训练结果 - 轮次: 5, 损失: 0.0009, 准确率: 0.9948
2025-08-27 12:51:00,304 - __main__ - INFO - 训练完成 - 轮次: 5, 损失: 0.0009, 准确率: 0.9948, 耗时: 68.75秒
2025-08-27 12:51:00,304 - __main__ - INFO - 返回训练指标: {'loss': 0.0009320935452521326, 'accuracy': 0.9948, 'num_examples': 300000, 'training_time': 68.75221109390259}
2025-08-27 12:51:00,352 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:51:00,354 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:51:01,695 - __main__ - INFO - 评估完成 - 轮次: 5, 损失: 0.0333, 准确率: 0.9948, 样本数: 10000
2025-08-27 12:51:01,696 - __main__ - INFO - 返回评估指标: {'loss': 0.03325242406846852, 'accuracy': 0.9948, 'num_examples': 10000, 'evaluation_time': 1.3437526226043701}
2025-08-27 12:51:01,703 - __main__ - INFO - 评估完成 - 轮次: 5, 损失: 0.0333, 准确率: 0.9948, 样本数: 10000
2025-08-27 12:51:01,703 - __main__ - INFO - 返回评估指标: {'loss': 0.03325242406846852, 'accuracy': 0.9948, 'num_examples': 10000, 'evaluation_time': 1.3475139141082764}
2025-08-27 12:51:01,717 - __main__ - INFO - 开始第 6 轮训练，本地训练 5 个epoch
2025-08-27 12:51:01,718 - __main__ - INFO - 开始第 6 轮训练，本地训练 5 个epoch
2025-08-27 12:51:01,725 - __main__ - INFO - 轮次 6, Epoch 1/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:51:01,726 - __main__ - INFO - 轮次 6, Epoch 1/5, 批次 0/1875, 损失: 0.0019
2025-08-27 12:51:10,677 - __main__ - INFO - 轮次 6, Epoch 1/5, 批次 1000/1875, 损失: 0.0001
2025-08-27 12:51:10,819 - __main__ - INFO - 轮次 6, Epoch 1/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:51:16,910 - __main__ - INFO - 轮次 6, Epoch 1/5 完成, 损失: 0.0021
2025-08-27 12:51:16,971 - __main__ - INFO - 轮次 6, Epoch 1/5 完成, 损失: 0.0030
2025-08-27 12:51:18,088 - __main__ - INFO - 轮次 6, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:51:18,166 - __main__ - INFO - 轮次 6, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:51:23,842 - __main__ - INFO - 轮次 6, Epoch 2/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:51:23,930 - __main__ - INFO - 轮次 6, Epoch 2/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:51:28,780 - __main__ - INFO - 轮次 6, Epoch 2/5 完成, 损失: 0.0000
2025-08-27 12:51:28,916 - __main__ - INFO - 轮次 6, Epoch 2/5 完成, 损失: 0.0000
2025-08-27 12:51:29,944 - __main__ - INFO - 轮次 6, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:51:30,103 - __main__ - INFO - 轮次 6, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:51:35,603 - __main__ - INFO - 轮次 6, Epoch 3/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:51:35,807 - __main__ - INFO - 轮次 6, Epoch 3/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:51:40,838 - __main__ - INFO - 轮次 6, Epoch 3/5 完成, 损失: 0.0000
2025-08-27 12:51:41,204 - __main__ - INFO - 轮次 6, Epoch 3/5 完成, 损失: 0.0001
2025-08-27 12:51:42,098 - __main__ - INFO - 轮次 6, Epoch 4/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:51:42,385 - __main__ - INFO - 轮次 6, Epoch 4/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:51:47,843 - __main__ - INFO - 轮次 6, Epoch 4/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:51:48,161 - __main__ - INFO - 轮次 6, Epoch 4/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:51:53,144 - __main__ - INFO - 轮次 6, Epoch 4/5 完成, 损失: 0.0000
2025-08-27 12:51:53,616 - __main__ - INFO - 轮次 6, Epoch 4/5 完成, 损失: 0.0001
2025-08-27 12:51:54,406 - __main__ - INFO - 轮次 6, Epoch 5/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:51:54,747 - __main__ - INFO - 轮次 6, Epoch 5/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:52:00,370 - __main__ - INFO - 轮次 6, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:52:00,920 - __main__ - INFO - 轮次 6, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:52:05,833 - __main__ - INFO - 轮次 6, Epoch 5/5 完成, 损失: 0.0004
2025-08-27 12:52:06,404 - __main__ - INFO - 轮次 6, Epoch 5/5 完成, 损失: 0.0000
2025-08-27 12:52:08,490 - __main__ - INFO - 本地记录训练结果 - 轮次: 6, 损失: 0.0005, 准确率: 0.9932
2025-08-27 12:52:08,490 - __main__ - INFO - 训练完成 - 轮次: 6, 损失: 0.0005, 准确率: 0.9932, 耗时: 65.43秒
2025-08-27 12:52:08,491 - __main__ - INFO - 返回训练指标: {'loss': 0.0005097455482091999, 'accuracy': 0.9932, 'num_examples': 300000, 'training_time': 65.4277856349945}
2025-08-27 12:52:08,947 - __main__ - INFO - 本地记录训练结果 - 轮次: 6, 损失: 0.0007, 准确率: 0.9946
2025-08-27 12:52:08,947 - __main__ - INFO - 训练完成 - 轮次: 6, 损失: 0.0007, 准确率: 0.9946, 耗时: 66.03秒
2025-08-27 12:52:08,947 - __main__ - INFO - 返回训练指标: {'loss': 0.0006631233427371823, 'accuracy': 0.9946, 'num_examples': 300000, 'training_time': 66.02891898155212}
2025-08-27 12:52:08,994 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:52:08,994 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:52:10,358 - __main__ - INFO - 评估完成 - 轮次: 6, 损失: 0.0404, 准确率: 0.9945, 样本数: 10000
2025-08-27 12:52:10,358 - __main__ - INFO - 返回评估指标: {'loss': 0.04039657207103374, 'accuracy': 0.9945, 'num_examples': 10000, 'evaluation_time': 1.3639569282531738}
2025-08-27 12:52:10,366 - __main__ - INFO - 评估完成 - 轮次: 6, 损失: 0.0404, 准确率: 0.9945, 样本数: 10000
2025-08-27 12:52:10,366 - __main__ - INFO - 返回评估指标: {'loss': 0.04039657207103374, 'accuracy': 0.9945, 'num_examples': 10000, 'evaluation_time': 1.3724100589752197}
2025-08-27 12:52:10,380 - __main__ - INFO - 开始第 7 轮训练，本地训练 5 个epoch
2025-08-27 12:52:10,382 - __main__ - INFO - 开始第 7 轮训练，本地训练 5 个epoch
2025-08-27 12:52:10,388 - __main__ - INFO - 轮次 7, Epoch 1/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:52:10,390 - __main__ - INFO - 轮次 7, Epoch 1/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:52:19,114 - __main__ - INFO - 轮次 7, Epoch 1/5, 批次 1000/1875, 损失: 0.0001
2025-08-27 12:52:19,182 - __main__ - INFO - 轮次 7, Epoch 1/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:52:25,459 - __main__ - INFO - 轮次 7, Epoch 1/5 完成, 损失: 0.0019
2025-08-27 12:52:25,584 - __main__ - INFO - 轮次 7, Epoch 1/5 完成, 损失: 0.0021
2025-08-27 12:52:26,659 - __main__ - INFO - 轮次 7, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:52:26,788 - __main__ - INFO - 轮次 7, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:52:32,450 - __main__ - INFO - 轮次 7, Epoch 2/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:52:32,597 - __main__ - INFO - 轮次 7, Epoch 2/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:52:37,476 - __main__ - INFO - 轮次 7, Epoch 2/5 完成, 损失: 0.0000
2025-08-27 12:52:37,686 - __main__ - INFO - 轮次 7, Epoch 2/5 完成, 损失: 0.0000
2025-08-27 12:52:38,627 - __main__ - INFO - 轮次 7, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:52:38,827 - __main__ - INFO - 轮次 7, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:52:44,324 - __main__ - INFO - 轮次 7, Epoch 3/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:52:44,563 - __main__ - INFO - 轮次 7, Epoch 3/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:52:49,402 - __main__ - INFO - 轮次 7, Epoch 3/5 完成, 损失: 0.0000
2025-08-27 12:52:49,652 - __main__ - INFO - 轮次 7, Epoch 3/5 完成, 损失: 0.0000
2025-08-27 12:52:50,569 - __main__ - INFO - 轮次 7, Epoch 4/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:52:50,858 - __main__ - INFO - 轮次 7, Epoch 4/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:52:56,575 - __main__ - INFO - 轮次 7, Epoch 4/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:52:56,907 - __main__ - INFO - 轮次 7, Epoch 4/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:53:01,771 - __main__ - INFO - 轮次 7, Epoch 4/5 完成, 损失: 0.0000
2025-08-27 12:53:02,098 - __main__ - INFO - 轮次 7, Epoch 4/5 完成, 损失: 0.0000
2025-08-27 12:53:02,999 - __main__ - INFO - 轮次 7, Epoch 5/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:53:03,284 - __main__ - INFO - 轮次 7, Epoch 5/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:53:09,024 - __main__ - INFO - 轮次 7, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:53:09,400 - __main__ - INFO - 轮次 7, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:53:14,583 - __main__ - INFO - 轮次 7, Epoch 5/5 完成, 损失: 0.0000
2025-08-27 12:53:14,853 - __main__ - INFO - 轮次 7, Epoch 5/5 完成, 损失: 0.0000
2025-08-27 12:53:16,875 - __main__ - INFO - 本地记录训练结果 - 轮次: 7, 损失: 0.0004, 准确率: 0.9952
2025-08-27 12:53:16,875 - __main__ - INFO - 训练完成 - 轮次: 7, 损失: 0.0004, 准确率: 0.9952, 耗时: 65.34秒
2025-08-27 12:53:16,875 - __main__ - INFO - 返回训练指标: {'loss': 0.000375395440224876, 'accuracy': 0.9952, 'num_examples': 300000, 'training_time': 65.33603549003601}
2025-08-27 12:53:17,118 - __main__ - INFO - 本地记录训练结果 - 轮次: 7, 损失: 0.0004, 准确率: 0.9951
2025-08-27 12:53:17,119 - __main__ - INFO - 训练完成 - 轮次: 7, 损失: 0.0004, 准确率: 0.9951, 耗时: 65.58秒
2025-08-27 12:53:17,119 - __main__ - INFO - 返回训练指标: {'loss': 0.0004157510538100288, 'accuracy': 0.9951, 'num_examples': 300000, 'training_time': 65.5837893486023}
2025-08-27 12:53:17,172 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:53:17,173 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:53:18,461 - __main__ - INFO - 评估完成 - 轮次: 7, 损失: 0.0344, 准确率: 0.9952, 样本数: 10000
2025-08-27 12:53:18,461 - __main__ - INFO - 返回评估指标: {'loss': 0.0344046698376677, 'accuracy': 0.9952, 'num_examples': 10000, 'evaluation_time': 1.288649559020996}
2025-08-27 12:53:18,484 - __main__ - INFO - 评估完成 - 轮次: 7, 损失: 0.0344, 准确率: 0.9952, 样本数: 10000
2025-08-27 12:53:18,484 - __main__ - INFO - 返回评估指标: {'loss': 0.0344046698376677, 'accuracy': 0.9952, 'num_examples': 10000, 'evaluation_time': 1.3116798400878906}
2025-08-27 12:53:18,500 - __main__ - INFO - 开始第 8 轮训练，本地训练 5 个epoch
2025-08-27 12:53:18,503 - __main__ - INFO - 开始第 8 轮训练，本地训练 5 个epoch
2025-08-27 12:53:18,509 - __main__ - INFO - 轮次 8, Epoch 1/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:53:18,510 - __main__ - INFO - 轮次 8, Epoch 1/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:53:26,807 - __main__ - INFO - 轮次 8, Epoch 1/5, 批次 1000/1875, 损失: 0.0004
2025-08-27 12:53:26,820 - __main__ - INFO - 轮次 8, Epoch 1/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:53:33,045 - __main__ - INFO - 轮次 8, Epoch 1/5 完成, 损失: 0.0010
2025-08-27 12:53:33,122 - __main__ - INFO - 轮次 8, Epoch 1/5 完成, 损失: 0.0008
2025-08-27 12:53:34,169 - __main__ - INFO - 轮次 8, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:53:34,246 - __main__ - INFO - 轮次 8, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:53:39,971 - __main__ - INFO - 轮次 8, Epoch 2/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:53:40,075 - __main__ - INFO - 轮次 8, Epoch 2/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:53:44,896 - __main__ - INFO - 轮次 8, Epoch 2/5 完成, 损失: 0.0000
2025-08-27 12:53:45,106 - __main__ - INFO - 轮次 8, Epoch 2/5 完成, 损失: 0.0000
2025-08-27 12:53:46,021 - __main__ - INFO - 轮次 8, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:53:46,277 - __main__ - INFO - 轮次 8, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:53:51,653 - __main__ - INFO - 轮次 8, Epoch 3/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:53:51,938 - __main__ - INFO - 轮次 8, Epoch 3/5, 批次 1000/1875, 损失: 0.0001
2025-08-27 12:53:56,643 - __main__ - INFO - 轮次 8, Epoch 3/5 完成, 损失: 0.0000
2025-08-27 12:53:56,969 - __main__ - INFO - 轮次 8, Epoch 3/5 完成, 损失: 0.0000
2025-08-27 12:53:57,810 - __main__ - INFO - 轮次 8, Epoch 4/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:53:58,097 - __main__ - INFO - 轮次 8, Epoch 4/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:54:03,490 - __main__ - INFO - 轮次 8, Epoch 4/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:54:03,864 - __main__ - INFO - 轮次 8, Epoch 4/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:54:08,789 - __main__ - INFO - 轮次 8, Epoch 4/5 完成, 损失: 0.0000
2025-08-27 12:54:09,229 - __main__ - INFO - 轮次 8, Epoch 4/5 完成, 损失: 0.0000
2025-08-27 12:54:09,981 - __main__ - INFO - 轮次 8, Epoch 5/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:54:10,460 - __main__ - INFO - 轮次 8, Epoch 5/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:54:15,863 - __main__ - INFO - 轮次 8, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:54:16,397 - __main__ - INFO - 轮次 8, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:54:20,943 - __main__ - INFO - 轮次 8, Epoch 5/5 完成, 损失: 0.0000
2025-08-27 12:54:21,453 - __main__ - INFO - 轮次 8, Epoch 5/5 完成, 损失: 0.0000
2025-08-27 12:54:23,365 - __main__ - INFO - 本地记录训练结果 - 轮次: 8, 损失: 0.0002, 准确率: 0.9951
2025-08-27 12:54:23,365 - __main__ - INFO - 训练完成 - 轮次: 8, 损失: 0.0002, 准确率: 0.9951, 耗时: 63.68秒
2025-08-27 12:54:23,366 - __main__ - INFO - 返回训练指标: {'loss': 0.00020834158050407058, 'accuracy': 0.9951, 'num_examples': 300000, 'training_time': 63.67802667617798}
2025-08-27 12:54:23,799 - __main__ - INFO - 本地记录训练结果 - 轮次: 8, 损失: 0.0002, 准确率: 0.9952
2025-08-27 12:54:23,799 - __main__ - INFO - 训练完成 - 轮次: 8, 损失: 0.0002, 准确率: 0.9952, 耗时: 64.15秒
2025-08-27 12:54:23,799 - __main__ - INFO - 返回训练指标: {'loss': 0.00016879051307493764, 'accuracy': 0.9952, 'num_examples': 300000, 'training_time': 64.1508572101593}
2025-08-27 12:54:23,848 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:54:23,850 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:54:25,132 - __main__ - INFO - 评估完成 - 轮次: 8, 损失: 0.0344, 准确率: 0.9956, 样本数: 10000
2025-08-27 12:54:25,132 - __main__ - INFO - 返回评估指标: {'loss': 0.03441110933927215, 'accuracy': 0.9956, 'num_examples': 10000, 'evaluation_time': 1.284797191619873}
2025-08-27 12:54:25,163 - __main__ - INFO - 评估完成 - 轮次: 8, 损失: 0.0344, 准确率: 0.9956, 样本数: 10000
2025-08-27 12:54:25,163 - __main__ - INFO - 返回评估指标: {'loss': 0.03441110933927215, 'accuracy': 0.9956, 'num_examples': 10000, 'evaluation_time': 1.3133673667907715}
2025-08-27 12:54:25,178 - __main__ - INFO - 开始第 9 轮训练，本地训练 5 个epoch
2025-08-27 12:54:25,180 - __main__ - INFO - 开始第 9 轮训练，本地训练 5 个epoch
2025-08-27 12:54:25,188 - __main__ - INFO - 轮次 9, Epoch 1/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:54:25,189 - __main__ - INFO - 轮次 9, Epoch 1/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:54:34,062 - __main__ - INFO - 轮次 9, Epoch 1/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:54:34,081 - __main__ - INFO - 轮次 9, Epoch 1/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:54:40,900 - __main__ - INFO - 轮次 9, Epoch 1/5 完成, 损失: 0.0010
2025-08-27 12:54:40,903 - __main__ - INFO - 轮次 9, Epoch 1/5 完成, 损失: 0.0007
2025-08-27 12:54:42,203 - __main__ - INFO - 轮次 9, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:54:42,203 - __main__ - INFO - 轮次 9, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:54:48,390 - __main__ - INFO - 轮次 9, Epoch 2/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:54:48,403 - __main__ - INFO - 轮次 9, Epoch 2/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:54:54,261 - __main__ - INFO - 轮次 9, Epoch 2/5 完成, 损失: 0.0000
2025-08-27 12:54:54,293 - __main__ - INFO - 轮次 9, Epoch 2/5 完成, 损失: 0.0000
2025-08-27 12:54:55,421 - __main__ - INFO - 轮次 9, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:54:55,463 - __main__ - INFO - 轮次 9, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:55:01,347 - __main__ - INFO - 轮次 9, Epoch 3/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:55:01,373 - __main__ - INFO - 轮次 9, Epoch 3/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:55:06,428 - __main__ - INFO - 轮次 9, Epoch 3/5 完成, 损失: 0.0000
2025-08-27 12:55:06,511 - __main__ - INFO - 轮次 9, Epoch 3/5 完成, 损失: 0.0000
2025-08-27 12:55:07,667 - __main__ - INFO - 轮次 9, Epoch 4/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:55:07,752 - __main__ - INFO - 轮次 9, Epoch 4/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:55:13,925 - __main__ - INFO - 轮次 9, Epoch 4/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:55:14,002 - __main__ - INFO - 轮次 9, Epoch 4/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:55:19,409 - __main__ - INFO - 轮次 9, Epoch 4/5 完成, 损失: 0.0000
2025-08-27 12:55:19,575 - __main__ - INFO - 轮次 9, Epoch 4/5 完成, 损失: 0.0000
2025-08-27 12:55:20,553 - __main__ - INFO - 轮次 9, Epoch 5/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:55:20,703 - __main__ - INFO - 轮次 9, Epoch 5/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:55:26,990 - __main__ - INFO - 轮次 9, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:55:27,221 - __main__ - INFO - 轮次 9, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:55:31,960 - __main__ - INFO - 轮次 9, Epoch 5/5 完成, 损失: 0.0000
2025-08-27 12:55:32,175 - __main__ - INFO - 轮次 9, Epoch 5/5 完成, 损失: 0.0000
2025-08-27 12:55:34,267 - __main__ - INFO - 本地记录训练结果 - 轮次: 9, 损失: 0.0002, 准确率: 0.9946
2025-08-27 12:55:34,267 - __main__ - INFO - 训练完成 - 轮次: 9, 损失: 0.0002, 准确率: 0.9946, 耗时: 67.89秒
2025-08-27 12:55:34,267 - __main__ - INFO - 返回训练指标: {'loss': 0.00020203485048230407, 'accuracy': 0.9946, 'num_examples': 300000, 'training_time': 67.89461827278137}
2025-08-27 12:55:34,482 - __main__ - INFO - 本地记录训练结果 - 轮次: 9, 损失: 0.0001, 准确率: 0.9949
2025-08-27 12:55:34,482 - __main__ - INFO - 训练完成 - 轮次: 9, 损失: 0.0001, 准确率: 0.9949, 耗时: 68.12秒
2025-08-27 12:55:34,482 - __main__ - INFO - 返回训练指标: {'loss': 0.0001430814013251141, 'accuracy': 0.9949, 'num_examples': 300000, 'training_time': 68.11812233924866}
2025-08-27 12:55:34,533 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:55:34,534 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:55:35,826 - __main__ - INFO - 评估完成 - 轮次: 9, 损失: 0.0358, 准确率: 0.9951, 样本数: 10000
2025-08-27 12:55:35,826 - __main__ - INFO - 返回评估指标: {'loss': 0.03583193865385856, 'accuracy': 0.9951, 'num_examples': 10000, 'evaluation_time': 1.2936434745788574}
2025-08-27 12:55:35,852 - __main__ - INFO - 评估完成 - 轮次: 9, 损失: 0.0358, 准确率: 0.9951, 样本数: 10000
2025-08-27 12:55:35,853 - __main__ - INFO - 返回评估指标: {'loss': 0.03583193865385856, 'accuracy': 0.9951, 'num_examples': 10000, 'evaluation_time': 1.3177456855773926}
2025-08-27 12:55:35,865 - __main__ - INFO - 开始第 10 轮训练，本地训练 5 个epoch
2025-08-27 12:55:35,867 - __main__ - INFO - 开始第 10 轮训练，本地训练 5 个epoch
2025-08-27 12:55:35,874 - __main__ - INFO - 轮次 10, Epoch 1/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:55:35,875 - __main__ - INFO - 轮次 10, Epoch 1/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:55:44,404 - __main__ - INFO - 轮次 10, Epoch 1/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:55:44,526 - __main__ - INFO - 轮次 10, Epoch 1/5, 批次 1000/1875, 损失: 0.0007
2025-08-27 12:55:50,806 - __main__ - INFO - 轮次 10, Epoch 1/5 完成, 损失: 0.0008
2025-08-27 12:55:51,007 - __main__ - INFO - 轮次 10, Epoch 1/5 完成, 损失: 0.0005
2025-08-27 12:55:52,133 - __main__ - INFO - 轮次 10, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:55:52,307 - __main__ - INFO - 轮次 10, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:55:58,179 - __main__ - INFO - 轮次 10, Epoch 2/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:55:58,266 - __main__ - INFO - 轮次 10, Epoch 2/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:56:03,662 - __main__ - INFO - 轮次 10, Epoch 2/5 完成, 损失: 0.0000
2025-08-27 12:56:03,753 - __main__ - INFO - 轮次 10, Epoch 2/5 完成, 损失: 0.0000
2025-08-27 12:56:04,939 - __main__ - INFO - 轮次 10, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:56:05,039 - __main__ - INFO - 轮次 10, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:56:11,261 - __main__ - INFO - 轮次 10, Epoch 3/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:56:11,361 - __main__ - INFO - 轮次 10, Epoch 3/5, 批次 1000/1875, 损失: 0.0000
