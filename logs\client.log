2025-08-27 10:01:31,440 - __main__ - INFO - Using device: cuda
2025-08-27 10:01:31,440 - __main__ - INFO - Client state initialized with port 5001 and account None
2025-08-27 10:01:31,440 - __main__ - INFO - 客户端启动中...
2025-08-27 10:01:31,443 - __main__ - INFO - Starting Flower client directly (bypassing Flask server)
2025-08-27 10:01:31,444 - __main__ - INFO - Starting Flask server on port 5001
2025-08-27 10:01:33,511 - __main__ - INFO - 成功获取服务器端口: 8080
2025-08-27 10:01:33,511 - __main__ - INFO - 连接到服务器: localhost:8080
2025-08-27 10:01:33,617 - __main__ - INFO - Model structure:
2025-08-27 10:01:33,617 - __main__ - INFO - conv1.weight: torch.Size([32, 1, 5, 5])
2025-08-27 10:01:33,619 - __main__ - INFO - conv1.bias: torch.Size([32])
2025-08-27 10:01:33,620 - __main__ - INFO - bn1.weight: torch.Size([32])
2025-08-27 10:01:33,620 - __main__ - INFO - bn1.bias: torch.Size([32])
2025-08-27 10:01:33,620 - __main__ - INFO - conv2.weight: torch.Size([64, 32, 5, 5])
2025-08-27 10:01:33,620 - __main__ - INFO - conv2.bias: torch.Size([64])
2025-08-27 10:01:33,620 - __main__ - INFO - bn2.weight: torch.Size([64])
2025-08-27 10:01:33,620 - __main__ - INFO - bn2.bias: torch.Size([64])
2025-08-27 10:01:33,620 - __main__ - INFO - conv3.weight: torch.Size([128, 64, 3, 3])
2025-08-27 10:01:33,620 - __main__ - INFO - conv3.bias: torch.Size([128])
2025-08-27 10:01:33,620 - __main__ - INFO - bn3.weight: torch.Size([128])
2025-08-27 10:01:33,620 - __main__ - INFO - bn3.bias: torch.Size([128])
2025-08-27 10:01:33,620 - __main__ - INFO - fc1.weight: torch.Size([512, 128])
2025-08-27 10:01:33,620 - __main__ - INFO - fc1.bias: torch.Size([512])
2025-08-27 10:01:33,620 - __main__ - INFO - fc2.weight: torch.Size([10, 512])
2025-08-27 10:01:33,620 - __main__ - INFO - fc2.bias: torch.Size([10])
2025-08-27 10:01:33,650 - __main__ - INFO - 开始连接到服务器 localhost:8080...
2025-08-27 10:01:36,188 - __main__ - ERROR - 启动客户端失败: <_MultiThreadedRendezvous of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "failed to connect to all addresses; last error: UNAVAILABLE: ipv4:127.0.0.1:8080: ConnectEx: Connection refused (No connection could be made because the target machine actively refused it.

 -- 10061)"
	debug_error_string = "UNKNOWN:Error received from peer  {created_time:"2025-08-27T02:01:35.9806896+00:00", grpc_status:14, grpc_message:"failed to connect to all addresses; last error: UNAVAILABLE: ipv4:127.0.0.1:8080: ConnectEx: Connection refused (No connection could be made because the target machine actively refused it.\r\n -- 10061)"}"
>
2025-08-27 10:01:36,694 - __main__ - INFO - Using device: cuda
2025-08-27 10:01:36,694 - __main__ - INFO - Client state initialized with port 5001 and account None
2025-08-27 10:01:36,694 - __main__ - INFO - 客户端启动中...
2025-08-27 10:01:36,697 - __main__ - INFO - Starting Flower client directly (bypassing Flask server)
2025-08-27 10:01:36,698 - __main__ - INFO - Starting Flask server on port 5002
2025-08-27 10:01:38,749 - __main__ - INFO - 成功获取服务器端口: 8080
2025-08-27 10:01:38,749 - __main__ - INFO - 连接到服务器: localhost:8080
2025-08-27 10:01:38,835 - __main__ - INFO - Model structure:
2025-08-27 10:01:38,835 - __main__ - INFO - conv1.weight: torch.Size([32, 1, 5, 5])
2025-08-27 10:01:38,837 - __main__ - INFO - conv1.bias: torch.Size([32])
2025-08-27 10:01:38,837 - __main__ - INFO - bn1.weight: torch.Size([32])
2025-08-27 10:01:38,837 - __main__ - INFO - bn1.bias: torch.Size([32])
2025-08-27 10:01:38,837 - __main__ - INFO - conv2.weight: torch.Size([64, 32, 5, 5])
2025-08-27 10:01:38,837 - __main__ - INFO - conv2.bias: torch.Size([64])
2025-08-27 10:01:38,837 - __main__ - INFO - bn2.weight: torch.Size([64])
2025-08-27 10:01:38,837 - __main__ - INFO - bn2.bias: torch.Size([64])
2025-08-27 10:01:38,837 - __main__ - INFO - conv3.weight: torch.Size([128, 64, 3, 3])
2025-08-27 10:01:38,837 - __main__ - INFO - conv3.bias: torch.Size([128])
2025-08-27 10:01:38,837 - __main__ - INFO - bn3.weight: torch.Size([128])
2025-08-27 10:01:38,837 - __main__ - INFO - bn3.bias: torch.Size([128])
2025-08-27 10:01:38,837 - __main__ - INFO - fc1.weight: torch.Size([512, 128])
2025-08-27 10:01:38,837 - __main__ - INFO - fc1.bias: torch.Size([512])
2025-08-27 10:01:38,837 - __main__ - INFO - fc2.weight: torch.Size([10, 512])
2025-08-27 10:01:38,837 - __main__ - INFO - fc2.bias: torch.Size([10])
2025-08-27 10:01:38,867 - __main__ - INFO - 开始连接到服务器 localhost:8080...
2025-08-27 10:01:41,406 - __main__ - ERROR - 启动客户端失败: <_MultiThreadedRendezvous of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "failed to connect to all addresses; last error: UNAVAILABLE: ipv4:127.0.0.1:8080: ConnectEx: Connection refused (No connection could be made because the target machine actively refused it.

 -- 10061)"
	debug_error_string = "UNKNOWN:Error received from peer  {created_time:"2025-08-27T02:01:41.204343+00:00", grpc_status:14, grpc_message:"failed to connect to all addresses; last error: UNAVAILABLE: ipv4:127.0.0.1:8080: ConnectEx: Connection refused (No connection could be made because the target machine actively refused it.\r\n -- 10061)"}"
>
