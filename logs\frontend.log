
> frontend@0.0.0 dev
> vite

Re-optimizing dependencies because vite config has changed

  [32m[1mVITE[22m v6.0.11[39m  [2mready in [0m[1m5901[22m[2m[0m ms[22m

  [32m➜[39m  [1mLocal[22m:   [36mhttp://localhost:[1m5173[22m/[39m
[2m  [32m➜[39m  [1mNetwork[22m[2m: use [22m[1m--host[22m[2m to expose[22m
[2m[32m  ➜[39m[22m[2m  press [22m[1mh + enter[22m[2m to show help[22m
Error: [31m  Failed to scan for dependencies from entries:
  E:/ZhousProject - vs1/frontend/index.html
E:/ZhousProject - vs1/frontend/public/index.html

  [39m[31mX [41;31m[[41;97mERROR[41;31m][0m [1mENOENT: no such file or directory, open 'E:\ZhousProject - vs1\frontend\src\components\common\Breadcrumb.vue'[0m [1m[35m[plugin vite:dep-scan][0m

    script:E:/ZhousProject - vs1/frontend/src/views/client/DataManagement.vue?id=0:4:23:
[37m      4 │ import Breadcrumb from [32m'@/components/common/Breadcrumb.vue'[37m;
        ╵                        [32m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m


    at failureErrorWithLog (E:\ZhousProject - vs1\frontend\node_modules\esbuild\lib\main.js:1476:15)
    at E:\ZhousProject - vs1\frontend\node_modules\esbuild\lib\main.js:945:25
    at runOnEndCallbacks (E:\ZhousProject - vs1\frontend\node_modules\esbuild\lib\main.js:1316:45)
    at buildResponseToResult (E:\ZhousProject - vs1\frontend\node_modules\esbuild\lib\main.js:943:7)
    at E:\ZhousProject - vs1\frontend\node_modules\esbuild\lib\main.js:955:9
    at new Promise (<anonymous>)
    at requestCallbacks.on-end (E:\ZhousProject - vs1\frontend\node_modules\esbuild\lib\main.js:954:54)
    at handleRequest (E:\ZhousProject - vs1\frontend\node_modules\esbuild\lib\main.js:647:17)
    at handleIncomingPacket (E:\ZhousProject - vs1\frontend\node_modules\esbuild\lib\main.js:672:7)
    at Socket.readFromStdout (E:\ZhousProject - vs1\frontend\node_modules\esbuild\lib\main.js:600:7)
[2m09:29:52[22m [36m[1m[vite][22m[39m [90m[2m(client)[22m[39m [32m✨ new dependencies optimized: [33mvue, element-plus, vue-router, vuex, web3, axios, @element-plus/icons-vue, js-sha3[32m[39m
[2m09:29:52[22m [36m[1m[vite][22m[39m [90m[2m(client)[22m[39m [32m✨ optimized dependencies changed. reloading[39m
[2m09:29:58[22m [36m[1m[vite][22m[39m [90m[2m(client)[22m[39m [32m✨ new dependencies optimized: [33mecharts[32m[39m
[2m09:29:58[22m [36m[1m[vite][22m[39m [90m[2m(client)[22m[39m [32m✨ optimized dependencies changed. reloading[39m
