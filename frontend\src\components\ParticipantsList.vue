<template>
  <div class="participants-container">
    <h3>参与节点</h3>
    <div class="participants-content" v-if="!loading">
      <div v-if="participants.length" class="participants-list">
        <div v-for="participant in participants" :key="participant.address" class="participant-item">
          <div class="participant-info">
            <div class="participant-icon">
              <i class="fas fa-user"></i>
            </div>
            <div class="participant-details">
              <div class="participant-address">
                {{ formatAddress(participant.address) }}
                <span 
                  class="copy-icon" 
                  @click="copyAddress(participant.address)"
                  title="复制地址"
                >
                  <i class="fas fa-copy"></i>
                </span>
              </div>
              <div class="participant-status">
                <span 
                  class="status-dot"
                  :class="{ 'active': participant.isActive }"
                ></span>
                {{ participant.isActive ? '活跃' : '离线' }}
              </div>
            </div>
          </div>
          <div class="participant-metrics">
            <div class="metric">
              <span class="metric-label">贡献度</span>
              <span class="metric-value">{{ formatPercentage(participant.contribution) }}</span>
            </div>
            <div class="metric">
              <span class="metric-label">数据量</span>
              <span class="metric-value">{{ formatNumber(participant.dataSize) }}</span>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="no-participants">
        暂无参与节点
      </div>
    </div>
    <div v-else class="loading-container">
      <div class="loading-spinner"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ParticipantsList',
  props: {
    participants: {
      type: Array,
      required: true,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },

  setup() {
    const formatAddress = (address) => {
      if (!address) return '';
      return `${address.slice(0, 6)}...${address.slice(-4)}`;
    };

    const formatPercentage = (value) => {
      return typeof value === 'number' ? `${(value * 100).toFixed(2)}%` : '0%';
    };

    const formatNumber = (value) => {
      if (typeof value !== 'number') return '0';
      if (value >= 1000000) {
        return `${(value / 1000000).toFixed(1)}M`;
      } else if (value >= 1000) {
        return `${(value / 1000).toFixed(1)}K`;
      }
      return value.toString();
    };

    const copyAddress = async (address) => {
      try {
        await navigator.clipboard.writeText(address);
        // 这里可以添加一个提示，表示复制成功
      } catch (err) {
        console.error('复制失败:', err);
      }
    };

    return {
      formatAddress,
      formatPercentage,
      formatNumber,
      copyAddress
    };
  }
};
</script>

<style scoped>
.participants-container {
  height: 100%;
}

.participants-content {
  margin-top: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.participants-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.participant-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.participant-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.participant-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: #e3f2fd;
  color: #1976D2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.participant-details {
  flex: 1;
}

.participant-address {
  font-family: monospace;
  font-size: 0.9em;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.copy-icon {
  cursor: pointer;
  color: #666;
  font-size: 0.9em;
}

.copy-icon:hover {
  color: #1976D2;
}

.participant-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9em;
  color: #666;
  margin-top: 4px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background: #9E9E9E;
}

.status-dot.active {
  background: #4CAF50;
}

.participant-metrics {
  display: flex;
  gap: 16px;
}

.metric {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  font-size: 0.8em;
  color: #666;
}

.metric-value {
  font-size: 0.9em;
  font-weight: 500;
  color: #333;
}

.no-participants {
  text-align: center;
  color: #666;
  padding: 32px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 