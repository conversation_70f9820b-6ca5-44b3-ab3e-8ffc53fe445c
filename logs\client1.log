2025-08-27 10:58:49,891 - __main__ - INFO - Using device: cuda
2025-08-27 10:58:49,891 - __main__ - INFO - Client state initialized with port 5001 and account None
2025-08-27 10:58:49,891 - __main__ - INFO - 客户端启动中...
2025-08-27 10:58:49,894 - __main__ - INFO - Starting Flower client directly (bypassing Flask server)
2025-08-27 10:58:49,895 - __main__ - INFO - Starting Flask server on port 5001
 * Serving Flask app 'client'
 * Debug mode: off
2025-08-27 10:58:51,969 - __main__ - INFO - 成功获取服务器端口: 8080
2025-08-27 10:58:51,969 - __main__ - INFO - 连接到服务器: localhost:8080
2025-08-27 10:58:52,117 - __main__ - INFO - Model structure:
2025-08-27 10:58:52,117 - __main__ - INFO - conv1.weight: torch.<PERSON>ze([32, 1, 5, 5])
2025-08-27 10:58:52,117 - __main__ - INFO - conv1.bias: torch.<PERSON><PERSON>([32])
2025-08-27 10:58:52,117 - __main__ - INFO - bn1.weight: torch.Size([32])
2025-08-27 10:58:52,117 - __main__ - INFO - bn1.bias: torch.Size([32])
2025-08-27 10:58:52,117 - __main__ - INFO - conv2.weight: torch.Size([64, 32, 5, 5])
2025-08-27 10:58:52,117 - __main__ - INFO - conv2.bias: torch.Size([64])
2025-08-27 10:58:52,117 - __main__ - INFO - bn2.weight: torch.Size([64])
2025-08-27 10:58:52,117 - __main__ - INFO - bn2.bias: torch.Size([64])
2025-08-27 10:58:52,117 - __main__ - INFO - conv3.weight: torch.Size([128, 64, 3, 3])
2025-08-27 10:58:52,117 - __main__ - INFO - conv3.bias: torch.Size([128])
2025-08-27 10:58:52,117 - __main__ - INFO - bn3.weight: torch.Size([128])
2025-08-27 10:58:52,117 - __main__ - INFO - bn3.bias: torch.Size([128])
2025-08-27 10:58:52,117 - __main__ - INFO - fc1.weight: torch.Size([512, 128])
2025-08-27 10:58:52,117 - __main__ - INFO - fc1.bias: torch.Size([512])
2025-08-27 10:58:52,118 - __main__ - INFO - fc2.weight: torch.Size([10, 512])
2025-08-27 10:58:52,118 - __main__ - INFO - fc2.bias: torch.Size([10])
2025-08-27 10:58:52,152 - __main__ - INFO - 开始连接到服务器 localhost:8080...
WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
E0000 00:00:1756263532.158495    9132 trace.cc:90] Unknown tracer: none
2025-08-27 10:58:54,681 - __main__ - ERROR - 启动客户端失败: <_MultiThreadedRendezvous of RPC that terminated with:
	status = StatusCode.UNAVAILABLE
	details = "failed to connect to all addresses; last error: UNAVAILABLE: ipv4:127.0.0.1:8080: ConnectEx: Connection refused (No connection could be made because the target machine actively refused it.

 -- 10061)"
	debug_error_string = "UNKNOWN:Error received from peer  {grpc_message:"failed to connect to all addresses; last error: UNAVAILABLE: ipv4:127.0.0.1:8080: ConnectEx: Connection refused (No connection could be made because the target machine actively refused it.\r\n -- 10061)", grpc_status:14, created_time:"2025-08-27T02:58:54.473118+00:00"}"
>
2025-08-27 12:44:11,324 - __main__ - INFO - 收到训练控制请求: action=start, task_id=113
2025-08-27 12:44:11,326 - __main__ - INFO - 正在启动训练，参数：batch_size=32, learning_rate=0.0010000000474974513, epochs=1, timeout=60
2025-08-27 12:44:11,334 - __main__ - INFO - Flower客户端启动成功
2025-08-27 12:44:13,431 - __main__ - INFO - 成功获取服务器端口: 8080
2025-08-27 12:44:13,432 - __main__ - INFO - 连接到服务器: localhost:8080
2025-08-27 12:44:13,489 - __main__ - INFO - Model structure:
2025-08-27 12:44:13,490 - __main__ - INFO - conv1.weight: torch.Size([32, 1, 5, 5])
2025-08-27 12:44:13,490 - __main__ - INFO - conv1.bias: torch.Size([32])
2025-08-27 12:44:13,491 - __main__ - INFO - bn1.weight: torch.Size([32])
2025-08-27 12:44:13,491 - __main__ - INFO - bn1.bias: torch.Size([32])
2025-08-27 12:44:13,491 - __main__ - INFO - conv2.weight: torch.Size([64, 32, 5, 5])
2025-08-27 12:44:13,492 - __main__ - INFO - conv2.bias: torch.Size([64])
2025-08-27 12:44:13,492 - __main__ - INFO - bn2.weight: torch.Size([64])
2025-08-27 12:44:13,492 - __main__ - INFO - bn2.bias: torch.Size([64])
2025-08-27 12:44:13,492 - __main__ - INFO - conv3.weight: torch.Size([128, 64, 3, 3])
2025-08-27 12:44:13,493 - __main__ - INFO - conv3.bias: torch.Size([128])
2025-08-27 12:44:13,493 - __main__ - INFO - bn3.weight: torch.Size([128])
2025-08-27 12:44:13,494 - __main__ - INFO - bn3.bias: torch.Size([128])
2025-08-27 12:44:13,494 - __main__ - INFO - fc1.weight: torch.Size([512, 128])
2025-08-27 12:44:13,494 - __main__ - INFO - fc1.bias: torch.Size([512])
2025-08-27 12:44:13,494 - __main__ - INFO - fc2.weight: torch.Size([10, 512])
2025-08-27 12:44:13,494 - __main__ - INFO - fc2.bias: torch.Size([10])
2025-08-27 12:44:13,586 - __main__ - INFO - 开始连接到服务器 localhost:8080...
2025-08-27 12:44:16,305 - __main__ - INFO - 开始第 1 轮训练，本地训练 5 个epoch
2025-08-27 12:44:18,351 - __main__ - INFO - 轮次 1, Epoch 1/5, 批次 0/1875, 损失: 2.4010
2025-08-27 12:44:18,668 - __main__ - INFO - 收到训练控制请求: action=start, task_id=113
2025-08-27 12:44:18,668 - __main__ - WARNING - 客户端已经在训练中
2025-08-27 12:44:40,902 - __main__ - INFO - 轮次 1, Epoch 1/5, 批次 1000/1875, 损失: 0.1084
2025-08-27 12:45:00,259 - __main__ - INFO - 轮次 1, Epoch 1/5 完成, 损失: 0.1189
2025-08-27 12:45:05,302 - __main__ - INFO - 轮次 1, Epoch 2/5, 批次 0/1875, 损失: 0.1743
2025-08-27 12:45:26,328 - __main__ - INFO - 轮次 1, Epoch 2/5, 批次 1000/1875, 损失: 0.3298
2025-08-27 12:45:43,149 - __main__ - INFO - 轮次 1, Epoch 2/5 完成, 损失: 0.0463
2025-08-27 12:45:44,326 - __main__ - INFO - 轮次 1, Epoch 3/5, 批次 0/1875, 损失: 0.0209
2025-08-27 12:45:50,751 - __main__ - INFO - 轮次 1, Epoch 3/5, 批次 1000/1875, 损失: 0.1389
2025-08-27 12:45:55,932 - __main__ - INFO - 轮次 1, Epoch 3/5 完成, 损失: 0.0323
2025-08-27 12:45:57,128 - __main__ - INFO - 轮次 1, Epoch 4/5, 批次 0/1875, 损失: 0.0003
2025-08-27 12:46:03,294 - __main__ - INFO - 轮次 1, Epoch 4/5, 批次 1000/1875, 损失: 0.0296
2025-08-27 12:46:08,701 - __main__ - INFO - 轮次 1, Epoch 4/5 完成, 损失: 0.0261
2025-08-27 12:46:09,984 - __main__ - INFO - 轮次 1, Epoch 5/5, 批次 0/1875, 损失: 0.0011
2025-08-27 12:46:15,895 - __main__ - INFO - 轮次 1, Epoch 5/5, 批次 1000/1875, 损失: 0.0004
2025-08-27 12:46:21,088 - __main__ - INFO - 轮次 1, Epoch 5/5 完成, 损失: 0.0206
2025-08-27 12:46:23,364 - __main__ - INFO - 本地记录训练结果 - 轮次: 1, 损失: 0.0488, 准确率: 0.9907
2025-08-27 12:46:23,364 - __main__ - INFO - 训练完成 - 轮次: 1, 损失: 0.0488, 准确率: 0.9907, 耗时: 125.99秒
2025-08-27 12:46:23,364 - __main__ - INFO - 返回训练指标: {'loss': 0.04883323741862555, 'accuracy': 0.9907, 'num_examples': 300000, 'training_time': 125.98999905586243}
2025-08-27 12:46:23,407 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:46:24,727 - __main__ - INFO - 评估完成 - 轮次: 1, 损失: 0.2056, 准确率: 0.9658, 样本数: 10000
2025-08-27 12:46:24,727 - __main__ - INFO - 返回评估指标: {'loss': 0.20564549363851548, 'accuracy': 0.9658, 'num_examples': 10000, 'evaluation_time': 1.319856882095337}
2025-08-27 12:46:24,743 - __main__ - INFO - 开始第 2 轮训练，本地训练 5 个epoch
2025-08-27 12:46:24,752 - __main__ - INFO - 轮次 2, Epoch 1/5, 批次 0/1875, 损失: 0.2091
2025-08-27 12:46:33,903 - __main__ - INFO - 轮次 2, Epoch 1/5, 批次 1000/1875, 损失: 0.0048
2025-08-27 12:46:40,679 - __main__ - INFO - 轮次 2, Epoch 1/5 完成, 损失: 0.0382
2025-08-27 12:46:42,032 - __main__ - INFO - 轮次 2, Epoch 2/5, 批次 0/1875, 损失: 0.0024
2025-08-27 12:46:48,076 - __main__ - INFO - 轮次 2, Epoch 2/5, 批次 1000/1875, 损失: 0.0010
2025-08-27 12:46:53,424 - __main__ - INFO - 轮次 2, Epoch 2/5 完成, 损失: 0.0190
2025-08-27 12:46:54,616 - __main__ - INFO - 轮次 2, Epoch 3/5, 批次 0/1875, 损失: 0.0013
2025-08-27 12:47:00,411 - __main__ - INFO - 轮次 2, Epoch 3/5, 批次 1000/1875, 损失: 0.0008
2025-08-27 12:47:05,545 - __main__ - INFO - 轮次 2, Epoch 3/5 完成, 损失: 0.0141
2025-08-27 12:47:06,769 - __main__ - INFO - 轮次 2, Epoch 4/5, 批次 0/1875, 损失: 0.0020
2025-08-27 12:47:12,730 - __main__ - INFO - 轮次 2, Epoch 4/5, 批次 1000/1875, 损失: 0.0035
2025-08-27 12:47:17,780 - __main__ - INFO - 轮次 2, Epoch 4/5 完成, 损失: 0.0116
2025-08-27 12:47:18,944 - __main__ - INFO - 轮次 2, Epoch 5/5, 批次 0/1875, 损失: 0.0093
2025-08-27 12:47:24,751 - __main__ - INFO - 轮次 2, Epoch 5/5, 批次 1000/1875, 损失: 0.0011
2025-08-27 12:47:29,767 - __main__ - INFO - 轮次 2, Epoch 5/5 完成, 损失: 0.0103
2025-08-27 12:47:32,073 - __main__ - INFO - 本地记录训练结果 - 轮次: 2, 损失: 0.0187, 准确率: 0.9881
2025-08-27 12:47:32,074 - __main__ - INFO - 训练完成 - 轮次: 2, 损失: 0.0187, 准确率: 0.9881, 耗时: 66.21秒
2025-08-27 12:47:32,074 - __main__ - INFO - 返回训练指标: {'loss': 0.01865375180227103, 'accuracy': 0.9881, 'num_examples': 300000, 'training_time': 66.21347784996033}
2025-08-27 12:47:32,109 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:47:33,377 - __main__ - INFO - 评估完成 - 轮次: 2, 损失: 0.0283, 准确率: 0.9935, 样本数: 10000
2025-08-27 12:47:33,377 - __main__ - INFO - 返回评估指标: {'loss': 0.028283438022942208, 'accuracy': 0.9935, 'num_examples': 10000, 'evaluation_time': 1.26881742477417}
2025-08-27 12:47:33,394 - __main__ - INFO - 开始第 3 轮训练，本地训练 5 个epoch
2025-08-27 12:47:33,402 - __main__ - INFO - 轮次 3, Epoch 1/5, 批次 0/1875, 损失: 0.0019
2025-08-27 12:47:41,444 - __main__ - INFO - 轮次 3, Epoch 1/5, 批次 1000/1875, 损失: 0.0025
2025-08-27 12:47:48,562 - __main__ - INFO - 轮次 3, Epoch 1/5 完成, 损失: 0.0134
2025-08-27 12:47:49,977 - __main__ - INFO - 轮次 3, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:47:56,215 - __main__ - INFO - 轮次 3, Epoch 2/5, 批次 1000/1875, 损失: 0.0087
2025-08-27 12:48:01,214 - __main__ - INFO - 轮次 3, Epoch 2/5 完成, 损失: 0.0057
2025-08-27 12:48:02,481 - __main__ - INFO - 轮次 3, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:48:08,235 - __main__ - INFO - 轮次 3, Epoch 3/5, 批次 1000/1875, 损失: 0.0002
2025-08-27 12:48:13,530 - __main__ - INFO - 轮次 3, Epoch 3/5 完成, 损失: 0.0048
2025-08-27 12:48:14,667 - __main__ - INFO - 轮次 3, Epoch 4/5, 批次 0/1875, 损失: 0.0001
2025-08-27 12:48:20,470 - __main__ - INFO - 轮次 3, Epoch 4/5, 批次 1000/1875, 损失: 0.0622
2025-08-27 12:48:25,646 - __main__ - INFO - 轮次 3, Epoch 4/5 完成, 损失: 0.0047
2025-08-27 12:48:26,837 - __main__ - INFO - 轮次 3, Epoch 5/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:48:32,618 - __main__ - INFO - 轮次 3, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:48:37,879 - __main__ - INFO - 轮次 3, Epoch 5/5 完成, 损失: 0.0040
2025-08-27 12:48:40,084 - __main__ - INFO - 本地记录训练结果 - 轮次: 3, 损失: 0.0065, 准确率: 0.9920
2025-08-27 12:48:40,084 - __main__ - INFO - 训练完成 - 轮次: 3, 损失: 0.0065, 准确率: 0.9920, 耗时: 65.60秒
2025-08-27 12:48:40,084 - __main__ - INFO - 返回训练指标: {'loss': 0.006493621828693018, 'accuracy': 0.992, 'num_examples': 300000, 'training_time': 65.59729814529419}
2025-08-27 12:48:40,131 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:48:41,370 - __main__ - INFO - 评估完成 - 轮次: 3, 损失: 0.0288, 准确率: 0.9946, 样本数: 10000
2025-08-27 12:48:41,370 - __main__ - INFO - 返回评估指标: {'loss': 0.02876018466074146, 'accuracy': 0.9946, 'num_examples': 10000, 'evaluation_time': 1.2394421100616455}
2025-08-27 12:48:41,389 - __main__ - INFO - 开始第 4 轮训练，本地训练 5 个epoch
2025-08-27 12:48:41,397 - __main__ - INFO - 轮次 4, Epoch 1/5, 批次 0/1875, 损失: 0.0004
2025-08-27 12:48:49,358 - __main__ - INFO - 轮次 4, Epoch 1/5, 批次 1000/1875, 损失: 0.0223
2025-08-27 12:48:57,294 - __main__ - INFO - 轮次 4, Epoch 1/5 完成, 损失: 0.0062
2025-08-27 12:48:58,719 - __main__ - INFO - 轮次 4, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:49:04,927 - __main__ - INFO - 轮次 4, Epoch 2/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:49:10,306 - __main__ - INFO - 轮次 4, Epoch 2/5 完成, 损失: 0.0019
2025-08-27 12:49:11,529 - __main__ - INFO - 轮次 4, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:49:17,526 - __main__ - INFO - 轮次 4, Epoch 3/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:49:22,709 - __main__ - INFO - 轮次 4, Epoch 3/5 完成, 损失: 0.0017
2025-08-27 12:49:23,888 - __main__ - INFO - 轮次 4, Epoch 4/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:49:29,700 - __main__ - INFO - 轮次 4, Epoch 4/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:49:34,712 - __main__ - INFO - 轮次 4, Epoch 4/5 完成, 损失: 0.0024
2025-08-27 12:49:35,868 - __main__ - INFO - 轮次 4, Epoch 5/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:49:41,691 - __main__ - INFO - 轮次 4, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:49:46,724 - __main__ - INFO - 轮次 4, Epoch 5/5 完成, 损失: 0.0023
2025-08-27 12:49:48,996 - __main__ - INFO - 本地记录训练结果 - 轮次: 4, 损失: 0.0029, 准确率: 0.9944
2025-08-27 12:49:48,996 - __main__ - INFO - 训练完成 - 轮次: 4, 损失: 0.0029, 准确率: 0.9944, 耗时: 66.48秒
2025-08-27 12:49:48,996 - __main__ - INFO - 返回训练指标: {'loss': 0.0029097710036022446, 'accuracy': 0.9944, 'num_examples': 300000, 'training_time': 66.48057103157043}
2025-08-27 12:49:49,044 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:49:50,346 - __main__ - INFO - 评估完成 - 轮次: 4, 损失: 0.0329, 准确率: 0.9946, 样本数: 10000
2025-08-27 12:49:50,346 - __main__ - INFO - 返回评估指标: {'loss': 0.03292133901946804, 'accuracy': 0.9946, 'num_examples': 10000, 'evaluation_time': 1.301833152770996}
2025-08-27 12:49:50,363 - __main__ - INFO - 开始第 5 轮训练，本地训练 5 个epoch
2025-08-27 12:49:50,373 - __main__ - INFO - 轮次 5, Epoch 1/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:49:58,985 - __main__ - INFO - 轮次 5, Epoch 1/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:50:05,490 - __main__ - INFO - 轮次 5, Epoch 1/5 完成, 损失: 0.0032
2025-08-27 12:50:06,760 - __main__ - INFO - 轮次 5, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:50:16,816 - __main__ - INFO - 轮次 5, Epoch 2/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:50:21,826 - __main__ - INFO - 轮次 5, Epoch 2/5 完成, 损失: 0.0002
2025-08-27 12:50:23,003 - __main__ - INFO - 轮次 5, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:50:28,694 - __main__ - INFO - 轮次 5, Epoch 3/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:50:33,750 - __main__ - INFO - 轮次 5, Epoch 3/5 完成, 损失: 0.0011
2025-08-27 12:50:34,949 - __main__ - INFO - 轮次 5, Epoch 4/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:50:40,777 - __main__ - INFO - 轮次 5, Epoch 4/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:50:45,750 - __main__ - INFO - 轮次 5, Epoch 4/5 完成, 损失: 0.0001
2025-08-27 12:50:46,924 - __main__ - INFO - 轮次 5, Epoch 5/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:50:52,823 - __main__ - INFO - 轮次 5, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:50:57,982 - __main__ - INFO - 轮次 5, Epoch 5/5 完成, 损失: 0.0000
2025-08-27 12:51:00,304 - __main__ - INFO - 本地记录训练结果 - 轮次: 5, 损失: 0.0009, 准确率: 0.9948
2025-08-27 12:51:00,304 - __main__ - INFO - 训练完成 - 轮次: 5, 损失: 0.0009, 准确率: 0.9948, 耗时: 68.75秒
2025-08-27 12:51:00,304 - __main__ - INFO - 返回训练指标: {'loss': 0.0009320935452521326, 'accuracy': 0.9948, 'num_examples': 300000, 'training_time': 68.75221109390259}
2025-08-27 12:51:00,354 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:51:01,703 - __main__ - INFO - 评估完成 - 轮次: 5, 损失: 0.0333, 准确率: 0.9948, 样本数: 10000
2025-08-27 12:51:01,703 - __main__ - INFO - 返回评估指标: {'loss': 0.03325242406846852, 'accuracy': 0.9948, 'num_examples': 10000, 'evaluation_time': 1.3475139141082764}
2025-08-27 12:51:01,718 - __main__ - INFO - 开始第 6 轮训练，本地训练 5 个epoch
2025-08-27 12:51:01,726 - __main__ - INFO - 轮次 6, Epoch 1/5, 批次 0/1875, 损失: 0.0019
2025-08-27 12:51:10,819 - __main__ - INFO - 轮次 6, Epoch 1/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:51:16,971 - __main__ - INFO - 轮次 6, Epoch 1/5 完成, 损失: 0.0030
2025-08-27 12:51:18,166 - __main__ - INFO - 轮次 6, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:51:23,930 - __main__ - INFO - 轮次 6, Epoch 2/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:51:28,916 - __main__ - INFO - 轮次 6, Epoch 2/5 完成, 损失: 0.0000
2025-08-27 12:51:30,103 - __main__ - INFO - 轮次 6, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:51:35,807 - __main__ - INFO - 轮次 6, Epoch 3/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:51:41,204 - __main__ - INFO - 轮次 6, Epoch 3/5 完成, 损失: 0.0001
2025-08-27 12:51:42,385 - __main__ - INFO - 轮次 6, Epoch 4/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:51:48,161 - __main__ - INFO - 轮次 6, Epoch 4/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:51:53,616 - __main__ - INFO - 轮次 6, Epoch 4/5 完成, 损失: 0.0001
2025-08-27 12:51:54,747 - __main__ - INFO - 轮次 6, Epoch 5/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:52:00,920 - __main__ - INFO - 轮次 6, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:52:06,404 - __main__ - INFO - 轮次 6, Epoch 5/5 完成, 损失: 0.0000
2025-08-27 12:52:08,947 - __main__ - INFO - 本地记录训练结果 - 轮次: 6, 损失: 0.0007, 准确率: 0.9946
2025-08-27 12:52:08,947 - __main__ - INFO - 训练完成 - 轮次: 6, 损失: 0.0007, 准确率: 0.9946, 耗时: 66.03秒
2025-08-27 12:52:08,947 - __main__ - INFO - 返回训练指标: {'loss': 0.0006631233427371823, 'accuracy': 0.9946, 'num_examples': 300000, 'training_time': 66.02891898155212}
2025-08-27 12:52:08,994 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:52:10,366 - __main__ - INFO - 评估完成 - 轮次: 6, 损失: 0.0404, 准确率: 0.9945, 样本数: 10000
2025-08-27 12:52:10,366 - __main__ - INFO - 返回评估指标: {'loss': 0.04039657207103374, 'accuracy': 0.9945, 'num_examples': 10000, 'evaluation_time': 1.3724100589752197}
2025-08-27 12:52:10,380 - __main__ - INFO - 开始第 7 轮训练，本地训练 5 个epoch
2025-08-27 12:52:10,388 - __main__ - INFO - 轮次 7, Epoch 1/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:52:19,182 - __main__ - INFO - 轮次 7, Epoch 1/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:52:25,584 - __main__ - INFO - 轮次 7, Epoch 1/5 完成, 损失: 0.0021
2025-08-27 12:52:26,788 - __main__ - INFO - 轮次 7, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:52:32,597 - __main__ - INFO - 轮次 7, Epoch 2/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:52:37,686 - __main__ - INFO - 轮次 7, Epoch 2/5 完成, 损失: 0.0000
2025-08-27 12:52:38,827 - __main__ - INFO - 轮次 7, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:52:44,563 - __main__ - INFO - 轮次 7, Epoch 3/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:52:49,652 - __main__ - INFO - 轮次 7, Epoch 3/5 完成, 损失: 0.0000
2025-08-27 12:52:50,858 - __main__ - INFO - 轮次 7, Epoch 4/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:52:56,907 - __main__ - INFO - 轮次 7, Epoch 4/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:53:02,098 - __main__ - INFO - 轮次 7, Epoch 4/5 完成, 损失: 0.0000
2025-08-27 12:53:03,284 - __main__ - INFO - 轮次 7, Epoch 5/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:53:09,400 - __main__ - INFO - 轮次 7, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:53:14,853 - __main__ - INFO - 轮次 7, Epoch 5/5 完成, 损失: 0.0000
2025-08-27 12:53:17,118 - __main__ - INFO - 本地记录训练结果 - 轮次: 7, 损失: 0.0004, 准确率: 0.9951
2025-08-27 12:53:17,119 - __main__ - INFO - 训练完成 - 轮次: 7, 损失: 0.0004, 准确率: 0.9951, 耗时: 65.58秒
2025-08-27 12:53:17,119 - __main__ - INFO - 返回训练指标: {'loss': 0.0004157510538100288, 'accuracy': 0.9951, 'num_examples': 300000, 'training_time': 65.5837893486023}
2025-08-27 12:53:17,173 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:53:18,484 - __main__ - INFO - 评估完成 - 轮次: 7, 损失: 0.0344, 准确率: 0.9952, 样本数: 10000
2025-08-27 12:53:18,484 - __main__ - INFO - 返回评估指标: {'loss': 0.0344046698376677, 'accuracy': 0.9952, 'num_examples': 10000, 'evaluation_time': 1.3116798400878906}
2025-08-27 12:53:18,500 - __main__ - INFO - 开始第 8 轮训练，本地训练 5 个epoch
2025-08-27 12:53:18,509 - __main__ - INFO - 轮次 8, Epoch 1/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:53:26,820 - __main__ - INFO - 轮次 8, Epoch 1/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:53:33,122 - __main__ - INFO - 轮次 8, Epoch 1/5 完成, 损失: 0.0008
2025-08-27 12:53:34,246 - __main__ - INFO - 轮次 8, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:53:40,075 - __main__ - INFO - 轮次 8, Epoch 2/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:53:45,106 - __main__ - INFO - 轮次 8, Epoch 2/5 完成, 损失: 0.0000
2025-08-27 12:53:46,277 - __main__ - INFO - 轮次 8, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:53:51,938 - __main__ - INFO - 轮次 8, Epoch 3/5, 批次 1000/1875, 损失: 0.0001
2025-08-27 12:53:56,969 - __main__ - INFO - 轮次 8, Epoch 3/5 完成, 损失: 0.0000
2025-08-27 12:53:58,097 - __main__ - INFO - 轮次 8, Epoch 4/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:54:03,864 - __main__ - INFO - 轮次 8, Epoch 4/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:54:09,229 - __main__ - INFO - 轮次 8, Epoch 4/5 完成, 损失: 0.0000
2025-08-27 12:54:10,460 - __main__ - INFO - 轮次 8, Epoch 5/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:54:16,397 - __main__ - INFO - 轮次 8, Epoch 5/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:54:21,453 - __main__ - INFO - 轮次 8, Epoch 5/5 完成, 损失: 0.0000
2025-08-27 12:54:23,799 - __main__ - INFO - 本地记录训练结果 - 轮次: 8, 损失: 0.0002, 准确率: 0.9952
2025-08-27 12:54:23,799 - __main__ - INFO - 训练完成 - 轮次: 8, 损失: 0.0002, 准确率: 0.9952, 耗时: 64.15秒
2025-08-27 12:54:23,799 - __main__ - INFO - 返回训练指标: {'loss': 0.00016879051307493764, 'accuracy': 0.9952, 'num_examples': 300000, 'training_time': 64.1508572101593}
2025-08-27 12:54:23,850 - __main__ - INFO - 开始评估模型性能...
2025-08-27 12:54:25,163 - __main__ - INFO - 评估完成 - 轮次: 8, 损失: 0.0344, 准确率: 0.9956, 样本数: 10000
2025-08-27 12:54:25,163 - __main__ - INFO - 返回评估指标: {'loss': 0.03441110933927215, 'accuracy': 0.9956, 'num_examples': 10000, 'evaluation_time': 1.3133673667907715}
2025-08-27 12:54:25,178 - __main__ - INFO - 开始第 9 轮训练，本地训练 5 个epoch
2025-08-27 12:54:25,188 - __main__ - INFO - 轮次 9, Epoch 1/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:54:34,062 - __main__ - INFO - 轮次 9, Epoch 1/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:54:40,903 - __main__ - INFO - 轮次 9, Epoch 1/5 完成, 损失: 0.0007
2025-08-27 12:54:42,203 - __main__ - INFO - 轮次 9, Epoch 2/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:54:48,390 - __main__ - INFO - 轮次 9, Epoch 2/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:54:54,293 - __main__ - INFO - 轮次 9, Epoch 2/5 完成, 损失: 0.0000
2025-08-27 12:54:55,463 - __main__ - INFO - 轮次 9, Epoch 3/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:55:01,373 - __main__ - INFO - 轮次 9, Epoch 3/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:55:06,511 - __main__ - INFO - 轮次 9, Epoch 3/5 完成, 损失: 0.0000
2025-08-27 12:55:07,752 - __main__ - INFO - 轮次 9, Epoch 4/5, 批次 0/1875, 损失: 0.0000
2025-08-27 12:55:14,002 - __main__ - INFO - 轮次 9, Epoch 4/5, 批次 1000/1875, 损失: 0.0000
2025-08-27 12:55:19,575 - __main__ - INFO - 轮次 9, Epoch 4/5 完成, 损失: 0.0000
2025-08-27 12:55:20,703 - __main__ - INFO - 轮次 9, Epoch 5/5, 批次 0/1875, 损失: 0.0000
