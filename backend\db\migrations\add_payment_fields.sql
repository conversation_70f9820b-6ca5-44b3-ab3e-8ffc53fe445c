-- 在任务表中添加支付相关字段
-- 先检查列是否存在，MySQL不支持ADD COLUMN IF NOT EXISTS
-- 使用存储过程动态添加列，避免重复列错误

delimiter //

-- 检查并添加reward列
DROP PROCEDURE IF EXISTS add_column_if_not_exists//
CREATE PROCEDURE add_column_if_not_exists()
BEGIN
    -- 检查reward列是否存在
    IF NOT EXISTS (
        SELECT * FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'tasks' AND COLUMN_NAME = 'reward'
    ) THEN
        ALTER TABLE tasks ADD COLUMN reward DECIMAL(10,2) DEFAULT 0.00 COMMENT '任务总奖励';
    END IF;
    
    -- 检查reward_distribution列是否存在
    IF NOT EXISTS (
        SELECT * FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'tasks' AND COLUMN_NAME = 'reward_distribution'
    ) THEN
        ALTER TABLE tasks ADD COLUMN reward_distribution VARCHAR(255) DEFAULT 'equal' COMMENT '奖励分配方式: equal, contribution, data';
    END IF;
    
    -- 检查payment_currency列是否存在
    IF NOT EXISTS (
        SELECT * FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'tasks' AND COLUMN_NAME = 'payment_currency'
    ) THEN
        ALTER TABLE tasks ADD COLUMN payment_currency VARCHAR(10) DEFAULT 'USD' COMMENT '支付币种: USD, CNY';
    END IF;
    
    -- 检查installment_payment列是否存在
    IF NOT EXISTS (
        SELECT * FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'tasks' AND COLUMN_NAME = 'installment_payment'
    ) THEN
        ALTER TABLE tasks ADD COLUMN installment_payment BOOLEAN DEFAULT TRUE COMMENT '是否分期付款';
    END IF;
    
    -- 检查first_payment_rate列是否存在
    IF NOT EXISTS (
        SELECT * FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'tasks' AND COLUMN_NAME = 'first_payment_rate'
    ) THEN
        ALTER TABLE tasks ADD COLUMN first_payment_rate INTEGER DEFAULT 40 COMMENT '首期付款比例';
    END IF;
    
    -- 检查platform_fee_rate列是否存在
    IF NOT EXISTS (
        SELECT * FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'tasks' AND COLUMN_NAME = 'platform_fee_rate'
    ) THEN
        ALTER TABLE tasks ADD COLUMN platform_fee_rate INTEGER DEFAULT 10 COMMENT '平台服务费率';
    END IF;
    
    -- 检查payment_status列是否存在
    IF NOT EXISTS (
        SELECT * FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'tasks' AND COLUMN_NAME = 'payment_status'
    ) THEN
        ALTER TABLE tasks ADD COLUMN payment_status VARCHAR(50) DEFAULT NULL COMMENT '支付状态: pending, first_payment_completed, pending_second_payment, all_payments_completed, rewards_distributed';
    END IF;
    
    -- 检查payment_method列是否存在
    IF NOT EXISTS (
        SELECT * FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'tasks' AND COLUMN_NAME = 'payment_method'
    ) THEN
        ALTER TABLE tasks ADD COLUMN payment_method VARCHAR(50) DEFAULT NULL COMMENT '支付方式: balance, credit_card, alipay, wechat, paypal';
    END IF;
    
    -- 检查区块链钱包支付相关字段是否存在
    -- 检查wallet_type列是否存在
    IF NOT EXISTS (
        SELECT * FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'tasks' AND COLUMN_NAME = 'wallet_type'
    ) THEN
        ALTER TABLE tasks ADD COLUMN wallet_type VARCHAR(50) DEFAULT NULL;
    END IF;
    
    -- 检查blockchain_tx_hash列是否存在
    -- 特别注意：此字段与tasks表的定义可能冲突，跳过添加
    
    -- 检查blockchain_second_payment_tx列是否存在
    IF NOT EXISTS (
        SELECT * FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'tasks' AND COLUMN_NAME = 'blockchain_second_payment_tx'
    ) THEN
        ALTER TABLE tasks ADD COLUMN blockchain_second_payment_tx VARCHAR(255) DEFAULT NULL;
    END IF;
    
END//

-- 调用存储过程
CALL add_column_if_not_exists()//

-- 创建交易表
CREATE TABLE IF NOT EXISTS transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT DEFAULT NULL COMMENT '服务端用户ID',
    task_id INT DEFAULT NULL COMMENT '关联任务ID',
    type VARCHAR(50) NOT NULL COMMENT '交易类型: task_first_payment, task_second_payment, platform_fee, participant_reward',
    amount DECIMAL(10,2) NOT NULL COMMENT '交易金额 (正数为收入，负数为支出)',
    status VARCHAR(50) DEFAULT 'pending' COMMENT '交易状态: pending, completed, failed',
    currency VARCHAR(10) DEFAULT 'USD' COMMENT '交易币种: USD, CNY',
    payment_method VARCHAR(50) DEFAULT NULL COMMENT '支付方式: balance, credit_card, alipay, wechat, paypal, system',
    description TEXT DEFAULT NULL COMMENT '交易描述',
    reference_id INT DEFAULT NULL COMMENT '关联交易ID',
    client_address VARCHAR(255) DEFAULT NULL COMMENT '客户端地址',
    -- 区块链支付相关字段
    blockchain_tx_hash VARCHAR(255),
    wallet_type VARCHAR(50),
    gas_fee DECIMAL(10,8),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_task_id (task_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_client_address (client_address)
)//

-- 添加示例任务付款状态数据
UPDATE tasks SET payment_status = 'pending' WHERE payment_status IS NULL//

delimiter ; 