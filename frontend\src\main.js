import { createApp } from 'vue'
import './style.css'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'
import router from './router'
import store from './store'

// 添加调试信息
console.log('应用初始化开始');

// 从本地存储恢复用户状态
const userStr = localStorage.getItem('user');
if (userStr) {
  try {
    const userData = JSON.parse(userStr);
    if (userData && userData.address && userData.role) {
      console.log('从本地存储恢复用户状态:', userData);
      store.commit('user/SET_USER', userData);
    }
  } catch (error) {
    console.error('解析本地存储的用户数据失败:', error);
    localStorage.removeItem('user');
  }
}

// 初始化应用
const app = createApp(App)
app.use(router)
app.use(store)
app.use(ElementPlus)

// 挂载应用
app.mount('#app')
console.log('应用已挂载');

// 移除多余的路由守卫，避免冲突
// 路由守卫已在router/index.js中定义
