import axios from 'axios';

const API_BASE_URL = 'http://localhost:5000/api';
const DB_API_URL = 'http://localhost:3001'; // 去掉重复的/api，因为下面的请求都会添加/api

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  timeout: 10000 // 10秒超时
});

// 创建数据库API实例
const dbApi = axios.create({
  baseURL: DB_API_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  timeout: 10000 // 10秒超时
});

// 导出dbApi实例供直接使用
export { dbApi };

// 响应拦截器
dbApi.interceptors.response.use(
  response => {
    // 只返回response而不直接返回response.data，让各个API方法自行处理
    return response;
  },
  error => {
    console.error('DB API Error:', error);
    return Promise.reject(error);
  }
);

// 添加缓存机制
export const cache = {
  serverStatus: {
    data: null,
    timestamp: 0
  },
  clientStatus: {
    data: {},
    timestamp: 0
  }
};

// 缓存有效期（毫秒）
const CACHE_TTL = 3000; // 减少到3秒缓存，使前端能更快获取到最新的训练状态

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    console.log(`Sending ${config.method.toUpperCase()} request to ${config.url}`);
    return config;
  },
  error => {
    // 对请求错误做些什么
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    return response.data;
  },
  error => {
    // 对响应错误做点什么
    console.error('API Error:', error);
    
    // 如果服务器返回了错误信息，使用它
    if (error.response && error.response.data && error.response.data.error) {
      return Promise.reject(new Error(error.response.data.error));
    }
    
    // 否则使用通用错误消息
    return Promise.reject(error);
  }
);

// API服务
const apiService = {
  // 服务器API
  getServerStatus: async () => {
    try {
      // 检查缓存是否有效
      const now = Date.now();
      if (cache.serverStatus.data && now - cache.serverStatus.timestamp < CACHE_TTL) {
        console.log('Using cached server status');
        return cache.serverStatus.data;
      }
      
      // 缓存无效，发送请求
      const response = await apiClient.get('/server/status');
      
      // 确保我们处理的是response.data
      const data = response.data;
      
      // 更新缓存
      cache.serverStatus = {
        data,
        timestamp: now
      };
      
      return data;
    } catch (error) {
      console.error('Error getting server status:', error);
      return {
        isActive: false,
        isTraining: false,
        currentRound: 0,
        totalRounds: 0,
        participants: [],
        metrics: {
          loss: 0,
          accuracy: 0
        },
        history: []
      };
    }
  },
  
  startTraining: async (config) => {
    return await apiClient.post('/server/start', config);
  },
  
  stopTraining: async () => {
    return await apiClient.post('/server/stop');
  },
  
  resetTraining: async () => {
    return await apiClient.post('/server/reset');
  },
  
  getFinalModel: async () => {
    return await apiClient.get('/server/model');
  },
  
  // 客户端API
  joinTraining: async (account, dataset) => {
    // 确保dataset是一个对象
    const datasetObj = dataset || {
      name: "MNIST",
      size: 60000,
      classes: 10,
      type: "image"
    };
    
    // 准备发送到客户端的数据
    const clientPayload = { 
      account,  // 客户端API使用account
      dataset: datasetObj
    };
    
    // 根据账户地址选择不同的客户端端口
    let clientPort;
    
    // 硬编码账户地址与端口的映射关系
    if (account === "0xe56B1329f32D9c289C40054Fb20239DE52D6BbC0") {
      clientPort = 5001;
    } else if (account === "0x8D506914151782957A0627c18be93c0b7E770DFA") {
      clientPort = 5002;
    } else {
      // 对于其他账户，使用5003
      clientPort = 5003;
    }
    
    // 保存当前使用的端口到localStorage
    localStorage.setItem('clientPort', clientPort);
    
    console.log(`账户 ${account} 使用客户端端口: ${clientPort}`);
    
    const clientUrl = `http://localhost:${clientPort}/api/client/join`;
    
    try {
      console.log('发送到客户端的数据:', JSON.stringify(clientPayload));
      const response = await axios.post(clientUrl, clientPayload, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        timeout: 10000
      });
      
      return response.data;
    } catch (error) {
      console.error('加入训练失败:', error);
      if (error.response && error.response.data) {
        console.error('服务器返回错误:', error.response.data);
        throw new Error(error.response.data.message || error.response.data.error || '加入训练失败');
      }
      throw error;
    }
  },
  
  leaveTraining: async (address) => {
    return await apiClient.post('/client/leave', { address });
  },
  
  getClientStatus: async (address) => {
    try {
      // 检查缓存是否有效
      const now = Date.now();
      if (cache.clientStatus.data[address] && now - cache.clientStatus.timestamp < CACHE_TTL) {
        console.log(`Using cached client status for ${address}`);
        return cache.clientStatus.data[address];
      }
      
      // 根据账户地址选择不同的客户端端口
      let clientPort;
      
      // 硬编码账户地址与端口的映射关系
      if (address === "0xe56B1329f32D9c289C40054Fb20239DE52D6BbC0") {
        clientPort = 5001;
      } else if (address === "0x8D506914151782957A0627c18be93c0b7E770DFA") {
        clientPort = 5002;
      } else {
        // 对于其他账户，使用5003
        clientPort = 5003;
      }
      
      console.log(`获取客户端状态: 账户 ${address} 使用端口 ${clientPort}`);
      
      // 使用对应的客户端端口
      const clientUrl = `http://localhost:${clientPort}/api/client/status/${address}`;
      const response = await axios.get(clientUrl, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        timeout: 5000
      });
      
      const data = response.data;
      
      // 更新缓存
      if (!cache.clientStatus.data) {
        cache.clientStatus.data = {};
      }
      cache.clientStatus.data[address] = data;
      cache.clientStatus.timestamp = now;
      
      return data;
    } catch (error) {
      console.error(`Error getting client status for ${address}:`, error);
      return {
        isParticipating: false,
        currentRound: 0,
        metrics: {
          loss: 0,
          accuracy: 0
        },
        history: []
      };
    }
  },
  
  getClientConfig: async (address) => {
    try {
      // 添加缓存键
      const cacheKey = `config_${address}`;
      
      // 检查缓存是否有效
      const now = Date.now();
      if (cache[cacheKey] && now - cache[cacheKey].timestamp < CACHE_TTL * 2) {
        console.log(`使用缓存的客户端配置: ${address}`);
        return cache[cacheKey].data;
      }
      
      // 根据账户地址选择不同的客户端端口
      let clientPort;
      
      // 硬编码账户地址与端口的映射关系
      if (address === "0xe56B1329f32D9c289C40054Fb20239DE52D6BbC0") {
        clientPort = 5001;
      } else if (address === "0x8D506914151782957A0627c18be93c0b7E770DFA") {
        clientPort = 5002;
      } else {
        // 对于其他账户，使用5003
        clientPort = 5003;
      }
      
      console.log(`获取客户端配置: 账户 ${address} 使用端口 ${clientPort}`);
      
      // 使用对应的客户端端口
      const clientUrl = `http://localhost:${clientPort}/api/client/config/${address}`;
      const response = await axios.get(clientUrl, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        timeout: 5000
      });
      
      const data = response.data;
      
      // 更新缓存
      cache[cacheKey] = {
        data,
        timestamp: now
      };
      
      return data;
    } catch (error) {
      console.error(`获取客户端配置失败: ${address}:`, error);
      // 返回默认配置
      return {
        computeResource: 'cpu',
        batchSize: 32,
        modelConfig: {
          learningRate: 0.001,
          epochs: 5
        },
        persistentConnection: false
      };
    }
  },
  
  getDatasetInfo: async (address) => {
    try {
      // 添加缓存键
      const cacheKey = `dataset_${address}`;
      
      // 检查缓存是否有效
      const now = Date.now();
      if (cache[cacheKey] && now - cache[cacheKey].timestamp < CACHE_TTL * 2) { // 数据集信息缓存时间更长
        console.log(`Using cached dataset info for ${address}`);
        return cache[cacheKey].data;
      }
      
      // 根据账户地址选择不同的客户端端口
      let clientPort;
      
      // 硬编码账户地址与端口的映射关系
      if (address === "0xe56B1329f32D9c289C40054Fb20239DE52D6BbC0") {
        clientPort = 5001;
      } else if (address === "0x8D506914151782957A0627c18be93c0b7E770DFA") {
        clientPort = 5002;
      } else {
        // 对于其他账户，使用5003
        clientPort = 5003;
      }
      
      console.log(`获取数据集信息: 账户 ${address} 使用端口 ${clientPort}`);
      
      // 使用对应的客户端端口
      const clientUrl = `http://localhost:${clientPort}/api/client/dataset/${address}`;
      const response = await axios.get(clientUrl, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        timeout: 5000
      });
      
      const data = response.data;
      
      // 更新缓存
      cache[cacheKey] = {
        data,
        timestamp: now
      };
      
      return data;
    } catch (error) {
      console.error(`Error getting dataset info for ${address}:`, error);
      return {
        name: 'Unknown',
        size: 0,
        classes: 0
      };
    }
  },
  
  markResultSubmitted: async (address, round) => {
    try {
      // 根据账户地址选择不同的客户端端口
      let clientPort;
      
      // 硬编码账户地址与端口的映射关系
      if (address === "0xe56B1329f32D9c289C40054Fb20239DE52D6BbC0") {
        clientPort = 5001;
      } else if (address === "0x8D506914151782957A0627c18be93c0b7E770DFA") {
        clientPort = 5002;
      } else {
        // 对于其他账户，使用5003
        clientPort = 5003;
      }
      
      console.log(`标记结果已提交: 账户 ${address} 轮次 ${round} 使用端口 ${clientPort}`);
      
      // 使用对应的客户端端口
      const clientUrl = `http://localhost:${clientPort}/api/client/mark_submitted`;
      const response = await axios.post(clientUrl, {
        address,
        round
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        timeout: 5000
      });
      
      return response.data;
    } catch (error) {
      console.error(`标记结果已提交失败: ${address}, 轮次 ${round}:`, error);
      throw error;
    }
  },
  
  // 获取所有任务列表
  getAllTasks: async () => {
    try {
      // 从localStorage中获取任务列表
      const savedTasks = localStorage.getItem('tasks');
      let tasks = [];
      
      if (savedTasks) {
        try {
          tasks = JSON.parse(savedTasks);
          // 确保日期对象正确
          tasks = tasks.map(task => ({
            ...task,
            createdAt: new Date(task.createdAt),
            deadline: new Date(task.deadline || Date.now() + 7 * 24 * 60 * 60 * 1000)
          }));
        } catch (e) {
          console.error('解析本地任务失败:', e);
        }
      }
      
      // 合并本地任务和服务器任务 (模拟)
      const defaultTasks = [
        {
          id: 1,
          name: 'MNIST手写数字识别训练',
          description: '基于MNIST数据集的手写数字识别模型训练，需要提供高质量的手写数字样本。',
          status: 'published',
          dataType: 'image',
          applications: 8,
          requiredParticipants: 10,
          totalRounds: 10,
          reward: 500,
          deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
          requirements: [
            '至少1000个手写数字样本',
            'GPU计算资源优先',
            '需要维持在线状态直到训练完成'
          ]
        },
        {
          id: 2,
          name: '医疗影像分类模型',
          description: '利用胸部X光片进行肺炎检测，需要提供匿名化的医疗影像数据。',
          status: 'published',
          dataType: 'image',
          applications: 3,
          requiredParticipants: 5,
          totalRounds: 8,
          reward: 800,
          deadline: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
          createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
          requirements: [
            '需要医疗影像数据',
            '每个客户端至少提供500张标注图像',
            '高性能GPU优先'
          ]
        }
      ];
      
      // 结合默认任务和本地任务，避免重复
      const allTasks = [...defaultTasks];
      
      // 添加本地任务（如果ID不重复）
      tasks.forEach(task => {
        if (!allTasks.some(t => t.id === task.id)) {
          allTasks.push(task);
        }
      });
      
      return allTasks;
    } catch (error) {
      console.error('获取任务列表失败:', error);
      return [];
    }
  },
  
  // 获取可用任务列表（客户端视角）
  getAvailableTasks: async (clientAddress) => {
    try {
      if (!clientAddress) {
        console.error('获取可用任务时未提供客户端地址');
        return [];
      }
      
      console.log(`正在获取用户 ${clientAddress} 的可用任务`);
      
      // 获取所有任务
      const allTasks = await apiService.getAllTasks();
      console.log(`获取到 ${allTasks.length} 个任务`);
      
      // 获取当前客户端的申请记录 - 从数据库获取最新的申请记录
      console.log(`正在从数据库获取用户 ${clientAddress} 的申请记录...`);
      let applications = [];
      try {
        applications = await ParticipantAPI.getByClientAddress(clientAddress);
        console.log(`成功从数据库获取到 ${applications.length} 条申请记录:`, applications);
      } catch (error) {
        console.error(`从数据库获取用户 ${clientAddress} 的申请记录失败:`, error);
        // 如果从数据库获取失败，尝试从localStorage获取
        console.log('尝试从localStorage获取申请记录');
        const storageKey = `applications_${clientAddress}`;
        const savedApplications = localStorage.getItem(storageKey);
        if (savedApplications) {
          try {
            const localApps = JSON.parse(savedApplications);
            applications = localApps.map(app => ({
              task_id: app.taskId,
              client_address: app.clientAddress || clientAddress,
              status: app.status,
              data_size: app.dataSize,
              compute_resource: app.computeResource
            }));
            console.log(`从localStorage获取到 ${applications.length} 条申请记录`);
          } catch (e) {
            console.error('解析localStorage申请记录失败:', e);
          }
        }
      }
      
      console.log(`获取用户 ${clientAddress} 的申请记录:`, applications);
      
      // 标记客户端是否已申请每个任务
      const availableTasks = allTasks
        .filter(task => task.status === 'published') // 只返回已发布状态的任务
        .map(task => {
          // 检查当前用户是否已申请此任务
          const hasApplied = applications.some(app => {
            const result = parseInt(app.task_id) === parseInt(task.id);
            if (result) {
              console.log(`用户已申请任务 ${task.id}, 申请状态:`, app.status);
            }
            return result;
          });
          
          return {
            ...task,
            hasApplied
          };
        });
      
      console.log(`过滤后的可用任务: ${availableTasks.length} 个`);
      return availableTasks;
    } catch (error) {
      console.error('获取可用任务列表失败:', error);
      if (error.response) {
        console.error('HTTP错误:', error.response.status, error.response.data);
      } else if (error.request) {
        console.error('无响应:', error.request);
      } else {
        console.error('请求错误:', error.message);
      }
      return [];
    }
  },
  
  // 获取客户端的申请记录
  getClientApplications: async (clientAddress) => {
    try {
      console.log(`获取客户端 ${clientAddress} 的应用列表`);
      
      // 调用API获取客户端的应用列表
      try {
        const response = await dbApi.get(`/api/participants?client_address=${clientAddress}`);
        return response.data || [];
      } catch (apiError) {
        console.error('获取应用列表API调用失败:', apiError);
        
        // 如果API调用失败，返回空数组
        return [];
      }
    } catch (error) {
      console.error('获取客户端应用列表失败:', error);
      return [];
    }
  },
  
  // 申请参与任务
  applyForTask: async (clientAddress, taskId, applicationData) => {
    try {
      console.log(`用户 ${clientAddress} 申请参与任务 ${taskId}`);
      
      // 1. 先保存到数据库
      try {
        // 构建API请求数据 - 按后端要求的格式
        const participantData = {
          task_id: parseInt(taskId),
          client_address: clientAddress,
          status: 'registered', // 在数据库中初始状态为registered
          data_size: applicationData.dataSize || 1000,
          compute_resource: applicationData.computeResource || 'cpu'
        };
        
        console.log('发送到后端的参与者数据:', participantData);
        
        // 调用后端API创建参与者记录
        const dbResult = await ParticipantAPI.create(participantData);
        console.log('数据库保存成功，响应:', dbResult);
        
        // 2. 然后保存到localStorage以维持前端状态
      // 生成申请ID
      const applicationId = Date.now();
      
      // 创建申请记录
      const application = {
        id: applicationId,
        clientAddress,
        taskId,
        status: 'pending',
        applyTime: new Date(),
        reviewTime: null,
          dbId: dbResult.id, // 将数据库ID保存到本地记录中
        ...applicationData
      };
      
      // 保存到localStorage
      const savedApplications = localStorage.getItem(`applications_${clientAddress}`);
      let applications = [];
      
      if (savedApplications) {
        try {
          applications = JSON.parse(savedApplications);
        } catch (e) {
          console.error('解析申请记录失败:', e);
        }
      }
      
      // 检查是否已经有相同任务的申请
      if (applications.some(app => app.taskId === taskId)) {
          console.log('本地已有申请记录，已被覆盖');
      }
      
        // 更新或添加申请
        const existingIndex = applications.findIndex(app => app.taskId === taskId);
        if (existingIndex !== -1) {
          applications[existingIndex] = application;
        } else {
      applications.push(application);
        }
        
      localStorage.setItem(`applications_${clientAddress}`, JSON.stringify(applications));
      
        // 更新任务的申请数量（本地状态）
      const allTasks = await apiService.getAllTasks();
      const taskIndex = allTasks.findIndex(task => task.id === taskId);
      
      if (taskIndex !== -1) {
        const task = allTasks[taskIndex];
        task.applications = (task.applications || 0) + 1;
        
        // 更新localStorage中的任务
        localStorage.setItem('tasks', JSON.stringify(allTasks));
      }
      
      return application;
      } catch (dbError) {
        console.error('将申请保存到数据库失败:', dbError);
        throw new Error(`保存申请失败：${dbError.message}`);
      }
    } catch (error) {
      console.error('申请任务失败:', error);
      throw error;
    }
  },
  
  // 管理员审核任务申请
  reviewApplication: async (applicationId, taskId, status, reviewerAddress) => {
    try {
      console.log(`审核申请ID ${applicationId}，任务 ${taskId}，状态 ${status}`);
      
      // 获取所有客户端地址
      const clientAddresses = [
        "0xe56B1329f32D9c289C40054Fb20239DE52D6BbC0", 
        "0x8D506914151782957A0627c18be93c0b7E770DFA",
        "0x123456789abcdef123456789abcdef123456789ab" // 示例地址
      ];
      
      // 遍历客户端查找该申请在localStorage中的记录
      let foundApplication = null;
      let clientAddress = null;
      
      for (const address of clientAddresses) {
        const savedApplications = localStorage.getItem(`applications_${address}`);
        if (!savedApplications) continue;
        
        try {
          const applications = JSON.parse(savedApplications);
          const applicationIndex = applications.findIndex(app => app.id === applicationId);
          
          if (applicationIndex !== -1) {
            // 找到了申请
            foundApplication = applications[applicationIndex];
            clientAddress = address;
            
            // 更新申请状态
            applications[applicationIndex].status = status;
            applications[applicationIndex].reviewTime = new Date();
            applications[applicationIndex].reviewerAddress = reviewerAddress;
            
            // 保存回localStorage
            localStorage.setItem(`applications_${address}`, JSON.stringify(applications));
            break;
          }
        } catch (e) {
          console.error('解析申请记录失败:', e);
        }
      }
      
      if (!foundApplication) {
        throw new Error('未找到该申请记录');
      }
      
      // 更新数据库中的状态
      try {
        console.log(`正在更新数据库中参与者ID ${foundApplication.dbId} 的状态为 ${status === 'approved' ? 'training' : 'rejected'}`);
        
        // 将前端状态映射到数据库状态
        let dbStatus;
        if (status === 'approved') {
          dbStatus = 'training'; // 批准的申请在数据库中状态为training
        } else if (status === 'rejected') {
          dbStatus = 'rejected'; // 拒绝的申请
        } else {
          dbStatus = 'registered'; // 其他情况保持registered
        }
        
        // 调用后端API更新参与者状态
        if (foundApplication.dbId) {
          await ParticipantAPI.updateStatus(foundApplication.dbId, dbStatus);
          console.log('数据库状态更新成功');
        } else {
          console.warn('无法找到申请对应的数据库ID，无法更新数据库状态');
        }
      } catch (dbError) {
        console.error('更新数据库状态失败:', dbError);
        // 继续处理本地状态，避免前端中断
      }
      
      // 如果批准申请，则更新任务的参与者列表
      if (status === 'approved') {
        const allTasks = await apiService.getAllTasks();
        const taskIndex = allTasks.findIndex(task => task.id.toString() === taskId.toString());
        
        if (taskIndex !== -1) {
          // 确保participants是数组
          if (!Array.isArray(allTasks[taskIndex].participants)) {
            allTasks[taskIndex].participants = [];
          }
          
          // 检查客户端是否已经在参与者列表中
          if (!allTasks[taskIndex].participants.includes(clientAddress)) {
            // 添加客户端到参与者列表
            allTasks[taskIndex].participants.push(clientAddress);
            
            console.log(`已将客户端 ${clientAddress} 添加到任务 ${taskId} 的参与者列表中`);
            console.log('更新后的参与者列表:', allTasks[taskIndex].participants);
          }
          
          // 更新localStorage中的任务
          localStorage.setItem('tasks', JSON.stringify(allTasks));
        }
      }
      
      return { success: true, application: foundApplication };
    } catch (error) {
      console.error('审核申请失败:', error);
      throw error;
    }
  },
  
  // 开始训练任务
  startTask: async (taskId) => {
    try {
      const allTasks = await apiService.getAllTasks();
      const taskIndex = allTasks.findIndex(task => task.id.toString() === taskId.toString());
      
      if (taskIndex === -1) {
        throw new Error('任务不存在');
      }
      
      const task = allTasks[taskIndex];
      
      // 验证参与者数量
      const currentParticipants = task.participants?.length || 0;
      const requiredParticipants = task.requiredParticipants || 2;
      
      if (currentParticipants < requiredParticipants) {
        throw new Error(`参与者数量不足，需要至少 ${requiredParticipants} 名参与者才能开始训练`);
      }
      
      // 准备训练配置参数
      const trainingConfig = {
        taskId: task.id,
        rounds: task.totalRounds,
        participants: task.participants,
        learningRate: task.learningRate || 0.001,
        batchSize: task.batchSize || 32,
        timeout: task.timeout || 60
      };
      
      console.log('启动训练任务，配置:', trainingConfig);
      
      // 调用后端API启动联邦学习服务
      try {
        // 实际环境中，这应该是一个实际的API调用
        // 这里使用mock API
        const port = 5000; // 服务端端口
        const serverUrl = `http://localhost:${port}/api/server/start`;
        
        const response = await axios.post(serverUrl, trainingConfig, {
          headers: {
            'Content-Type': 'application/json'
          }
        }).catch(err => {
          console.log('调用后端API启动训练失败，使用模拟响应:', err);
          // 在实际API不可用时模拟响应
          return {
            data: {
              success: true,
              message: '训练已启动（模拟响应）',
              taskId: task.id,
              status: 'in_progress'
            }
          };
        });
        
        console.log('后端响应:', response.data);
        
        // 发送通知给所有参与客户端
        for (const clientAddress of task.participants) {
          await apiService.notifyClientToStart(clientAddress, task.id);
        }
      } catch (apiError) {
        console.error('调用联邦学习API失败:', apiError);
        // API 调用失败，但仍然更新本地状态以便演示
      }
      
      // 更新任务状态
      allTasks[taskIndex].status = 'in_progress';
      allTasks[taskIndex].currentRound = 1;
      allTasks[taskIndex].startTime = new Date();
      
      console.log(`任务 ${taskId} 开始训练，参与者: ${task.participants?.length}，状态: ${allTasks[taskIndex].status}`);
      
      // 更新localStorage中的任务
      localStorage.setItem('tasks', JSON.stringify(allTasks));
      
      return { success: true, task: allTasks[taskIndex] };
    } catch (error) {
      console.error('开始任务失败:', error);
      throw error;
    }
  },
  
  // 通知客户端开始训练
  notifyClientToStart: async (clientAddress, taskId) => {
    try {
      // 获取客户端端口
      let clientPort;
      if (clientAddress === "0xe56B1329f32D9c289C40054Fb20239DE52D6BbC0") {
        clientPort = 5001;
      } else if (clientAddress === "0x8D506914151782957A0627c18be93c0b7E770DFA") {
        clientPort = 5002;
      } else {
        clientPort = 5003;
      }
      
      console.log(`通知客户端 ${clientAddress} (端口: ${clientPort}) 开始参与任务 ${taskId}`);
      
      // 构建请求数据
      const requestData = {
        taskId: taskId,
        action: 'start_training'
      };
      
      // 调用客户端API
      try {
        const clientUrl = `http://localhost:${clientPort}/api/client/start_training`;
        const response = await axios.post(clientUrl, requestData, {
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        console.log(`客户端 ${clientAddress} 响应:`, response.data);
        return response.data;
      } catch (apiError) {
        console.error(`通知客户端 ${clientAddress} 失败:`, apiError);
        // 继续执行，不中断整个流程
        return { success: false, error: apiError.message };
      }
    } catch (error) {
      console.error('通知客户端失败:', error);
      throw error;
    }
  },
  
  // 客户端开始参与训练
  startClientTraining: async (clientAddress, taskId) => {
    try {
      console.log(`客户端 ${clientAddress} 开始参与任务 ${taskId} 的训练`);
      
      // 获取任务信息
      const allTasks = await apiService.getAllTasks();
      const task = allTasks.find(t => t.id.toString() === taskId.toString());
      
      if (!task) {
        throw new Error('任务不存在');
      }
      
      // 检查任务状态
      if (task.status !== 'in_progress') {
        throw new Error(`任务状态(${task.status})不允许参与训练，需要为"进行中"状态`);
      }
      
      // 检查客户端是否被批准参与
      if (!task.participants || !task.participants.includes(clientAddress)) {
        throw new Error('您未被批准参与此任务');
      }
      
      // 获取客户端端口
      let clientPort;
      if (clientAddress === "0xe56B1329f32D9c289C40054Fb20239DE52D6BbC0") {
        clientPort = 5001;
      } else if (clientAddress === "0x8D506914151782957A0627c18be93c0b7E770DFA") {
        clientPort = 5002;
      } else {
        clientPort = 5003;
      }
      
      // 构建训练参数
      const trainingParams = {
        taskId: task.id,
        clientAddress: clientAddress,
        currentRound: task.currentRound,
        totalRounds: task.totalRounds,
        learningRate: task.learningRate || 0.001,
        batchSize: task.batchSize || 32
      };
      
      // 调用客户端API启动本地训练
      try {
        const clientUrl = `http://localhost:${clientPort}/api/client/train`;
        console.log(`尝试连接到已启动的客户端API: ${clientUrl}`);
        
        const response = await axios.post(clientUrl, {
          action: 'start',
          taskId: task.id,
          ...trainingParams
        }, {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 5000 // 设置5秒超时，避免长时间等待
        });
        
        console.log(`客户端 ${clientAddress} 开始训练，响应:`, response.data);
        
        // 保存客户端训练状态
        localStorage.setItem(`training_status_${clientAddress}_${taskId}`, JSON.stringify({
          isTraining: true,
          startedAt: new Date(),
          currentRound: task.currentRound,
          totalRounds: task.totalRounds
        }));
        
        return response.data;
      } catch (apiError) {
        console.error('调用客户端训练API失败:', apiError);
        throw new Error(`连接到客户端失败: ${apiError.message}。请确保客户端已启动并正在运行。`);
      }
    } catch (error) {
      console.error('客户端开始训练失败:', error);
      throw error;
    }
  },
  
  // 获取客户端训练状态
  getClientTrainingStatus: async (clientAddress, taskId) => {
    try {
      console.log(`获取客户端 ${clientAddress} 在任务 ${taskId} 中的训练状态`);
      
      // 从localStorage获取训练状态
      const savedStatus = localStorage.getItem(`training_status_${clientAddress}_${taskId}`);
      
      if (savedStatus) {
        try {
          return JSON.parse(savedStatus);
        } catch (e) {
          console.error('解析训练状态失败:', e);
        }
      }
      
      // 如果没有找到保存的状态，则返回默认状态
      return {
        isTraining: false,
        currentRound: 0,
        totalRounds: 0,
        accuracy: 0,
        loss: 0
      };
    } catch (error) {
      console.error('获取客户端训练状态失败:', error);
      return {
        isTraining: false,
        error: error.message
      };
    }
  },
  
  // 提交训练结果
  submitTrainingResult: async (clientAddress, taskId, result) => {
    try {
      // 获取任务
      const allTasks = await apiService.getAllTasks();
      const taskIndex = allTasks.findIndex(task => task.id === taskId);
      
      if (taskIndex === -1) {
        throw new Error('任务不存在');
      }
      
      // 确保task有训练历史记录
      if (!allTasks[taskIndex].history) {
        allTasks[taskIndex].history = [];
      }
      
      // 添加训练结果到历史记录
      const trainingResult = {
        clientAddress,
        round: allTasks[taskIndex].currentRound,
        loss: result.loss,
        accuracy: result.accuracy,
        timestamp: new Date()
      };
      
      allTasks[taskIndex].history.push(trainingResult);
      
      // 如果所有参与者都提交了当前轮次的结果，进入下一轮
      const currentRound = allTasks[taskIndex].currentRound;
      const participantsCount = allTasks[taskIndex].participants?.length || 0;
      const roundResults = allTasks[taskIndex].history.filter(h => h.round === currentRound);
      
      if (roundResults.length >= participantsCount) {
        // 计算平均准确率和损失值
        const avgAccuracy = roundResults.reduce((sum, r) => sum + r.accuracy, 0) / roundResults.length;
        const avgLoss = roundResults.reduce((sum, r) => sum + r.loss, 0) / roundResults.length;
        
        // 更新任务准确率
        allTasks[taskIndex].accuracy = avgAccuracy;
        allTasks[taskIndex].loss = avgLoss;
        
        // 进入下一轮或完成任务
        if (currentRound >= allTasks[taskIndex].totalRounds) {
          allTasks[taskIndex].status = 'completed';
          allTasks[taskIndex].completedTime = new Date();
        } else {
          allTasks[taskIndex].currentRound = currentRound + 1;
        }
      }
      
      // 更新localStorage中的任务
      localStorage.setItem('tasks', JSON.stringify(allTasks));
      
      return { success: true, result: trainingResult };
    } catch (error) {
      console.error('提交训练结果失败:', error);
      throw error;
    }
  },
  
  // 获取本地训练状态(给ClientHome使用)
  getLocalTrainingStatus: async () => {
    try {
      console.log('获取本地训练状态');
      
      // 获取当前用户地址
      // 从store中获取用户信息
      const userJSON = localStorage.getItem('user');
      let address = null;
      
      if (userJSON) {
        try {
          const userData = JSON.parse(userJSON);
          address = userData.address;
        } catch (e) {
          console.error('解析用户数据失败:', e);
        }
      }
      
      if (!address) {
        return {
          isParticipating: false,
          metrics: { loss: 0, accuracy: 0, epochs: 0, trainingTime: 0 },
          contribution: { roundsParticipated: 0, dataContributed: 0, avgAccuracy: 0, totalReward: 0 },
          resources: { cpu: 0, memory: 0, network: 0, storage: 0 },
          history: []
        };
      }
      
      // 尝试获取所有任务中的训练状态
      const tasks = await apiService.getAllTasks();
      const activeTasks = tasks.filter(task => 
        task.status === 'in_progress' || task.status === 'active'
      );
      
      let isParticipating = false;
      let metrics = { loss: 0, accuracy: 0, epochs: 0, trainingTime: 0 };
      let history = [];
      
      // 遍历所有活跃任务，查找该用户参与的任务
      for (const task of activeTasks) {
        const taskId = task.id;
        const statusKey = `training_status_${address}_${taskId}`;
        const savedStatus = localStorage.getItem(statusKey);
        
        if (savedStatus) {
          try {
            const status = JSON.parse(savedStatus);
            if (status.isTraining) {
              isParticipating = true;
              
              // 获取最新指标
              metrics.loss = status.loss || 0;
              metrics.accuracy = status.accuracy || 0;
              metrics.epochs = status.currentRound || 0;
              
              // 计算训练时长
              if (status.startedAt) {
                const startTime = new Date(status.startedAt);
                const now = new Date();
                metrics.trainingTime = Math.floor((now - startTime) / 1000);
              }
              
              // 获取历史记录
              if (status.history) {
                history = status.history;
              }
              
              break; // 找到一个正在参与的任务就跳出循环
            }
          } catch (e) {
            console.error('解析训练状态失败:', e);
          }
        }
      }
      
      // 模拟贡献统计
      const contribution = {
        roundsParticipated: metrics.epochs || Math.floor(Math.random() * 10),
        dataContributed: Math.floor(Math.random() * 5000) + 1000,
        avgAccuracy: metrics.accuracy || Math.random() * 0.3 + 0.6,
        totalReward: Math.floor(Math.random() * 500) + 100
      };
      
      // 模拟资源使用情况
      const resources = {
        cpu: Math.random() * 0.6 + 0.2,
        memory: Math.random() * 0.5 + 0.3,
        network: Math.random() * 0.4 + 0.1,
        storage: Math.random() * 0.7 + 0.1
      };
      
      return {
        isParticipating,
        metrics,
        contribution,
        resources,
        history
      };
    } catch (error) {
      console.error('获取本地训练状态失败:', error);
      return {
        isParticipating: false,
        metrics: { loss: 0, accuracy: 0, epochs: 0, trainingTime: 0 },
        contribution: { roundsParticipated: 0, dataContributed: 0, avgAccuracy: 0, totalReward: 0 },
        resources: { cpu: 0, memory: 0, network: 0, storage: 0 },
        history: []
      };
    }
  },
  
  // 更新任务信息
  updateTask: async (taskId, updates) => {
    try {
      const allTasks = await apiService.getAllTasks();
      const taskIndex = allTasks.findIndex(task => task.id === taskId);
      
      if (taskIndex === -1) {
        throw new Error('任务不存在');
      }
      
      // 更新任务信息
      allTasks[taskIndex] = {
        ...allTasks[taskIndex],
        ...updates,
        updatedAt: new Date()
      };
      
      // 保存更新到localStorage
      localStorage.setItem('tasks', JSON.stringify(allTasks));
      
      return { success: true, task: allTasks[taskIndex] };
    } catch (error) {
      console.error('更新任务失败:', error);
      throw error;
    }
  },
  
  // 获取任务的所有参与者申请
  getTaskApplications: async (taskId) => {
    try {
      console.log(`获取任务 ${taskId} 的所有申请`);
      
      // 创建一个结果数组存放所有申请
      let allApplications = [];
      
      // 1. 首先从数据库获取所有申请记录
      try {
        console.log(`从数据库获取任务 ${taskId} 的申请记录...`);
        const response = await dbApi.get(`/api/tasks/${taskId}/participants`);
        const dbApplications = response.data || [];
        console.log(`数据库返回 ${dbApplications.length} 条申请记录:`, dbApplications);
        
        // 将数据库中的记录转换为前端格式
        const formattedDbApplications = dbApplications.map(app => ({
          id: app.id,
          dbId: app.id,
          taskId: app.task_id,
          clientAddress: app.client_address,
          status: app.status === 'training' ? 'approved' : app.status,
          dataSize: app.data_size,
          computeResource: app.compute_resource,
          applyTime: new Date(app.created_at),
          reviewTime: app.last_update ? new Date(app.last_update) : null
        }));
        
        allApplications = formattedDbApplications;
        console.log(`转换后的申请记录 ${allApplications.length} 条`);
      } catch (dbError) {
        console.error(`从数据库获取任务 ${taskId} 的申请记录失败:`, dbError);
        console.log('将尝试从localStorage获取申请记录');
        
        // 2. 如果数据库查询失败，则从localStorage获取
        // 获取所有客户端地址
        const clientAddresses = [
          "0xe56B1329f32D9c289C40054Fb20239DE52D6BbC0", 
          "0x8D506914151782957A0627c18be93c0b7E770DFA",
          "0x123456789abcdef123456789abcdef123456789ab" // 示例地址
        ];
        
        // 遍历所有客户端地址，获取他们对此任务的申请
        for (const address of clientAddresses) {
          const savedApplications = localStorage.getItem(`applications_${address}`);
          if (!savedApplications) continue;
          
          try {
            const applications = JSON.parse(savedApplications);
            const taskApplications = applications.filter(app => app.taskId.toString() === taskId.toString());
            
            if (taskApplications.length > 0) {
              allApplications.push(...taskApplications);
            }
          } catch (e) {
            console.error(`解析用户 ${address} 的申请记录失败:`, e);
          }
        }
      }
      
      // 只返回已批准的申请作为实际参与者（状态为 'approved', 'training', 'completed' 等非'registered'和'rejected'状态）
      const approvedApplications = allApplications.filter(app => 
        app.status === 'approved' || app.status === 'training' || app.status === 'completed'
      );
      
      console.log(`任务 ${taskId} 的所有申请 ${allApplications.length} 条，其中已批准 ${approvedApplications.length} 条`);
      
      return {
        all: allApplications,
        approved: approvedApplications
      };
    } catch (error) {
      console.error('获取任务申请失败:', error);
      return { all: [], approved: [] };
    }
  },
  
  // 获取所有申请（所有客户端）
  getAllApplications: async () => {
    try {
      // 获取注册的客户端地址列表
      const clientAddresses = [
        "0xe56B1329f32D9c289C40054Fb20239DE52D6BbC0", 
        "0x8D506914151782957A0627c18be93c0b7E770DFA",
        "0x123456789abcdef123456789abcdef123456789ab",
        // 可以添加更多客户端地址
      ];
      
      // 收集所有客户端的申请
      let allApplications = [];
      
      // 收集存储在公共applications键中的申请
      const savedCommonApplications = localStorage.getItem('applications');
      if (savedCommonApplications) {
        try {
          const commonApplications = JSON.parse(savedCommonApplications);
          allApplications = [...allApplications, ...commonApplications];
        } catch (e) {
          console.error('解析公共申请记录失败:', e);
        }
      }
      
      // 收集各个客户端的申请
      for (const address of clientAddresses) {
        const savedApplications = localStorage.getItem(`applications_${address}`);
        if (!savedApplications) continue;
        
        try {
          const applications = JSON.parse(savedApplications);
          // 确保每个申请都有客户端地址
          const clientApplications = applications.map(app => ({
            ...app,
            clientAddress: app.clientAddress || address,
            applicant: app.applicant || address,
            applyTime: new Date(app.applyTime || Date.now()),
            reviewTime: app.reviewTime ? new Date(app.reviewTime) : null
          }));
          
          allApplications = [...allApplications, ...clientApplications];
        } catch (e) {
          console.error(`解析客户端 ${address} 的申请记录失败:`, e);
        }
      }
      
      return allApplications;
    } catch (error) {
      console.error('获取所有申请记录失败:', error);
      return [];
    }
  },

  // 完成模型下载并支付二期款项
  completeModelDownload: async (taskId, userId, additionalData = {}) => {
    try {
      console.log(`完成任务(ID: ${taskId})模型下载，用户ID: ${userId}，附加数据:`, additionalData);
      const requestData = { userId, ...additionalData };
      console.log('发送到后端的请求数据:', JSON.stringify(requestData));
      const response = await dbApi.post(`/api/tasks/${taskId}/complete-download`, requestData);
      return response.data;
    } catch (error) {
      console.error(`完成任务(ID: ${taskId})模型下载失败:`, error);
      throw error;
    }
  },

  // 分配参与者奖励
  distributeRewards: async (taskId, participants) => {
    try {
      const response = await dbApi.post(`/api/tasks/${taskId}/distribute-rewards`, { participants });
      return response.data;
    } catch (error) {
      console.error(`分配任务(ID: ${taskId})奖励失败:`, error);
      throw error;
    }
  }
};

// 数据库API服务
// 任务相关API
export const TaskAPI = {
  // 获取所有任务
  getAll: async () => {
    try {
      console.log('请求任务列表: /api/tasks');
      const response = await dbApi.get('/api/tasks');
      console.log('获取到任务列表原始响应:', response);
      
      // 记录详细的返回数据信息
      let tasks = response.data || []; // 直接使用response.data，如果为undefined则默认为空数组
      console.log('API返回的任务数据:', tasks);
      
      // 获取每个任务的参与者，确保计数只包含已批准的参与者
      for (const task of tasks) {
        try {
          console.log(`获取任务${task.id}的参与者`);
          // 调用后端API获取参与者
          const participantsResponse = await dbApi.get(`/api/tasks/${task.id}/participants`);
          const participantsData = participantsResponse.data || [];
          console.log(`任务${task.id}的参与者数据:`, participantsData);
          
          // 只获取非registered状态的参与者
          const approvedParticipants = participantsData.filter(p => p.status !== 'registered');
          console.log(`任务${task.id}的已批准参与者数量:`, approvedParticipants.length);
          
          task.participants = approvedParticipants;
          task.currentParticipants = approvedParticipants.length;
        } catch (err) {
          console.error(`获取任务${task.id}参与者失败:`, err);
          
          // 如果API调用失败，尝试从本地获取已批准的申请
          try {
            console.log(`尝试从本地获取任务${task.id}的已批准申请`);
            const applications = await apiService.getTaskApplications(task.id);
            task.participants = applications.approved;
            task.currentParticipants = applications.approved.length;
          } catch (localErr) {
            console.error(`从本地获取任务${task.id}参与者失败:`, localErr);
            task.participants = [];
            task.currentParticipants = 0;
          }
        }
      }
      
      console.log('最终处理完成的任务列表:', tasks);
      return tasks;
    } catch (error) {
      console.error('获取任务列表失败:', error);
      if (error.response) {
        console.error('HTTP响应状态:', error.response.status);
        console.error('响应数据:', error.response.data);
      } else if (error.request) {
        console.error('请求未收到响应:', error.request);
      } else {
        console.error('请求配置错误:', error.message);
      }
      console.error('请求配置:', error.config);
      return []; // 失败时返回空数组，而不是抛出异常
    }
  },

  // 获取单个任务
  getById: async (id) => {
    try {
      const response = await dbApi.get(`/api/tasks/${id}`);
      return response.data;
    } catch (error) {
      console.error(`获取任务(ID: ${id})失败:`, error);
      throw error;
    }
  },

  // 创建任务
  create: async (taskData) => {
    try {
      // 状态值映射，确保前端状态值与数据库枚举值匹配
      const statusMap = {
        'draft': 'pending',
        'published': 'pending',
        'in_progress': 'in_progress',
        'completed': 'completed',
        'cancelled': 'stopped',
        'failed': 'failed'
      };

      // 转换状态值
      const dbTaskData = {
        ...taskData,
        status: statusMap[taskData.status] || 'pending',
        data_type: taskData.dataType || 'image'  // 确保字段名称匹配
      };

      // 删除不需要的字段
      delete dbTaskData.dataType;
      delete dbTaskData.applications;
      delete dbTaskData.participants;
      delete dbTaskData.accuracy;

      console.log('创建任务，提交数据:', taskData);
      console.log('转换后的数据库数据:', dbTaskData);
      
      const response = await dbApi.post('/api/tasks', dbTaskData);
      console.log('创建任务成功，返回数据:', response.data);
      return response.data;
    } catch (error) {
      console.error('创建任务失败:', error);
      throw error;
    }
  },

  // 更新任务
  update: async (id, taskData) => {
    try {
      // 状态值映射，确保前端状态值与数据库枚举值匹配
      const statusMap = {
        'draft': 'pending',
        'published': 'pending',
        'in_progress': 'in_progress',
        'completed': 'completed',
        'cancelled': 'stopped',
        'failed': 'failed'
      };

      // 如果只更新状态
      if (typeof taskData === 'object' && Object.keys(taskData).length === 1 && taskData.status) {
        const dbStatus = statusMap[taskData.status] || 'pending';
        console.log(`更新任务(ID: ${id})状态: ${taskData.status} -> ${dbStatus}`);
        
        const response = await dbApi.put(`/api/tasks/${id}`, { status: dbStatus });
        console.log('更新任务成功，返回数据:', response.data);
        return response.data;
      }
      
      // 如果更新多个字段
      const dbTaskData = { ...taskData };
      
      // 转换状态值
      if (dbTaskData.status) {
        dbTaskData.status = statusMap[dbTaskData.status] || 'pending';
      }
      
      // 转换数据类型字段
      if (dbTaskData.dataType) {
        dbTaskData.data_type = dbTaskData.dataType;
        delete dbTaskData.dataType;
      }
      
      // 删除不需要的字段
      delete dbTaskData.applications;
      delete dbTaskData.participants;
      delete dbTaskData.accuracy;
      
      console.log(`更新任务(ID: ${id})，提交数据:`, taskData);
      console.log('转换后的数据库数据:', dbTaskData);
      
      const response = await dbApi.put(`/api/tasks/${id}`, dbTaskData);
      console.log('更新任务成功，返回数据:', response.data);
      return response.data;
    } catch (error) {
      console.error(`更新任务(ID: ${id})失败:`, error);
      throw error;
    }
  },

  // 删除任务
  delete: async (id) => {
    try {
      console.log(`删除任务(ID: ${id})`);
      const response = await dbApi.delete(`/api/tasks/${id}`);
      console.log('删除任务成功');
      return true;
    } catch (error) {
      console.error(`删除任务(ID: ${id})失败:`, error);
      throw error;
    }
  },

  // 获取任务指标
  getMetrics: async (taskId) => {
    try {
      const response = await dbApi.get(`/api/tasks/${taskId}/metrics`);
      return response.data;
    } catch (error) {
      console.error(`获取任务(ID: ${taskId})指标失败:`, error);
      throw error;
    }
  },

  // 获取任务日志
  getLogs: async (taskId, limit = 100, offset = 0) => {
    try {
      const response = await dbApi.get(`/api/tasks/${taskId}/logs`, {
        params: { limit, offset }
      });
      return response.data;
    } catch (error) {
      console.error(`获取任务(ID: ${taskId})日志失败:`, error);
      throw error;
    }
  }
};

// 模型相关API
export const ModelAPI = {
  // 获取所有模型
  getAll: async () => {
    try {
      const response = await dbApi.get('/api/models');
      return response.data;
    } catch (error) {
      console.error('获取模型列表失败:', error);
      throw error;
    }
  },

  // 获取单个模型
  getById: async (id) => {
    try {
      const response = await dbApi.get(`/api/models/${id}`);
      return response.data;
    } catch (error) {
      console.error(`获取模型(ID: ${id})失败:`, error);
      throw error;
    }
  },

  // 创建模型
  create: async (modelData) => {
    try {
      const response = await dbApi.post('/api/models', modelData);
      return response.data;
    } catch (error) {
      console.error('创建模型失败:', error);
      throw error;
    }
  },

  // 发布模型
  publish: async (id) => {
    try {
      const response = await dbApi.put(`/api/models/${id}/publish`);
      return response.data;
    } catch (error) {
      console.error(`发布模型(ID: ${id})失败:`, error);
      throw error;
    }
  },
  
  // 更新模型
  update: async (id, modelData) => {
    try {
      console.log(`更新模型(ID: ${id})信息:`, modelData);
      const response = await dbApi.put(`/api/models/${id}`, modelData);
      return response.data;
    } catch (error) {
      console.error(`更新模型(ID: ${id})失败:`, error);
      throw error;
    }
  },
  
  // 增加下载计数
  incrementDownloads: async (id) => {
    try {
      const response = await dbApi.put(`/api/models/${id}/downloads/increment`);
      return response.data;
    } catch (error) {
      console.error(`增加模型(ID: ${id})下载计数失败:`, error);
      throw error;
    }
  }
};

// 参与者相关API
export const ParticipantAPI = {
  // 获取所有参与者
  getAll: async () => {
    try {
      const response = await dbApi.get('/api/participants');
      return response.data;
    } catch (error) {
      console.error('获取参与者列表失败:', error);
      throw error;
    }
  },

  // 获取单个参与者
  getById: async (id) => {
    try {
      const response = await dbApi.get(`/api/participants/${id}`);
      return response.data;
    } catch (error) {
      console.error(`获取参与者(ID: ${id})失败:`, error);
      throw error;
    }
  },
  
  // 根据任务ID获取参与者
  getByTaskId: async (taskId) => {
    try {
      const response = await dbApi.get(`/api/tasks/${taskId}/participants`);
      return response.data;
    } catch (error) {
      console.error(`获取任务(ID: ${taskId})的参与者失败:`, error);
      throw error;
    }
  },
  
  // 更新参与者状态
  updateStatus: async (id, status) => {
    try {
      console.log(`更新参与者(ID: ${id})状态为: ${status}`);
      const response = await dbApi.put(`/api/participants/${id}/status`, { status });
      return response.data;
    } catch (error) {
      console.error(`更新参与者(ID: ${id})状态失败:`, error);
      throw error;
    }
  },
  
  // 创建参与者
  create: async (participantData) => {
    try {
      console.log('创建参与者，提交数据:', participantData);
      const response = await dbApi.post('/api/participants', participantData);
      return response.data;
    } catch (error) {
      console.error('创建参与者失败:', error);
      throw error;
    }
  },
  
  // 删除参与者
  delete: async (id) => {
    try {
      console.log(`删除参与者(ID: ${id})`);
      const response = await dbApi.delete(`/api/participants/${id}`);
      console.log('删除参与者成功');
      return true;
    } catch (error) {
      console.error(`删除参与者(ID: ${id})失败:`, error);
      throw error;
    }
  },

  // 根据客户端地址获取申请记录
  getByClientAddress: async (clientAddress) => {
    try {
      const response = await dbApi.get(`/api/applications/client/${clientAddress}`);
      return response.data;
    } catch (error) {
      console.error(`获取客户端 ${clientAddress} 的申请记录失败:`, error);
      throw error;
    }
  }
};

// 日志相关API
export const LogAPI = {
  // 获取所有日志
  getAll: async (limit = 100, offset = 0) => {
    try {
      const response = await dbApi.get('/api/logs', {
        params: { limit, offset }
      });
      return response.data;
    } catch (error) {
      console.error('获取日志列表失败:', error);
      throw error;
    }
  },

  // 根据任务ID获取日志
  getByTaskId: async (taskId, limit = 100, offset = 0) => {
    try {
      const response = await dbApi.get(`/api/tasks/${taskId}/logs`, {
        params: { limit, offset }
      });
      return response.data;
    } catch (error) {
      console.error(`获取任务(ID: ${taskId})的日志失败:`, error);
      throw error;
    }
  },

  // 创建日志
  create: async (logData) => {
    try {
      const response = await dbApi.post('/api/logs', logData);
      return response.data;
    } catch (error) {
      console.error('创建日志失败:', error);
      throw error;
    }
  }
};

// 用户认证API
export const AuthAPI = {
  // 用户登录
  login: async (username, password) => {
    try {
      const response = await dbApi.post('/api/auth/login', { username, password });
      return response.data;
    } catch (error) {
      console.error('用户登录失败:', error);
      throw error;
    }
  }
};

// 指标 API
export const MetricAPI = {
  // 获取所有指标
  getAll: async () => {
    try {
      const response = await dbApi.get('/api/metrics');
      return response.data;
    } catch (error) {
      console.error('获取所有指标失败:', error);
      throw error;
    }
  },
  
  // 根据任务ID获取指标
  getByTaskId: async (taskId) => {
    try {
      const response = await dbApi.get(`/api/metrics/task/${taskId}`);
      return response.data;
    } catch (error) {
      console.error(`获取任务(ID: ${taskId})的指标失败:`, error);
      throw error;
    }
  },
  
  // 根据任务ID和轮次获取指标
  getByTaskIdAndRound: async (taskId, round) => {
    try {
      const response = await dbApi.get(`/api/metrics/task/${taskId}/round/${round}`);
      return response.data;
    } catch (error) {
      console.error(`获取任务(ID: ${taskId})轮次(${round})的指标失败:`, error);
      throw error;
    }
  },
  
  // 创建指标
  create: async (metricData) => {
    try {
      const response = await dbApi.post('/api/metrics', metricData);
      return response.data;
    } catch (error) {
      console.error('创建指标失败:', error);
      throw error;
    }
  },
  
  // 更新指标
  update: async (id, metricData) => {
    try {
      const response = await dbApi.put(`/api/metrics/${id}`, metricData);
      return response.data;
    } catch (error) {
      console.error(`更新指标(ID: ${id})失败:`, error);
      throw error;
    }
  },
  
  // 删除指标
  delete: async (id) => {
    try {
      const response = await dbApi.delete(`/api/metrics/${id}`);
      return response.data;
    } catch (error) {
      console.error(`删除指标(ID: ${id})失败:`, error);
      throw error;
    }
  }
};

// 导出API服务
export { apiService };

// 导出所有API
export default {
  Task: TaskAPI,
  Model: ModelAPI,
  Participant: ParticipantAPI,
  Log: LogAPI,
  Auth: AuthAPI,
  Metric: MetricAPI
}; 