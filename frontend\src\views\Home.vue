<template>
  <div class="home-container">
    <div class="loading-spinner"></div>
    <p class="loading-text">正在加载，请稍候...</p>
  </div>
</template>

<script>
export default {
  name: 'Home',
  setup() {
    // 这个组件只是一个过渡页面，实际上会被路由守卫重定向
    return {};
  }
};
</script>

<style scoped>
.home-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-text {
  font-size: 18px;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 