<!-- 客户端数据管理页面 -->
<template>
  <div class="data-management">
    <div class="page-header">
      <h1>数据管理</h1>
      <div class="page-actions">
        <el-button type="primary" @click="uploadData">
          <i class="fas fa-upload"></i> 上传数据
        </el-button>
        <el-button type="success" @click="refreshData">
          <i class="fas fa-sync"></i> 刷新数据
        </el-button>
      </div>
    </div>
    
    <Breadcrumb />
    
    <!-- 数据统计卡片 -->
    <div class="stats-cards">
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.totalDatasets }}</div>
        <div class="stat-label">数据集总数</div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.totalSamples }}</div>
        <div class="stat-label">样本总数</div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.usedInTasks }}</div>
        <div class="stat-label">参与任务数</div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.totalRewards }}</div>
        <div class="stat-label">累计奖励</div>
      </el-card>
    </div>
    
    <!-- 数据集列表筛选 -->
    <div class="filter-section">
      <div class="filter-group">
        <span class="filter-label">数据类型：</span>
        <el-select v-model="typeFilter" placeholder="所有类型" clearable @change="filterDatasets">
          <el-option label="图像数据" value="image" />
          <el-option label="文本数据" value="text" />
          <el-option label="表格数据" value="tabular" />
          <el-option label="音频数据" value="audio" />
        </el-select>
      </div>
      
      <div class="search-box">
        <el-input v-model="searchQuery" placeholder="搜索数据集名称或描述" clearable @input="filterDatasets">
          <template #prefix>
            <i class="fas fa-search"></i>
          </template>
        </el-input>
      </div>
    </div>
    
    <!-- 数据集列表 -->
    <el-table
      :data="filteredDatasets"
      style="width: 100%"
      v-loading="loading"
      row-key="id"
      :empty-text="emptyText"
    >
      <el-table-column prop="name" label="数据集名称" min-width="180">
        <template #default="scope">
          <div class="dataset-name">{{ scope.row.name }}</div>
          <div class="dataset-created">创建时间: {{ formatDate(scope.row.createdAt) }}</div>
        </template>
      </el-table-column>
      
      <el-table-column prop="type" label="数据类型" width="120">
        <template #default="scope">
          <el-tag :type="getTypeTagType(scope.row.type)">
            {{ getTypeText(scope.row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="size" label="样本数量" width="120">
        <template #default="scope">
          {{ scope.row.size.toLocaleString() }}
        </template>
      </el-table-column>
      
      <el-table-column prop="tasksUsed" label="使用情况" width="120">
        <template #default="scope">
          {{ scope.row.tasksUsed }} 个任务
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="120">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="220" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="viewDataset(scope.row)" type="primary" plain>查看详情</el-button>
          <el-button size="small" @click="editDataset(scope.row)" type="warning" plain>编辑</el-button>
          <el-button size="small" @click="deleteDataset(scope.row)" type="danger" plain>删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalDatasets"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 上传数据对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传数据集"
      width="600px"
    >
      <el-form ref="uploadForm" :model="uploadForm" :rules="uploadRules" label-width="100px">
        <el-form-item label="数据集名称" prop="name">
          <el-input v-model="uploadForm.name" placeholder="请输入数据集名称"></el-input>
        </el-form-item>
        
        <el-form-item label="数据类型" prop="type">
          <el-select v-model="uploadForm.type" placeholder="请选择数据类型">
            <el-option label="图像数据" value="image" />
            <el-option label="文本数据" value="text" />
            <el-option label="表格数据" value="tabular" />
            <el-option label="音频数据" value="audio" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="数据描述" prop="description">
          <el-input
            v-model="uploadForm.description"
            type="textarea"
            rows="3"
            placeholder="请输入数据集描述"
          ></el-input>
        </el-form-item>
        
        <el-form-item label="标签" prop="tags">
          <el-tag
            v-for="tag in uploadForm.tags"
            :key="tag"
            class="tag-item"
            closable
            @close="removeTag(tag)"
          >
            {{ tag }}
          </el-tag>
          <el-input
            v-if="tagInputVisible"
            ref="tagInputRef"
            v-model="tagInputValue"
            class="tag-input"
            size="small"
            @keyup.enter="addTag"
            @blur="addTag"
          />
          <el-button v-else size="small" @click="showTagInput">
            + 添加标签
          </el-button>
        </el-form-item>
        
        <el-form-item label="数据文件" prop="file">
          <el-upload
            class="upload-demo"
            drag
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange"
            :limit="1"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">拖拽文件到此处或<em>点击上传</em></div>
            <template #tip>
              <div class="el-upload__tip">
                支持ZIP/CSV/JSON等格式文件，单个文件不超过500MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="uploadDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitUpload" :loading="uploading">上传</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, nextTick, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import Breadcrumb from '@/components/common/Breadcrumb.vue';

// 页面加载状态
const loading = ref(false);
const uploading = ref(false);
const emptyText = ref('暂无数据');

// 数据集列表
const datasets = ref([]);
const typeFilter = ref('');
const searchQuery = ref('');
const currentPage = ref(1);
const pageSize = ref(10);

// 统计信息
const statistics = reactive({
  totalDatasets: 0,
  totalSamples: 0,
  usedInTasks: 0,
  totalRewards: 0
});

// 上传对话框相关
const uploadDialogVisible = ref(false);
const uploadForm = reactive({
  name: '',
  type: '',
  description: '',
  tags: [],
  file: null
});
const uploadRules = {
  name: [{ required: true, message: '请输入数据集名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择数据类型', trigger: 'change' }],
  description: [{ required: true, message: '请输入数据集描述', trigger: 'blur' }],
  file: [{ required: true, message: '请上传数据文件', trigger: 'change' }]
};

// 标签输入相关
const tagInputVisible = ref(false);
const tagInputValue = ref('');
const tagInputRef = ref(null);

// 计算属性：过滤后的数据集
const filteredDatasets = computed(() => {
  return datasets.value.filter(dataset => {
    const matchesType = !typeFilter.value || dataset.type === typeFilter.value;
    const matchesSearch = !searchQuery.value || 
                           dataset.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
                           dataset.description.toLowerCase().includes(searchQuery.value.toLowerCase());
    return matchesType && matchesSearch;
  }).slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value);
});

// 计算属性：总数据集数
const totalDatasets = computed(() => {
  return datasets.value.length;
});

// 生命周期钩子：组件挂载后加载数据集列表
onMounted(() => {
  loadDatasets();
});

// 加载数据集列表
async function loadDatasets() {
  loading.value = true;
  emptyText.value = '加载中...';
  
  try {
    // 这里应该是实际的API调用
    // const response = await api.getDatasets();
    // datasets.value = response.data;
    
    // 模拟数据 - 实际项目中替换为API调用
    setTimeout(() => {
      datasets.value = generateMockDatasets();
      updateStatistics();
      loading.value = false;
      emptyText.value = '暂无数据';
    }, 1000);
  } catch (error) {
    console.error('加载数据集失败：', error);
    ElMessage.error('加载数据集失败，请重试');
    loading.value = false;
    emptyText.value = '加载失败，请重试';
  }
}

// 生成模拟数据
function generateMockDatasets() {
  const types = ['image', 'text', 'tabular', 'audio'];
  const statuses = ['active', 'inactive', 'archived'];
  const names = [
    '医疗影像数据集', '新闻文本语料库', '用户消费记录', '中文语音数据集',
    '自然场景图片集', '产品评论数据', '气象预测数据', '人体姿态数据集'
  ];
  
  return Array.from({ length: 25 }, (_, i) => {
    const type = types[Math.floor(Math.random() * types.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const name = names[Math.floor(Math.random() * names.length)] + (i + 1);
    const size = Math.floor(Math.random() * 50000) + 1000;
    const tasksUsed = Math.floor(Math.random() * 5);
    
    return {
      id: i + 1,
      name,
      type,
      description: `这是一个${getTypeText(type)}，包含${size}个样本，适用于各类机器学习任务。`,
      size,
      tasksUsed,
      status,
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 10000000000)),
      tags: ['机器学习', type, `数据集${i+1}`]
    };
  });
}

// 更新统计信息
function updateStatistics() {
  statistics.totalDatasets = datasets.value.length;
  statistics.totalSamples = datasets.value.reduce((sum, dataset) => sum + dataset.size, 0);
  statistics.usedInTasks = datasets.value.reduce((sum, dataset) => sum + dataset.tasksUsed, 0);
  statistics.totalRewards = formatReward(datasets.value.reduce((sum, dataset) => sum + (dataset.tasksUsed * 200), 0));
}

// 格式化日期
function formatDate(date) {
  if (!date) return '';
  const d = new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
}

// 格式化奖励
function formatReward(amount) {
  return amount.toLocaleString();
}

// 获取数据类型显示文本
function getTypeText(type) {
  const typeMap = {
    'image': '图像数据',
    'text': '文本数据',
    'tabular': '表格数据',
    'audio': '音频数据'
  };
  return typeMap[type] || type;
}

// 获取数据类型标签类型
function getTypeTagType(type) {
  const typeTagMap = {
    'image': 'success',
    'text': 'primary',
    'tabular': 'warning',
    'audio': 'info'
  };
  return typeTagMap[type] || '';
}

// 获取状态显示文本
function getStatusText(status) {
  const statusMap = {
    'active': '活跃',
    'inactive': '未使用',
    'archived': '已归档'
  };
  return statusMap[status] || status;
}

// 获取状态标签类型
function getStatusTagType(status) {
  const statusTagMap = {
    'active': 'success',
    'inactive': 'info',
    'archived': 'danger'
  };
  return statusTagMap[status] || '';
}

// 过滤数据集
function filterDatasets() {
  currentPage.value = 1;
}

// 分页处理
function handleSizeChange(val) {
  pageSize.value = val;
}

function handleCurrentChange(val) {
  currentPage.value = val;
}

// 上传数据
function uploadData() {
  resetUploadForm();
  uploadDialogVisible.value = true;
}

// 重置上传表单
function resetUploadForm() {
  Object.assign(uploadForm, {
    name: '',
    type: '',
    description: '',
    tags: [],
    file: null
  });
}

// 处理文件变更
function handleFileChange(file) {
  uploadForm.file = file.raw;
}

// 提交上传
async function submitUpload() {
  const uploadFormRef = document.querySelector('[ref="uploadForm"]').__vueParentComponent.ctx;
  
  try {
    await uploadFormRef.validate();
    uploading.value = true;
    
    // 这里应该是实际的API调用
    // const formData = new FormData();
    // formData.append('name', uploadForm.name);
    // formData.append('type', uploadForm.type);
    // formData.append('description', uploadForm.description);
    // formData.append('tags', JSON.stringify(uploadForm.tags));
    // formData.append('file', uploadForm.file);
    // await api.uploadDataset(formData);
    
    // 模拟上传
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    ElMessage.success('数据集上传成功');
    uploadDialogVisible.value = false;
    loadDatasets(); // 重新加载数据集列表
  } catch (error) {
    console.error('上传数据集失败：', error);
    ElMessage.error('上传数据集失败，请重试');
  } finally {
    uploading.value = false;
  }
}

// 刷新数据
function refreshData() {
  loadDatasets();
}

// 查看数据集详情
function viewDataset(dataset) {
  ElMessage.info(`查看数据集详情：${dataset.name}`);
  // 实际项目中跳转到数据集详情页面
  // router.push(`/client/data/${dataset.id}`);
}

// 编辑数据集
function editDataset(dataset) {
  ElMessage.info(`编辑数据集：${dataset.name}`);
  // 实际项目中打开编辑对话框或跳转到编辑页面
}

// 删除数据集
function deleteDataset(dataset) {
  ElMessageBox.confirm(
    `确定要删除数据集 "${dataset.name}" 吗？此操作不可恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // 这里应该是实际的API调用
      // await api.deleteDataset(dataset.id);
      
      // 模拟删除
      await new Promise(resolve => setTimeout(resolve, 1000));
      const index = datasets.value.findIndex(item => item.id === dataset.id);
      if (index !== -1) {
        datasets.value.splice(index, 1);
        updateStatistics();
      }
      
      ElMessage.success('数据集删除成功');
    } catch (error) {
      console.error('删除数据集失败：', error);
      ElMessage.error('删除数据集失败，请重试');
    }
  }).catch(() => {
    // 取消删除
  });
}

// 标签输入相关方法
function showTagInput() {
  tagInputVisible.value = true;
  nextTick(() => {
    tagInputRef.value.focus();
  });
}

function addTag() {
  if (tagInputValue.value) {
    if (!uploadForm.tags.includes(tagInputValue.value)) {
      uploadForm.tags.push(tagInputValue.value);
    }
  }
  tagInputVisible.value = false;
  tagInputValue.value = '';
}

function removeTag(tag) {
  const index = uploadForm.tags.indexOf(tag);
  if (index !== -1) {
    uploadForm.tags.splice(index, 1);
  }
}
</script>

<style scoped>
.data-management {
  padding: 20px;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.page-actions {
  display: flex;
  gap: 10px;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 24px;
}

@media (max-width: 1200px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }
}

.stat-card {
  border-radius: 8px;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

/* 筛选区域 */
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-label {
  font-weight: 500;
  color: #606266;
}

.search-box {
  flex: 1;
  max-width: 300px;
}

/* 表格样式定制 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.dataset-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.dataset-created {
  font-size: 12px;
  color: #909399;
}

/* 分页 */
.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 上传对话框 */
.tag-item {
  margin-right: 6px;
  margin-bottom: 6px;
}

.tag-input {
  width: 100px;
  margin-right: 6px;
  margin-bottom: 6px;
  vertical-align: bottom;
}

.el-upload {
  width: 100%;
}

.el-upload-dragger {
  width: 100%;
}

.upload-demo {
  margin-top: 10px;
}
</style> 