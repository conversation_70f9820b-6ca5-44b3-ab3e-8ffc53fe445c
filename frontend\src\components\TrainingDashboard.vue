<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <h2>联邦学习训练仪表盘</h2>
      <div class="training-controls">
        <button 
          :class="['control-btn', { 'active': isTraining }]"
          @click="toggleTraining"
          :disabled="loading"
        >
          {{ isTraining ? '停止训练' : '开始训练' }}
        </button>
        <span class="status-badge" :class="statusClass">
          {{ trainingStatus }}
        </span>
      </div>
    </div>

    <div class="dashboard-grid">
      <!-- 训练进度和指标 -->
      <div class="grid-item main">
        <TrainingChart 
          :history="trainingHistory"
          :loading="loading"
        />
      </div>
      
      <!-- 模型指标 -->
      <div class="grid-item">
        <ModelMetrics 
          :metrics="currentMetrics"
          :loading="loading"
        />
      </div>

      <!-- 参与者列表 -->
      <div class="grid-item">
        <ParticipantsList 
          :participants="participants"
          :loading="loading"
        />
      </div>

      <!-- 训练进度 -->
      <div class="grid-item">
        <TrainingProgress 
          :currentRound="currentRound"
          :totalRounds="totalRounds"
          :loading="loading"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue';
import TrainingChart from './TrainingChart.vue';
import ModelMetrics from './ModelMetrics.vue';
import ParticipantsList from './ParticipantsList.vue';
import TrainingProgress from './TrainingProgress.vue';
import web3Service from '../services/web3';
import apiService from '../services/api';

export default {
  name: 'TrainingDashboard',
  components: {
    TrainingChart,
    ModelMetrics,
    ParticipantsList,
    TrainingProgress
  },

  setup() {
    const isTraining = ref(false);
    const loading = ref(false);
    const trainingHistory = ref([]);
    const currentMetrics = ref({
      loss: 0,
      accuracy: 0,
      participants: 0
    });
    const participants = ref([]);
    const currentRound = ref(0);
    const totalRounds = ref(5);
    const updateInterval = ref(null);

    const statusClass = computed(() => ({
      'status-active': isTraining.value,
      'status-inactive': !isTraining.value
    }));

    const trainingStatus = computed(() => 
      isTraining.value ? '训练进行中' : '训练未开始'
    );

    // 开始/停止训练
    const toggleTraining = async () => {
      try {
        loading.value = true;
        if (!isTraining.value) {
          await apiService.startTraining();
          isTraining.value = true;
          startUpdateLoop();
        } else {
          await apiService.stopTraining();
          isTraining.value = false;
          stopUpdateLoop();
        }
      } catch (error) {
        console.error('训练控制失败:', error);
      } finally {
        loading.value = false;
      }
    };

    // 更新训练状态
    const updateTrainingStatus = async () => {
      try {
        const [serverStatus, contractStatus] = await Promise.all([
          apiService.getServerStatus(),
          web3Service.getContract().methods.getCurrentRound().call()
        ]);

        currentRound.value = parseInt(contractStatus);
        currentMetrics.value = serverStatus.metrics;
        participants.value = serverStatus.participants;
        
        // 更新训练历史
        if (serverStatus.history) {
          trainingHistory.value = serverStatus.history;
        }

        // 检查训练是否完成
        if (currentRound.value >= totalRounds.value) {
          isTraining.value = false;
          stopUpdateLoop();
        }
      } catch (error) {
        console.error('更新状态失败:', error);
      }
    };

    // 开始定期更新
    const startUpdateLoop = () => {
      updateInterval.value = setInterval(updateTrainingStatus, 5000);
    };

    // 停止定期更新
    const stopUpdateLoop = () => {
      if (updateInterval.value) {
        clearInterval(updateInterval.value);
        updateInterval.value = null;
      }
    };

    onMounted(async () => {
      try {
        loading.value = true;
        await updateTrainingStatus();
      } finally {
        loading.value = false;
      }
    });

    onUnmounted(() => {
      stopUpdateLoop();
    });

    return {
      isTraining,
      loading,
      trainingHistory,
      currentMetrics,
      participants,
      currentRound,
      totalRounds,
      statusClass,
      trainingStatus,
      toggleTraining
    };
  }
};
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.training-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-btn {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  background: #1976D2;
  color: white;
}

.control-btn:hover {
  background: #1565C0;
}

.control-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.control-btn.active {
  background: #D32F2F;
}

.control-btn.active:hover {
  background: #C62828;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.9em;
  font-weight: 500;
}

.status-active {
  background: #4CAF50;
  color: white;
}

.status-inactive {
  background: #9E9E9E;
  color: white;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.grid-item {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.grid-item.main {
  grid-column: span 2;
}

@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .grid-item.main {
    grid-column: span 1;
  }
}
</style> 