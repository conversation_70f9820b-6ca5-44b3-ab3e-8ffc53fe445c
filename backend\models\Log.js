/**
 * 日志数据模型
 */
const { query, pool } = require('../db/init');

// 日志类型枚举
const LOG_TYPES = {
  INFO: 'info',
  WARNING: 'warning', 
  ERROR: 'error',
  SYSTEM: 'system',
  TASK: 'task',
  MODEL: 'model',
  PARTICIPANT: 'participant',
  BLOCKCHAIN: 'blockchain'
};

class Log {
  /**
   * 获取所有日志
   * @param {number} limit 限制返回数量
   * @param {number} offset 偏移量
   * @returns {Promise<Array>} 日志列表
   */
  static async getAll(limit = 100, offset = 0) {
    try {
      // 确保所有参数都是数字类型
      const limitNum = Number(limit);
      const offsetNum = Number(offset);
      
      console.log(`获取所有日志, limit: ${limitNum}, offset: ${offsetNum}`);
      
      // 使用直接的SQL语句避免占位符问题
      const sql = `SELECT * FROM logs ORDER BY timestamp DESC LIMIT ${limitNum} OFFSET ${offsetNum}`;
      
      console.log('执行直接SQL查询:', sql);
      const [rows] = await query(sql);
      return rows;
    } catch (error) {
      console.error('获取所有日志失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取日志
   * @param {number} id 日志ID
   * @returns {Promise<Object>} 日志详情
   */
  static async getById(id) {
    try {
      const logs = await query('SELECT * FROM logs WHERE id = ?', [id]);
      return logs.length > 0 ? logs[0] : null;
    } catch (error) {
      console.error(`获取日志(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 根据任务ID获取日志
   * @param {number} taskId 任务ID
   * @param {number} limit 限制返回数量
   * @param {number} offset 偏移量
   * @returns {Promise<Array>} 日志列表
   */
  static async getByTaskId(taskId, limit = 100, offset = 0) {
    try {
      // 确保所有参数都是数字类型
      const taskIdNum = Number(taskId);
      const limitNum = Number(limit);
      const offsetNum = Number(offset);
      
      console.log(`获取任务(ID: ${taskIdNum})的日志, limit: ${limitNum}, offset: ${offsetNum}`);
      
      // 使用直接的SQL语句避免占位符问题
      const sql = `SELECT * FROM logs WHERE task_id = ${taskIdNum} ORDER BY timestamp DESC LIMIT ${limitNum} OFFSET ${offsetNum}`;
      
      console.log('执行直接SQL查询:', sql);
      const [rows] = await query(sql);
      return rows;
    } catch (error) {
      console.error(`获取任务(ID: ${taskId})的日志失败:`, error);
      throw error;
    }
  }

  /**
   * 根据类型获取日志
   * @param {string} type 日志类型
   * @param {number} limit 限制返回数量
   * @param {number} offset 偏移量
   * @returns {Promise<Array>} 日志列表
   */
  static async getByType(type, limit = 100, offset = 0) {
    try {
      // 确保所有参数都是数字类型
      const limitNum = Number(limit);
      const offsetNum = Number(offset);
      
      // 转义字符串参数，防止SQL注入
      const escapedType = type.replace(/'/g, "''");
      
      console.log(`获取类型(${escapedType})的日志, limit: ${limitNum}, offset: ${offsetNum}`);
      
      // 使用直接的SQL语句避免占位符问题
      const sql = `SELECT * FROM logs WHERE type = '${escapedType}' ORDER BY timestamp DESC LIMIT ${limitNum} OFFSET ${offsetNum}`;
      
      console.log('执行直接SQL查询:', sql);
      const [rows] = await query(sql);
      return rows;
    } catch (error) {
      console.error(`获取类型(${type})的日志失败:`, error);
      throw error;
    }
  }

  /**
   * 创建日志
   * @param {Object} logData 日志数据
   * @returns {Promise<Object>} 创建的日志
   */
  static async create(logData) {
    try {
      const {
        task_id = null, type, message
      } = logData;

      // 验证日志类型
      if (!Object.values(LOG_TYPES).includes(type)) {
        throw new Error(`无效的日志类型: ${type}`);
      }

      // 创建日志
      const result = await query(
        'INSERT INTO logs (task_id, type, message) VALUES (?, ?, ?)',
        [task_id, type, message]
      );

      return this.getById(result.insertId);
    } catch (error) {
      console.error('创建日志失败:', error);
      throw error;
    }
  }

  /**
   * 删除日志
   * @param {number} id 日志ID
   * @returns {Promise<boolean>} 删除结果
   */
  static async delete(id) {
    try {
      const result = await query('DELETE FROM logs WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`删除日志(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 删除任务的所有日志
   * @param {number} taskId 任务ID
   * @returns {Promise<boolean>} 删除结果
   */
  static async deleteByTaskId(taskId) {
    try {
      const result = await query('DELETE FROM logs WHERE task_id = ?', [taskId]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`删除任务(ID: ${taskId})的所有日志失败:`, error);
      throw error;
    }
  }

  /**
   * 创建信息日志
   * @param {string} message 日志消息
   * @param {number} taskId 任务ID（可选）
   * @returns {Promise<Object>} 创建的日志
   */
  static async info(message, taskId = null) {
    return await this.create({
      task_id: taskId,
      type: LOG_TYPES.INFO,
      message
    });
  }

  /**
   * 创建警告日志
   * @param {string} message 日志消息
   * @param {number} taskId 任务ID（可选）
   * @returns {Promise<Object>} 创建的日志
   */
  static async warning(message, taskId = null) {
    return await this.create({
      task_id: taskId,
      type: LOG_TYPES.WARNING,
      message
    });
  }

  /**
   * 创建错误日志
   * @param {string} message 日志消息
   * @param {number} taskId 任务ID（可选）
   * @returns {Promise<Object>} 创建的日志
   */
  static async error(message, taskId = null) {
    return await this.create({
      task_id: taskId,
      type: LOG_TYPES.ERROR,
      message
    });
  }

  /**
   * 创建系统日志
   * @param {string} message 日志消息
   * @returns {Promise<Object>} 创建的日志
   */
  static async system(message) {
    return await this.create({
      type: LOG_TYPES.SYSTEM,
      message
    });
  }

  /**
   * 创建任务日志
   * @param {string} message 日志消息
   * @param {number} taskId 任务ID
   * @returns {Promise<Object>} 创建的日志
   */
  static async task(message, taskId) {
    return await this.create({
      task_id: taskId,
      type: LOG_TYPES.TASK,
      message
    });
  }

  /**
   * 创建模型日志
   * @param {string} message 日志消息
   * @param {number} taskId 任务ID（可选）
   * @returns {Promise<Object>} 创建的日志
   */
  static async model(message, taskId = null) {
    return await this.create({
      task_id: taskId,
      type: LOG_TYPES.MODEL,
      message
    });
  }

  /**
   * 创建参与者日志
   * @param {string} message 日志消息
   * @param {number} taskId 任务ID
   * @returns {Promise<Object>} 创建的日志
   */
  static async participant(message, taskId) {
    return await this.create({
      task_id: taskId,
      type: LOG_TYPES.PARTICIPANT,
      message
    });
  }

  /**
   * 创建区块链日志
   * @param {string} message 日志消息
   * @param {number} taskId 任务ID（可选）
   * @returns {Promise<Object>} 创建的日志
   */
  static async blockchain(message, taskId = null) {
    return await this.create({
      task_id: taskId,
      type: LOG_TYPES.BLOCKCHAIN,
      message
    });
  }
}

// 导出日志类型枚举
Log.TYPES = LOG_TYPES;

module.exports = Log; 