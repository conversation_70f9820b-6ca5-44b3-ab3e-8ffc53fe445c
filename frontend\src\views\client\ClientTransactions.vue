<!-- 客户端交易记录页面 -->
<template>
  <div class="client-transactions">
    <div class="page-header">
      <h1>交易记录</h1>
      <div class="page-actions">
        <el-button type="primary" @click="refreshData">
          <i class="fas fa-sync"></i> 刷新数据
        </el-button>
        <el-button type="success" @click="exportTransactions">
          <i class="fas fa-file-export"></i> 导出数据
        </el-button>
      </div>
    </div>
    
    <Breadcrumb />
    
    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.totalTransactions }}</div>
        <div class="stat-label">总交易数</div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.totalEarned }}</div>
        <div class="stat-label">总收入</div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.totalSpent }}</div>
        <div class="stat-label">总支出</div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.currentBalance }}</div>
        <div class="stat-label">当前余额</div>
      </el-card>
    </div>
    
    <!-- 交易过滤和搜索 -->
    <div class="filter-section">
      <div class="filter-group">
        <span class="filter-label">交易类型：</span>
        <el-select v-model="typeFilter" placeholder="所有类型" clearable @change="filterTransactions">
          <el-option label="训练任务奖励" value="reward" />
          <el-option label="数据贡献奖励" value="data_contribution" />
          <el-option label="算力贡献奖励" value="compute_contribution" />
          <el-option label="服务消费" value="service_fee" />
          <el-option label="模型购买" value="model_purchase" />
        </el-select>
      </div>
      
      <div class="filter-group">
        <span class="filter-label">日期范围：</span>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :shortcuts="dateShortcuts"
          @change="filterTransactions"
        />
      </div>
      
      <div class="search-box">
        <el-input v-model="searchQuery" placeholder="搜索交易ID或描述" clearable @input="filterTransactions">
          <template #prefix>
            <i class="fas fa-search"></i>
          </template>
        </el-input>
      </div>
    </div>
    
    <!-- 交易列表 -->
    <el-table
      :data="filteredTransactions"
      style="width: 100%"
      v-loading="loading"
      row-key="id"
    >
      <el-table-column type="expand">
        <template #default="props">
          <div class="transaction-details">
            <div class="detail-item">
              <span class="detail-label">交易ID：</span>
              <span class="detail-value">{{ props.row.id }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">区块链交易哈希：</span>
              <span class="detail-value hash">{{ props.row.txHash }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">关联任务：</span>
              <span class="detail-value">{{ props.row.taskName || '无关联任务' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">详细描述：</span>
              <span class="detail-value">{{ props.row.description }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="timestamp" label="时间" width="180">
        <template #default="scope">
          {{ formatDateTime(scope.row.timestamp) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="type" label="类型" width="150">
        <template #default="scope">
          <el-tag :type="getTypeTagType(scope.row.type)">
            {{ getTypeText(scope.row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="amount" label="金额" width="120">
        <template #default="scope">
          <span :class="{ 'income': scope.row.amount > 0, 'expense': scope.row.amount < 0 }">
            {{ formatAmount(scope.row.amount) }}
          </span>
        </template>
      </el-table-column>
      
      <el-table-column prop="balance" label="余额" width="120">
        <template #default="scope">
          {{ formatBalance(scope.row.balance) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="description" label="描述" min-width="250">
        <template #default="scope">
          <div class="transaction-brief">{{ scope.row.description }}</div>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="viewTransaction(scope.row)" type="primary" plain>详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalTransactions"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 交易详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="交易详情"
      width="600px"
      v-if="currentTransaction"
    >
      <div class="transaction-dialog-content">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="交易ID">{{ currentTransaction.id }}</el-descriptions-item>
          <el-descriptions-item label="交易类型">
            <el-tag :type="getTypeTagType(currentTransaction.type)">
              {{ getTypeText(currentTransaction.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="交易时间">{{ formatDateTime(currentTransaction.timestamp) }}</el-descriptions-item>
          <el-descriptions-item label="交易金额">
            <span :class="{ 'income': currentTransaction.amount > 0, 'expense': currentTransaction.amount < 0 }">
              {{ formatAmount(currentTransaction.amount) }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="交易后余额">{{ formatBalance(currentTransaction.balance) }}</el-descriptions-item>
          <el-descriptions-item label="相关任务">{{ currentTransaction.taskName || '无关联任务' }}</el-descriptions-item>
          <el-descriptions-item label="交易描述">{{ currentTransaction.description }}</el-descriptions-item>
          <el-descriptions-item label="区块链交易哈希">
            <div class="hash-container">
              <span class="hash">{{ currentTransaction.txHash }}</span>
              <el-button type="primary" size="small" @click="viewOnExplorer(currentTransaction.txHash)">
                在区块浏览器中查看
              </el-button>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import Breadcrumb from '@/components/Breadcrumb.vue';

// 页面状态
const loading = ref(false);
const transactions = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const typeFilter = ref('');
const dateRange = ref(null);
const searchQuery = ref('');

// 统计信息
const statistics = reactive({
  totalTransactions: 0,
  totalEarned: 0,
  totalSpent: 0,
  currentBalance: 0
});

// 交易详情对话框
const detailDialogVisible = ref(false);
const currentTransaction = ref(null);

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

// 计算属性：过滤后的交易
const filteredTransactions = computed(() => {
  let result = transactions.value;
  
  // 类型过滤
  if (typeFilter.value) {
    result = result.filter(tx => tx.type === typeFilter.value);
  }
  
  // 日期范围过滤
  if (dateRange.value && dateRange.value.length === 2) {
    const startTime = dateRange.value[0].getTime();
    const endTime = dateRange.value[1].getTime();
    result = result.filter(tx => {
      const txTime = new Date(tx.timestamp).getTime();
      return txTime >= startTime && txTime <= endTime;
    });
  }
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(tx => 
      tx.id.toLowerCase().includes(query) || 
      tx.description.toLowerCase().includes(query)
    );
  }
  
  return result.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value);
});

// 计算属性：总交易数
const totalTransactions = computed(() => {
  let result = transactions.value;
  
  // 类型过滤
  if (typeFilter.value) {
    result = result.filter(tx => tx.type === typeFilter.value);
  }
  
  // 日期范围过滤
  if (dateRange.value && dateRange.value.length === 2) {
    const startTime = dateRange.value[0].getTime();
    const endTime = dateRange.value[1].getTime();
    result = result.filter(tx => {
      const txTime = new Date(tx.timestamp).getTime();
      return txTime >= startTime && txTime <= endTime;
    });
  }
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(tx => 
      tx.id.toLowerCase().includes(query) || 
      tx.description.toLowerCase().includes(query)
    );
  }
  
  return result.length;
});

// 生命周期钩子
onMounted(() => {
  loadTransactions();
});

// 加载交易数据
async function loadTransactions() {
  loading.value = true;
  
  try {
    // 实际项目中这里应该是API调用
    // const response = await api.getClientTransactions();
    // transactions.value = response.data;
    
    // 模拟数据
    setTimeout(() => {
      transactions.value = generateMockTransactions();
      updateStatistics();
      loading.value = false;
    }, 1000);
  } catch (error) {
    console.error('加载交易记录失败:', error);
    ElMessage.error('加载交易记录失败，请重试');
    loading.value = false;
  }
}

// 生成模拟交易数据
function generateMockTransactions() {
  const types = ['reward', 'data_contribution', 'compute_contribution', 'service_fee', 'model_purchase'];
  const taskNames = ['MNIST手写数字识别', '医疗影像分类', '金融交易欺诈检测', '自然语言情感分析', '产品推荐系统'];
  
  let balance = 0;
  
  return Array.from({ length: 50 }, (_, i) => {
    const type = types[Math.floor(Math.random() * types.length)];
    const isIncome = ['reward', 'data_contribution', 'compute_contribution'].includes(type);
    const amount = isIncome 
      ? Math.floor(Math.random() * 300) + 50 
      : -1 * (Math.floor(Math.random() * 200) + 30);
    
    balance += amount;
    
    const taskName = ['reward', 'data_contribution', 'compute_contribution'].includes(type)
      ? taskNames[Math.floor(Math.random() * taskNames.length)]
      : null;
    
    return {
      id: `TX${String(50 - i).padStart(6, '0')}`,
      txHash: `0x${Array(64).fill(0).map(() => Math.floor(Math.random() * 16).toString(16)).join('')}`,
      timestamp: new Date(Date.now() - i * 24 * 60 * 60 * 1000 * Math.random() * 3),
      type,
      amount,
      balance,
      description: getTransactionDescription(type, amount, taskName),
      taskName
    };
  });
}

// 获取交易描述
function getTransactionDescription(type, amount, taskName) {
  switch (type) {
    case 'reward':
      return `参与任务"${taskName}"的训练奖励`;
    case 'data_contribution':
      return `为任务"${taskName}"提供数据的贡献奖励`;
    case 'compute_contribution':
      return `为任务"${taskName}"提供算力的贡献奖励`;
    case 'service_fee':
      return `平台服务费用支出`;
    case 'model_purchase':
      return `购买模型使用权限`;
    default:
      return `系统交易`;
  }
}

// 更新统计信息
function updateStatistics() {
  statistics.totalTransactions = transactions.value.length;
  
  // 计算总收入
  statistics.totalEarned = transactions.value
    .filter(tx => tx.amount > 0)
    .reduce((sum, tx) => sum + tx.amount, 0);
  
  // 计算总支出
  statistics.totalSpent = Math.abs(transactions.value
    .filter(tx => tx.amount < 0)
    .reduce((sum, tx) => sum + tx.amount, 0));
  
  // 当前余额
  statistics.currentBalance = transactions.value.length > 0 
    ? transactions.value[0].balance 
    : 0;
}

// 格式化日期时间
function formatDateTime(date) {
  if (!date) return '';
  const d = new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}`;
}

// 格式化金额
function formatAmount(amount) {
  return (amount > 0 ? '+' : '') + amount.toLocaleString();
}

// 格式化余额
function formatBalance(balance) {
  return balance.toLocaleString();
}

// 获取交易类型标签类型
function getTypeTagType(type) {
  const typeMap = {
    'reward': 'success',
    'data_contribution': 'success',
    'compute_contribution': 'success',
    'service_fee': 'danger',
    'model_purchase': 'danger'
  };
  return typeMap[type] || 'info';
}

// 获取交易类型文本
function getTypeText(type) {
  const typeMap = {
    'reward': '训练奖励',
    'data_contribution': '数据贡献',
    'compute_contribution': '算力贡献',
    'service_fee': '服务费用',
    'model_purchase': '模型购买'
  };
  return typeMap[type] || type;
}

// 过滤交易
function filterTransactions() {
  currentPage.value = 1;
}

// 分页处理
function handleSizeChange(val) {
  pageSize.value = val;
}

function handleCurrentChange(val) {
  currentPage.value = val;
}

// 查看交易详情
function viewTransaction(transaction) {
  currentTransaction.value = transaction;
  detailDialogVisible.value = true;
}

// 刷新数据
function refreshData() {
  loadTransactions();
}

// 导出交易数据
function exportTransactions() {
  ElMessage.success('交易数据导出成功');
  // 实际项目中这里应该是下载CSV或Excel文件
}

// 在区块浏览器中查看
function viewOnExplorer(txHash) {
  // 实际项目中这里应该打开区块浏览器的链接
  const url = `https://etherscan.io/tx/${txHash}`;
  window.open(url, '_blank');
}
</script>

<style scoped>
.client-transactions {
  padding: 20px;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.page-actions {
  display: flex;
  gap: 10px;
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 24px;
}

@media (max-width: 1200px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }
}

.stat-card {
  border-radius: 8px;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

/* 筛选区域 */
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-label {
  font-weight: 500;
  color: #606266;
}

.search-box {
  flex: 1;
  max-width: 300px;
}

/* 交易展示 */
.transaction-brief {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}

.transaction-details {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
}

.detail-label {
  font-weight: bold;
  width: 120px;
  color: #606266;
}

.detail-value {
  flex: 1;
}

.detail-value.hash {
  font-family: monospace;
  word-break: break-all;
}

.income {
  color: #67C23A;
  font-weight: bold;
}

.expense {
  color: #F56C6C;
  font-weight: bold;
}

/* 分页 */
.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 交易详情对话框 */
.transaction-dialog-content {
  max-height: 60vh;
  overflow-y: auto;
}

.hash-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.hash {
  font-family: monospace;
  word-break: break-all;
}

@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    max-width: none;
  }
}
</style> 