<!-- 客户端可用任务页面 -->
<template>
  <div class="available-tasks">
    <div class="page-header">
      <h1>可用训练任务</h1>
    </div>
    
    <Breadcrumb />
    
    <!-- 任务过滤和搜索 -->
    <div class="filter-section">
      <div class="filter-group">
        <span class="filter-label">数据类型：</span>
        <el-select v-model="dataTypeFilter" placeholder="所有类型" clearable>
          <el-option label="图像数据" value="image" />
          <el-option label="文本数据" value="text" />
          <el-option label="表格数据" value="tabular" />
          <el-option label="音频数据" value="audio" />
        </el-select>
      </div>
      
      <div class="filter-group">
        <span class="filter-label">最低奖励：</span>
        <el-slider v-model="minReward" :min="0" :max="1000" :step="10" show-stops show-input />
      </div>
      
      <div class="search-box">
        <el-input v-model="searchQuery" placeholder="搜索任务名称或描述" clearable @input="filterTasks">
          <template #prefix>
            <i class="fas fa-search"></i>
          </template>
        </el-input>
      </div>
    </div>
    
    <!-- 任务卡片 -->
    <div class="task-grid" v-loading="loading">
      <el-empty v-if="filteredTasks.length === 0" description="暂无可用任务" />
      
      <el-card v-for="task in filteredTasks" :key="task.id" class="task-card" :class="{ 'applied': task.hasApplied }">
        <div class="task-card-header">
          <span class="task-name">{{ task.name }}</span>
          <el-tag :type="task.hasApplied ? 'info' : 'success'" size="small">
            {{ task.hasApplied ? '已申请' : '可申请' }}
          </el-tag>
        </div>
        
        <div class="task-description">{{ task.description }}</div>
        
        <div class="task-meta">
          <div class="meta-item">
            <i class="fas fa-database"></i>
            <span>{{ task.dataType }} 数据</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-users"></i>
            <span>已申请: {{ task.applications }}/{{ task.requiredParticipants }}</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-history"></i>
            <span>训练轮次: {{ task.totalRounds }}</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-coins"></i>
            <span>奖励: {{ task.reward }} 代币</span>
          </div>
          <div class="meta-item">
            <i class="fas fa-clock"></i>
            <span>截止日期: {{ formatDate(task.deadline) }}</span>
          </div>
        </div>
        
        <div class="task-requirements" v-if="task.requirements">
          <div class="requirements-title">参与要求：</div>
          <ul class="requirements-list">
            <li v-for="(req, index) in task.requirements" :key="index">
              {{ req }}
            </li>
          </ul>
        </div>
        
        <div class="task-actions">
          <el-button 
            type="primary" 
            :disabled="task.hasApplied"
            @click="applyForTask(task)"
          >
            {{ task.hasApplied ? '已申请' : '申请参与' }}
          </el-button>
          <el-button @click="viewTaskDetails(task)">查看详情</el-button>
        </div>
      </el-card>
    </div>
    
    <!-- 申请表单对话框 -->
    <el-dialog
      v-model="applyDialogVisible"
      title="申请参与训练任务"
      width="600px"
    >
      <div v-if="currentTask">
        <h3>{{ currentTask.name }}</h3>
        <p>{{ currentTask.description }}</p>
        
        <el-form ref="applyFormRef" :model="applyForm" label-width="120px" :rules="applyRules">
          <el-form-item label="数据描述" prop="dataDescription">
            <el-input 
              v-model="applyForm.dataDescription" 
              type="textarea" 
              rows="3"
              placeholder="请描述您将用于训练的数据集特征"
            />
          </el-form-item>
          
          <el-form-item label="数据样本量" prop="dataSize">
            <el-input-number 
              v-model="applyForm.dataSize" 
              :min="100" 
              :step="100"
              controls-position="right"
            />
          </el-form-item>
          
          <el-form-item label="计算资源" prop="computeResource">
            <el-select v-model="applyForm.computeResource" placeholder="请选择可提供的计算资源">
              <el-option label="CPU" value="cpu" />
              <el-option label="GPU" value="gpu" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="训练时间估计" prop="estimatedTime">
            <el-select v-model="applyForm.estimatedTime" placeholder="请选择预计训练时间">
              <el-option label="小于1小时" value="<1h" />
              <el-option label="1-3小时" value="1-3h" />
              <el-option label="3-6小时" value="3-6h" />
              <el-option label="6-12小时" value="6-12h" />
              <el-option label="12-24小时" value="12-24h" />
              <el-option label="超过24小时" value=">24h" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="附加说明">
            <el-input 
              v-model="applyForm.notes" 
              type="textarea" 
              rows="2"
              placeholder="其他说明信息（可选）"
            />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="applyDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitApplication" :loading="submitting">提交申请</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, onActivated } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { ElMessage, ElMessageBox } from 'element-plus';
import Breadcrumb from '@/components/Breadcrumb.vue';
import dbAPI, { TaskAPI, ParticipantAPI } from '@/services/api';
import store from '@/store';

// 直接在这里导入apiService对象，避免改动其他文件
const apiService = {
  applyForTask: async (clientAddress, taskId, applicationData) => {
    try {
      console.log(`用户 ${clientAddress} 申请参与任务 ${taskId}`);
      
      // 1. 先保存到数据库
      try {
        // 构建API请求数据 - 按后端要求的格式
        const participantData = {
          task_id: parseInt(taskId),
          client_address: clientAddress,
          status: 'registered', // 在数据库中初始状态为registered
          data_size: applicationData.dataSize || 1000,
          compute_resource: applicationData.computeResource || 'cpu'
        };
        
        console.log('发送到后端的参与者数据:', participantData);
        
        // 调用后端API创建参与者记录
        const dbResult = await ParticipantAPI.create(participantData);
        console.log('数据库保存成功，响应:', dbResult);
        
        // 2. 然后保存到localStorage以维持前端状态
        // 生成申请ID
        const applicationId = Date.now();
        
        // 创建申请记录
        const application = {
          id: applicationId,
          clientAddress,
          taskId,
          status: 'pending',
          applyTime: new Date(),
          reviewTime: null,
          dbId: dbResult.id, // 将数据库ID保存到本地记录中
          ...applicationData
        };
        
        // 保存到localStorage
        const savedApplications = localStorage.getItem(`applications_${clientAddress}`);
        let applications = [];
        
        if (savedApplications) {
          try {
            applications = JSON.parse(savedApplications);
          } catch (e) {
            console.error('解析申请记录失败:', e);
          }
        }
        
        // 检查是否已经有相同任务的申请
        if (applications.some(app => app.taskId === taskId)) {
          console.log('本地已有申请记录，已被覆盖');
        }
        
        // 更新或添加申请
        const existingIndex = applications.findIndex(app => app.taskId === taskId);
        if (existingIndex !== -1) {
          applications[existingIndex] = application;
        } else {
          applications.push(application);
        }
        
        localStorage.setItem(`applications_${clientAddress}`, JSON.stringify(applications));
        
        return application;
      } catch (dbError) {
        console.error('将申请保存到数据库失败:', dbError);
        throw new Error(`保存申请失败：${dbError.message}`);
      }
    } catch (error) {
      console.error('申请任务失败:', error);
      throw error;
    }
  }
};

export default {
  name: 'AvailableTasks',
  components: {
    Breadcrumb
  },
  setup() {
    const router = useRouter();
    const loading = ref(false);
    const submitting = ref(false);
    const tasks = ref([]);
    const dataTypeFilter = ref('');
    const minReward = ref(0);
    const searchQuery = ref('');
    const applyDialogVisible = ref(false);
    const currentTask = ref(null);
    const lastUserAddress = ref(''); // 追踪上次加载时的用户地址
    
    // 申请表单
    const applyForm = ref({
      dataDescription: '',
      dataSize: 1000,
      computeResource: '',
      estimatedTime: '',
      notes: ''
    });
    
    // 表单引用
    const applyFormRef = ref(null);
    
    // 表单验证规则
    const applyRules = {
      dataDescription: [
        { required: true, message: '请描述您的数据集', trigger: 'blur' },
        { min: 10, message: '描述不能少于10个字符', trigger: 'blur' }
      ],
      dataSize: [
        { required: true, message: '请输入数据样本量', trigger: 'blur' },
        { type: 'number', min: 100, message: '样本量不能小于100', trigger: 'blur' }
      ],
      computeResource: [
        { required: true, message: '请选择计算资源', trigger: 'change' }
      ],
      estimatedTime: [
        { required: true, message: '请选择预计训练时间', trigger: 'change' }
      ]
    };
    
    // 加载可用任务
    const loadTasks = async () => {
      loading.value = true;
      try {
        // 获取用户地址 - 直接从store获取当前登录的用户地址，而不是从localStorage
        const userAddress = store.state.user.address || localStorage.getItem('userAddress');
        
        if (!userAddress) {
          console.error('无法获取用户地址，未登录？');
          ElMessage.error('无法获取用户信息，请重新登录');
          return;
        }
        
        lastUserAddress.value = userAddress; // 记录当前用户地址
        
        console.log('正在加载用户地址为', userAddress, '的可用任务');
        
        // 清除该用户之前的任务状态缓存
        const cacheKey = `availableTasks_${userAddress}`;
        localStorage.removeItem(cacheKey);
        
        // 调用API获取可用任务列表 - 使用TaskAPI替代apiService
        const availableTasks = await TaskAPI.getAll();
        console.log('获取到的任务列表:', availableTasks);
        
        // 处理任务数据，增加客户端所需的字段
        tasks.value = availableTasks.map(task => {
          return {
            id: task.id,
            name: task.name,
            description: task.description,
            dataType: task.data_type,
            requiredParticipants: task.required_participants,
            totalRounds: task.total_rounds,
            currentRound: task.current_round || 0,
            reward: 500, // 默认奖励值
            applications: 0, // 默认申请数
            hasApplied: false, // 默认未申请
            deadline: task.start_time ? new Date(task.start_time) : new Date(Date.now() + 7*24*60*60*1000), // 默认截止日期为一周后
            requirements: ["无特殊要求"] // 默认要求
          };
        });
        
        // 获取用户申请状态 - 直接从数据库查询
        try {
          // 使用参与者API获取当前用户的所有申请
          console.log(`正在获取用户${userAddress}的应用记录...`);
          const userParticipants = await ParticipantAPI.getByClientAddress(userAddress);
          console.log(`获取到用户 ${userAddress} 的应用记录:`, userParticipants);
          
          if (userParticipants && userParticipants.length > 0) {
            // 标记用户已申请的任务
            tasks.value = tasks.value.map(task => {
              // 检查当前任务是否在用户的申请列表中
              const matchingApp = userParticipants.find(p => p.task_id === task.id);
              const hasApplied = !!matchingApp;
              
              if (hasApplied) {
                console.log(`用户已申请任务 ${task.id}，应用状态:`, matchingApp.status);
              }
              
              return {
                ...task,
                hasApplied: hasApplied
              };
            });
          } else {
            console.log(`用户 ${userAddress} 没有应用记录`);
          }
        } catch (err) {
          console.error('获取用户应用状态失败:', err);
          if (err.response) {
            console.error('HTTP错误:', err.response.status, err.response.data);
          } else if (err.request) {
            console.error('无响应:', err.request);
          } else {
            console.error('请求错误:', err.message);
          }
          // 尝试从localStorage获取应用状态
          console.log('尝试从localStorage获取应用状态');
          try {
            const savedApplications = localStorage.getItem(`applications_${userAddress}`);
            if (savedApplications) {
              const applications = JSON.parse(savedApplications);
              console.log(`从localStorage获取到 ${applications.length} 条应用记录`);
              
              // 标记用户已申请的任务
              tasks.value = tasks.value.map(task => {
                const hasApplied = applications.some(app => app.taskId === task.id);
                if (hasApplied) {
                  console.log(`用户已申请任务 ${task.id}`);
                }
                return {
                  ...task,
                  hasApplied: hasApplied
                };
              });
            } else {
              console.log(`localStorage中没有用户 ${userAddress} 的应用记录`);
            }
          } catch (e) {
            console.error('从localStorage获取应用状态失败:', e);
          }
        }
      } catch (error) {
        console.error('加载任务列表失败:', error);
        if (error.response) {
          console.error('HTTP错误:', error.response.status, error.response.data);
        } else if (error.request) {
          console.error('无响应:', error.request);
        } else {
          console.error('请求错误:', error.message);
        }
        ElMessage.error('加载任务列表失败，请重试');
      } finally {
        loading.value = false;
      }
    };
    
    // 监听用户地址变化
    const checkUserAddressChange = () => {
      const currentUserAddress = localStorage.getItem('userAddress');
      if (currentUserAddress && currentUserAddress !== lastUserAddress.value) {
        console.log('检测到用户地址变化，重新加载任务列表');
        loadTasks();
      }
    };
    
    // 每次组件激活时检查用户地址是否变化
    const handleActivated = () => {
      // 获取当前用户地址
      const currentUserAddress = store.state.user.address;
      
      // 如果没有用户地址，可能未登录
      if (!currentUserAddress) {
        console.error('组件激活但未找到用户地址');
        return;
      }
      
      // 检查是否与上次记录的地址不同
      if (currentUserAddress !== lastUserAddress.value) {
        console.log(`检测到用户地址变化: ${lastUserAddress.value} -> ${currentUserAddress}`);
        lastUserAddress.value = currentUserAddress;
        loadTasks(); // 重新加载任务
      } else {
        // 即使地址没变，也刷新任务列表以获取最新状态
        console.log('组件激活，刷新任务列表');
        loadTasks();
      }
    };
    
    // 格式化日期
    const formatDate = (date) => {
      if (!date) return '';
      if (typeof date === 'string') {
        date = new Date(date);
      }
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    };
    
    // 计算过滤后的任务
    const filteredTasks = computed(() => {
      let result = tasks.value;
      
      // 数据类型过滤
      if (dataTypeFilter.value) {
        result = result.filter(task => 
          task.dataType.toLowerCase().includes(dataTypeFilter.value.toLowerCase())
        );
      }
      
      // 最低奖励过滤
      if (minReward.value > 0) {
        result = result.filter(task => task.reward >= minReward.value);
      }
      
      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(task => 
          task.name.toLowerCase().includes(query) || 
          (task.description && task.description.toLowerCase().includes(query))
        );
      }
      
      return result;
    });
    
    // 过滤任务方法
    const filterTasks = () => {
      // 任务过滤逻辑
    };
    
    // 查看任务详情
    const viewTaskDetails = (task) => {
      router.push(`/client/tasks/${task.id}`);
    };
    
    // 申请参与任务
    const applyForTask = (task) => {
      currentTask.value = task;
      applyDialogVisible.value = true;
      
      // 重置表单
      applyForm.value = {
        dataDescription: '',
        dataSize: 1000,
        computeResource: '',
        estimatedTime: '',
        notes: ''
      };
    };
    
    // 提交申请
    const submitApplication = async () => {
      if (!applyFormRef.value) return;
      
      await applyFormRef.value.validate(async (valid, fields) => {
        if (!valid) {
          console.log('表单验证失败', fields);
          return;
        }
        
        submitting.value = true;
        try {
          // 从store获取当前用户地址
          const userAddress = store.state.user.address;
          
          if (!userAddress) {
            throw new Error('用户未登录或无法获取地址');
          }
          
          console.log('使用地址提交申请:', userAddress);
          
          // 构建申请数据
          const applicationData = {
            task_id: currentTask.value.id,
            client_address: userAddress,
            status: 'registered',
            data_size: applyForm.value.dataSize,
            compute_resource: applyForm.value.computeResource
          };
          
          console.log('准备提交的申请数据:', applicationData);
          
          // 使用apiService.applyForTask将申请同时保存到数据库和localStorage
          const result = await apiService.applyForTask(userAddress, currentTask.value.id, {
            dataSize: applyForm.value.dataSize,
            computeResource: applyForm.value.computeResource,
            dataDescription: applyForm.value.dataDescription,
            estimatedTime: applyForm.value.estimatedTime,
            notes: applyForm.value.notes
          });
          console.log('申请提交成功:', result);
          
          // 更新任务状态 - 只更新当前任务的状态
          const taskIndex = tasks.value.findIndex(t => t.id === currentTask.value.id);
          if (taskIndex !== -1) {
            // 创建新的任务对象并更新状态，确保响应式更新
            const updatedTask = { ...tasks.value[taskIndex] };
            updatedTask.hasApplied = true;
            
            // 增加申请计数
            if (typeof updatedTask.applications === 'number') {
              updatedTask.applications += 1;
            } else {
              updatedTask.applications = 1;
            }
            
            // 更新任务列表
            const newTasks = [...tasks.value];
            newTasks[taskIndex] = updatedTask;
            tasks.value = newTasks;
          }
          
          ElMessage({
            message: '申请已提交到数据库，请等待审核',
            type: 'success'
          });
          
          applyDialogVisible.value = false;
        } catch (error) {
          console.error('提交申请失败:', error);
          ElMessage.error('提交申请失败: ' + (error.message || '未知错误'));
        } finally {
          submitting.value = false;
        }
      });
    };
    
    onMounted(() => {
      loadTasks();
      
      // 添加定期检查用户地址变化的功能
      const addressCheckInterval = setInterval(checkUserAddressChange, 5000);
      
      // 组件卸载时清除定时器
      onUnmounted(() => {
        clearInterval(addressCheckInterval);
      });
    });
    
    // 添加组件激活时的处理
    onActivated(() => {
      handleActivated();
    });
    
    return {
      loading,
      submitting,
      tasks,
      dataTypeFilter,
      minReward,
      searchQuery,
      applyDialogVisible,
      currentTask,
      applyForm,
      applyFormRef,
      applyRules,
      filteredTasks,
      formatDate,
      filterTasks,
      viewTaskDetails,
      applyForTask,
      submitApplication,
      handleActivated,
      checkUserAddressChange
    };
  }
};
</script>

<style scoped>
.available-tasks {
  width: 100%;
  padding: var(--spacing-md);
  box-sizing: border-box;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  width: 100%;
  padding: var(--spacing-md);
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  box-sizing: border-box;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: var(--text-primary);
  font-weight: 600;
}

.filter-section {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  align-items: center;
  width: 100%;
  background-color: var(--surface-color);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  box-sizing: border-box;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.filter-label {
  font-weight: 500;
  color: var(--text-secondary);
  white-space: nowrap;
}

.search-box {
  flex-grow: 1;
  min-width: 200px;
}

.task-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-md);
  width: 100%;
  margin-bottom: var(--spacing-md);
}

/* 卡片样式 */
.task-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.task-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-4px);
}

.task-card.applied {
  border-left: 4px solid var(--primary-color);
}

.task-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.task-name {
  font-size: 18px;
  font-weight: bold;
  color: var(--text-primary);
}

.task-description {
  color: var(--text-secondary);
  margin-bottom: 16px;
  flex-grow: 1;
}

.task-meta {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-bottom: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 14px;
}

.meta-item i {
  color: var(--primary-color);
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .task-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .available-tasks {
    padding: var(--spacing-sm);
  }
  
  .filter-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    width: 100%;
  }
  
  .task-grid {
    grid-template-columns: 1fr;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
}
</style> 