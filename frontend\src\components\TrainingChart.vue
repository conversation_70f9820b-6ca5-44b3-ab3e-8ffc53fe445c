<template>
  <div class="chart-container">
    <div class="chart-header">
      <h3>训练进度</h3>
      <div class="chart-controls">
        <select v-model="selectedMetric">
          <option value="loss">损失</option>
          <option value="accuracy">准确率</option>
        </select>
      </div>
    </div>
    <div class="chart-content" ref="chartContainer"></div>
  </div>
</template>

<script>
import { ref, onMounted, watch, onUnmounted } from 'vue';
import * as echarts from 'echarts';

export default {
  name: 'TrainingChart',
  props: {
    history: {
      type: Array,
      required: true,
      default: () => []
    }
  },

  setup(props) {
    const chartContainer = ref(null);
    const chart = ref(null);
    const selectedMetric = ref('loss');

    const initChart = () => {
      if (chartContainer.value) {
        chart.value = echarts.init(chartContainer.value);
      }
    };

    const updateChart = () => {
      if (!chart.value) return;

      // 检查history是否为空或无效
      if (!props.history || !Array.isArray(props.history) || props.history.length === 0) {
        // 如果history为空或无效，显示空图表
        const emptyOption = {
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: [],
            name: '轮次'
          },
          yAxis: {
            type: 'value',
            name: selectedMetric.value === 'loss' ? '损失' : '准确率'
          },
          series: [{
            data: [],
            type: 'line',
            smooth: true
          }]
        };
        chart.value.setOption(emptyOption);
        return;
      }

      // 确保每个数据点都有必要的属性
      const validHistory = props.history.map(item => ({
        round: item.round || 0,
        loss: typeof item.loss === 'number' ? item.loss : 0,
        accuracy: typeof item.accuracy === 'number' ? item.accuracy : 0,
        time: item.time || 0
      }));

      const rounds = validHistory.map((item, index) => item.round || (index + 1));
      const values = validHistory.map(item => 
        selectedMetric.value === 'loss' ? item.loss : item.accuracy
      );

      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            const value = params[0].value;
            // 根据指标类型设置不同的精度
            return selectedMetric.value === 'loss' ? 
              `轮次 ${params[0].name}<br/>损失: ${value.toFixed(4)}` :
              `轮次 ${params[0].name}<br/>准确率: ${(value * 100).toFixed(2)}%`;
          }
        },
        xAxis: {
          type: 'category',
          data: rounds,
          name: '轮次'
        },
        yAxis: {
          type: 'value',
          name: selectedMetric.value === 'loss' ? '损失' : '准确率',
          // 根据指标类型设置不同的轴配置
          axisLabel: {
            formatter: function(value) {
              return selectedMetric.value === 'loss' ? 
                value.toFixed(4) : 
                (value * 100).toFixed(2) + '%';
            }
          },
          // 设置分割段数，使波动更明显
          splitNumber: 10,
          // 设置最小/最大值，优化显示范围
          min: function(value) {
            if (selectedMetric.value === 'loss') {
              return 0;
            } else {
              // 对于准确率，设置一个合适的最小值，比如最小值减去5%
              return Math.max(0, Math.floor(value.min * 100 - 5) / 100);
            }
          },
          max: function(value) {
            if (selectedMetric.value === 'loss') {
              return value.max;
            } else {
              // 对于准确率，设置一个合适的最大值，比如最大值加上5%
              return Math.min(1, Math.ceil(value.max * 100 + 5) / 100);
            }
          }
        },
        series: [{
          data: values,
          type: 'line',
          smooth: true,
          lineStyle: {
            color: selectedMetric.value === 'loss' ? '#f44336' : '#4CAF50'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: selectedMetric.value === 'loss' ? 
                'rgba(244, 67, 54, 0.3)' : 
                'rgba(76, 175, 80, 0.3)'
            }, {
              offset: 1,
              color: 'rgba(255, 255, 255, 0.1)'
            }])
          }
        }]
      };

      chart.value.setOption(option);
    };

    // 监听数据变化
    watch(() => props.history, updateChart, { deep: true });
    watch(selectedMetric, updateChart);

    // 监听窗口大小变化
    const handleResize = () => {
      chart.value?.resize();
    };

    onMounted(() => {
      initChart();
      updateChart();
      window.addEventListener('resize', handleResize);
    });

    onUnmounted(() => {
      chart.value?.dispose();
      window.removeEventListener('resize', handleResize);
    });

    return {
      chartContainer,
      selectedMetric
    };
  }
};
</script>

<style scoped>
.chart-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-content {
  height: 300px;
  width: 100%;
}

select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
}
</style> 