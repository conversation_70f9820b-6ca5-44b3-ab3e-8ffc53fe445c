<!-- 客户端申请管理页面 -->
<template>
  <div class="my-applications">
    <div class="page-header">
      <h1>我的申请</h1>
      <div class="page-actions">
        <el-button type="primary" @click="refreshData">
          <i class="fas fa-sync"></i> 刷新数据
        </el-button>
      </div>
    </div>
    
    <Breadcrumb />
    
    <!-- 申请统计卡片 -->
    <div class="stats-cards">
      <el-card class="stat-card">
        <div class="stat-value">{{ statistics.total }}</div>
        <div class="stat-label">总申请数</div>
      </el-card>
      <el-card class="stat-card status-card-pending">
        <div class="stat-value">{{ statistics.pending }}</div>
        <div class="stat-label">待审核</div>
      </el-card>
      <el-card class="stat-card status-card-approved">
        <div class="stat-value">{{ statistics.approved }}</div>
        <div class="stat-label">已批准</div>
      </el-card>
      <el-card class="stat-card status-card-rejected">
        <div class="stat-value">{{ statistics.rejected }}</div>
        <div class="stat-label">已拒绝</div>
      </el-card>
    </div>
    
    <!-- 申请列表筛选 -->
    <div class="filter-section">
      <el-radio-group v-model="statusFilter" @change="filterApplications" class="status-filter">
        <el-radio-button value="all">全部</el-radio-button>
        <el-radio-button value="pending">待审核</el-radio-button>
        <el-radio-button value="approved">已批准</el-radio-button>
        <el-radio-button value="rejected">已拒绝</el-radio-button>
      </el-radio-group>
      
      <div class="search-box">
        <el-input v-model="searchQuery" placeholder="搜索任务名称" clearable @input="filterApplications">
          <template #prefix>
            <i class="fas fa-search"></i>
          </template>
        </el-input>
      </div>
    </div>
    
    <!-- 申请列表 -->
    <el-table
      :data="filteredApplications"
      style="width: 100%"
      v-loading="loading"
      row-key="id"
      :empty-text="emptyText"
    >
      <el-table-column prop="task.name" label="任务名称" min-width="200">
        <template #default="scope">
          <div class="task-name">{{ scope.row.task.name }}</div>
          <div class="task-sponsor">发起方: {{ scope.row.task.sponsor }}</div>
        </template>
      </el-table-column>
      
      <el-table-column prop="applyTime" label="申请时间" width="180">
        <template #default="scope">
          {{ formatDateTime(scope.row.applyTime) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="120">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="reviewTime" label="审核时间" width="180">
        <template #default="scope">
          <template v-if="scope.row.reviewTime">
            {{ formatDateTime(scope.row.reviewTime) }}
          </template>
          <template v-else>
            <span class="text-muted">- -</span>
          </template>
        </template>
      </el-table-column>
      
      <el-table-column prop="reward" label="预期奖励" width="120">
        <template #default="scope">
          {{ scope.row.task.reward }} 代币
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="160" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="viewTaskDetails(scope.row.task)" type="primary" plain>查看任务</el-button>
          <el-button size="small" @click="viewApplicationDetails(scope.row)" type="info" plain>申请详情</el-button>
          <el-button 
            size="small" 
            @click="cancelApplication(scope.row)" 
            type="danger" 
            plain
            v-if="scope.row.status === 'pending'"
          >取消申请</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalApplications"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 申请详情对话框 -->
    <el-dialog
      v-model="applicationDetailVisible"
      title="申请详情"
      width="700px"
    >
      <div v-if="currentApplication" class="application-detail">
        <div class="detail-header">
          <div class="detail-task-name">{{ currentApplication.task.name }}</div>
          <el-tag :type="getStatusTagType(currentApplication.status)" class="detail-status">
            {{ getStatusText(currentApplication.status) }}
          </el-tag>
        </div>
        
        <div class="detail-grid">
          <div class="detail-item">
            <div class="detail-label">申请时间</div>
            <div class="detail-value">{{ formatDateTime(currentApplication.applyTime) }}</div>
          </div>
          
          <div class="detail-item">
            <div class="detail-label">审核时间</div>
            <div class="detail-value">
              {{ currentApplication.reviewTime ? formatDateTime(currentApplication.reviewTime) : '- -' }}
            </div>
          </div>
          
          <div class="detail-item">
            <div class="detail-label">任务奖励</div>
            <div class="detail-value">{{ currentApplication.task.reward }} 代币</div>
          </div>
          
          <div class="detail-item">
            <div class="detail-label">任务截止日期</div>
            <div class="detail-value">{{ formatDate(currentApplication.task.deadline) }}</div>
          </div>
          
          <div class="detail-item">
            <div class="detail-label">任务发起方</div>
            <div class="detail-value">{{ currentApplication.task.sponsor }}</div>
          </div>
          
          <div class="detail-item">
            <div class="detail-label">训练轮次</div>
            <div class="detail-value">{{ currentApplication.task.totalRounds }} 轮</div>
          </div>
        </div>
        
        <div class="detail-section">
          <div class="section-title">数据描述</div>
          <div class="section-content">{{ currentApplication.dataDescription }}</div>
        </div>
        
        <div class="detail-section">
          <div class="section-title">提供数据</div>
          <div class="section-content">{{ currentApplication.dataSize }} 个样本</div>
        </div>
        
        <div class="detail-section">
          <div class="section-title">计算资源</div>
          <div class="section-content">{{ getComputeResourceText(currentApplication.computeResource) }}</div>
        </div>
        
        <div class="detail-section">
          <div class="section-title">预计训练时间</div>
          <div class="section-content">{{ getEstimatedTimeText(currentApplication.estimatedTime) }}</div>
        </div>
        
        <div class="detail-section" v-if="currentApplication.notes">
          <div class="section-title">附加说明</div>
          <div class="section-content">{{ currentApplication.notes }}</div>
        </div>
        
        <div class="detail-section" v-if="currentApplication.feedback">
          <div class="section-title">审核反馈</div>
          <div class="section-content">{{ currentApplication.feedback }}</div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="applicationDetailVisible = false">关闭</el-button>
        <el-button 
          type="danger" 
          @click="cancelApplication(currentApplication)"
          v-if="currentApplication && currentApplication.status === 'pending'"
        >取消申请</el-button>
        <el-button 
          type="primary" 
          @click="viewTaskDetails(currentApplication.task)"
        >查看任务</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import Breadcrumb from '@/components/Breadcrumb.vue';
import apiService, { TaskAPI, ParticipantAPI } from '@/services/api';

export default {
  name: 'MyApplications',
  components: {
    Breadcrumb
  },
  setup() {
    const router = useRouter();
    const loading = ref(false);
    const applications = ref([]);
    const statusFilter = ref('all');
    const searchQuery = ref('');
    const currentPage = ref(1);
    const pageSize = ref(10);
    const applicationDetailVisible = ref(false);
    const currentApplication = ref(null);
    
    // 统计数据
    const statistics = reactive({
      total: 0,
      pending: 0,
      approved: 0,
      rejected: 0
    });
    
    // 加载申请列表
    const loadApplications = async () => {
      loading.value = true;
      emptyText.value = '正在加载数据...';
      
      try {
        // 获取用户地址
        const userAddress = localStorage.getItem('userAddress') || '0xe56B1329f32D9c289C40054Fb20239DE52D6BbC0';
        
        // 获取所有参与者(通过API)，然后过滤出当前用户的申请
        const allParticipants = await ParticipantAPI.getAll();
        const userParticipants = allParticipants.filter(
          p => p.client_address === userAddress
        );
        
        // 获取所有任务信息，用于关联到申请
        const allTasks = await TaskAPI.getAll();
        
        // 将任务信息关联到申请
        applications.value = userParticipants.map(participant => {
          // 找到关联的任务
          const taskInfo = allTasks.find(task => task.id === participant.task_id) || {
            name: '未知任务',
            sponsor: '未知发起方',
            reward: 0,
            deadline: new Date(),
            totalRounds: 0,
            id: participant.task_id
          };
          
          // 映射状态
          const statusMap = {
            'registered': 'pending',
            'rejected': 'rejected',
            'training': 'approved',
            'completed': 'approved',
            'failed': 'rejected',
            'disconnected': 'rejected'
          };
          
          // 返回转换后的申请记录
          return {
            id: participant.id,
            taskId: participant.task_id,
            status: statusMap[participant.status] || 'pending',
            clientAddress: participant.client_address,
            dataSize: participant.data_size || 0,
            computeResource: participant.compute_resource || 'cpu',
            dataDescription: participant.data_description || '(无数据描述)',
            estimatedTime: participant.estimated_time || '1-3h',
            applyTime: new Date(participant.created_at),
            reviewTime: participant.last_update ? new Date(participant.last_update) : null,
            task: taskInfo
          };
        });
        
        // 更新统计信息
        updateStatistics();
        
        emptyText.value = '暂无申请数据';
      } catch (error) {
        console.error('加载申请数据失败:', error);
        ElMessage.error('加载申请数据失败，请重试');
        emptyText.value = '加载数据失败';
      } finally {
        loading.value = false;
      }
    };
    
    // 更新统计数据
    const updateStatistics = () => {
      statistics.total = applications.value.length;
      statistics.pending = applications.value.filter(app => app.status === 'pending').length;
      statistics.approved = applications.value.filter(app => app.status === 'approved').length;
      statistics.rejected = applications.value.filter(app => app.status === 'rejected').length;
    };
    
    // 格式化日期
    const formatDate = (date) => {
      if (!date) return '';
      if (typeof date === 'string') {
        date = new Date(date);
      }
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    };
    
    // 格式化日期时间
    const formatDateTime = (date) => {
      if (!date) return '';
      if (typeof date === 'string') {
        date = new Date(date);
      }
      
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    };
    
    // 获取状态标签类型
    const getStatusTagType = (status) => {
      const types = {
        pending: 'warning',
        approved: 'success',
        rejected: 'danger'
      };
      return types[status] || 'info';
    };
    
    // 获取状态文本
    const getStatusText = (status) => {
      const texts = {
        pending: '待审核',
        approved: '已批准',
        rejected: '已拒绝'
      };
      return texts[status] || status;
    };
    
    // 获取计算资源文本
    const getComputeResourceText = (resource) => {
      const texts = {
        cpu: 'CPU',
        gpu: 'GPU'
      };
      return texts[resource] || resource;
    };
    
    // 获取预计时间文本
    const getEstimatedTimeText = (time) => {
      const texts = {
        '<1h': '小于1小时',
        '1-3h': '1-3小时',
        '3-6h': '3-6小时',
        '6-12h': '6-12小时',
        '12-24h': '12-24小时',
        '>24h': '超过24小时'
      };
      return texts[time] || time;
    };
    
    // 计算过滤后的申请
    const filteredApplications = computed(() => {
      let result = applications.value;
      
      // 状态过滤
      if (statusFilter.value !== 'all') {
        result = result.filter(app => app.status === statusFilter.value);
      }
      
      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(app => 
          app.task.name.toLowerCase().includes(query)
        );
      }
      
      // 分页数据
      const startIndex = (currentPage.value - 1) * pageSize.value;
      const endIndex = startIndex + pageSize.value;
      return result.slice(startIndex, endIndex);
    });
    
    // 设置空状态文本
    const emptyText = ref('正在加载数据...');
    
    // 计算总记录数
    const totalApplications = computed(() => {
      let result = applications.value;
      
      // 状态过滤
      if (statusFilter.value !== 'all') {
        result = result.filter(app => app.status === statusFilter.value);
      }
      
      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(app => 
          app.task.name.toLowerCase().includes(query)
        );
      }
      
      return result.length;
    });
    
    // 过滤申请方法
    const filterApplications = () => {
      currentPage.value = 1;  // 重置到第一页
    };
    
    // 页面大小改变
    const handleSizeChange = (size) => {
      pageSize.value = size;
    };
    
    // 当前页改变
    const handleCurrentChange = (page) => {
      currentPage.value = page;
    };
    
    // 查看任务详情
    const viewTaskDetails = (task) => {
      router.push(`/client/tasks/${task.id}`);
    };
    
    // 查看申请详情
    const viewApplicationDetails = (application) => {
      currentApplication.value = application;
      applicationDetailVisible.value = true;
    };
    
    // 取消申请
    const cancelApplication = async (application) => {
      try {
        await ElMessageBox.confirm(
          `确定要取消该申请吗？`,
          '取消申请',
          {
            confirmButtonText: '确定',
            cancelButtonText: '返回',
            type: 'warning'
          }
        );
        
        // 获取用户地址
        const userAddress = localStorage.getItem('userAddress') || '0xe56B1329f32D9c289C40054Fb20239DE52D6BbC0';
        
        // 使用ParticipantAPI删除参与者
        await ParticipantAPI.delete(application.id);
        
        // 重新加载申请列表
        await loadApplications();
        
        ElMessage({
          message: '申请已取消',
          type: 'success',
          duration: 3000
        });
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消申请失败:', error);
          ElMessage.error('取消申请失败，请重试');
        }
      }
    };
    
    // 刷新数据
    const refreshData = async () => {
      await loadApplications();
      ElMessage({
        message: '数据已刷新',
        type: 'success',
        duration: 2000
      });
    };
    
    onMounted(() => {
      loadApplications();
    });
    
    return {
      loading,
      applications,
      statusFilter,
      searchQuery,
      currentPage,
      pageSize,
      applicationDetailVisible,
      currentApplication,
      statistics,
      filteredApplications,
      totalApplications,
      emptyText,
      formatDate,
      formatDateTime,
      getStatusTagType,
      getStatusText,
      getComputeResourceText,
      getEstimatedTimeText,
      filterApplications,
      handleSizeChange,
      handleCurrentChange,
      viewTaskDetails,
      viewApplicationDetails,
      cancelApplication,
      refreshData
    };
  }
};
</script>

<style scoped>
.my-applications {
  width: 100%;
  padding: var(--spacing-md);
  margin: 0;
  box-sizing: border-box;
  overflow-x: hidden;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  background-color: var(--surface-color);
  width: 100%;
  box-sizing: border-box;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: var(--text-primary);
  font-weight: 600;
}

.page-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin: 0 var(--spacing-md) var(--spacing-md);
  width: calc(100% - var(--spacing-md) * 2);
  box-sizing: border-box;
}

.application-list {
  margin: 0 var(--spacing-md);
  width: calc(100% - var(--spacing-md) * 2);
  box-sizing: border-box;
}

.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.status-card-pending .stat-value {
  color: #E6A23C;
}

.status-card-approved .stat-value {
  color: #67C23A;
}

.status-card-rejected .stat-value {
  color: #F56C6C;
}

.filter-section {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  align-items: center;
}

.search-box {
  width: 300px;
}

.task-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.task-sponsor {
  font-size: 12px;
  color: #999;
}

.text-muted {
  color: #999;
}

.pagination {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

.application-detail {
  padding: 16px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.detail-task-name {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.detail-status {
  font-size: 14px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.detail-item {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
}

.detail-value {
  font-size: 16px;
  color: #303133;
}

.detail-section {
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

.section-content {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

@media (max-width: 1200px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }
  
  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .search-box {
    width: 100%;
  }
}
</style> 