<template>
  <div class="progress-container">
    <h3>训练进度</h3>
    <div class="progress-content" v-if="!loading">
      <div class="progress-info">
        <div class="round-display">
          <div class="current-round">{{ currentRound }}</div>
          <div class="total-rounds">/ {{ totalRounds }}</div>
        </div>
        <div class="progress-label">轮次</div>
      </div>

      <div class="progress-bar-container">
        <div class="progress-bar">
          <div 
            class="progress-fill"
            :style="{ width: `${progressPercentage}%` }"
          ></div>
        </div>
        <div class="progress-percentage">{{ progressPercentage }}%</div>
      </div>

      <div class="progress-stats">
        <div class="stat-item">
          <div class="stat-label">预计剩余时间</div>
          <div class="stat-value">{{ estimatedTimeRemaining }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-label">平均每轮时间</div>
          <div class="stat-value">{{ averageTimePerRound }}</div>
        </div>
      </div>
    </div>
    <div v-else class="loading-container">
      <div class="loading-spinner"></div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue';

export default {
  name: 'TrainingProgress',
  props: {
    currentRound: {
      type: Number,
      required: true
    },
    totalRounds: {
      type: Number,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    averageRoundTime: {
      type: Number,
      default: 0
    }
  },

  setup(props) {
    const progressPercentage = computed(() => {
      return Math.round((props.currentRound / props.totalRounds) * 100);
    });

    const formatTime = (minutes) => {
      if (minutes < 1) return '< 1分钟';
      if (minutes < 60) return `${Math.round(minutes)}分钟`;
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = Math.round(minutes % 60);
      return `${hours}小时 ${remainingMinutes}分钟`;
    };

    const estimatedTimeRemaining = computed(() => {
      const remainingRounds = props.totalRounds - props.currentRound;
      const estimatedMinutes = remainingRounds * (props.averageRoundTime || 2);
      return formatTime(estimatedMinutes);
    });

    const averageTimePerRound = computed(() => {
      return formatTime(props.averageRoundTime || 2);
    });

    return {
      progressPercentage,
      estimatedTimeRemaining,
      averageTimePerRound
    };
  }
};
</script>

<style scoped>
.progress-container {
  height: 100%;
}

.progress-content {
  margin-top: 16px;
}

.progress-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
}

.round-display {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.current-round {
  font-size: 2.5em;
  font-weight: 600;
  color: #1976D2;
}

.total-rounds {
  font-size: 1.5em;
  color: #666;
}

.progress-label {
  font-size: 0.9em;
  color: #666;
  margin-top: 4px;
}

.progress-bar-container {
  margin: 24px 0;
}

.progress-bar {
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #2196F3, #1976D2);
  transition: width 0.3s ease;
}

.progress-percentage {
  text-align: right;
  font-size: 0.9em;
  color: #666;
}

.progress-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-top: 24px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-label {
  font-size: 0.8em;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 0.9em;
  font-weight: 500;
  color: #333;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 