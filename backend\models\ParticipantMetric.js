/**
 * 参与者训练指标数据模型
 */
const { query } = require('../db/init');

class ParticipantMetric {
  /**
   * 获取所有参与者指标
   * @returns {Promise<Array>} 指标列表
   */
  static async getAll() {
    try {
      return await query('SELECT * FROM participant_metrics ORDER BY task_id, participant_id, round');
    } catch (error) {
      console.error('获取所有参与者指标失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取参与者指标
   * @param {number} id 指标ID
   * @returns {Promise<Object>} 指标详情
   */
  static async getById(id) {
    try {
      const metrics = await query('SELECT * FROM participant_metrics WHERE id = ?', [id]);
      return metrics.length > 0 ? metrics[0] : null;
    } catch (error) {
      console.error(`获取参与者指标(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 根据任务ID获取参与者指标
   * @param {number} taskId 任务ID
   * @returns {Promise<Array>} 指标列表
   */
  static async getByTaskId(taskId) {
    try {
      return await query(
        'SELECT * FROM participant_metrics WHERE task_id = ? ORDER BY participant_id, round', 
        [taskId]
      );
    } catch (error) {
      console.error(`获取任务(ID: ${taskId})的参与者指标失败:`, error);
      throw error;
    }
  }

  /**
   * 根据参与者ID获取指标
   * @param {number} participantId 参与者ID
   * @returns {Promise<Array>} 指标列表
   */
  static async getByParticipantId(participantId) {
    try {
      return await query(
        'SELECT * FROM participant_metrics WHERE participant_id = ? ORDER BY round', 
        [participantId]
      );
    } catch (error) {
      console.error(`获取参与者(ID: ${participantId})的指标失败:`, error);
      throw error;
    }
  }

  /**
   * 根据任务ID和参与者ID获取指标
   * @param {number} taskId 任务ID
   * @param {number} participantId 参与者ID
   * @returns {Promise<Array>} 指标列表
   */
  static async getByTaskAndParticipant(taskId, participantId) {
    try {
      return await query(
        'SELECT * FROM participant_metrics WHERE task_id = ? AND participant_id = ? ORDER BY round', 
        [taskId, participantId]
      );
    } catch (error) {
      console.error(`获取任务(ID: ${taskId})参与者(ID: ${participantId})的指标失败:`, error);
      throw error;
    }
  }

  /**
   * 根据任务ID、参与者ID和轮次获取指标
   * @param {number} taskId 任务ID
   * @param {number} participantId 参与者ID
   * @param {number} round 轮次
   * @returns {Promise<Object>} 指标详情
   */
  static async getByTaskParticipantAndRound(taskId, participantId, round) {
    try {
      const metrics = await query(
        'SELECT * FROM participant_metrics WHERE task_id = ? AND participant_id = ? AND round = ?',
        [taskId, participantId, round]
      );
      return metrics.length > 0 ? metrics[0] : null;
    } catch (error) {
      console.error(`获取任务(ID: ${taskId})参与者(ID: ${participantId})轮次(${round})的指标失败:`, error);
      throw error;
    }
  }

  /**
   * 创建参与者指标
   * @param {Object} metricData 指标数据
   * @returns {Promise<Object>} 创建的指标
   */
  static async create(metricData) {
    try {
      const {
        task_id, participant_id, round, loss, accuracy, training_time
      } = metricData;

      // 检查是否已存在该参与者该轮次的指标
      const existingMetric = await this.getByTaskParticipantAndRound(task_id, participant_id, round);
      
      if (existingMetric) {
        // 更新现有指标
        return await this.update(existingMetric.id, metricData);
      }

      // 创建新指标
      const result = await query(
        `INSERT INTO participant_metrics 
         (task_id, participant_id, round, loss, accuracy, training_time) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [task_id, participant_id, round, loss, accuracy, training_time]
      );

      return this.getById(result.insertId);
    } catch (error) {
      console.error('创建参与者指标失败:', error);
      throw error;
    }
  }

  /**
   * 更新参与者指标
   * @param {number} id 指标ID
   * @param {Object} metricData 更新的指标数据
   * @returns {Promise<Object>} 更新后的指标
   */
  static async update(id, metricData) {
    try {
      const {
        loss, accuracy, training_time
      } = metricData;

      await query(
        `UPDATE participant_metrics SET 
           loss = ?, 
           accuracy = ?, 
           training_time = ?,
           timestamp = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [loss, accuracy, training_time, id]
      );

      return this.getById(id);
    } catch (error) {
      console.error(`更新参与者指标(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 删除参与者指标
   * @param {number} id 指标ID
   * @returns {Promise<boolean>} 删除结果
   */
  static async delete(id) {
    try {
      const result = await query('DELETE FROM participant_metrics WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`删除参与者指标(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 删除任务的所有参与者指标
   * @param {number} taskId 任务ID
   * @returns {Promise<boolean>} 删除结果
   */
  static async deleteByTaskId(taskId) {
    try {
      const result = await query('DELETE FROM participant_metrics WHERE task_id = ?', [taskId]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`删除任务(ID: ${taskId})的所有参与者指标失败:`, error);
      throw error;
    }
  }

  /**
   * 删除参与者的所有指标
   * @param {number} participantId 参与者ID
   * @returns {Promise<boolean>} 删除结果
   */
  static async deleteByParticipantId(participantId) {
    try {
      const result = await query('DELETE FROM participant_metrics WHERE participant_id = ?', [participantId]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`删除参与者(ID: ${participantId})的所有指标失败:`, error);
      throw error;
    }
  }

  /**
   * 获取任务每轮的平均指标
   * @param {number} taskId 任务ID
   * @returns {Promise<Array>} 平均指标列表
   */
  static async getAverageMetricsByTaskId(taskId) {
    try {
      return await query(
        `SELECT 
          round, 
          AVG(loss) as avg_loss, 
          AVG(accuracy) as avg_accuracy, 
          AVG(training_time) as avg_training_time,
          COUNT(*) as participants_count
        FROM 
          participant_metrics 
        WHERE 
          task_id = ? 
        GROUP BY 
          round
        ORDER BY 
          round`,
        [taskId]
      );
    } catch (error) {
      console.error(`获取任务(ID: ${taskId})的平均指标失败:`, error);
      throw error;
    }
  }
}

module.exports = ParticipantMetric; 