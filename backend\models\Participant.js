/**
 * 参与者数据模型
 */
const { query } = require('../db/init');

class Participant {
  /**
   * 获取所有参与者
   * @returns {Promise<Array>} 参与者列表
   */
  static async getAll() {
    try {
      return await query('SELECT * FROM participants ORDER BY created_at DESC');
    } catch (error) {
      console.error('获取所有参与者失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取参与者
   * @param {number} id 参与者ID
   * @returns {Promise<Object>} 参与者详情
   */
  static async getById(id) {
    try {
      const participants = await query('SELECT * FROM participants WHERE id = ?', [id]);
      return participants.length > 0 ? participants[0] : null;
    } catch (error) {
      console.error(`获取参与者(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 根据任务ID获取所有参与者
   * @param {number} taskId 任务ID
   * @returns {Promise<Array>} 参与者列表
   */
  static async getByTaskId(taskId) {
    try {
      return await query('SELECT * FROM participants WHERE task_id = ?', [taskId]);
    } catch (error) {
      console.error(`获取任务(ID: ${taskId})的参与者失败:`, error);
      throw error;
    }
  }

  /**
   * 根据客户端地址获取参与者
   * @param {string} clientAddress 客户端地址
   * @returns {Promise<Array>} 参与者列表
   */
  static async getByClientAddress(clientAddress) {
    try {
      return await query('SELECT * FROM participants WHERE client_address = ?', [clientAddress]);
    } catch (error) {
      console.error(`获取客户端地址(${clientAddress})的参与者失败:`, error);
      throw error;
    }
  }

  /**
   * 创建参与者
   * @param {Object} participantData 参与者数据
   * @returns {Promise<Object>} 创建的参与者
   */
  static async create(participantData) {
    try {
      const {
        task_id, client_address, status, 
        data_size, compute_resource
      } = participantData;

      const result = await query(
        `INSERT INTO participants 
         (task_id, client_address, status, data_size, compute_resource) 
         VALUES (?, ?, ?, ?, ?)`,
        [task_id, client_address, status, data_size, compute_resource]
      );

      return this.getById(result.insertId);
    } catch (error) {
      console.error('创建参与者失败:', error);
      throw error;
    }
  }

  /**
   * 更新参与者状态
   * @param {number} id 参与者ID
   * @param {string} status 新状态
   * @returns {Promise<boolean>} 更新结果
   */
  static async updateStatus(id, status) {
    try {
      const now = new Date();
      const result = await query(
        'UPDATE participants SET status = ?, last_update = ? WHERE id = ?',
        [status, now, id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`更新参与者(ID: ${id})状态失败:`, error);
      throw error;
    }
  }

  /**
   * 更新参与者当前轮次状态
   * @param {number} id 参与者ID
   * @param {string} roundStatus 轮次状态
   * @returns {Promise<boolean>} 更新结果
   */
  static async updateRoundStatus(id, roundStatus) {
    try {
      const now = new Date();
      const result = await query(
        'UPDATE participants SET current_round_status = ?, last_update = ? WHERE id = ?',
        [roundStatus, now, id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`更新参与者(ID: ${id})轮次状态失败:`, error);
      throw error;
    }
  }

  /**
   * 更新参与者贡献度
   * @param {number} id 参与者ID
   * @param {number} contribution 贡献度
   * @returns {Promise<boolean>} 更新结果
   */
  static async updateContribution(id, contribution) {
    try {
      const result = await query(
        'UPDATE participants SET contribution = ? WHERE id = ?',
        [contribution, id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`更新参与者(ID: ${id})贡献度失败:`, error);
      throw error;
    }
  }

  /**
   * 删除参与者
   * @param {number} id 参与者ID
   * @returns {Promise<boolean>} 删除结果
   */
  static async delete(id) {
    try {
      const result = await query('DELETE FROM participants WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`删除参与者(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 获取参与者训练指标
   * @param {number} participantId 参与者ID
   * @returns {Promise<Array>} 训练指标列表
   */
  static async getMetrics(participantId) {
    try {
      return await query(
        'SELECT * FROM participant_metrics WHERE participant_id = ? ORDER BY round ASC',
        [participantId]
      );
    } catch (error) {
      console.error(`获取参与者(ID: ${participantId})训练指标失败:`, error);
      throw error;
    }
  }

  /**
   * 添加参与者训练指标
   * @param {Object} metricData 指标数据
   * @returns {Promise<Object>} 添加的指标ID
   */
  static async addMetric(metricData) {
    try {
      const {
        task_id, participant_id, round,
        loss, accuracy, training_time
      } = metricData;

      const result = await query(
        `INSERT INTO participant_metrics 
         (task_id, participant_id, round, loss, accuracy, training_time) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [task_id, participant_id, round, loss, accuracy, training_time]
      );

      return { id: result.insertId };
    } catch (error) {
      console.error('添加参与者训练指标失败:', error);
      throw error;
    }
  }
}

module.exports = Participant; 