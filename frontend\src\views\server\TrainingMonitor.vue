<!-- 训练监控页面 -->
<template>
  <div class="training-monitor">
    <div class="page-header">
      <h1>训练监控</h1>
      <div class="page-actions">
        <el-button @click="goBack">
          <i class="fas fa-arrow-left"></i> 返回
        </el-button>
        <el-button type="primary" @click="refreshData" :loading="loading">
          <i class="fas fa-sync"></i> 刷新数据
        </el-button>
        <el-button type="success" @click="exportData">
          <i class="fas fa-file-export"></i> 导出数据
        </el-button>
      </div>
    </div>
    
    <Breadcrumb />
    
    <!-- 任务选择 -->
    <div class="task-selector" v-if="!currentTask">
      <el-card class="selector-card">
        <template #header>
          <div class="card-header">
            <span>选择要监控的训练任务</span>
          </div>
        </template>
        
        <el-table
          :data="activeTasks"
          style="width: 100%"
          v-loading="loading"
          @row-click="selectTask"
        >
          <el-table-column prop="name" label="任务名称" min-width="180" />
          <el-table-column prop="dataType" label="数据类型" width="120" />
          <el-table-column prop="participants" label="参与者" width="120">
            <template #default="scope">
              {{ scope.row.participants.length }}/{{ scope.row.requiredParticipants }}
            </template>
          </el-table-column>
          <el-table-column prop="progress" label="进度" width="200">
            <template #default="scope">
              <el-progress 
                :percentage="calculateProgress(scope.row)" 
                :status="getProgressStatus(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="startTime" label="开始时间" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.startTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button size="small" type="primary" @click.stop="selectTask(scope.row)">
                监控
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    
    <!-- 监控面板 -->
    <div class="monitor-panel" v-else>
      <!-- 任务概览 -->
      <el-card class="overview-card">
        <template #header>
          <div class="card-header monitor-header">
            <div class="task-info">
              <h2>{{ currentTask.name }}</h2>
              <div class="task-meta">
                <el-tag :type="currentTask.status === 'in_progress' ? 'success' : 'primary'">
                  {{ currentTask.status === 'in_progress' ? '进行中' : '已完成' }}
                </el-tag>
                <span class="meta-item">
                  <i class="fas fa-users"></i> {{ currentTask.participants.length }}/{{ currentTask.requiredParticipants }} 参与者
                </span>
                <span class="meta-item">
                  <i class="fas fa-history"></i> {{ currentTask.currentRound }}/{{ currentTask.totalRounds }} 轮次
                </span>
                <span class="meta-item">
                  <i class="fas fa-clock"></i> 开始于 {{ formatDateTime(currentTask.startTime) }}
                </span>
              </div>
            </div>
            <el-button type="warning" @click="stopTask" :disabled="isStopping || currentTask.status === 'completed'">
              <i class="fas fa-stop-circle"></i> {{ currentTask.status === 'in_progress' ? '停止训练' : '训练已完成' }}
            </el-button>
            <el-button type="primary" @click="goToModelManagement" v-if="currentTask.status === 'completed'">
              <i class="fas fa-cubes"></i> 查看模型
            </el-button>
          </div>
        </template>
        
        <div class="progress-section">
          <div class="progress-card">
            <div class="progress-title">总体进度</div>
            <el-progress 
              :percentage="calculateProgress(currentTask)" 
              :status="getProgressStatus(currentTask)"
              :stroke-width="20"
            />
            <div class="progress-details">
              轮次 {{ currentTask.currentRound }}/{{ currentTask.totalRounds }} 
              ({{ Math.round(calculateProgress(currentTask)) }}%)
            </div>
          </div>
          
          <div class="progress-card">
            <div class="progress-title">当前轮次进度</div>
            <el-progress 
              :percentage="currentTask.status === 'completed' ? 100 : currentRoundProgress" 
              :status="(currentTask.status === 'completed' || currentRoundProgress === 100) ? 'success' : ''"
              :stroke-width="20"
            />
            <div class="progress-details">
              完成客户端: {{ currentTask.status === 'completed' ? currentTask.participants.length : completedClients }}/{{ currentTask.participants.length }}
            </div>
          </div>
          
          <div class="progress-card">
            <div class="progress-title">预计剩余时间</div>
            <div class="time-display">{{ estimatedTimeRemaining }}</div>
            <div class="progress-details">
              当前轮用时: {{ currentRoundTime }}
            </div>
          </div>
        </div>
      </el-card>
      
      <!-- 性能指标 -->
      <div class="metrics-grid">
        <el-card class="metric-card">
          <div class="metric-value">{{ currentAccuracy }}%</div>
          <div class="metric-label">准确率</div>
          <div class="trend-indicator" :class="accuracyTrend.class">
            <i :class="accuracyTrend.icon"></i>
            <span>{{ accuracyTrend.value }}%</span>
          </div>
        </el-card>
        
        <el-card class="metric-card">
          <div class="metric-value">{{ currentLoss }}</div>
          <div class="metric-label">损失值</div>
          <div class="trend-indicator" :class="lossTrend.class">
            <i :class="lossTrend.icon"></i>
            <span>{{ lossTrend.value }}</span>
          </div>
        </el-card>
        
        <el-card class="metric-card">
          <div class="metric-value">{{ activeParticipants }}/{{ currentTask.participants.length }}</div>
          <div class="metric-label">活跃参与者</div>
          <div class="active-participant-progress">
            <el-progress 
              :percentage="calculateActivePercentage()" 
              :stroke-width="6"
            />
          </div>
        </el-card>
        
        <el-card class="metric-card">
          <div class="metric-value">{{ avgRoundTime }}</div>
          <div class="metric-label">平均轮次时间</div>
          <div class="trend-indicator" :class="roundTimeTrend.class">
            <i :class="roundTimeTrend.icon"></i>
            <span>{{ roundTimeTrend.value }}</span>
          </div>
        </el-card>
      </div>
      
      <!-- 图表 -->
      <div class="charts-section">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header chart-header">
              <span>模型性能趋势</span>
              <el-select v-model="selectedMetric" size="small" placeholder="选择指标" style="width: 120px">
                <el-option label="准确率" value="accuracy" />
                <el-option label="损失值" value="loss" />
              </el-select>
            </div>
          </template>
          <div class="chart-container" ref="performanceChart" v-loading="loading"></div>
        </el-card>
        
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>参与者贡献</span>
            </div>
          </template>
          <div class="chart-container" ref="contributionChart" v-loading="loading"></div>
        </el-card>
      </div>
      
      <!-- 参与者状态 -->
      <el-card class="participants-card">
        <template #header>
          <div class="card-header">
            <span>参与者状态 ({{ currentTask.participants.length }})</span>
          </div>
        </template>
        
        <el-table :data="currentTask.participants" style="width: 100%">
          <el-table-column prop="client" label="客户端" min-width="220">
            <template #default="scope">
              <div class="client-info">
                <el-avatar :size="30" :src="getAvatarUrl(scope.row.client)" class="client-avatar"></el-avatar>
                <span>{{ shortenAddress(scope.row.client) }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="dataSize" label="数据量" width="100" />
          
          <el-table-column prop="computeResource" label="计算资源" width="120">
            <template #default="scope">
              <el-tag size="small" :type="scope.row.computeResource === 'GPU' ? 'success' : 'info'">
                {{ scope.row.computeResource }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="currentRoundStatus" label="当前轮次" width="120">
            <template #default="scope">
              <el-tag :type="getParticipantStatusType(scope.row.currentRoundStatus)">
                {{ getParticipantStatusText(scope.row.currentRoundStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="accuracy" label="准确率" width="100">
            <template #default="scope">
              {{ scope.row.accuracy !== null && scope.row.accuracy !== undefined ? (scope.row.accuracy * 100).toFixed(2) + '%' : '未知' }}
            </template>
          </el-table-column>
          
          <el-table-column prop="loss" label="损失值" width="100">
            <template #default="scope">
              {{ scope.row.loss !== null && scope.row.loss !== undefined ? parseFloat(scope.row.loss).toFixed(4) : '未知' }}
            </template>
          </el-table-column>
          
          <el-table-column prop="lastUpdate" label="最后更新" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.lastUpdate) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="contribution" label="贡献度" width="150">
            <template #default="scope">
              <el-progress 
                :percentage="scope.row.contribution" 
                :color="getContributionColor(scope.row.contribution)"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      
      <!-- 日志记录 -->
      <el-card class="logs-card">
        <template #header>
          <div class="card-header">
            <span>训练日志</span>
            <el-button type="primary" size="small" @click="clearLogs">清空日志</el-button>
          </div>
        </template>
        
        <div class="logs-container">
          <div v-for="(log, index) in trainingLogs" :key="index" class="log-entry" :class="log.type">
            <span class="log-time">{{ formatTime(log.time) }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
          <div v-if="trainingLogs.length === 0" class="empty-logs">
            暂无训练日志
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import Breadcrumb from '@/components/Breadcrumb.vue';
import apiService, { TaskAPI, ParticipantAPI, LogAPI, MetricAPI, ModelAPI } from '@/services/api';
import * as echarts from 'echarts';
import axios from 'axios';

export default {
  name: 'TrainingMonitor',
  components: {
    Breadcrumb
  },
  setup() {
    const route = useRoute();
    const router = useRouter();
    const loading = ref(false);
    const isStopping = ref(false);
    const currentTask = ref(null);
    const trainingLogs = ref([
      {
        time: new Date(Date.now() - 60000),
        message: '轮次 3 已开始，8 个客户端参与训练',
        type: 'info'
      },
      {
        time: new Date(Date.now() - 120000),
        message: '客户端 0x3d5f...8a4e 完成轮次 2 训练，准确率: 83.2%',
        type: 'success'
      },
      {
        time: new Date(Date.now() - 180000),
        message: '客户端 0x67ac...5b2f 完成轮次 2 训练，准确率: 82.1%',
        type: 'success'
      },
      {
        time: new Date(Date.now() - 240000),
        message: '客户端 0x8e2d...9c4a 连接超时，尝试重新连接',
        type: 'warning'
      },
      {
        time: new Date(Date.now() - 300000),
        message: '轮次 2 已开始，8 个客户端参与训练',
        type: 'info'
      }
    ]);
    const activeTasks = ref([]);
    const selectedMetric = ref('accuracy');
    const performanceChart = ref(null);
    const contributionChart = ref(null);
    const refreshInterval = ref(null);
    const currentRoundProgress = ref(0);
    const completedClients = ref(0);
    const activeParticipants = ref(0);
    
    // 指标显示
    const currentAccuracy = ref('0.00');
    const currentLoss = ref('0.0000');
    const oldAccuracy = ref(0); // 添加保存旧准确率的变量
    const oldLoss = ref(0); // 添加保存旧损失值的变量
    const estimatedTimeRemaining = ref('计算中...');
    const avgRoundTime = ref('计算中...');
    const currentRoundTime = ref('计算中...');
    
    // 趋势指标
    const accuracyTrend = reactive({
      value: '0.00',
      icon: 'fas fa-minus',
      class: ''
    });
    
    const lossTrend = reactive({
      value: '0.000',
      icon: 'fas fa-minus',
      class: ''
    });
    
    const roundTimeTrend = reactive({
      value: '-20s',
      icon: 'fas fa-arrow-down',
      class: 'positive'
    });
    
    // 存储图表实例
    const charts = reactive({
      performanceChart: null,
      contributionChart: null
    });
    
    // 加载活跃任务
    const loadActiveTasks = async () => {
      loading.value = true;
      try {
        console.log('正在加载活跃训练任务和最近完成的任务...');
        
        // 从数据库API获取所有任务
        const allTasks = await TaskAPI.getAll();
        console.log('获取到所有任务:', allTasks.length, '个');
        
        // 筛选出正在进行中的任务和最近完成的任务（最近7天内）
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        
        // 状态映射 (数据库状态 -> 前端状态)
        const statusMap = {
          'pending': 'published',  // 数据库中的pending表示前端中的published(已发布)
          'in_progress': 'in_progress',
          'completed': 'completed',
          'stopped': 'cancelled',
          'failed': 'cancelled'
        };
        
        // 处理任务列表，转换状态和字段名
        let processedTasks = allTasks.map(task => {
          // 获取适合前端的状态
          const frontendStatus = statusMap[task.status] || 'draft';
          
          return {
            id: task.id,
            name: task.name,
            description: task.description,
            status: frontendStatus,
            dataType: task.data_type,
            currentRound: task.current_round || 0,
            totalRounds: task.total_rounds,
            requiredParticipants: task.required_participants,
            participants: [], // 默认空参与者列表，后续会加载
            applications: 0,  // 默认申请数
            accuracy: null,   // 准确率
            learningRate: task.learning_rate,
            batchSize: task.batch_size,
            timeout: task.timeout,
            createdAt: new Date(task.created_at),
            updatedAt: new Date(task.updated_at),
            startTime: task.start_time ? new Date(task.start_time) : null,
            endTime: task.end_time ? new Date(task.end_time) : null
          };
        });
        
        // 筛选出相关任务（正在进行中的和最近完成的）
        let relevantTasks = processedTasks.filter(task => 
          task.status === 'in_progress' || 
          (task.status === 'completed' && 
           task.endTime && new Date(task.endTime) > sevenDaysAgo)
        );
        
        console.log('筛选出的相关任务:', relevantTasks.length, '个');
        
        // 异步加载每个任务的参与者
        for (const task of relevantTasks) {
          try {
            // 获取每个任务的参与者
            const participants = await ParticipantAPI.getByTaskId(task.id);
            console.log(`获取到任务 ${task.id} 的参与者:`, participants.length);
            
            // 添加参与者信息到任务
            task.participants = participants.map(p => ({
              id: p.id,
              client: p.client_address,
              currentRoundStatus: p.status,
              contribution: 0, // 默认值，后续可能从指标中获取
              dataSize: p.data_size || 0,
              computeResource: p.compute_resource || 'CPU',
              accuracy: null,
              loss: null,
              lastUpdate: new Date(p.updated_at || p.created_at)
            }));
            
            // 更新申请数量
            task.applications = participants.length;
            
          } catch (err) {
            console.error(`获取任务 ${task.id} 的参与者失败:`, err);
          }
        }
        
        // 按状态和开始时间排序：进行中的在前，然后是最近完成的
        relevantTasks.sort((a, b) => {
          // 首先按状态排序
          if (a.status === 'in_progress' && b.status !== 'in_progress') return -1;
          if (a.status !== 'in_progress' && b.status === 'in_progress') return 1;
          
          // 如果状态相同，按时间逆序（最近的在前）
          const timeA = a.startTime ? new Date(a.startTime) : new Date(0);
          const timeB = b.startTime ? new Date(b.startTime) : new Date(0);
          return timeB - timeA;
        });
        
        // 如果没有找到任何任务，显示提示信息
        if (relevantTasks.length === 0) {
          console.log('没有找到相关任务');
          ElMessage.info('当前没有正在进行或最近完成的训练任务');
        } else {
          console.log(`找到 ${relevantTasks.length} 个相关任务`);
        }
        
        activeTasks.value = relevantTasks;
        
        // 检查URL参数中是否包含taskId（从模型管理页面跳转）
        const taskId = route.query.taskId || route.query.task;
        if (taskId) {
          console.log('URL中包含任务ID参数:', taskId);
          const task = activeTasks.value.find(t => t.id && t.id.toString() === taskId.toString());
          if (task) {
            console.log('从URL参数中选择任务:', task.name);
            selectTask(task);
          } else {
            console.log('无法找到与URL参数匹配的任务:', taskId);
            ElMessage.warning(`无法找到任务 #${taskId}，可能该任务已完成或已被删除`);
          }
        }
      } catch (error) {
        console.error('加载任务失败:', error);
        ElMessage.error(`加载任务列表失败: ${error.message}`);
      } finally {
        loading.value = false;
      }
    };
    
    // 选择任务
    const selectTask = async (task) => {
      try {
        loading.value = true;
        console.log(`选择任务:`, task);
        
        // 清空之前的数据
        trainingLogs.value = [];
        
        // 创建一个深拷贝，避免引用问题
        currentTask.value = JSON.parse(JSON.stringify(task));
        
        // 确保participants是一个数组
        if (!currentTask.value.participants) {
          currentTask.value.participants = [];
        }
        
        // 确保每个参与者都有详细信息
        currentTask.value.participants = currentTask.value.participants.map(p => {
          if (typeof p === 'string') {
            // 如果p是字符串（地址），转换为对象
            return {
              client: p,
              status: 'pending',
              contribution: 0,
              accuracy: 0,
              loss: 0
            };
          }
          return p;
        });
        
        // 初始化历史记录，如果不存在
        if (!currentTask.value.history) {
          currentTask.value.history = [];
        }
        
        // 重置指标值
        currentAccuracy.value = '0.00';
        currentLoss.value = '0.0000';
        oldAccuracy.value = 0;
        oldLoss.value = 0;
        
        // 获取最新任务状态和历史数据
        try {
          console.log('开始获取服务器状态...');
          // 使用axios直接调用API获取服务器状态
          const response = await axios.get('http://localhost:5000/api/server/status');
          const serverStatusResponse = response.data;
          
          // 打印完整的响应内容以便调试
          console.log('获取到的服务器状态:', serverStatusResponse);
          
          // 确保得到的响应是一个有效对象
          if (!serverStatusResponse) {
            console.error('服务器状态响应为空');
            throw new Error('服务器状态响应为空');
          }
          
          // 使用解构赋值提取需要的字段，并提供默认值防止出错
          const { 
            isTraining = false,
            currentRound = 0,
            totalRounds = 0,
            participants = [],
            history = [] 
          } = serverStatusResponse;
          
          if (isTraining) {
            console.log(`服务器正在训练中，当前轮次: ${currentRound}/${totalRounds}`);
            // 更新当前轮次和总轮次
            currentTask.value.currentRound = currentRound;
            currentTask.value.totalRounds = totalRounds;
            
            // 如果轮次为0但存在参与者，将当前轮次设为1以显示进度
            if (currentTask.value.currentRound === 0 && participants && participants.length > 0) {
              console.log('当前轮次为0但有参与者，设置为轮次1以显示进度');
              currentTask.value.currentRound = 1;
            }
            
            // 合并服务器历史数据
            if (history && history.length > 0) {
              console.log('合并服务器历史数据，数据点数量:', history.length);
              
              // 直接使用服务器的历史数据，确保数据完整性
              currentTask.value.history = [...history];
              console.log('已更新历史数据，数据点数量:', currentTask.value.history.length);
              
              // 确保历史记录按轮次排序
              currentTask.value.history.sort((a, b) => {
                const roundA = typeof a.round === 'number' ? a.round : parseInt(a.round);
                const roundB = typeof b.round === 'number' ? b.round : parseInt(b.round);
                return roundA - roundB;
              });
              
              // 提取最新一轮的性能指标
              if (currentTask.value.history.length > 0) {
                const latestHistory = currentTask.value.history[currentTask.value.history.length - 1];
                if (latestHistory.accuracy) {
                  currentAccuracy.value = (latestHistory.accuracy * 100).toFixed(2);
                  oldAccuracy.value = parseFloat(currentAccuracy.value) || 0;
                }
                if (latestHistory.loss) {
                  currentLoss.value = latestHistory.loss.toFixed(4);
                  oldLoss.value = parseFloat(currentLoss.value) || 0;
                }
              }
            }
            
            // 更新参与者状态
            if (participants && Array.isArray(participants)) {
              console.log('服务器参与者数量:', participants.length);
              for (const serverParticipant of participants) {
                // 确保参与者对象具有 address 属性
                const participantAddress = serverParticipant.address || serverParticipant.client;
                if (!participantAddress) {
                  console.warn('参与者缺少地址信息:', serverParticipant);
                  continue;
                }
                
                const participantIndex = currentTask.value.participants.findIndex(
                  p => p.client === participantAddress
                );
                
                if (participantIndex !== -1) {
                  // 更新现有参与者
                  console.log(`更新参与者 ${participantAddress} 的状态`);
                  currentTask.value.participants[participantIndex].status = serverParticipant.status;
                  currentTask.value.participants[participantIndex].contribution = serverParticipant.contribution || 0;
                } else {
                  // 添加新参与者
                  console.log(`添加新参与者 ${participantAddress}`);
                  currentTask.value.participants.push({
                    client: participantAddress,
                    status: serverParticipant.status,
                    contribution: serverParticipant.contribution || 0,
                    accuracy: 0,
                    loss: 0
                  });
                }
              }
            }
            
            // 更新任务状态为in_progress
            currentTask.value.status = 'in_progress';
          } else {
            console.log('服务器训练未进行');
            // 如果服务器不在训练，但有历史数据，则标记为已完成
            if (history && history.length > 0) {
              console.log('服务器训练已完成，使用历史数据显示结果');
              currentTask.value.history = [...history];
              currentTask.value.status = 'completed';
            }
          }
        } catch (error) {
          console.error('获取服务器状态失败:', error);
          // 提供更详细的错误信息
          if (error.response) {
            console.error('错误响应状态:', error.response.status);
            console.error('错误响应数据:', error.response.data);
          }
          ElMessage.warning('无法获取最新的服务器状态，显示本地任务数据');
        }
        
        // 初始化图表
        nextTick(() => {
          initCharts();
        });
        
        // 开始定时刷新
        startRefreshTimer();
        
        // 立即刷新一次数据
        await refreshData();
      } catch (error) {
        console.error('选择任务失败:', error);
        ElMessage.error(`选择任务失败: ${error.message}`);
      } finally {
        loading.value = false;
      }
    };
    
    // 初始化图表
    const initCharts = () => {
      try {
        console.log('初始化图表...');
        
        // 初始化性能趋势图
        if (performanceChart.value) {
          console.log('初始化性能趋势图...');
          charts.performanceChart = echarts.init(performanceChart.value);
          
          // 设置图表基本配置
          charts.performanceChart.setOption({
            title: {
              text: selectedMetric.value === 'accuracy' ? '准确率趋势' : '损失值趋势',
              left: 'center',
              textStyle: {
                fontSize: 16,
                fontWeight: 'normal'
              }
            },
            tooltip: {
              trigger: 'axis',
              formatter: function(params) {
                let tooltip = params[0].name + '<br/>';
                params.forEach(param => {
                  const value = param.value !== null ? param.value.toFixed(3) : '无数据';
                  tooltip += `${param.seriesName}: ${value}<br/>`;
                });
                return tooltip;
              }
            },
            legend: {
              data: selectedMetric.value === 'accuracy' ? ['准确率'] : ['损失值'],
              bottom: 0,
              selectedMode: false
            },
            grid: {
              left: '5%',
              right: '5%',
              bottom: '10%',
              top: '15%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: ['暂无数据'],
              axisLine: {
                lineStyle: {
                  color: '#ddd'
                }
              },
              axisLabel: {
                color: '#666',
                fontWeight: 'bold'
              }
            },
            yAxis: {
              name: selectedMetric.value === 'accuracy' ? '准确率' : '损失值',
              nameTextStyle: {
                fontWeight: 'bold'
              },
              type: 'value',
              min: selectedMetric.value === 'accuracy' ? 0.8 : 0, // 准确率设置最小值为0.8，让微小变化更加明显
              max: selectedMetric.value === 'accuracy' ? 1.0 : 2, // 准确率设置最大值为1.0
              position: 'left',
              axisLine: {
                lineStyle: {
                  color: selectedMetric.value === 'accuracy' ? '#91cc75' : '#ee6666',
                  width: 2
                }
              },
              axisLabel: {
                formatter: function(value) {
                  // 控制小数显示位数，最多保留3位小数
                  return value.toFixed(3);
                },
                color: '#666',
                fontWeight: 'bold'
              },
              splitLine: {
                lineStyle: {
                  type: 'dashed',
                  color: '#eee'
                }
              }
            },
            series: [
              {
                name: selectedMetric.value === 'accuracy' ? '准确率' : '损失值',
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 8,
                sampling: 'average',
                data: [], // 初始化时不显示数据
                itemStyle: {
                  color: selectedMetric.value === 'accuracy' ? '#91cc75' : '#ee6666'
                },
                lineStyle: {
                  width: 4
                },
                connectNulls: true
              }
            ]
          });
          
          console.log('性能趋势图初始化完成');
        }
        
        // 初始化参与者贡献图
        if (contributionChart.value) {
          console.log('初始化参与者贡献图...');
          charts.contributionChart = echarts.init(contributionChart.value);
          
          // 设置图表基本配置
          charts.contributionChart.setOption({
            title: {
              text: '参与者贡献',
              left: 'center',
              textStyle: {
                fontSize: 16,
                fontWeight: 'normal'
              }
            },
            tooltip: {
              trigger: 'item',
              formatter: '{b}: {c} ({d}%)'
            },
            legend: {
              orient: 'horizontal',
              bottom: 10,
              data: ['等待数据...']
            },
            series: [
              {
                name: '贡献度',
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['50%', '45%'],
                avoidLabelOverlap: true,
                itemStyle: {
                  borderRadius: 10,
                  borderColor: '#fff',
                  borderWidth: 2
                },
                label: {
                  show: true,
                  formatter: '{b}: {c}'
                },
                emphasis: {
                  label: {
                    show: true,
                    fontSize: '14',
                    fontWeight: 'bold'
                  },
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                },
                data: [{ value: 100, name: '等待数据...' }]
              }
            ]
          });
          
          console.log('参与者贡献图初始化完成');
        }
        
        // 注册窗口大小变化处理函数，自适应图表大小
        window.addEventListener('resize', () => {
          if (charts.performanceChart) {
            charts.performanceChart.resize();
          }
          if (charts.contributionChart) {
            charts.contributionChart.resize();
          }
        });
        
        // 初始化完成后立即更新图表数据
        updateCharts();
      } catch (error) {
        console.error('初始化图表失败:', error);
      }
    };
    
    // 更新图表数据
    const updateCharts = () => {
      if (!currentTask.value) return;
      
      try {
        console.log('正在更新图表数据...');
        
        // 更新性能趋势图
        if (charts.performanceChart) {
          // 获取历史数据
          let historyData = [];
          
          // 尝试从当前任务中获取历史记录
          if (currentTask.value.history && currentTask.value.history.length > 0) {
            // 过滤掉轮次为0的数据点，只保留轮次>=1的数据
            historyData = [...currentTask.value.history].filter(entry => {
              const round = typeof entry.round === 'number' ? entry.round : parseInt(entry.round);
              return round >= 1; // 只保留轮次>=1的数据
            });
            
            // 按照轮次排序
            historyData = historyData.sort((a, b) => {
              // 确保对比的是数字
              const roundA = typeof a.round === 'number' ? a.round : parseInt(a.round);
              const roundB = typeof b.round === 'number' ? b.round : parseInt(b.round);
              return roundA - roundB;
            });
            console.log('使用任务历史数据更新图表，原始数据点数量:', historyData.length);
            if (historyData.length > 0) {
              console.log('历史数据样本:', historyData[0]);
            }
            
            // 对历史数据按轮次进行合并和平均，确保每个轮次只有一个数据点
            const roundsData = {};
            
            // 填充实际数据
            for (const entry of historyData) {
              const round = typeof entry.round === 'number' ? entry.round : parseInt(entry.round);
              if (!roundsData[round]) {
                roundsData[round] = { 
                  accuracySum: 0, 
                  lossSum: 0, 
                  count: 0,
                  hasData: false
                };
              }
              
              // 累加有效数据
              if (typeof entry.accuracy === 'number' && !isNaN(entry.accuracy)) {
                roundsData[round].accuracySum += entry.accuracy;
                roundsData[round].count++;
                roundsData[round].hasData = true;
              }
              
              if (typeof entry.loss === 'number' && !isNaN(entry.loss)) {
                roundsData[round].lossSum += entry.loss;
                if (!roundsData[round].hasData) {
                  roundsData[round].count++;
                  roundsData[round].hasData = true;
                }
              }
            }
            
            // 计算平均值并创建图表数据
            const rounds = Object.keys(roundsData).map(r => parseInt(r)).sort((a, b) => a - b);
            const accuracyData = [];
            const lossData = [];
            
            for (const round of rounds) {
              const data = roundsData[round];
              if (data.count > 0) {
                const avgAccuracy = data.accuracySum / data.count;
                const avgLoss = data.lossSum / data.count;
                
                console.log(`轮次 ${round} 平均指标: 准确率=${avgAccuracy.toFixed(4)}, 损失=${avgLoss.toFixed(4)}`);
                
                accuracyData.push({
                  value: [round, avgAccuracy],
                  round
                });
                
                lossData.push({
                  value: [round, avgLoss],
                  round
                });
              }
            }
            
            console.log('准备图表数据 - 轮次数量:', rounds.length, '准确率数据点:', accuracyData.length, '损失值数据点:', lossData.length);
            
            // 设置图表数据
            const metricData = selectedMetric.value === 'accuracy' ? accuracyData : lossData;
            const xAxisData = rounds.map(r => `轮次 ${r}`);
            const seriesName = selectedMetric.value === 'accuracy' ? '准确率' : '损失值';
            
            // 如果有数据，更新图表
            if (metricData.length > 0) {
              // 计算Y轴范围
              let minValue, maxValue;
              
              if (selectedMetric.value === 'accuracy') {
                // 准确率通常范围为0-1，但可能需要调整以显示细微变化
                const values = metricData.map(d => d.value[1]);
                minValue = Math.max(0, Math.min(...values) - 0.05);
                maxValue = Math.min(1.0, Math.max(...values) + 0.05);
              } else {
                // 损失值从大到小，要确保能看到全部变化
                const values = metricData.map(d => d.value[1]);
                minValue = Math.max(0, Math.min(...values) - 0.1);
                maxValue = Math.max(...values) + 0.1;
              }
              
              console.log(`图表Y轴范围: ${minValue.toFixed(4)} - ${maxValue.toFixed(4)}`);
              
              // 格式化数据以确保图表正确显示
              const formattedData = metricData.map(d => {
                return d.value[1];
              });
              
              charts.performanceChart.setOption({
                title: {
                  text: selectedMetric.value === 'accuracy' ? '准确率趋势' : '损失值趋势'
                },
                legend: {
                  // 确保legend数据与series名字匹配
                  data: [seriesName]
                },
                xAxis: {
                  data: xAxisData
                },
                yAxis: {
                  type: 'value',
                  name: seriesName,
                  min: minValue,
                  max: maxValue,
                  axisLabel: {
                    formatter: function(value) {
                      return value.toFixed(3);
                    }
                  }
                },
                series: [{
                  name: seriesName,
                  type: 'line',
                  data: formattedData
                }]
              });
              console.log('性能趋势图更新完成，显示', formattedData.length, '个数据点');
            } else {
              // 没有有效数据，显示空图表
              const seriesName = selectedMetric.value === 'accuracy' ? '准确率' : '损失值';
              charts.performanceChart.setOption({
                title: {
                  text: selectedMetric.value === 'accuracy' ? '准确率趋势（暂无数据）' : '损失值趋势（暂无数据）'
                },
                legend: {
                  data: [seriesName]
                },
                xAxis: {
                  data: ['暂无数据']
                },
                series: [{
                  name: seriesName,
                  type: 'line',
                  data: []
                }]
              });
              console.log('无有效性能数据，显示空图表');
            }
          } else {
            // 无历史数据，显示空图表
            const seriesName = selectedMetric.value === 'accuracy' ? '准确率' : '损失值';
            charts.performanceChart.setOption({
              title: {
                text: selectedMetric.value === 'accuracy' ? '准确率趋势（暂无数据）' : '损失值趋势（暂无数据）'
              },
              legend: {
                data: [seriesName]
              },
              xAxis: {
                data: ['暂无数据']
              },
              series: [{
                name: seriesName,
                type: 'line',
                data: []
              }]
            });
            console.log('无历史数据，显示空图表');
          }
        }
        
        // 更新参与者贡献图
        if (charts.contributionChart && currentTask.value.participants && currentTask.value.participants.length > 0) {
          console.log('正在更新参与者贡献图...');
          
          // 提取参与者贡献数据
          let participantData = [];
          
          // 筛选出有贡献度的参与者
          const contributingParticipants = currentTask.value.participants.filter(p => p.contribution > 0);
          
          // 如果有至少一个有贡献度的参与者
          if (contributingParticipants.length > 0) {
            // 为每个参与者创建贡献数据点，使用漂亮的颜色方案
            const colorPalette = [
              '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', 
              '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#6e7079'
            ];
            
            participantData = contributingParticipants.map((p, index) => ({
              value: p.contribution,
              name: shortenAddress(p.client),
              itemStyle: {
                color: colorPalette[index % colorPalette.length]
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }))
            .sort((a, b) => b.value - a.value); // 按贡献度排序
            
            // 如果有有效数据，更新贡献图
            if (participantData.length > 0) {
              console.log('更新参与者贡献图，参与者数量:', participantData.length);
              console.log('贡献数据:', participantData);
              
              // 提取所有参与者的名称用于图例
              const legendData = participantData.map(item => item.name);
              
              charts.contributionChart.setOption({
                legend: {
                  data: legendData,
                  orient: 'horizontal',
                  bottom: 10,
                  type: 'scroll',
                  pageIconSize: 12
                },
                series: [{
                  name: '贡献度',
                  data: participantData,
                  label: {
                    formatter: '{b}: {c}%'
                  }
                }]
              });
              console.log('参与者贡献图更新完成，显示', participantData.length, '个参与者');
              return;
            }
          }
          
          // 如果没有有效的贡献数据，显示指示信息
          const emptyName = '暂无贡献数据';
          charts.contributionChart.setOption({
            legend: {
              data: [emptyName]
            },
            series: [{
              name: '贡献度',
              data: [{ 
                value: 1, 
                name: emptyName, 
                itemStyle: { color: '#eee' } 
              }]
            }]
          });
          console.log('无有效贡献数据，显示提示信息');
        } else if (charts.contributionChart) {
          // 没有参与者数据，显示空图表
          const emptyName = '暂无参与者';
          charts.contributionChart.setOption({
            legend: {
              data: [emptyName]
            },
            series: [{
              name: '贡献度',
              data: [{ 
                value: 1, 
                name: emptyName, 
                itemStyle: { color: '#eee' } 
              }]
            }]
          });
          console.log('无参与者数据，显示空图表');
        }
      } catch (error) {
        console.error('更新图表失败:', error);
      }
    };
    
    // 计算任务进度
    const calculateProgress = (task) => {
      if (!task) return 0;
      
      // 如果任务状态是"已完成"，则进度为100%
      if (task.status === 'completed') return 100;
      
      // 确保有效的轮次数据
      let currentRound = task.currentRound || 0;
      const totalRounds = task.totalRounds || 1;
      
      // 避免除以零的情况
      if (totalRounds <= 0) return 0;
      
      // 调整：如果当前有活跃的参与者，但轮次仍为0，则视为第1轮正在进行中
      if (currentRound === 0 && task.participants && task.participants.length > 0 && task.status === 'in_progress') {
        currentRound = 1;
      }
      
      // 计算进度百分比 (当前轮次 / 总轮次) * 100
      return Math.min(Math.round((currentRound / totalRounds) * 100), 100);
    };
    
    // 格式化持续时间（秒转为分钟和小时等）
    const formatDuration = (seconds) => {
      if (!seconds || seconds < 0) return '计算中...';
      
      // 将秒数取整
      seconds = Math.round(seconds);
      
      if (seconds < 60) {
        return `${seconds}秒`;
      } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
      } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const remainingSeconds = seconds % 60;
        
        let result = `${hours}h`;
        if (minutes > 0) result += ` ${minutes}m`;
        if (remainingSeconds > 0) result += ` ${remainingSeconds}s`;
        
        return result;
      }
    };
    
    // 获取进度状态
    const getProgressStatus = (task) => {
      const progress = calculateProgress(task);
      if (progress >= 100) return 'success';
      return '';
    };
    
    // 获取参与者状态类型
    const getParticipantStatusType = (status) => {
      if (!status) return 'info';
      
      const statusMap = {
        'waiting': 'info',
        'training': 'success',
        'completed': 'primary',
        'failed': 'danger',
        'timeout': 'warning',
        'inactive': 'info'
      };
      
      return statusMap[status] || 'info';
    };
    
    // 获取参与者状态文本
    const getParticipantStatusText = (status) => {
      if (!status) return '等待中';
      
      const statusTextMap = {
        'waiting': '等待中',
        'training': '训练中',
        'completed': '已完成',
        'failed': '失败',
        'timeout': '超时',
        'inactive': '未活跃'
      };
      
      return statusTextMap[status] || '等待中';
    };
    
    // 获取贡献度颜色
    const getContributionColor = (contribution) => {
      if (contribution >= 90) return '#67C23A';
      if (contribution >= 75) return '#E6A23C';
      if (contribution >= 60) return '#F56C6C';
      return '#909399';
    };
    
    // 获取头像URL
    const getAvatarUrl = (address) => {
      return `https://api.dicebear.com/7.x/identicon/svg?seed=${address}`;
    };
    
    // 缩短地址显示
    const shortenAddress = (address) => {
      if (!address) return '';
      if (address.includes('...')) return address; // 如果已经是缩短的格式
      return `${address.slice(0, 6)}...${address.slice(-4)}`;
    };
    
    // 格式化日期时间
    const formatDateTime = (date) => {
      if (!date) return '-';
      if (typeof date === 'string') {
        date = new Date(date);
      }
      
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    };
    
    // 格式化时间
    const formatTime = (date) => {
      if (!date) return '';
      if (typeof date === 'string') {
        date = new Date(date);
      }
      
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      
      return `${hours}:${minutes}:${seconds}`;
    };
    
    // 开始定时刷新
    const startRefreshTimer = () => {
      // 清除之前的定时器
      if (refreshInterval.value) {
        clearInterval(refreshInterval.value);
      }
      
      // 立即执行一次刷新
      refreshData();
      
      // 根据任务状态设置不同的刷新间隔
      const refreshTime = currentTask.value?.status === 'completed' ? 15000 : 3000;
      
      // 设置新的定时器
      refreshInterval.value = setInterval(() => {
        console.log(`执行定时刷新...(间隔: ${refreshTime/1000}秒)`);
        refreshData();
      }, refreshTime);
      
      console.log(`已启动定时刷新，间隔: ${refreshTime/1000}秒, 任务状态: ${currentTask.value?.status || '无任务'}`);
    };
    
    // 停止定时刷新
    const stopRefreshTimer = () => {
      if (refreshInterval.value) {
        clearInterval(refreshInterval.value);
        refreshInterval.value = null;
      }
    };
    
    // 显示空图表
    const emptyCharts = () => {
      try {
        console.log('显示空图表');
        
        // 初始化空的性能趋势图
        if (charts.performanceChart) {
          charts.performanceChart.setOption({
            title: {
              text: selectedMetric.value === 'accuracy' ? '准确率趋势' : '损失值趋势'
            },
            legend: {
              data: [selectedMetric.value === 'accuracy' ? '准确率' : '损失值']
            },
            xAxis: {
              data: ['暂无数据']
            },
            yAxis: {
              type: 'value',
              name: selectedMetric.value === 'accuracy' ? '准确率' : '损失值',
              min: selectedMetric.value === 'accuracy' ? 0.8 : 0, 
              max: selectedMetric.value === 'accuracy' ? 1.0 : null,
              axisLabel: {
                formatter: function(value) {
                  return value.toFixed(3);
                }
              }
            },
            series: [{
              name: selectedMetric.value === 'accuracy' ? '准确率' : '损失值',
              type: 'line',
              data: []
            }]
          });
        }
        
        // 初始化空的参与者贡献图
        if (charts.contributionChart) {
          charts.contributionChart.setOption({
            title: {
              text: '参与者贡献'
            },
            tooltip: {
              trigger: 'item'
            },
            series: [{
              name: '贡献度',
              type: 'pie',
              radius: ['40%', '70%'],
              center: ['50%', '45%'],
              data: [{ value: 1, name: '暂无贡献数据', itemStyle: { color: '#eee' } }]
            }]
          });
          console.log('空贡献图已初始化');
        }
      } catch (error) {
        console.error('显示空图表失败:', error);
      }
    };

    // 添加日志
    const addLog = (message, type = 'info') => {
      trainingLogs.value.unshift({
        time: new Date(),
        message,
        type
      });
      
      // 限制日志数量，避免过多
      if (trainingLogs.value.length > 100) {
        trainingLogs.value = trainingLogs.value.slice(0, 100);
      }
    };

    // 刷新数据
    const refreshData = async () => {
      if (!currentTask.value) return;
      
      try {
        console.log(`开始刷新训练数据，任务ID: ${currentTask.value.id}`);
        console.log('当前任务状态:', {
          id: currentTask.value.id,
          name: currentTask.value.name,
          status: currentTask.value.status,
          currentRound: currentTask.value.currentRound,
          totalRounds: currentTask.value.totalRounds,
          participantsCount: currentTask.value.participants ? currentTask.value.participants.length : 0
        });
        
        // 获取任务最新数据
        const taskData = await TaskAPI.getById(currentTask.value.id);
        console.log('获取到的任务数据:', taskData);
        
        if (taskData) {
          // 更新任务状态和轮次信息
          currentTask.value.status = taskData.status === 'pending' ? 'published' : taskData.status;
          currentTask.value.currentRound = taskData.current_round || 0;
          currentTask.value.totalRounds = taskData.total_rounds;
          
          // 打印轮次信息用于调试
          console.log(`从数据库获取到任务轮次信息: 当前轮次=${currentTask.value.currentRound}, 总轮次=${currentTask.value.totalRounds}`);
          
          // 如果轮次为0但任务已在进行中，将当前轮次设为1以显示进度
          if (currentTask.value.currentRound === 0 && 
              currentTask.value.status === 'in_progress' && 
              currentTask.value.participants && 
              currentTask.value.participants.length > 0) {
            console.log('当前轮次为0但任务进行中，设置为轮次1以显示进度');
            currentTask.value.currentRound = 1;
          }
          
          // 尝试从服务器状态获取更准确的轮次信息
          try {
            console.log('尝试从服务器状态获取更准确的轮次信息...');
            const serverStatusResponse = await axios.get('http://localhost:5000/api/server/status');
            
            if (serverStatusResponse && serverStatusResponse.data) {
              const serverCurrentRound = serverStatusResponse.data.currentRound || 0;
              console.log(`服务器状态中的当前轮次: ${serverCurrentRound}, 数据库中的当前轮次: ${currentTask.value.currentRound}`);
              
              // 使用服务器状态中的轮次，如果它比数据库中的更大
              if (serverCurrentRound > currentTask.value.currentRound) {
                console.log(`使用服务器状态中的轮次 ${serverCurrentRound} 替换数据库中的轮次 ${currentTask.value.currentRound}`);
                currentTask.value.currentRound = serverCurrentRound;
              }
            }
          } catch (serverStatusError) {
            console.error('获取服务器状态失败:', serverStatusError);
            // 继续使用数据库中的轮次信息
          }
          
          // 记录旧的指标，用于计算趋势
          oldAccuracy.value = parseFloat(currentAccuracy.value) || 0;
          oldLoss.value = parseFloat(currentLoss.value) || 0;
          
          try {
            // 获取参与者信息
            const participants = await ParticipantAPI.getByTaskId(currentTask.value.id);
            console.log(`获取到任务 ${currentTask.value.id} 的参与者:`, participants.length);
            
            // 更新参与者信息
            if (participants && participants.length > 0) {
              // 更新活跃参与者计数
              activeParticipants.value = participants.filter(p => 
                p.status === 'training' || p.status === 'approved'
              ).length;
              
              // 计算已完成当前轮次的客户端数量
              const currentRound = currentTask.value.currentRound;
              completedClients.value = participants.filter(p => 
                p.status === 'completed' || p.status === 'training' || p.status === 'approved'
              ).length;
              
              // 计算总数据量，用于计算贡献度
              const totalDataSize = participants.reduce((sum, p) => sum + (p.data_size || 0), 0);
              
              // 更新参与者列表
              currentTask.value.participants = participants.map(p => {
                // 计算贡献度 - 如果总数据量为0，则平均分配；否则根据数据量比例计算
                let contribution = 0;
                if (totalDataSize > 0) {
                  contribution = Math.round((p.data_size || 0) / totalDataSize * 100);
                } else if (participants.length > 0) {
                  contribution = Math.round(100 / participants.length);
                }
                
                // 确保贡献度不为0，以便在饼图中显示
                if (contribution === 0 && p.status === 'training') {
                  contribution = 1; // 至少给活跃参与者1%的贡献度
                }
                
                return {
                  id: p.id,
                  client: p.client_address,
                  currentRoundStatus: p.status,
                  contribution: contribution,
                  dataSize: p.data_size || 0,
                  computeResource: p.compute_resource || 'CPU',
                  accuracy: null,
                  loss: null,
                  lastUpdate: new Date(p.updated_at || p.created_at)
                };
              });
              
              console.log('更新参与者贡献度:', currentTask.value.participants.map(p => ({
                client: shortenAddress(p.client),
                contribution: p.contribution,
                dataSize: p.dataSize
              })));
            }
          } catch (err) {
            console.error(`获取任务 ${currentTask.value.id} 的参与者失败:`, err);
          }
          
          // 获取训练指标数据
          let hasMetricsData = false;
          try {
            console.log(`正在调用API获取任务 ${currentTask.value.id} 的指标...`);
            const metrics = await MetricAPI.getByTaskId(currentTask.value.id);
            console.log(`获取到任务 ${currentTask.value.id} 的指标:`, metrics);
            
            if (metrics && metrics.length > 0) {
              hasMetricsData = true;
              // 构建历史数据
              const historyData = metrics.map(metric => ({
                round: parseInt(metric.round),
                accuracy: parseFloat(metric.accuracy),
                loss: parseFloat(metric.loss),
                timestamp: new Date(metric.timestamp || Date.now()),
                participants: parseInt(metric.participants_count) || 0
              }));
              
              console.log('转换后的历史数据:', historyData);
              
              // 确保轮次排序
              historyData.sort((a, b) => a.round - b.round);
              
              // 更新历史数据
              currentTask.value.history = historyData;
              
              // 获取最新轮次的指标
              const latestMetric = metrics.reduce((latest, current) => 
                (!latest || parseInt(current.round) > parseInt(latest.round)) ? current : latest, null);
                
              if (latestMetric) {
                const metricAccuracy = parseFloat(latestMetric.accuracy);
                const metricLoss = parseFloat(latestMetric.loss);
                
                // 确保数据有效
                if (!isNaN(metricAccuracy)) {
                  currentAccuracy.value = (metricAccuracy * 100).toFixed(2);
                  console.log('更新准确率:', metricAccuracy, '->', currentAccuracy.value);
                }
                
                if (!isNaN(metricLoss)) {
                  currentLoss.value = metricLoss.toFixed(4);
                  console.log('更新损失值:', metricLoss, '->', currentLoss.value);
                }
                
                console.log('从指标中更新性能指标 - 准确率:', currentAccuracy.value, '损失值:', currentLoss.value);
              }
            } else {
              console.warn('API返回的指标数据为空或格式不正确');
            }
          } catch (err) {
            console.error(`获取任务 ${currentTask.value.id} 的指标失败:`, err);
            if (err.response) {
              console.error('API错误响应:', err.response.status, err.response.data);
            }
            // 如果是404错误，可能是API路径不对或数据不存在，我们使用默认值
            hasMetricsData = false;
          }
          
          // 获取任务日志
          try {
            const logs = await LogAPI.getByTaskId(currentTask.value.id, 20);
            console.log(`获取到任务 ${currentTask.value.id} 的日志:`, logs);
            
            if (logs && logs.length > 0) {
              // 更新日志显示
              trainingLogs.value = logs.map(log => ({
                time: new Date(log.timestamp || log.created_at),
                message: log.message,
                type: getLogTypeFromMessage(log.message)
              }));
              
              console.log('更新了训练日志:', trainingLogs.value.length, '条');
            }
          } catch (err) {
            console.error(`获取任务 ${currentTask.value.id} 的日志失败:`, err);
            // 不要让日志获取失败阻止其他数据的加载和显示
          }
          
          // 计算当前轮次进度
          if (currentTask.value.status === 'in_progress') {
            // 基于已完成客户端数量计算
            const totalClients = currentTask.value.participants.length;
            const completed = completedClients.value;
            currentRoundProgress.value = totalClients > 0 ? Math.round((completed / totalClients) * 100) : 0;
            console.log('基于完成客户端计算当前轮次进度:', currentRoundProgress.value);
          } else if (currentTask.value.status === 'completed') {
            currentRoundProgress.value = 100;
          }
          
          // 计算准确率和损失值的趋势变化
          const newAccuracy = parseFloat(currentAccuracy.value) || 0;
          const newLoss = parseFloat(currentLoss.value) || 0;
          
          // 计算变化值并更新趋势数据
          const accuracyChange = (newAccuracy - oldAccuracy.value).toFixed(2);
          const lossChange = (newLoss - oldLoss.value).toFixed(3);
          
          // 更新趋势指标
          if (accuracyChange !== '0.00') {
            accuracyTrend.value = accuracyChange > 0 ? `+${accuracyChange}` : `${accuracyChange}`;
            accuracyTrend.icon = accuracyChange > 0 ? 'fas fa-arrow-up' : 'fas fa-arrow-down';
            accuracyTrend.class = accuracyChange > 0 ? 'positive' : 'negative';
          }
          
          if (lossChange !== '0.000') {
            lossTrend.value = lossChange > 0 ? `+${lossChange}` : `${lossChange}`;
            lossTrend.icon = lossChange < 0 ? 'fas fa-arrow-down' : 'fas fa-arrow-up';
            lossTrend.class = lossChange < 0 ? 'positive' : 'negative';
          }
          
          // 更新时间估计（基于实际数据计算）
          estimatedTimeRemaining.value = estimateRemainingTime(currentTask.value);
          
          // 计算平均轮次时间
          if (currentTask.value.history && currentTask.value.history.length > 1) {
            // 如果有历史数据，计算平均轮次时间
            const roundTimes = [];
            for (let i = 1; i < currentTask.value.history.length; i++) {
              const timeDiff = new Date(currentTask.value.history[i].timestamp) - new Date(currentTask.value.history[i-1].timestamp);
              roundTimes.push(timeDiff);
            }
            const avgTime = roundTimes.reduce((sum, time) => sum + time, 0) / roundTimes.length;
            avgRoundTime.value = formatDuration(avgTime / 1000); // 转换为秒
          } else {
            avgRoundTime.value = "计算中...";
          }
          
          // 计算当前轮次已用时间
          if (currentTask.value.status === 'in_progress' && currentTask.value.history && currentTask.value.history.length > 0) {
            const lastRoundData = currentTask.value.history[currentTask.value.history.length - 1];
            const timeSinceLastRound = Date.now() - new Date(lastRoundData.timestamp).getTime();
            currentRoundTime.value = formatDuration(timeSinceLastRound / 1000); // 转换为秒
          } else {
            currentRoundTime.value = "0s";
          }
          
          // 如果任务有历史数据，使用它来更新图表
          if (hasMetricsData && currentTask.value.history && currentTask.value.history.length > 0) {
            console.log('使用历史数据更新图表，数据点数量:', currentTask.value.history.length);
            // 更新图表
            updateCharts();
          } else {
            console.log('任务没有历史数据，将显示空图表');
            // 没有历史数据，显示空图表
            emptyCharts();
          }
        }
      } catch (error) {
        console.error('刷新数据失败:', error);
        // 添加错误日志但不中断显示
        trainingLogs.value.unshift({
          time: new Date(),
          message: `刷新数据时发生错误: ${error.message}`,
          type: 'error'
        });
      } finally {
        loading.value = false;
      }
    };
    
    // 从消息内容判断日志类型
    const getLogTypeFromMessage = (message) => {
      if (!message) return 'info';
      
      const lowerMsg = message.toLowerCase();
      if (lowerMsg.includes('error') || lowerMsg.includes('失败') || lowerMsg.includes('错误')) {
        return 'error';
      } else if (lowerMsg.includes('warning') || lowerMsg.includes('警告') || lowerMsg.includes('超时') || lowerMsg.includes('断开连接')) {
        return 'warning';
      } else if (lowerMsg.includes('success') || lowerMsg.includes('完成') || lowerMsg.includes('成功')) {
        return 'success';
      }
      return 'info';
    };
    
    // 停止任务
    const stopTask = async () => {
      if (!currentTask.value) return;
      
      try {
        await ElMessageBox.confirm(
          `确定要停止任务 "${currentTask.value.name}" 的训练吗？`,
          '停止训练',
          {
            confirmButtonText: '确定停止',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );
        
        isStopping.value = true;
        
        // 使用TaskAPI更新任务状态为stopped
        try {
          await TaskAPI.update(currentTask.value.id, { status: 'cancelled' });
          console.log(`任务 ${currentTask.value.id} 已停止训练`);
        
        // 添加日志
        trainingLogs.value.unshift({
          time: new Date(),
          message: `训练已手动停止，已完成 ${currentTask.value.currentRound}/${currentTask.value.totalRounds} 轮训练`,
          type: 'warning'
        });
        
        // 更新任务状态
        currentTask.value.status = 'completed';
        
          // 创建模型到模型管理页面
          createModelFromTraining(currentTask.value);
        
          ElMessage.success('训练已成功停止，模型已自动创建');
          
          // 显示前往模型管理页面的提示
        setTimeout(() => {
            ElMessageBox.confirm(
              '训练已停止并生成模型。您现在要前往模型管理页面查看吗？',
              '前往模型管理',
              {
                confirmButtonText: '查看模型',
                cancelButtonText: '留在当前页面',
                type: 'info',
              }
            )
              .then(() => {
                // 用户选择查看模型，跳转到模型管理页面
                goToModelManagement();
              })
              .catch(() => {
                // 用户选择留在当前页面，继续显示当前任务或重置
          currentTask.value = null;
          stopRefreshTimer();
          loadActiveTasks();
              });
          }, 1000);
        } catch (error) {
          console.error('停止训练API调用失败:', error);
          ElMessage.error(`停止训练失败: ${error.message}`);
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('停止训练失败:', error);
          ElMessage.error('停止训练失败，请重试');
        }
      } finally {
        isStopping.value = false;
      }
    };
    
    // 返回上一页
    const goBack = () => {
      if (currentTask.value) {
        // 返回任务选择
        stopRefreshTimer();
        currentTask.value = null;
      } else {
        // 返回任务列表
        router.push('/server/tasks');
      }
    };
    
    // 导出数据
    const exportData = () => {
      if (!currentTask.value) return;
      
      ElMessage.success('训练数据导出功能将在后续版本实现');
    };
    
    // 清空日志
    const clearLogs = () => {
      trainingLogs.value = [];
    };
    
    // 监听指标选择变化
    watch(selectedMetric, (newValue) => {
      console.log('指标选择变化:', newValue);
      if (charts.performanceChart) {
        // 立即更新图表配置
        charts.performanceChart.setOption({
          title: {
            text: newValue === 'accuracy' ? '准确率趋势' : '损失值趋势'
          },
          legend: {
            data: newValue === 'accuracy' ? ['准确率'] : ['损失值']
          },
          yAxis: {
            name: newValue === 'accuracy' ? '准确率' : '损失值',
            // 为准确率设置动态范围
            min: newValue === 'accuracy' ? 0.8 : 0, // 准确率设置最小值为0.8，让微小变化更加明显
            max: newValue === 'accuracy' ? 1.0 : null, // 准确率设置最大值为1.0
            axisLine: {
              lineStyle: {
                color: newValue === 'accuracy' ? '#91cc75' : '#ee6666'
              }
            },
            axisLabel: {
              formatter: function(value) {
                // 保持与initCharts和updateCharts一致的格式
                return value.toFixed(3);
              }
            }
          },
          series: [{
            name: newValue === 'accuracy' ? '准确率' : '损失值',
            itemStyle: {
              color: newValue === 'accuracy' ? '#91cc75' : '#ee6666'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0, 
                y: 0, 
                x2: 0, 
                y2: 1,
                colorStops: [{
                  offset: 0,
                  color: newValue === 'accuracy' ? 'rgba(145, 204, 117, 0.5)' : 'rgba(238, 102, 102, 0.5)'
                }, {
                  offset: 1,
                  color: newValue === 'accuracy' ? 'rgba(145, 204, 117, 0.05)' : 'rgba(238, 102, 102, 0.05)'
                }]
              }
            }
          }]
        });
        
        // 重新计算Y轴范围并更新完整图表
        updateCharts();
      }
    });
    
    onMounted(() => {
      loadActiveTasks();
    });
    
    onBeforeUnmount(() => {
      console.log('监控组件即将卸载，清理资源...');
      
      // 停止刷新定时器
      stopRefreshTimer();
      
      // 清理图表实例
      if (charts.performanceChart) {
        try {
          charts.performanceChart.dispose();
          charts.performanceChart = null;
          console.log('性能图表实例已清理');
        } catch (error) {
          console.error('清理性能图表失败:', error);
        }
      }
      
      if (charts.contributionChart) {
        try {
          charts.contributionChart.dispose();
          charts.contributionChart = null;
          console.log('贡献图表实例已清理');
        } catch (error) {
          console.error('清理贡献图表失败:', error);
        }
      }
      
      // 清空当前任务和相关数据的引用
      currentTask.value = null;
      trainingLogs.value = [];
      activeTasks.value = [];
      
      console.log('所有资源已清理完毕');
    });
    
    // 计算活跃参与者百分比
    const calculateActivePercentage = () => {
      if (!currentTask.value || !currentTask.value.participants || currentTask.value.participants.length === 0) {
        return 0;
      }
      // 确保计算结果不超过100%
      return Math.min((activeParticipants.value / currentTask.value.participants.length) * 100, 100);
    };
    
    // 跳转到模型管理页面
    const goToModelManagement = () => {
      router.push({
        path: '/server/models',
        query: { taskId: currentTask.value?.id } // 可选：传递任务ID，方便在模型页面直接定位到相关模型
      });
    };
    
    // 添加创建模型的方法
    const createModelFromTraining = async (task) => {
      try {
        console.log('从训练任务创建模型，任务详情:', task);
        
        // 准备模型数据
        const modelData = {
          name: task.name,
          type: task.dataType || 'image_classification', // 根据任务数据类型设置
          version: '1.0.0',
          description: `从训练任务 "${task.name}" 自动创建的模型，任务ID: ${task.id}`,
          task_id: task.id, // 注意使用下划线格式以匹配后端API期望的字段名
          task_name: task.name,
          performance: task.accuracy ? parseFloat(task.accuracy) : (currentAccuracy.value ? parseFloat(currentAccuracy.value) / 100 : 0.9), // 使用当前显示的准确率
          status: 'draft', // 初始为草稿状态
          from_training: true, // 标记为从训练任务创建
          is_latest: true, // 标记为最新版本
          file_path: `./models/${task.id}_model.h5`, // 使用相对路径，确保后端能正确访问
          size: 1024 * 1024 * 10 // 估计大小，如10MB
        };
        
        // 把JSON格式的数字转换为JS数字类型
        if (typeof modelData.performance === 'string') {
          modelData.performance = parseFloat(modelData.performance);
        }
        if (typeof modelData.task_id === 'string') {
          modelData.task_id = parseInt(modelData.task_id, 10);
        }
        if (typeof modelData.size === 'string') {
          modelData.size = parseInt(modelData.size, 10);
        }
        
        console.log('调用API创建模型，提交数据:', JSON.stringify(modelData, null, 2));
        
        // 调用API创建模型
        try {
          const response = await ModelAPI.create(modelData);
          console.log('模型已成功创建，API响应:', response);
          addLog(`已为任务 ${task.name} 创建模型，模型状态为"草稿"`, 'success');
          
          // 立即显示确认提示
          ElMessage.success(`已为任务 "${task.name}" 创建模型，可在模型管理页面查看`);
        } catch (apiError) {
          console.error('API创建模型失败:', apiError);
          
          if (apiError.response) {
            console.error('API错误响应:', apiError.response.status, apiError.response.data);
          }
          
          throw new Error(`API创建模型失败: ${apiError.message}`);
        }
      } catch (error) {
        console.error('创建模型失败:', error);
        ElMessage.error('创建模型失败: ' + error.message);
        addLog(`为任务 ${task.name} 创建模型失败: ${error.message}`, 'error');
      }
    };
    
    // 在setup中添加路由监听
    watch(() => route.query, (newQuery) => {
      if (newQuery.taskId && (!currentTask.value || currentTask.value.id.toString() !== newQuery.taskId.toString())) {
        console.log('URL参数变化，重新加载任务:', newQuery.taskId);
        // 如果当前没有选择任务或选择的任务不是URL中的任务，重新加载
        loadActiveTasks();
      }
    }, { deep: true });
    
    // 估算剩余时间（当API不可用时使用）
    const estimateRemainingTime = (task) => {
      const totalRounds = task.totalRounds;
      const currentRound = task.currentRound;
      const remainingRounds = totalRounds - currentRound;
      
      // 假设每轮训练时间大约是3分钟
      const minutesPerRound = 3;
      const totalMinutes = remainingRounds * minutesPerRound;
      
      if (totalMinutes < 1) return "不到1分钟";
      if (totalMinutes < 60) return `${totalMinutes}分钟`;
      
      const hours = Math.floor(totalMinutes / 60);
      const minutes = totalMinutes % 60;
      return `${hours}小时${minutes > 0 ? ` ${minutes}分钟` : ''}`;
    };
    
    return {
      loading,
      isStopping,
      activeTasks,
      currentTask,
      trainingLogs,
      selectedMetric,
      currentRoundProgress,
      completedClients,
      currentAccuracy,
      currentLoss,
      activeParticipants,
      avgRoundTime,
      estimatedTimeRemaining,
      currentRoundTime,
      accuracyTrend,
      lossTrend,
      roundTimeTrend,
      performanceChart,
      contributionChart,
      calculateProgress,
      getProgressStatus,
      getParticipantStatusType,
      getParticipantStatusText,
      getContributionColor,
      getAvatarUrl,
      shortenAddress,
      formatDateTime,
      formatTime,
      selectTask,
      refreshData,
      stopTask,
      goBack,
      exportData,
      clearLogs,
      initCharts,
      updateCharts,
      estimateRemainingTime,
      formatDuration,
      calculateActivePercentage,
      goToModelManagement
    };
  }
};
</script>

<style scoped>
.training-monitor {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.page-actions {
  display: flex;
  gap: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selector-card {
  margin-top: 24px;
}

/* 监控面板样式 */
.monitor-panel {
  margin-top: 24px;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-info h2 {
  margin: 0 0 8px 0;
  font-size: 20px;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #606266;
  font-size: 14px;
}

.progress-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-top: 24px;
}

.progress-card {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.progress-title {
  font-weight: 500;
  color: #606266;
}

.progress-details {
  color: #909399;
  font-size: 14px;
  margin-top: 4px;
}

.time-display {
  font-size: 24px;
  font-weight: 600;
  color: #409EFF;
  margin: 8px 0;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-top: 24px;
}

.metric-card {
  padding: 20px;
  text-align: center;
}

.metric-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.metric-label {
  color: #909399;
  font-size: 14px;
  margin-bottom: 16px;
}

.trend-indicator {
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.trend-indicator.positive {
  color: #67C23A;
  background-color: rgba(103, 194, 58, 0.1);
}

.trend-indicator.negative {
  color: #F56C6C;
  background-color: rgba(245, 108, 108, 0.1);
}

.progress-bar {
  margin-top: 16px;
}

.charts-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.chart-container {
  width: 100%;
  height: 360px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
}

/* 移动设备适配 */
@media (max-width: 768px) {
  .charts-section {
    grid-template-columns: 1fr;
  }
  
  .chart-container {
    height: 300px;
  }
}

.participants-card,
.logs-card {
  margin-top: 24px;
}

.client-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.client-avatar {
  flex-shrink: 0;
}

.logs-container {
  height: 300px;
  overflow-y: auto;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 16px;
}

.log-entry {
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  gap: 12px;
}

.log-time {
  font-family: monospace;
  color: #909399;
}

.log-message {
  flex: 1;
}

.log-entry.info {
  border-left: 4px solid #909399;
}

.log-entry.success {
  border-left: 4px solid #67C23A;
}

.log-entry.warning {
  border-left: 4px solid #E6A23C;
}

.log-entry.error {
  border-left: 4px solid #F56C6C;
}

.empty-logs {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  font-style: italic;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
}

.chart-header span {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

/* 调整选择框样式 */
:deep(.el-select .el-input) {
  width: 120px;
}

:deep(.el-select .el-input__wrapper) {
  background-color: #f5f7fa;
  border-radius: 4px;
}

:deep(.el-select .el-input__inner) {
  font-size: 14px;
}

.active-participant-progress {
  margin-top: 16px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.active-participant-progress :deep(.el-progress) {
  width: 100%;
}

.active-participant-progress :deep(.el-progress-bar) {
  padding-right: 0;
}
</style>