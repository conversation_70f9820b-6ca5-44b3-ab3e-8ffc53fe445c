<template>
  <div class="login-container">
    <div class="login-card">
      <h2 class="title">用户登录</h2>
      
      <el-form 
        @submit.prevent="handleLogin" 
        :model="formData"
        :rules="rules"
        ref="loginForm"
        label-position="top"
      >
        <el-form-item label="用户名" prop="username">
          <el-input 
            v-model="formData.username"
            placeholder="请输入用户名"
            :prefix-icon="User"
          />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input 
            v-model="formData.password"
            type="password"
            placeholder="请输入密码"
            show-password
            :prefix-icon="Lock"
          />
        </el-form-item>

        <el-form-item label="选择角色" prop="role">
          <el-select 
            v-model="formData.role" 
            placeholder="请选择角色" 
            style="width: 100%"
          >
            <el-option label="客户端" value="client"></el-option>
            <el-option label="服务端" value="server"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button 
            type="primary" 
            native-type="submit"
            :loading="isLoggingIn"
            class="submit-btn"
            round
            style="width: 100%"
          >
            {{ isLoggingIn ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>

        <el-alert
          v-if="errorMessage"
          :title="errorMessage"
          type="error"
          show-icon
          :closable="false"
          style="margin-top: 1rem"
        />
      </el-form>

      <div class="register-link">
        还没有账号？
        <router-link to="/register">立即注册</router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import web3Service from '@/services/web3'
import { User, Lock } from '@element-plus/icons-vue'
import { keccak256 } from '@/utils/crypto'

const router = useRouter()
const store = useStore()

// 状态
const formData = ref({
  username: '',
  password: '',
  role: 'client'  // 默认选择客户端
})
const isLoggingIn = ref(false)
const errorMessage = ref('')
const walletAddress = ref('')

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度应为3-20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 3, message: '密码长度不能小于3个字符', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// 添加密码哈希生成函数
function generatePasswordHash(password) {
  return keccak256(password)
}

// 处理登录
async function handleLogin() {
  if (isLoggingIn.value) return
  
  try {
    isLoggingIn.value = true
    errorMessage.value = ''

    // 获取钱包地址
    walletAddress.value = await web3Service.connectWallet()
    console.log('Connected wallet address:', walletAddress.value)
    
    // 生成密码哈希值
    const passwordHash = web3Service.web3.utils.keccak256(
      web3Service.web3.utils.utf8ToHex(formData.value.password)
    )
    console.log('Login attempt:', {
      username: formData.value.username,
      passwordHash: passwordHash,
      address: walletAddress.value
    })

    // 调用智能合约验证登录
    const isValid = await web3Service.verifyLogin(
      walletAddress.value,
      formData.value.username,
      passwordHash
    )
    
    if (!isValid) {
      throw new Error('用户名或密码错误')
    }

    // 获取用户角色
    const accountInfo = await web3Service.getAccountInfo(walletAddress.value, 0)
    const userRole = accountInfo.role
    console.log('Account Info:', accountInfo)
    console.log('User role from contract (raw):', userRole)
    console.log('User role type:', typeof userRole)

    // 登录成功，更新状态
    await store.dispatch('user/login', {
      address: walletAddress.value,
      username: formData.value.username,
      role: formData.value.role  // 直接使用字符串角色
    })

    // 根据角色确定重定向路径
    let redirectPath = `/${formData.value.role}`;

    console.log('Redirecting to:', redirectPath);

    // 使用 await 确保路由跳转完成
    await router.push(redirectPath);
    
  } catch (error) {
    console.error('登录失败:', error)
    errorMessage.value = '登录失败: ' + (error.message || '未知错误')
  } finally {
    isLoggingIn.value = false
  }
}
</script>

<style scoped>
.login-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: auto;
}

.login-card {
  background: white;
  padding: 2rem 1.5rem;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 360px;
  margin: 20px;
}

.title {
  text-align: center;
  font-size: 1.5rem;
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.submit-btn {
  margin-top: 1rem;
  height: 40px;
  font-size: 1.1rem;
}

.register-link {
  margin-top: 1.5rem;
  text-align: center;
  color: #606266;
}

.register-link a {
  color: #409EFF;
  text-decoration: none;
  font-weight: 500;
  margin-left: 0.5rem;
}

.register-link a:hover {
  text-decoration: underline;
}

:deep(.el-form-item) {
  margin-bottom: 1rem;
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:deep(.el-button) {
  height: 36px;
}

@media (max-width: 480px) {
  .login-card {
    margin: 0 1rem;
  }
}
</style>
