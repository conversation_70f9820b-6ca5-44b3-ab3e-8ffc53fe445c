<template>
  <div class="chart-component">
    <div class="component-header">
      <h3>本地训练进度</h3>
    </div>
    
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
    </div>
    
    <div v-else-if="!history || history.length === 0" class="empty-chart">
      <i class="fas fa-chart-line"></i>
      <p>暂无训练数据</p>
    </div>
    
    <div v-else class="chart-container" ref="chartContainer"></div>
  </div>
</template>

<script>
import { ref, onMounted, watch, nextTick } from 'vue';
import * as echarts from 'echarts/core';
import { LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  ToolboxComponent,
  DataZoomComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必要的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  LineChart,
  CanvasRenderer,
  ToolboxComponent,
  DataZoomComponent
]);

export default {
  name: 'LocalTrainingChart',
  props: {
    history: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const chartContainer = ref(null);
    const chart = ref(null);
    
    // 初始化图表
    const initChart = () => {
      if (chartContainer.value) {
        // 如果图表已存在，销毁重建
        if (chart.value) {
          chart.value.dispose();
        }
        
        // 创建新图表
        chart.value = echarts.init(chartContainer.value);
        updateChart();
      }
    };
    
    // 更新图表数据
    const updateChart = () => {
      if (!chart.value || !props.history || props.history.length === 0) return;
      
      // 过滤掉轮次为0的数据点
      const filteredHistory = props.history.filter(item => {
        const round = typeof item.round === 'number' ? item.round : parseInt(item.round);
        return round >= 1; // 只保留轮次>=1的数据
      });
      
      // 如果过滤后没有数据，显示空图表
      if (filteredHistory.length === 0) {
        const emptyOption = {
          title: {
            text: '本地训练进度（暂无数据）',
            left: 'center',
            top: 0,
            textStyle: {
              fontSize: 16,
              fontWeight: 'normal'
            }
          },
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: [],
            name: '轮次'
          },
          yAxis: [
            {
              type: 'value',
              name: '损失值'
            },
            {
              type: 'value',
              name: '准确率'
            }
          ],
          series: [
            {
              name: '损失值',
              type: 'line',
              data: []
            },
            {
              name: '准确率',
              type: 'line',
              data: []
            }
          ]
        };
        chart.value.setOption(emptyOption);
        return;
      }
      
      const rounds = filteredHistory.map(item => item.round);
      const lossValues = filteredHistory.map(item => item.loss);
      const accuracyValues = filteredHistory.map(item => item.accuracy);
      
      const option = {
        title: {
          text: '本地训练进度',
          left: 'center',
          top: 0,
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['损失值', '准确率'],
          bottom: '0%'
        },
        toolbox: {
          feature: {
            saveAsImage: { title: '保存为图片' }
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '60px',
          top: '60px',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: rounds,
          name: '轮次',
          nameLocation: 'middle',
          nameGap: 30
        },
        yAxis: [
          {
            type: 'value',
            name: '损失值',
            position: 'left',
            axisLine: {
              show: true,
              lineStyle: {
                color: '#5470C6'
              }
            },
            axisLabel: {
              formatter: '{value}'
            }
          },
          {
            type: 'value',
            name: '准确率',
            position: 'right',
            min: 0,
            max: 1,
            axisLine: {
              show: true,
              lineStyle: {
                color: '#91CC75'
              }
            },
            axisLabel: {
              formatter: '{value}'
            }
          }
        ],
        series: [
          {
            name: '损失值',
            type: 'line',
            yAxisIndex: 0,
            data: lossValues,
            smooth: true,
            lineStyle: {
              width: 2
            },
            itemStyle: {
              color: '#5470C6'
            },
            symbol: 'circle',
            symbolSize: 5
          },
          {
            name: '准确率',
            type: 'line',
            yAxisIndex: 1,
            data: accuracyValues,
            smooth: true,
            lineStyle: {
              width: 2
            },
            itemStyle: {
              color: '#91CC75'
            },
            symbol: 'circle',
            symbolSize: 5
          }
        ],
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          },
          {
            start: 0,
            end: 100
          }
        ]
      };
      
      chart.value.setOption(option);
    };
    
    // 处理窗口大小变化
    const handleResize = () => {
      if (chart.value) {
        chart.value.resize();
      }
    };
    
    // 监听历史数据变化
    watch(() => props.history, async () => {
      if (!props.loading) {
        await nextTick();
        updateChart();
      }
    }, { deep: true });
    
    // 监听加载状态变化
    watch(() => props.loading, async (newVal) => {
      if (!newVal) {
        await nextTick();
        updateChart();
      }
    });
    
    onMounted(async () => {
      window.addEventListener('resize', handleResize);
      await nextTick();
      initChart();
    });
    
    // 组件卸载时清理
    const onUnmounted = () => {
      window.removeEventListener('resize', handleResize);
      if (chart.value) {
        chart.value.dispose();
        chart.value = null;
      }
    };
    
    return {
      chartContainer,
      initChart,
      updateChart,
      handleResize,
      onUnmounted
    };
  }
};
</script>

<style scoped>
.chart-component {
  background: white;
  border-radius: 16px;
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.component-header {
  margin-bottom: 16px;
}

.component-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
  font-weight: 600;
}

.chart-container {
  flex-grow: 1;
  height: 300px;
  width: 100%;
}

.empty-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #ccc;
}

.empty-chart i {
  font-size: 64px;
  margin-bottom: 16px;
}

.empty-chart p {
  font-size: 16px;
}

.loading-container {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1976D2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 