2025-08-27 09:29:10,909 - __main__ - INFO - Initializing Web3 and contract...
2025-08-27 09:29:10,936 - __main__ - INFO - Using server account: 0x201d1Fa19f8f7C246007340d9ea87A73005dCF10
2025-08-27 09:29:10,945 - __main__ - INFO - Web3 and contract initialized successfully
2025-08-27 09:29:10,945 - __main__ - INFO - ��������ѵ��״̬...
2025-08-27 09:29:11,007 - __main__ - INFO - Using server account: 0x201d1Fa19f8f7C246007340d9ea87A73005dCF10
2025-08-27 09:29:11,071 - __main__ - INFO - ������ѵ��״̬���óɹ�
2025-08-27 09:29:11,071 - __main__ - INFO - ѵ��״̬��ȫ���óɹ�
2025-08-27 09:29:11,071 - __main__ - INFO - ������״̬�ѳ�ʼ��: is_active=False, is_training=False
2025-08-27 09:29:11,071 - __main__ - INFO - Starting Flask server on port 5000...
 * Serving Flask app 'server'
 * Debug mode: off
2025-08-27 09:29:19,798 - __main__ - INFO - �����ȡ������ѵ��״̬
2025-08-27 09:29:19,798 - __main__ - INFO - ������״̬: ѵ����=False, ��ǰ�ִ�=0, ���ִ�=0
2025-08-27 09:29:24,970 - __main__ - INFO - �����ȡ������ѵ��״̬
2025-08-27 09:29:24,970 - __main__ - INFO - ������״̬: ѵ����=False, ��ǰ�ִ�=0, ���ִ�=0
2025-08-27 09:29:24,970 - __main__ - INFO - ������״̬: ѵ����=False, ��ǰ�ִ�=0, ���ִ�=0
