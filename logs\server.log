2025-08-27 09:46:24,187 - __main__ - INFO - Initializing Web3 and contract...
2025-08-27 09:46:24,210 - __main__ - INFO - Using server account: 0x201d1Fa19f8f7C246007340d9ea87A73005dCF10
2025-08-27 09:46:24,219 - __main__ - INFO - Web3 and contract initialized successfully
2025-08-27 09:46:24,219 - __main__ - INFO - ��������ѵ��״̬...
2025-08-27 09:46:24,279 - __main__ - INFO - Using server account: 0x201d1Fa19f8f7C246007340d9ea87A73005dCF10
2025-08-27 09:46:24,345 - __main__ - INFO - ������ѵ��״̬���óɹ�
2025-08-27 09:46:24,346 - __main__ - INFO - ѵ��״̬��ȫ���óɹ�
2025-08-27 09:46:24,346 - __main__ - INFO - ������״̬�ѳ�ʼ��: is_active=False, is_training=False
2025-08-27 09:46:24,346 - __main__ - INFO - Starting Flask server on port 5000...
 * Serving Flask app 'server'
 * Debug mode: off
2025-08-27 09:46:32,387 - __main__ - INFO - �����ȡ������ѵ��״̬
2025-08-27 09:46:32,387 - __main__ - INFO - ������״̬: ѵ����=False, ��ǰ�ִ�=0, ���ִ�=0
2025-08-27 09:46:38,011 - __main__ - INFO - �����ȡ������ѵ��״̬
2025-08-27 09:46:38,011 - __main__ - INFO - ������״̬: ѵ����=False, ��ǰ�ִ�=0, ���ִ�=0
INFO - 请求获取服务器训练状态
2025-08-27 09:46:38,011 - __main__ - INFO - 服务器状态: 训练中=False, 当前轮次=0, 总轮次=0
