<template>
  <div class="register-container">
    <div class="register-card">
      <h2 class="title">用户注册</h2>
      <el-form
        ref="registerForm"
        :model="formData"
        :rules="rules"
        @submit.prevent="handleRegister"
        label-position="top"
      >
        <el-form-item label="钱包地址" prop="walletAddress">
          <el-input
            v-model="formData.walletAddress"
            placeholder="请输入您的钱包地址"
            :prefix-icon="Wallet"
            clearable
          >
            <template #append>
              <el-button @click="connectWallet" :disabled="isWalletConnected">
                {{ isWalletConnected ? '已连接' : '连接钱包' }}
              </el-button>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="formData.username"
            placeholder="请输入用户名"
            :prefix-icon="User"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item label="角色" prop="role">
          <el-select 
            v-model="formData.role" 
            placeholder="请选择角色"
            style="width: 100%"
          >
            <el-option label="客户端" value="client"></el-option>
            <el-option label="服务端" value="server"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
            v-model="formData.password"
            type="password"
            placeholder="请输入密码"
            :prefix-icon="Lock"
            show-password
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            native-type="submit"
            :loading="isRegistering"
            :disabled="!isFormValid"
            round
            style="width: 100%"
          >
            {{ isRegistering ? '注册中...' : '注册' }}
          </el-button>
        </el-form-item>

        <el-alert
          v-if="errorMessage"
          :title="errorMessage"
          type="error"
          show-icon
          :closable="false"
          style="margin-top: 1rem"
        />
      </el-form>

      <div class="login-link">
        已有账号？
        <router-link to="/login">立即登录</router-link>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { User, Lock, Wallet } from '@element-plus/icons-vue'
import Web3Service from '@/services/web3';
import { generateKeyPair } from '@/utils/crypto';

export default {
  name: 'Register',
  setup() {
    const store = useStore();
    const router = useRouter();

    // 表单数据
    const formData = reactive({
      username: '',
      password: '',
      role: 'client',  // 默认选择客户端
      walletAddress: '',
    });

    // 状态
    const isRegistering = ref(false);
    const errorMessage = ref('');
    const errors = ref({
      username: '',
    });

    // 验证规则
    const rules = reactive({
      walletAddress: [
        { required: true, message: '请输入钱包地址', trigger: 'blur' },
        { pattern: /^0x[a-fA-F0-9]{40}$/, message: '无效的钱包地址', trigger: 'blur' },
      ],
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度应为3-20个字符', trigger: 'blur' },
        { pattern: /^[a-zA-Z0-9]+$/, message: '用户名只能包含字母和数字', trigger: 'blur' },
      ],
      role: [{ required: true, message: '请选择角色', trigger: 'change' }],
      password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
    });

    // 钱包连接状态
    const isWalletConnected = ref(false);

    // 连接钱包
    async function connectWallet() {
      try {
        const account = await Web3Service.connectWallet();
        if (account) {
          formData.walletAddress = account;
          isWalletConnected.value = true;
        }
      } catch (error) {
        console.error('连接钱包失败:', error);
        errorMessage.value = '连接钱包失败: ' + (error.message || '未知错误');
      }
    }

    // 表单验证
    const isFormValid = computed(() => {
      return (
        formData.username.length >= 3 &&
        formData.username.length <= 20 &&
        /^[a-zA-Z0-9]+$/.test(formData.username)
      );
    });

    // 处理注册
    async function handleRegister() {
      if (isRegistering.value || !isFormValid.value) return;

      try {
        isRegistering.value = true;
        errorMessage.value = '';

        // 确保钱包已连接
        if (!formData.walletAddress) {
          await connectWallet();
        }

        // 生成密码哈希值
        const passwordHash = Web3Service.web3.utils.keccak256(
          Web3Service.web3.utils.utf8ToHex(formData.password)
        );
        
        console.log('Registration details:', {
          username: formData.username,
          passwordHash: passwordHash,
          role: formData.role
        });

        // 检查用户是否已注册
        const isRegistered = await Web3Service.isUserRegistered(formData.walletAddress);
        if (isRegistered) {
          throw new Error('该钱包地址已经注册');
        }

        // 调用智能合约注册用户
        await Web3Service.registerUser(
          formData.username,
          passwordHash,
          Web3Service.convertRoleToNumber(formData.role)  // 使用辅助方法转换角色
        );

        // 注册成功后直接跳转到登录页
        router.push('/login');
      } catch (error) {
        console.error('注册失败:', error);
        if (error.message.includes('User already registered')) {
          errorMessage.value = '该钱包地址已经注册';
        } else if (error.message.includes('Username already exists')) {
          errorMessage.value = '用户名已被注册';
        } else {
          errorMessage.value = '注册失败: ' + (error.message || '未知错误');
        }
      } finally {
        isRegistering.value = false;
      }
    }

    return {
      formData,
      isRegistering,
      errorMessage,
      errors,
      isFormValid,
      rules,
      connectWallet,
      handleRegister,
      User,
      Lock,
      Wallet,
    };
  },
};
</script>

<style scoped>
.register-container {
  position: fixed;  /* 改为固定定位 */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: auto;  /* 允许内容滚动 */
}

.register-card {
  background: white;
  padding: 2rem 1.5rem;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 360px;
  margin: 20px;  /* 添加外边距防止贴边 */
}

.title {
  text-align: center;
  font-size: 1.5rem;  /* 稍微减小标题大小 */
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.login-link {
  margin-top: 1.5rem;
  text-align: center;
  color: #606266;
}

.login-link a {
  color: #409EFF;
  text-decoration: none;
  font-weight: 500;
  margin-left: 0.5rem;
}

.login-link a:hover {
  text-decoration: underline;
}

:deep(.el-form-item) {
  margin-bottom: 1rem;  /* 减小表单项之间的间距 */
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

:deep(.el-button) {
  height: 36px;  /* 稍微调整按钮高度 */
}

@media (max-width: 480px) {
  .register-card {
    margin: 0 1rem;
  }
}
</style>
