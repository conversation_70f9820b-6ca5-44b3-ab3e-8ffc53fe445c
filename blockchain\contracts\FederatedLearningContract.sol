// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract FederatedLearningContract {
    // 用户角色枚举
    enum Role { Client, Server }
    
    // 用户账户结构
    struct Account {
        string username;
        string passwordHash;
        Role role;
        bool isActive;
    }
    
    // 训练结果结构
    struct TrainingResult {
        uint256 round;
        string loss;
        string accuracy;
        address[] participants;
        uint256 timestamp;
    }
    
    // 参与者贡献结构
    struct ParticipantContribution {
        uint256 totalRounds;
        string averageAccuracy;
        uint256 totalTime;
        uint256 rank;
    }
    
    // 最终模型结构
    struct FinalModel {
        string modelHash;
        string accuracy;
        address[] contributors;
        uint256 timestamp;
    }
    
    // 训练状态
    struct TrainingStatus {
        bool isActive;
        uint256 currentRound;
        uint256 totalRounds;
        uint256 minParticipants;
        uint256 timeout;
        uint256 startTime;
        address[] activeParticipants;
        uint256 totalParticipants;
    }
    
    // 状态变量
    mapping(address => mapping(uint256 => Account)) private accounts;
    mapping(address => uint256) private accountCounts;
    mapping(uint256 => TrainingResult) private trainingResults;
    mapping(address => ParticipantContribution) private participantContributions;
    mapping(address => mapping(uint256 => bool)) private roundParticipation;
    mapping(uint256 => uint256) private modelVersions;
    
    TrainingStatus private trainingStatus;
    FinalModel private finalModel;
    
    address private owner;
    uint256 private resultCount;
    address public serverAddress; // 服务器地址
    
    // 事件
    event UserRegistered(address indexed userAddress, string username, uint8 role);
    event TrainingStarted(uint256 totalRounds, uint256 minParticipants, uint256 timeout);
    event TrainingEnded(uint256 totalRounds, uint256 timestamp);
    event TrainingReset();
    event ParticipantJoined(address indexed participant);
    event ParticipantLeft(address indexed participant);
    event ResultRecorded(uint256 round, string loss, string accuracy, address[] participants);
    event FinalModelStored(string modelHash, string accuracy, uint256 timestamp);
    event ServerAddressSet(address indexed serverAddress);
    
    // 修饰符
    modifier onlyOwner() {
        require(msg.sender == owner, "Only owner can call this function");
        _;
    }
    
    modifier onlyServer() {
        // 如果serverAddress已设置，则使用它来验证
        if (serverAddress != address(0)) {
            require(msg.sender == serverAddress, "Only server can call this function");
        } else {
            // 否则使用旧的验证方式
            require(
                accountCounts[msg.sender] > 0 && 
                accounts[msg.sender][0].role == Role.Server, 
                "Only server can call this function"
            );
        }
        _;
    }
    
    modifier onlyRegistered() {
        require(accountCounts[msg.sender] > 0, "User not registered");
        _;
    }
    
    modifier trainingActive() {
        require(trainingStatus.isActive, "Training is not active");
        _;
    }
    
    modifier trainingNotActive() {
        require(!trainingStatus.isActive, "Training is already active");
        _;
    }
    
    // 构造函数
    constructor() {
        owner = msg.sender;
        resultCount = 0;
        serverAddress = address(0); // 初始化为零地址
        
        // 初始化训练状态
        trainingStatus.isActive = false;
        trainingStatus.currentRound = 0;
        trainingStatus.totalRounds = 0;
        trainingStatus.minParticipants = 0;
        trainingStatus.timeout = 0;
        trainingStatus.startTime = 0;
        trainingStatus.totalParticipants = 0;
    }
    
    // 设置服务器地址
    function setServerAddress(address _serverAddress) public onlyOwner {
        require(_serverAddress != address(0), "Server address cannot be zero");
        serverAddress = _serverAddress;
        emit ServerAddressSet(_serverAddress);
    }
    
    // 用户注册
    function registerUser(string memory username, string memory passwordHash, uint8 role) public {
        require(role <= 1, "Invalid role");
        require(bytes(username).length > 0, "Username cannot be empty");
        require(bytes(passwordHash).length > 0, "Password hash cannot be empty");
        
        uint256 accountIndex = accountCounts[msg.sender];
        accounts[msg.sender][accountIndex] = Account({
            username: username,
            passwordHash: passwordHash,
            role: Role(role),
            isActive: true
        });
        
        accountCounts[msg.sender]++;
        
        emit UserRegistered(msg.sender, username, role);
    }
    
    // 验证登录
    function verifyLogin(address userAddress, string memory username, string memory passwordHash) public view returns (bool) {
        if (accountCounts[userAddress] == 0) {
            return false;
        }
        
        Account memory account = accounts[userAddress][0];
        return (
            keccak256(bytes(account.username)) == keccak256(bytes(username)) &&
            keccak256(bytes(account.passwordHash)) == keccak256(bytes(passwordHash))
        );
    }
    
    // 获取账户信息
    function getAccountInfo(address userAddress, uint256 index) public view returns (
        string memory username,
        Role role,
        bool isActive
    ) {
        require(index < accountCounts[userAddress], "Account index out of bounds");
        
        Account memory account = accounts[userAddress][index];
        return (
            account.username,
            account.role,
            account.isActive
        );
    }
    
    // 获取账户数量
    function getAccountCount(address userAddress) public view returns (uint256) {
        return accountCounts[userAddress];
    }
    
    // 开始训练
    function startTraining(uint256 totalRounds, uint256 minParticipants, uint256 timeout) public onlyServer trainingNotActive {
        require(totalRounds > 0, "Total rounds must be greater than 0");
        require(minParticipants > 0, "Min participants must be greater than 0");
        
        trainingStatus.isActive = true;
        trainingStatus.currentRound = 1;
        trainingStatus.totalRounds = totalRounds;
        trainingStatus.minParticipants = minParticipants;
        trainingStatus.timeout = timeout;
        trainingStatus.startTime = block.timestamp;
        delete trainingStatus.activeParticipants;
        
        emit TrainingStarted(totalRounds, minParticipants, timeout);
    }
    
    // 停止训练
    function stopTraining() public onlyServer trainingActive {
        trainingStatus.isActive = false;
        
        emit TrainingEnded(trainingStatus.currentRound, block.timestamp);
    }
    
    // 重置训练
    function resetTraining() public onlyServer {
        trainingStatus.isActive = false;
        trainingStatus.currentRound = 0;
        trainingStatus.totalRounds = 0;
        trainingStatus.minParticipants = 0;
        trainingStatus.timeout = 0;
        trainingStatus.startTime = 0;
        delete trainingStatus.activeParticipants;
        trainingStatus.totalParticipants = 0;
        
        emit TrainingReset();
    }
    
    // 加入训练
    function joinTraining() public onlyRegistered trainingActive {
        bool alreadyJoined = false;
        for (uint i = 0; i < trainingStatus.activeParticipants.length; i++) {
            if (trainingStatus.activeParticipants[i] == msg.sender) {
                alreadyJoined = true;
                break;
            }
        }
        
        if (!alreadyJoined) {
            trainingStatus.activeParticipants.push(msg.sender);
            trainingStatus.totalParticipants++;
            
            // 初始化参与者贡献
            if (participantContributions[msg.sender].totalRounds == 0) {
                participantContributions[msg.sender] = ParticipantContribution({
                    totalRounds: 0,
                    averageAccuracy: "0",
                    totalTime: 0,
                    rank: 0
                });
            }
            
            emit ParticipantJoined(msg.sender);
        }
    }
    
    // 退出训练
    function leaveTraining() public onlyRegistered trainingActive {
        for (uint i = 0; i < trainingStatus.activeParticipants.length; i++) {
            if (trainingStatus.activeParticipants[i] == msg.sender) {
                // 移除参与者
                trainingStatus.activeParticipants[i] = trainingStatus.activeParticipants[trainingStatus.activeParticipants.length - 1];
                trainingStatus.activeParticipants.pop();
                
                emit ParticipantLeft(msg.sender);
                break;
            }
        }
    }
    
    // 提交训练结果
    function submitTrainingResult(uint256 round, string memory loss, string memory accuracy) public onlyRegistered trainingActive {
        require(round == trainingStatus.currentRound, "Invalid round number");
        require(!roundParticipation[msg.sender][round], "Already submitted result for this round");
        
        // 标记参与
        roundParticipation[msg.sender][round] = true;
        
        // 更新参与者贡献
        ParticipantContribution storage contribution = participantContributions[msg.sender];
        contribution.totalRounds++;
        
        // 更新平均准确率 (简单实现，实际应该使用更复杂的计算)
        // 这里假设accuracy是一个字符串形式的浮点数，如"0.95"
        contribution.averageAccuracy = accuracy;
        
        // 更新总时间
        contribution.totalTime += trainingStatus.timeout;
        
        // 检查是否所有活跃参与者都提交了结果
        bool allSubmitted = true;
        for (uint i = 0; i < trainingStatus.activeParticipants.length; i++) {
            if (!roundParticipation[trainingStatus.activeParticipants[i]][round]) {
                allSubmitted = false;
                break;
            }
        }
        
        // 如果所有参与者都提交了结果或者超时，进入下一轮
        if (allSubmitted || (block.timestamp - trainingStatus.startTime) > trainingStatus.timeout) {
            trainingStatus.currentRound++;
            
            // 如果达到总轮次，结束训练
            if (trainingStatus.currentRound > trainingStatus.totalRounds) {
                trainingStatus.isActive = false;
                emit TrainingEnded(trainingStatus.totalRounds, block.timestamp);
            }
        }
    }
    
    // 记录训练结果 (由服务器调用)
    function recordTrainingResult(uint256 round, string memory loss, string memory accuracy, address[] memory participants) public onlyServer {
        require(round > 0, "Round must be greater than 0");
        
        trainingResults[resultCount] = TrainingResult({
            round: round,
            loss: loss,
            accuracy: accuracy,
            participants: participants,
            timestamp: block.timestamp
        });
        
        resultCount++;
        
        // 更新参与者排名
        updateParticipantRanks();
        
        emit ResultRecorded(round, loss, accuracy, participants);
    }
    
    // 存储最终模型
    function storeFinalModel(string memory modelHash, string memory accuracy, address[] memory contributors) public onlyServer {
        finalModel = FinalModel({
            modelHash: modelHash,
            accuracy: accuracy,
            contributors: contributors,
            timestamp: block.timestamp
        });
        
        modelVersions[trainingStatus.totalRounds] = resultCount;
        
        emit FinalModelStored(modelHash, accuracy, block.timestamp);
    }
    
    // 获取训练状态
    function getTrainingStatus() public view returns (
        bool isActive,
        uint256 currentRound,
        uint256 totalRounds,
        uint256 minParticipants,
        uint256 timeout,
        uint256 startTime,
        address[] memory activeParticipants,
        uint256 totalParticipants
    ) {
        return (
            trainingStatus.isActive,
            trainingStatus.currentRound,
            trainingStatus.totalRounds,
            trainingStatus.minParticipants,
            trainingStatus.timeout,
            trainingStatus.startTime,
            trainingStatus.activeParticipants,
            trainingStatus.totalParticipants
        );
    }
    
    // 获取当前轮次
    function getCurrentRound() public view returns (uint256) {
        return trainingStatus.currentRound;
    }
    
    // 检查训练是否活跃
    function isTrainingActive() public view returns (bool) {
        return trainingStatus.isActive;
    }
    
    // 获取训练结果
    function getTrainingResult(uint256 index) public view returns (
        uint256 round,
        string memory loss,
        string memory accuracy,
        address[] memory participants,
        uint256 timestamp
    ) {
        require(index < resultCount, "Result index out of bounds");
        
        TrainingResult memory result = trainingResults[index];
        return (
            result.round,
            result.loss,
            result.accuracy,
            result.participants,
            result.timestamp
        );
    }
    
    // 获取结果数量
    function getResultCount() public view returns (uint256) {
        return resultCount;
    }
    
    // 获取参与者贡献
    function getParticipantContribution(address participant) public view returns (
        uint256 totalRounds,
        string memory averageAccuracy,
        uint256 totalTime,
        uint256 rank
    ) {
        ParticipantContribution memory contribution = participantContributions[participant];
        return (
            contribution.totalRounds,
            contribution.averageAccuracy,
            contribution.totalTime,
            contribution.rank
        );
    }
    
    // 获取最终模型
    function getFinalModel() public view returns (
        string memory modelHash,
        string memory accuracy,
        address[] memory contributors,
        uint256 timestamp
    ) {
        return (
            finalModel.modelHash,
            finalModel.accuracy,
            finalModel.contributors,
            finalModel.timestamp
        );
    }
    
    // 更新参与者排名 (内部函数)
    function updateParticipantRanks() internal {
        // 简单实现，实际应该使用更复杂的排序算法
        // 这里只是按照参与轮次排序
        address[] memory participants = new address[](trainingStatus.totalParticipants);
        uint256[] memory scores = new uint256[](trainingStatus.totalParticipants);
        
        // 收集所有参与者
        uint256 count = 0;
        for (uint i = 0; i < trainingStatus.activeParticipants.length; i++) {
            address participant = trainingStatus.activeParticipants[i];
            participants[count] = participant;
            scores[count] = participantContributions[participant].totalRounds;
            count++;
        }
        
        // 简单的冒泡排序
        for (uint i = 0; i < count; i++) {
            for (uint j = i + 1; j < count; j++) {
                if (scores[i] < scores[j]) {
                    // 交换分数
                    uint256 tempScore = scores[i];
                    scores[i] = scores[j];
                    scores[j] = tempScore;
                    
                    // 交换地址
                    address tempAddr = participants[i];
                    participants[i] = participants[j];
                    participants[j] = tempAddr;
                }
            }
        }
        
        // 更新排名
        for (uint i = 0; i < count; i++) {
            participantContributions[participants[i]].rank = i + 1;
        }
    }
}
