/**
 * 交易数据模型
 */
const { query } = require('../db/init');

class Transaction {
  /**
   * 获取所有交易
   * @returns {Promise<Array>} 交易列表
   */
  static async getAll() {
    try {
      return await query('SELECT * FROM transactions ORDER BY created_at DESC');
    } catch (error) {
      console.error('获取所有交易失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取交易
   * @param {number} id 交易ID
   * @returns {Promise<Object>} 交易信息
   */
  static async getById(id) {
    try {
      const transactions = await query('SELECT * FROM transactions WHERE id = ?', [id]);
      return transactions.length > 0 ? transactions[0] : null;
    } catch (error) {
      console.error(`获取交易(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 获取用户交易
   * @param {string} userId 用户ID
   * @returns {Promise<Array>} 交易列表
   */
  static async getByUserId(userId) {
    try {
      return await query(
        'SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC',
        [userId]
      );
    } catch (error) {
      console.error(`获取用户(ID: ${userId})交易失败:`, error);
      throw error;
    }
  }

  /**
   * 获取任务交易
   * @param {number} taskId 任务ID
   * @returns {Promise<Array>} 交易列表
   */
  static async getByTaskId(taskId) {
    try {
      return await query(
        'SELECT * FROM transactions WHERE task_id = ? ORDER BY created_at DESC',
        [taskId]
      );
    } catch (error) {
      console.error(`获取任务(ID: ${taskId})交易失败:`, error);
      throw error;
    }
  }

  /**
   * 创建交易
   * @param {Object} transactionData 交易数据
   * @returns {Promise<Object>} 创建的交易
   */
  static async create(transactionData) {
    try {
      const {
        user_id, task_id, type, amount, 
        status, currency, payment_method,
        description, reference_id, client_address,
        blockchain_tx_hash, wallet_type // 新增区块链相关字段
      } = transactionData;

      console.log('开始创建交易，接收到的数据:', JSON.stringify(transactionData, null, 2));
      
      // 验证必要字段
      if (!task_id) {
        throw new Error('交易必须关联任务ID');
      }
      
      if (amount === undefined || amount === null) {
        throw new Error('交易金额不能为空');
      }
      
      // 构建SQL查询和参数
      let fields = [
        'user_id', 'task_id', 'type', 'amount', 'status', 
        'currency', 'payment_method', 'description', 
        'reference_id', 'client_address', 'created_at'
      ];
      
      let values = [
        user_id, 
        task_id, 
        type, 
        amount, 
        status || 'pending', 
        currency || 'USD', 
        payment_method, 
        description, 
        reference_id, 
        client_address,
        new Date()
      ];
      
      // 如果有区块链相关字段，添加到SQL中
      if (blockchain_tx_hash) {
        fields.push('blockchain_tx_hash');
        values.push(blockchain_tx_hash);
      }
      
      if (wallet_type) {
        fields.push('wallet_type');
        values.push(wallet_type);
      }
      
      // 创建交易记录
      const fieldsString = fields.join(', ');
      const placeholders = values.map(() => '?').join(', ');
      
      console.log(`执行SQL: INSERT INTO transactions (${fieldsString}) VALUES (${placeholders})`);
      
      const result = await query(
        `INSERT INTO transactions (${fieldsString}) VALUES (${placeholders})`,
        values
      );

      if (!result || !result.insertId) {
        throw new Error('创建交易失败，未返回插入ID');
      }
      
      console.log(`交易创建成功，ID: ${result.insertId}`);
      
      // 获取新创建的交易
      const newTransaction = await this.getById(result.insertId);
      
      if (!newTransaction) {
        throw new Error(`无法查询新创建的交易 (ID: ${result.insertId})`);
      }
      
      return newTransaction;
    } catch (error) {
      console.error('创建交易失败:', error);
      console.error('错误详情:', error.stack);
      throw new Error(`创建交易失败: ${error.message}`);
    }
  }

  /**
   * 更新交易状态
   * @param {number} id 交易ID
   * @param {string} status 新状态
   * @returns {Promise<boolean>} 更新结果
   */
  static async updateStatus(id, status) {
    try {
      const result = await query(
        'UPDATE transactions SET status = ?, updated_at = NOW() WHERE id = ?',
        [status, id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`更新交易(ID: ${id})状态失败:`, error);
      throw error;
    }
  }

  /**
   * 创建任务首期付款交易
   * @param {Object} taskData 任务数据
   * @param {string} userId 用户ID
   * @returns {Promise<Object>} 创建的交易
   */
  static async createFirstPayment(taskData, userId) {
    try {
      // 计算首期付款金额
      const firstPaymentAmount = taskData.reward * (taskData.first_payment_rate / 100);
      
      // 计算平台服务费
      const platformFeeAmount = firstPaymentAmount * (taskData.platform_fee_rate / 100);
      
      // 创建首期付款交易
      const transactionData = {
        user_id: userId,
        task_id: taskData.id,
        type: 'task_first_payment',
        amount: -firstPaymentAmount, // 负数表示支出
        status: 'completed',
        currency: taskData.payment_currency || 'USD',
        payment_method: taskData.payment_method,
        description: `任务"${taskData.name}"的首期付款 (${taskData.first_payment_rate}%)`,
        reference_id: null,
        client_address: null
      };
      
      const transaction = await this.create(transactionData);
      
      // 创建平台服务费交易
      const platformFeeData = {
        user_id: userId,
        task_id: taskData.id,
        type: 'platform_fee',
        amount: -platformFeeAmount, // 负数表示支出
        status: 'completed',
        currency: taskData.payment_currency || 'USD',
        payment_method: taskData.payment_method,
        description: `任务"${taskData.name}"的平台服务费 (${taskData.platform_fee_rate}%)`,
        reference_id: transaction.id,
        client_address: null
      };
      
      await this.create(platformFeeData);
      
      return transaction;
    } catch (error) {
      console.error('创建首期付款交易失败:', error);
      throw error;
    }
  }

  /**
   * 创建任务二期付款交易
   * @param {Object} taskData 任务数据
   * @param {string} userId 用户ID
   * @returns {Promise<Object>} 创建的交易
   */
  static async createSecondPayment(taskData, userId) {
    try {
      // 计算二期付款金额
      const secondPaymentAmount = taskData.reward * ((100 - taskData.first_payment_rate) / 100);
      
      // 计算平台服务费
      const platformFeeAmount = secondPaymentAmount * (taskData.platform_fee_rate / 100);
      
      // 创建二期付款交易
      const transactionData = {
        user_id: userId,
        task_id: taskData.id,
        type: 'task_second_payment',
        amount: -secondPaymentAmount, // 负数表示支出
        status: 'completed',
        currency: taskData.payment_currency || 'USD',
        payment_method: taskData.payment_method,
        description: `任务"${taskData.name}"的二期付款 (${100 - taskData.first_payment_rate}%)`,
        reference_id: null,
        client_address: null
      };
      
      const transaction = await this.create(transactionData);
      
      // 创建平台服务费交易
      const platformFeeData = {
        user_id: userId,
        task_id: taskData.id,
        type: 'platform_fee',
        amount: -platformFeeAmount, // 负数表示支出
        status: 'completed',
        currency: taskData.payment_currency || 'USD',
        payment_method: taskData.payment_method,
        description: `任务"${taskData.name}"的平台服务费 (${taskData.platform_fee_rate}%)`,
        reference_id: transaction.id,
        client_address: null
      };
      
      await this.create(platformFeeData);
      
      return transaction;
    } catch (error) {
      console.error('创建二期付款交易失败:', error);
      throw error;
    }
  }

  /**
   * 创建参与者奖励交易
   * @param {Object} participantData 参与者数据
   * @param {Object} taskData 任务数据
   * @param {number} contributionPercentage 贡献百分比
   * @returns {Promise<Object>} 创建的交易
   */
  static async createParticipantReward(participantData, taskData, contributionPercentage) {
    try {
      // 计算总金额 (减去平台费用的90%)
      const totalRewardAmount = taskData.reward * 0.9;
      
      // 计算参与者奖励金额
      const rewardAmount = totalRewardAmount * (contributionPercentage / 100);
      
      // 创建奖励交易
      const transactionData = {
        user_id: null,
        task_id: taskData.id,
        type: 'participant_reward',
        amount: rewardAmount, // 正数表示收入
        status: 'completed',
        currency: taskData.payment_currency || 'USD',
        payment_method: 'system',
        description: `任务"${taskData.name}"的贡献奖励 (贡献度: ${contributionPercentage}%)`,
        reference_id: null,
        client_address: participantData.client_address
      };
      
      return await this.create(transactionData);
    } catch (error) {
      console.error('创建参与者奖励交易失败:', error);
      throw error;
    }
  }

  /**
   * 获取统计数据
   * @returns {Promise<Object>} 统计数据
   */
  static async getStatistics() {
    try {
      const totalTransactions = await query('SELECT COUNT(*) as count FROM transactions');
      const totalAmount = await query('SELECT SUM(ABS(amount)) as sum FROM transactions');
      const income = await query('SELECT SUM(amount) as sum FROM transactions WHERE amount > 0');
      const expense = await query('SELECT SUM(amount) as sum FROM transactions WHERE amount < 0');

      return {
        totalTransactions: totalTransactions[0].count || 0,
        totalAmount: totalAmount[0].sum || 0,
        totalIncome: income[0].sum || 0,
        totalExpense: Math.abs(expense[0].sum) || 0
      };
    } catch (error) {
      console.error('获取交易统计数据失败:', error);
      throw error;
    }
  }

  /**
   * 创建区块链钱包首期付款交易记录
   * @param {Object} task 任务对象
   * @param {string} userId 用户ID
   * @param {string} txHash 区块链交易哈希
   * @param {string} walletType 钱包类型
   * @returns {Object} 交易记录
   */
  static async createBlockchainFirstPayment(task, userId, txHash, walletType) {
    try {
      // 计算首期付款金额
      const firstPaymentRate = task.first_payment_rate || 40;
      const firstPaymentAmount = (task.reward * firstPaymentRate) / 100;
      
      // 计算平台服务费
      const platformFeeRate = task.platform_fee_rate || 10;
      const platformFeeAmount = (task.reward * platformFeeRate) / 100;
      
      // 总金额
      const totalAmount = firstPaymentAmount + platformFeeAmount;
      
      // 创建首期付款交易记录
      const transaction = await this.create({
        user_id: userId,
        task_id: task.id,
        type: 'first_payment',
        amount: totalAmount,
        status: 'completed',
        currency: task.payment_currency || 'USD',
        payment_method: 'wallet',
        description: `任务"${task.name}"的首期付款`,
        reference_id: null,
        blockchain_tx_hash: txHash,
        wallet_type: walletType,
        created_at: new Date(),
        updated_at: new Date()
      });
      
      console.log(`创建区块链首期付款交易记录成功，交易ID: ${transaction.id}`);
      return transaction;
    } catch (error) {
      console.error('创建区块链首期付款交易记录失败:', error);
      throw error;
    }
  }

  /**
   * 创建区块链钱包二期付款交易记录
   * @param {Object} task 任务对象
   * @param {string} userId 用户ID
   * @param {string} txHash 区块链交易哈希
   * @returns {Object} 交易记录
   */
  static async createBlockchainSecondPayment(task, userId, txHash) {
    try {
      console.log(`尝试创建区块链二期付款记录，任务ID: ${task.id}, 用户ID: ${userId}, 交易哈希: ${txHash}`);
      
      // 验证参数
      if (!task || !task.id) {
        throw new Error('任务对象无效或缺少ID');
      }
      
      if (!userId) {
        throw new Error('用户ID不能为空');
      }
      
      if (!txHash) {
        throw new Error('区块链交易哈希不能为空');
      }
      
      // 计算二期付款金额
      const firstPaymentRate = task.first_payment_rate || 40;
      const secondPaymentRate = 100 - firstPaymentRate;
      const secondPaymentAmount = (task.reward * secondPaymentRate) / 100;
      
      console.log(`计算付款金额 - 任务总奖励: ${task.reward}, 首期付款比例: ${firstPaymentRate}%, 二期付款比例: ${secondPaymentRate}%, 二期付款金额: ${secondPaymentAmount}`);
      
      // 计算平台服务费
      const platformFeeRate = task.platform_fee_rate || 10;
      const platformFeeAmount = (task.reward * platformFeeRate) / 100;
      console.log(`平台服务费 - 费率: ${platformFeeRate}%, 金额: ${platformFeeAmount}`);
      
      // 总金额
      const totalAmount = secondPaymentAmount + platformFeeAmount;
      console.log(`总付款金额: ${totalAmount}`);
      
      // 准备交易数据
      const transactionData = {
        user_id: userId,
        task_id: task.id,
        type: 'second_payment',
        amount: totalAmount,
        status: 'completed',
        currency: task.payment_currency || 'USD',
        payment_method: 'wallet',
        description: `任务"${task.name || task.id}"的第二期付款`,
        reference_id: null,
        blockchain_tx_hash: txHash,
        wallet_type: task.wallet_type || 'unknown'
      };
      
      console.log(`准备创建交易记录:`, JSON.stringify(transactionData));
      
      // 创建二期付款交易记录
      const transaction = await this.create(transactionData);
      
      if (!transaction) {
        throw new Error('创建交易记录失败，未返回交易数据');
      }
      
      console.log(`创建区块链二期付款交易记录成功，交易ID: ${transaction.id}`);
      return transaction;
    } catch (error) {
      console.error('创建区块链二期付款交易记录失败:', error);
      console.error('错误详情:', error.stack);
      throw new Error(`创建区块链二期付款交易失败: ${error.message}`);
    }
  }
}

module.exports = Transaction; 