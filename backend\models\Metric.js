/**
 * 训练指标数据模型
 */
const { query } = require('../db/init');

class Metric {
  /**
   * 获取所有指标
   * @returns {Promise<Array>} 指标列表
   */
  static async getAll() {
    try {
      return await query('SELECT * FROM metrics ORDER BY task_id, round');
    } catch (error) {
      console.error('获取所有指标失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取指标
   * @param {number} id 指标ID
   * @returns {Promise<Object>} 指标详情
   */
  static async getById(id) {
    try {
      const metrics = await query('SELECT * FROM metrics WHERE id = ?', [id]);
      return metrics.length > 0 ? metrics[0] : null;
    } catch (error) {
      console.error(`获取指标(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 根据任务ID获取指标
   * @param {number} taskId 任务ID
   * @returns {Promise<Array>} 指标列表
   */
  static async getByTaskId(taskId) {
    try {
      return await query('SELECT * FROM metrics WHERE task_id = ? ORDER BY round', [taskId]);
    } catch (error) {
      console.error(`获取任务(ID: ${taskId})的指标失败:`, error);
      throw error;
    }
  }

  /**
   * 根据任务ID和轮次获取指标
   * @param {number} taskId 任务ID
   * @param {number} round 轮次
   * @returns {Promise<Object>} 指标详情
   */
  static async getByTaskIdAndRound(taskId, round) {
    try {
      const metrics = await query(
        'SELECT * FROM metrics WHERE task_id = ? AND round = ?',
        [taskId, round]
      );
      return metrics.length > 0 ? metrics[0] : null;
    } catch (error) {
      console.error(`获取任务(ID: ${taskId})轮次(${round})的指标失败:`, error);
      throw error;
    }
  }

  /**
   * 创建指标
   * @param {Object} metricData 指标数据
   * @returns {Promise<Object>} 创建的指标
   */
  static async create(metricData) {
    try {
      const {
        task_id, round, loss, accuracy, participants_count
      } = metricData;

      // 检查是否已存在该任务轮次的指标
      const existingMetric = await this.getByTaskIdAndRound(task_id, round);
      
      if (existingMetric) {
        // 更新现有指标
        return await this.update(existingMetric.id, metricData);
      }

      // 创建新指标
      const result = await query(
        `INSERT INTO metrics 
         (task_id, round, loss, accuracy, participants_count) 
         VALUES (?, ?, ?, ?, ?)`,
        [task_id, round, loss, accuracy, participants_count]
      );

      return this.getById(result.insertId);
    } catch (error) {
      console.error('创建指标失败:', error);
      throw error;
    }
  }

  /**
   * 更新指标
   * @param {number} id 指标ID
   * @param {Object} metricData 更新的指标数据
   * @returns {Promise<Object>} 更新后的指标
   */
  static async update(id, metricData) {
    try {
      const {
        loss, accuracy, participants_count
      } = metricData;

      await query(
        `UPDATE metrics SET 
           loss = ?, 
           accuracy = ?, 
           participants_count = ?,
           timestamp = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [loss, accuracy, participants_count, id]
      );

      return this.getById(id);
    } catch (error) {
      console.error(`更新指标(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 删除指标
   * @param {number} id 指标ID
   * @returns {Promise<boolean>} 删除结果
   */
  static async delete(id) {
    try {
      const result = await query('DELETE FROM metrics WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`删除指标(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 删除任务的所有指标
   * @param {number} taskId 任务ID
   * @returns {Promise<boolean>} 删除结果
   */
  static async deleteByTaskId(taskId) {
    try {
      const result = await query('DELETE FROM metrics WHERE task_id = ?', [taskId]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`删除任务(ID: ${taskId})的所有指标失败:`, error);
      throw error;
    }
  }

  /**
   * 获取任务的最新指标
   * @param {number} taskId 任务ID
   * @returns {Promise<Object>} 最新指标
   */
  static async getLatestByTaskId(taskId) {
    try {
      const metrics = await query(
        'SELECT * FROM metrics WHERE task_id = ? ORDER BY round DESC LIMIT 1',
        [taskId]
      );
      return metrics.length > 0 ? metrics[0] : null;
    } catch (error) {
      console.error(`获取任务(ID: ${taskId})的最新指标失败:`, error);
      throw error;
    }
  }
}

module.exports = Metric; 