<!-- 服务端首页仪表盘 -->
<template>
  <div class="server-home">
    <div class="page-header">
      <h1>服务端控制台</h1>
      <div class="quick-actions">
        <el-button type="primary" @click="createTask">
          <i class="fas fa-plus"></i> 创建新任务
        </el-button>
        <el-button @click="refreshData">
          <i class="fas fa-sync"></i> 刷新数据
        </el-button>
      </div>
    </div>
    
    <Breadcrumb />
    
    <!-- 系统概况卡片 -->
    <div class="stats-cards" v-loading="loading">
      <el-card class="stat-card">
        <div class="stat-card-content">
          <div class="stat-header">
            <i class="fas fa-tasks card-icon task-icon"></i>
            <div class="stat-title">训练任务</div>
          </div>
          <div class="stat-main-value">{{ stats.totalTasks }}</div>
          <div class="stat-divider"></div>
          <div class="stat-details">
            <div class="stat-item">
              <div class="stat-label">进行中</div>
              <div class="stat-value highlight-primary">{{ stats.activeTasks }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">已完成</div>
              <div class="stat-value highlight-success">{{ stats.completedTasks }}</div>
            </div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-card-content">
          <div class="stat-header">
            <i class="fas fa-users card-icon client-icon"></i>
            <div class="stat-title">客户端</div>
          </div>
          <div class="stat-main-value">{{ stats.totalClients }}</div>
          <div class="stat-divider"></div>
          <div class="stat-details">
            <div class="stat-item">
              <div class="stat-label">活跃</div>
              <div class="stat-value highlight-primary">{{ stats.activeClients }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">新增</div>
              <div class="stat-value highlight-warning">+{{ stats.newClients }} 本周</div>
            </div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-card-content">
          <div class="stat-header">
            <i class="fas fa-clock card-icon time-icon"></i>
            <div class="stat-title">训练时间</div>
          </div>
          <div class="stat-main-value">{{ formatTime(stats.totalTrainingTime) }}</div>
          <div class="stat-divider"></div>
          <div class="stat-details">
            <div class="stat-item">
              <div class="stat-label">平均每轮</div>
              <div class="stat-value">{{ formatTime(stats.avgRoundTime) }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">效率</div>
              <div class="stat-value" :class="{'highlight-success': stats.efficiency > 0.8, 'highlight-warning': stats.efficiency <= 0.8 && stats.efficiency >= 0.6, 'highlight-danger': stats.efficiency < 0.6}">
                {{ (stats.efficiency * 100).toFixed(1) }}%
              </div>
            </div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card">
        <div class="stat-card-content">
          <div class="stat-header">
            <i class="fas fa-brain card-icon model-icon"></i>
            <div class="stat-title">模型性能</div>
          </div>
          <div class="stat-main-value">{{ (stats.avgAccuracy * 100).toFixed(2) }}%</div>
          <div class="stat-divider"></div>
          <div class="stat-details">
            <div class="stat-item">
              <div class="stat-label">最高准确率</div>
              <div class="stat-value highlight-success">{{ (stats.bestAccuracy * 100).toFixed(2) }}%</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">模型数量</div>
              <div class="stat-value highlight-primary">{{ stats.modelCount }}</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 活跃任务和通知 -->
    <div class="dashboard-main">
      <div class="dashboard-section">
        <h2 class="section-title">活跃任务</h2>
        <el-table 
          :data="activeTasks" 
          style="width: 100%;" 
          v-loading="tasksLoading"
          :empty-text="'暂无活跃任务'"
          max-height="300"
        >
          <el-table-column prop="name" label="任务名称" min-width="180">
            <template #default="scope">
              <router-link :to="`/server/tasks/${scope.row.id}`" class="task-link">
                {{ scope.row.name }}
              </router-link>
            </template>
          </el-table-column>
          <el-table-column prop="progress" label="进度" width="200">
            <template #default="scope">
              <el-progress 
                :percentage="calculateProgress(scope.row)" 
                :status="getProgressStatus(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="participants" label="参与者" width="120">
            <template #default="scope">
              {{ scope.row.participants.length }}/{{ scope.row.requiredParticipants }}
            </template>
          </el-table-column>
          <el-table-column prop="currentRound" label="当前轮次" width="100" />
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="scope">
              <el-button size="small" @click="monitorTask(scope.row)" type="primary" plain>监控</el-button>
              <el-button size="small" @click="stopTask(scope.row)" type="warning" plain>停止</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="view-all">
          <router-link to="/server/tasks" class="view-all-link">
            查看所有任务 <i class="fas fa-arrow-right"></i>
          </router-link>
        </div>
      </div>
      
      <div class="dashboard-section">
        <h2 class="section-title">通知与待办</h2>
        <div class="notifications" v-loading="notificationsLoading">
          <div v-if="notifications.length === 0" class="empty-notifications">
            <i class="fas fa-bell-slash"></i>
            <p>暂无通知</p>
          </div>
          
          <el-timeline v-else>
            <el-timeline-item
              v-for="(notification, index) in notifications"
              :key="index"
              :type="notification.type"
              :color="getNotificationColor(notification.type)"
              :timestamp="notification.time"
            >
              <h4>{{ notification.title }}</h4>
              <p>{{ notification.content }}</p>
              <div class="notification-actions" v-if="notification.actions">
                <el-button 
                  v-for="action in notification.actions" 
                  :key="action.name"
                  size="small" 
                  :type="action.type || 'default'"
                  @click="handleNotificationAction(notification, action)"
                >
                  {{ action.label }}
                </el-button>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </div>
    
    <!-- 数据统计图表 -->
    <div class="dashboard-charts">
      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>训练任务状态分布</span>
            <el-button link @click="exportChart('taskChart')">
              <i class="fas fa-download"></i> 导出
            </el-button>
          </div>
        </template>
        <div class="chart-container" ref="taskChartRef"></div>
      </el-card>
      
      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>模型性能趋势</span>
            <el-button link @click="exportChart('performanceChart')">
              <i class="fas fa-download"></i> 导出
            </el-button>
          </div>
        </template>
        <div class="chart-container" ref="performanceChartRef"></div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import * as echarts from 'echarts';
import { ElMessage } from 'element-plus';
import Breadcrumb from '@/components/Breadcrumb.vue';

export default {
  name: 'ServerHome',
  components: {
    Breadcrumb
  },
  setup() {
    const router = useRouter();
    const loading = ref(false);
    const tasksLoading = ref(false);
    const notificationsLoading = ref(false);
    const stats = ref({
      totalTasks: 0,
      activeTasks: 0,
      completedTasks: 0,
      totalClients: 0,
      activeClients: 0,
      newClients: 0,
      totalTrainingTime: 0,
      avgRoundTime: 0,
      efficiency: 0,
      avgAccuracy: 0,
      bestAccuracy: 0,
      modelCount: 0
    });
    const activeTasks = ref([]);
    const notifications = ref([]);
    
    // 图表引用
    const taskChartRef = ref(null);
    const performanceChartRef = ref(null);
    let taskChart = null;
    let performanceChart = null;
    
    // 加载仪表盘数据
    const loadDashboardData = async () => {
      loading.value = true;
      try {
        // 这里应该调用API获取仪表盘数据
        await new Promise(resolve => setTimeout(resolve, 500)); // 模拟API调用
        
        // 临时数据
        stats.value = {
          totalTasks: 18,
          activeTasks: 3,
          completedTasks: 12,
          totalClients: 27,
          activeClients: 15,
          newClients: 4,
          totalTrainingTime: 176400, // 49小时
          avgRoundTime: 3600, // 1小时
          efficiency: 0.85,
          avgAccuracy: 0.923,
          bestAccuracy: 0.978,
          modelCount: 12
        };
      } catch (error) {
        console.error('加载仪表盘数据失败:', error);
        ElMessage.error('加载仪表盘数据失败，请重试');
      } finally {
        loading.value = false;
      }
    };
    
    // 加载活跃任务
    const loadActiveTasks = async () => {
      tasksLoading.value = true;
      try {
        // 这里应该调用API获取活跃任务
        await new Promise(resolve => setTimeout(resolve, 300)); // 模拟API调用
        
        // 临时数据
        activeTasks.value = [
          {
            id: 1,
            name: '医疗影像分类模型',
            status: 'in_progress',
            participants: new Array(3),
            requiredParticipants: 5,
            currentRound: 2,
            totalRounds: 8,
            accuracy: 0.8345
          },
          {
            id: 2,
            name: '金融交易欺诈检测',
            status: 'in_progress',
            participants: new Array(4),
            requiredParticipants: 4,
            currentRound: 3,
            totalRounds: 5,
            accuracy: 0.8732
          },
          {
            id: 3,
            name: '交通流量预测',
            status: 'in_progress',
            participants: new Array(2),
            requiredParticipants: 3,
            currentRound: 1,
            totalRounds: 6,
            accuracy: 0.7643
          }
        ];
      } catch (error) {
        console.error('加载活跃任务失败:', error);
        ElMessage.error('加载活跃任务失败，请重试');
      } finally {
        tasksLoading.value = false;
      }
    };
    
    // 加载通知
    const loadNotifications = async () => {
      notificationsLoading.value = true;
      try {
        // 这里应该调用API获取通知
        await new Promise(resolve => setTimeout(resolve, 400)); // 模拟API调用
        
        // 临时数据
        notifications.value = [
          {
            type: 'warning',
            title: '申请审核待处理',
            content: '医疗影像分类模型有3个新的参与申请等待审核',
            time: '10分钟前',
            actions: [
              { label: '去审核', type: 'primary', name: 'review', taskId: 1 }
            ]
          },
          {
            type: 'success',
            title: '训练完成',
            content: '自然语言情感分析模型已完成全部5轮训练，最终准确率达到94.56%',
            time: '2小时前',
            actions: [
              { label: '查看结果', type: 'primary', name: 'view', taskId: 4 }
            ]
          },
          {
            type: 'info',
            title: '客户端加入',
            content: '新客户端 0x8D50...DFA 已加入系统',
            time: '昨天 15:42'
          }
        ];
      } catch (error) {
        console.error('加载通知失败:', error);
        ElMessage.error('加载通知失败，请重试');
      } finally {
        notificationsLoading.value = false;
      }
    };
    
    // 初始化图表
    const initCharts = () => {
      console.log('设置图表选项');
      
      if (!taskChart || !performanceChart) {
        console.error('图表实例未创建');
        return;
      }
      
      // 任务状态分布图表
      const taskChartOption = {
        backgroundColor: '#ffffff',
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          type: 'scroll',
          orient: 'horizontal',
          bottom: 10,
          left: 'center',
          data: ['草稿', '已发布', '进行中', '已完成', '已取消'],
          itemWidth: 15,
          itemHeight: 15,
          textStyle: {
            fontSize: 12,
            color: '#333'
          },
          show: true,
          selectedMode: true,
          itemGap: 12
        },
        series: [
          {
            name: '任务状态',
            type: 'pie',
            radius: ['40%', '65%'],
            center: ['50%', '45%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 8,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 3, name: '草稿', itemStyle: { color: '#909399' } },
              { value: 5, name: '已发布', itemStyle: { color: '#E6A23C' } },
              { value: 3, name: '进行中', itemStyle: { color: '#67C23A' } },
              { value: 12, name: '已完成', itemStyle: { color: '#409EFF' } },
              { value: 2, name: '已取消', itemStyle: { color: '#F56C6C' } }
            ]
          }
        ]
      };
      
      // 性能趋势图表
      const performanceChartOption = {
        backgroundColor: '#ffffff',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['准确率', '损失'],
          bottom: 10,
          left: 'center',
          itemWidth: 15,
          itemHeight: 15,
          textStyle: {
            fontSize: 12,
            color: '#333'
          },
          show: true,
          selectedMode: true,
          itemGap: 20
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '15%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['轮次1', '轮次2', '轮次3', '轮次4', '轮次5', '轮次6', '轮次7', '轮次8', '轮次9', '轮次10'],
          axisLabel: {
            fontSize: 11,
            interval: 0,
            rotate: 30
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '准确率',
            min: 0,
            max: 1.0,
            interval: 0.1,
            position: 'left',
            axisLabel: {
              formatter: '{value}',
              fontSize: 10
            }
          },
          {
            type: 'value',
            name: '损失',
            min: 0,
            max: 3.0,
            interval: 0.5,
            position: 'right',
            axisLabel: {
              formatter: '{value}',
              fontSize: 10
            }
          }
        ],
        series: [
          {
            name: '准确率',
            type: 'line',
            smooth: true,
            data: [0.65, 0.72, 0.78, 0.85, 0.88, 0.89, 0.91, 0.93, 0.95, 0.978],
            yAxisIndex: 0,
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#409EFF'
            },
            lineStyle: {
              width: 2
            }
          },
          {
            name: '损失',
            type: 'line',
            smooth: true,
            data: [2.5, 1.8, 1.2, 0.9, 0.7, 0.5, 0.35, 0.25, 0.15, 0.1],
            yAxisIndex: 1,
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#F56C6C'
            },
            lineStyle: {
              width: 2
            }
          }
        ]
      };
      
      // 设置图表选项
      taskChart.setOption(taskChartOption);
      performanceChart.setOption(performanceChartOption);
      
      console.log('图表选项设置完成');
    };
    
    // 窗口大小变化处理
    const handleResize = () => {
      if (taskChart) {
        taskChart.resize();
      }
      if (performanceChart) {
        performanceChart.resize();
      }
    };
    
    // 格式化时间
    const formatTime = (seconds) => {
      if (!seconds) return '0h 0m';
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}h ${minutes}m`;
    };
    
    // 计算任务进度百分比
    const calculateProgress = (task) => {
      if (!task.totalRounds) return 0;
      return Math.floor((task.currentRound / task.totalRounds) * 100);
    };
    
    // 获取进度条状态
    const getProgressStatus = (task) => {
      const progress = calculateProgress(task);
      if (progress >= 90) return 'success';
      if (progress >= 60) return '';
      if (progress >= 30) return 'warning';
      return 'exception';
    };
    
    // 获取通知图标颜色
    const getNotificationColor = (type) => {
      const colors = {
        success: '#67C23A',
        warning: '#E6A23C',
        info: '#909399',
        error: '#F56C6C'
      };
      return colors[type] || colors.info;
    };
    
    // 处理通知操作
    const handleNotificationAction = (notification, action) => {
      if (action.name === 'review') {
        router.push(`/server/tasks/${action.taskId}/applications`);
      } else if (action.name === 'view') {
        router.push(`/server/tasks/${action.taskId}/results`);
      }
    };
    
    // 创建新任务
    const createTask = () => {
      router.push('/server/tasks/create');
    };
    
    // 监控任务
    const monitorTask = (task) => {
      router.push(`/server/training?task=${task.id}`);
    };
    
    // 停止任务
    const stopTask = async (task) => {
      try {
        // 这里应该调用API停止任务
        await new Promise(resolve => setTimeout(resolve, 500)); // 模拟API调用
        ElMessage.success(`已停止任务: ${task.name}`);
      } catch (error) {
        console.error('停止任务失败:', error);
        ElMessage.error('停止任务失败，请重试');
      }
    };
    
    // 刷新数据
    const refreshData = async () => {
      try {
        await Promise.all([
          loadDashboardData(),
          loadActiveTasks(),
          loadNotifications()
        ]);
        ElMessage.success('数据已刷新');
      } catch (error) {
        console.error('刷新数据失败:', error);
        ElMessage.error('刷新数据失败，请重试');
      }
    };
    
    // 导出图表
    const exportChart = (chartType) => {
      let chart = null;
      if (chartType === 'taskChart') {
        chart = taskChart;
      } else if (chartType === 'performanceChart') {
        chart = performanceChart;
      }
      
      if (chart) {
        const url = chart.getDataURL({
          pixelRatio: 2,
          backgroundColor: '#fff'
        });
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `${chartType}_${new Date().getTime()}.png`;
        link.click();
      }
    };
    
    onMounted(() => {
      refreshData();
      
      // 等待DOM完全渲染后再初始化图表
      setTimeout(() => {
        if (taskChartRef.value && performanceChartRef.value) {
          // 确保DOM元素准备好
          console.log('初始化图表');
          
          // 销毁现有图表实例（如果有）
          if (taskChart) taskChart.dispose();
          if (performanceChart) performanceChart.dispose();
          
          // 重新初始化图表
          taskChart = echarts.init(taskChartRef.value);
          performanceChart = echarts.init(performanceChartRef.value);
          
          // 配置和加载图表
          initCharts();
          
          // 添加resize监听
          window.addEventListener('resize', handleResize);
          
          // 分阶段调整大小以确保正确渲染
          let resizeAttempts = 0;
          const resizeInterval = setInterval(() => {
            if (taskChart && performanceChart) {
              taskChart.resize();
              performanceChart.resize();
              resizeAttempts++;
              
              if (resizeAttempts >= 5) {
                clearInterval(resizeInterval);
              }
            } else {
              clearInterval(resizeInterval);
            }
          }, 300);
          
          // 确保图例正确显示
          setTimeout(() => {
            if (taskChart && performanceChart) {
              console.log('重新设置图表选项以确保图例显示');
              taskChart.setOption({
                legend: {
                  show: true
                }
              });
              performanceChart.setOption({
                legend: {
                  show: true
                }
              });
            }
          }, 800);
        }
      }, 500);
    });
    
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize);
      if (taskChart) taskChart.dispose();
      if (performanceChart) performanceChart.dispose();
    });
    
    return {
      loading,
      tasksLoading,
      notificationsLoading,
      stats,
      activeTasks,
      notifications,
      taskChartRef,
      performanceChartRef,
      formatTime,
      calculateProgress,
      getProgressStatus,
      getNotificationColor,
      handleNotificationAction,
      createTask,
      monitorTask,
      stopTask,
      refreshData,
      exportChart
    };
  }
};
</script>

<style scoped>
.server-home {
  width: 100%;
  min-height: calc(100vh - 120px);
  box-sizing: border-box;
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  overflow-y: auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  width: 100%;
  background-color: transparent;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: var(--text-primary);
  font-weight: 600;
}

.quick-actions {
  display: flex;
  gap: 12px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 24px;
  margin-bottom: 30px;
  width: 100%;
}

.stat-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  border: none;
  overflow: hidden;
  height: 100%;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
}

.stat-card-content {
  display: flex;
  flex-direction: column;
  padding: 24px;
  height: 100%;
}

.stat-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 16px;
}

.card-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.task-icon {
  color: #409EFF;
}

.client-icon {
  color: #67C23A;
}

.time-icon {
  color: #E6A23C;
}

.model-icon {
  color: #9013FE;
}

.stat-title {
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.2px;
}

.stat-main-value {
  font-size: 36px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 16px;
  line-height: 1.1;
  letter-spacing: -0.5px;
}

.stat-divider {
  height: 1px;
  background-color: rgba(0, 0, 0, 0.06);
  margin-bottom: 16px;
  width: 100%;
}

.stat-details {
  display: flex;
  justify-content: space-between;
  gap: 16px;
  margin-top: auto;
}

.stat-item {
  display·: flex;
  flex-direction: column;
  flex: 1;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 13px;
  margin-bottom: 6px;
  white-space: nowrap;
}

.stat-value {
  font-weight: 600;
  font-size: 15px;
  color: var(--text-primary);
  white-space: nowrap;
}

.highlight-primary {
  color: #409EFF;
}

.highlight-success {
  color: #67C23A;
}

.highlight-warning {
  color: #E6A23C;
}

.highlight-danger {
  color: #F56C6C;
}

.dashboard-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 20px;
  width: 100%;
  margin-bottom: 24px;
}

.chart-card {
  height: 460px;  /* 稍微增加高度以容纳图例 */
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.chart-container {
  flex: 1;
  height: 360px;
  width: 100%;
  padding: 10px 10px 35px 10px; /* 增加底部内边距给图例 */
  position: relative;
}

.dashboard-main {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 20px;
  width: 100%;
  margin-bottom: 24px;
}

.dashboard-section {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.section-title {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.task-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.task-link:hover {
  text-decoration: underline;
}

.view-all {
  text-align: center;
  margin-top: 16px;
}

.view-all-link {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.view-all-link:hover {
  text-decoration: underline;
}

.notifications {
  flex: 1;
  overflow-y: auto;
  max-height: 300px;
}

.empty-notifications {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-hint);
}

.empty-notifications i {
  font-size: 36px;
  margin-bottom: 12px;
}

.notification-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

@media (max-width: 1200px) {
  .stats-cards {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 16px;
  }
  
  .stat-card-content {
    padding: 20px;
  }
  
  .stat-main-value {
    font-size: 32px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .quick-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .stats-cards {
    gap: 12px;
  }
  
  .stat-card-content {
    padding: 16px;
  }
  
  .stat-main-value {
    font-size: 28px;
    margin-bottom: 12px;
  }
  
  .stat-details {
    flex-direction: column;
    gap: 12px;
  }
}
</style> 