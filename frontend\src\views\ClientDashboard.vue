<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <h1>数据交易系统客户端</h1>
      <div class="dashboard-status">
        <span class="status-label">参与状态:</span>
        <span class="status-badge" :class="{ 'success': isParticipating, 'warning': !isParticipating }" v-if="!loading">
          {{ isParticipating ? '已参与' : '未参与' }}
        </span>
        <span class="status-badge info" v-else>
          <i class="fas fa-sync fa-spin"></i> 加载中
        </span>
      </div>
    </div>

    <!-- 嵌套路由视图 -->
    <router-view></router-view>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import API from '../services/api';
import web3Service from '../services/web3';

export default {
  name: 'ClientDashboard',
  setup() {
    const store = useStore();
    const isParticipating = ref(false);
    const loading = ref(true);
    const error = ref(null);
    const updateInterval = ref(null);

    // 检查客户端状态
    const checkClientStatus = async () => {
      try {
        loading.value = true;
        error.value = null;
        
        // 检查Web3服务是否初始化
        try {
          // 尝试获取合约，如果失败则初始化web3服务
          try {
            web3Service.getContract();
          } catch (error) {
            console.log('需要初始化Web3服务');
            await web3Service.init();
          }
          
          const address = store.state.user.address;
          if (!address) {
            error.value = '用户未登录或地址未获取';
            return;
          }
          
          // 获取链上参与状态
          try {
            const contract = web3Service.getContract();
            // 获取所有活跃任务
            const tasks = await API.Task.getAll();
            const activeTaskIds = tasks.filter(task => 
              task.status === 'CREATED' || task.status === 'ACTIVE').map(task => task.id);
            
            // 检查用户是否参与任何活跃任务
            isParticipating.value = false;
            if (activeTaskIds.length > 0) {
              for (const taskId of activeTaskIds) {
                try {
                  const isParticipantInTask = await contract.methods.isParticipant(taskId, address).call();
                  if (isParticipantInTask) {
                    isParticipating.value = true;
                    break;
                  }
                } catch (taskErr) {
                  console.warn(`检查任务 ${taskId} 参与状态失败:`, taskErr);
                }
              }
            }
          } catch (err) {
            console.error('获取链上参与状态失败:', err);
            // 如果链上状态检查失败，尝试通过API获取
            try {
              // 查找客户端是否已在任何任务中注册
              const tasks = await API.Task.getAll();
              const participants = await Promise.all(tasks.map(task => API.Participant.getByTaskId(task.id)));
              const flattenedParticipants = participants.flat();
              
              // 判断是否有该客户端地址的参与记录
              isParticipating.value = flattenedParticipants.some(p => p.client_address === address);
            } catch (apiErr) {
              console.error('获取API参与状态失败:', apiErr);
              error.value = '无法获取参与状态';
              isParticipating.value = false; // 默认设为未参与
            }
          }
        } catch (err) {
          console.error('Web3初始化失败:', err);
          error.value = '区块链连接失败';
          isParticipating.value = false; // 默认设为未参与
        }
      } catch (err) {
        console.error('检查客户端状态失败:', err);
        error.value = err.message || '检查状态失败';
        isParticipating.value = false; // 确保出错时有默认值
      } finally {
        loading.value = false;
      }
    };

    // 开始定期更新状态
    const startStatusUpdates = () => {
      // 如果已有更新间隔，先清除
      if (updateInterval.value) {
        clearInterval(updateInterval.value);
      }
      
      // 设置为每10秒更新一次
      updateInterval.value = setInterval(checkClientStatus, 10000);
    };

    // 停止定期更新
    const stopStatusUpdates = () => {
      if (updateInterval.value) {
        clearInterval(updateInterval.value);
        updateInterval.value = null;
      }
    };

    onMounted(async () => {
      await checkClientStatus();
      startStatusUpdates();
    });

    onUnmounted(() => {
      stopStatusUpdates();
    });

    return {
      isParticipating,
      loading,
      error
    };
  }
};
</script>

<style scoped>
/* 使用全局CSS变量 */
.dashboard-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  width: 100%;
  overflow: hidden;
}

.dashboard-header h1 {
  color: var(--primary-color);
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.dashboard-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  min-width: 100px;
  max-width: 180px;
  overflow: hidden;
}

.status-label {
  color: var(--text-secondary);
  font-weight: 500;
  white-space: nowrap;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
}

.status-badge.success {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
}

.status-badge.warning {
  background-color: rgba(255, 152, 0, 0.1);
  color: var(--warning-color);
}

.status-badge.error {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--error-color);
}

.status-badge.info {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  background-color: rgba(0, 123, 255, 0.1);
  color: var(--primary-color);
  white-space: nowrap;
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
  
  .dashboard-status {
    width: 100%;
    justify-content: flex-start;
  }
}
</style> 