<template>
  <div class="app">
    <Sidebar v-if="isLoggedIn" />
    <div class="main-content" :class="{ 'with-sidebar': isLoggedIn }">
      <router-view></router-view>
    </div>
    
    <!-- 通知组件 -->
    <Notifications />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import web3Service from './services/web3';
import Sidebar from './components/Sidebar.vue';
import Notifications from './components/Notifications.vue';
import './assets/styles/global.css';
import apiService from './services/api';
import { cache } from './services/api';

export default {
  name: 'App',
  components: {
    Sidebar,
    Notifications
  },
  setup() {
    const store = useStore();
    const router = useRouter();
    const account = ref(null);
    const isAdmin = ref(false);

    const isLoggedIn = computed(() => {
      return store.state.user && store.state.user.address && store.state.user.address !== null;
    });

    const userAddress = computed(() => store.state.user.address);
    const userRole = computed(() => store.state.user.role);
    const userRoleText = computed(() => {
      const roles = {
        client: '客户端用户',
        server: '服务端管理员'
      };
      return roles[userRole.value] || '未知角色';
    });

    const formatAddress = (address) => {
      if (!address) return '';
      return `${address.slice(0, 6)}...${address.slice(-4)}`;
    };

    const connect = async () => {
      try {
        // 保存旧用户地址以检测变化
        const oldUserAddress = localStorage.getItem('userAddress');
        
        const connectedAccount = await web3Service.connectWallet();
        account.value = connectedAccount;
        
        // 如果用户地址变化，清除之前用户的缓存
        if (oldUserAddress && oldUserAddress !== connectedAccount) {
          console.log('用户地址已变化，清除旧用户缓存');
          
          // 清除与旧用户相关的所有缓存
          clearUserCache(oldUserAddress);
          
          // 清除通用缓存
          clearGeneralCache();
        }
        
        // 保存用户地址到localStorage供其他组件使用
        localStorage.setItem('userAddress', connectedAccount);

        // 检查是否是管理员
        const role = await web3Service.getUserRole(connectedAccount);
        isAdmin.value = role === 'admin';
        
        // 如果是管理员且当前在客户端页面，跳转到服务端页面
        if (isAdmin.value && router.currentRoute.value.path === '/client') {
          router.push('/server');
        }
      } catch (error) {
        console.error('连接钱包失败:', error);
      }
    };
    
    // 清除特定用户的缓存
    const clearUserCache = (userAddress) => {
      // 清除该用户的申请记录
      localStorage.removeItem(`applications_${userAddress}`);
      
      // 清除与该用户相关的其他缓存
      localStorage.removeItem(`availableTasks_${userAddress}`);
      
      // 清除客户端状态缓存
      if (cache.clientStatus && cache.clientStatus.data) {
        delete cache.clientStatus.data[userAddress];
      }
      
      console.log(`已清除用户 ${userAddress} 的缓存`);
    };
    
    // 清除通用缓存
    const clearGeneralCache = () => {
      // 重置缓存时间戳，强制刷新
      if (cache.serverStatus) {
        cache.serverStatus.timestamp = 0;
      }
      
      if (cache.clientStatus) {
        cache.clientStatus.timestamp = 0;
      }
      
      console.log('已清除通用缓存');
    };

    const handleLogout = async () => {
      try {
        // 获取当前用户地址，以便清除缓存
        const currentUserAddress = localStorage.getItem('userAddress');
        
        // 尝试断开Web3连接
        try {
          // 如果disconnect方法存在，则调用它
          if (typeof web3Service.disconnect === 'function') {
            await web3Service.disconnect();
          }
        } catch (error) {
          console.error('断开Web3连接失败:', error);
          // 继续执行，不要因为这个错误而阻止退出登录
        }
        
        // 如果有用户地址，清除相关缓存
        if (currentUserAddress) {
          clearUserCache(currentUserAddress);
        }
        
        // 清除通用缓存
        clearGeneralCache();
        
        // 清除用户状态 - 使用正确的mutation名称
        store.commit('user/CLEAR_USER');
        // 同时从localStorage中移除用户信息
        localStorage.removeItem('user');
        localStorage.removeItem('userAddress');
        
        // 跳转到登录页
        router.push('/login');
      } catch (error) {
        console.error('退出登录失败:', error);
        // 显示错误提示
        store.dispatch('notification/addErrorNotification', {
          title: '退出失败',
          message: '退出登录失败: ' + error.message
        });
      }
    };

    onMounted(async () => {
      try {
        await web3Service.init();
        if (web3Service.account) {
          account.value = web3Service.account;
          const role = await web3Service.getUserRole(web3Service.account);
          isAdmin.value = role === 'admin';
  
          // 如果用户已登录但没有在store中设置，则更新store
          if (!store.state.user.address) {
            // 获取用户角色
            const userRole = role === 1 ? 'server' : 'client';
            
            // 更新store
            store.commit('user/SET_USER', {
              address: web3Service.account,
              role: userRole
            });
            
            // 如果当前在根路径或404页面，重定向到对应角色页面
            const currentPath = router.currentRoute.value.path;
            if (currentPath === '/' || currentPath === '/404') {
              router.push(`/${userRole}`);
            }
          }
        }
      } catch (error) {
        console.error('初始化失败:', error);
      }
    });

    return {
      isLoggedIn,
      userAddress,
      userRole,
      userRoleText,
      formatAddress,
      connect,
      handleLogout
    };
  }
};
</script>

<style>
.app {
  min-height: 100vh;
  display: flex;
  background: var(--background-color);
  position: relative;
  width: 100%;
  overflow-x: hidden; /* 防止水平滚动 */
}

.main-content {
  flex: 1;
  min-height: 100vh;
  transition: all 0.3s ease;
  padding: 0;
  width: 100%;
  box-sizing: border-box;
  overflow-y: auto; /* 只允许垂直滚动 */
  overflow-x: visible; /* 允许内容水平扩展 */
  display: flex;
  flex-direction: column; /* 确保内容正确堆叠 */
  position: relative;
  z-index: 60; /* 高于侧边栏的z-index */
  background-color: transparent; /* 背景设为透明 */
}

.main-content.with-sidebar {
  margin-left: 240px;
  width: calc(100% - 240px);
  z-index: 60; /* 高于侧边栏的z-index */
  padding-right: 0; /* 确保右侧没有额外内边距 */
}

/* 确保移动端布局正确 */
@media (max-width: 768px) {
  .main-content,
  .main-content.with-sidebar {
    margin-left: 0;
    width: 100%;
  }
}

@media (max-width: 576px) {
  .main-content {
    padding: 0;
  }
}
</style>
