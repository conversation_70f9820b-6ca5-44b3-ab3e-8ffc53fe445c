<template>
  <div class="task-details-container">
    <!-- ... existing code ... -->
    
    <!-- 任务参与者信息 -->
    <div class="task-info-section">
      <h3>参与者信息 ({{ participants.length }}/{{ task.requiredParticipants || 0 }})</h3>
      <!-- 显示已批准的参与者 -->
      <div v-if="participants.length > 0">
        <div class="participant-list">
          <div v-for="participant in participants" :key="participant.id" class="participant-item">
            <div class="participant-info">
              <div><strong>地址:</strong> {{ shortenAddress(participant.clientAddress) }}</div>
              <div><strong>状态:</strong> <span :class="`status-${participant.status}`">{{ getStatusLabel(participant.status) }}</span></div>
              <div><strong>申请时间:</strong> {{ formatDate(participant.applyTime) }}</div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="no-data">
        <p>暂无已批准的参与者</p>
      </div>
      
      <!-- 申请列表 -->
      <h3>申请列表 ({{ applications.length }})</h3>
      <div v-if="applications.length > 0">
        <div class="application-list">
          <div v-for="application in applications" :key="application.id" class="application-item">
            <div class="application-info">
              <div><strong>地址:</strong> {{ shortenAddress(application.clientAddress) }}</div>
              <div><strong>状态:</strong> <span :class="`status-${application.status}`">{{ getStatusLabel(application.status) }}</span></div>
              <div><strong>申请时间:</strong> {{ formatDate(application.applyTime) }}</div>
              <div><strong>数据规模:</strong> {{ application.dataSize || '未指定' }}</div>
              <div><strong>计算资源:</strong> {{ application.computeResource || '未指定' }}</div>
            </div>
            <div class="application-actions" v-if="application.status === 'pending'">
              <el-button type="success" size="small" @click="approveApplication(application)">批准</el-button>
              <el-button type="danger" size="small" @click="rejectApplication(application)">拒绝</el-button>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="no-data">
        <p>暂无申请记录</p>
      </div>
    </div>
    
    <!-- ... existing code ... -->
  </div>
</template>

<script>
import API from '@/api';

export default {
  data() {
    return {
      taskId: '',
      task: {},
      applications: [],
      participants: [],
    };
  },
  methods: {
    // 获取任务参与者
    async fetchTaskParticipants() {
      try {
        const applications = await API.Task.getTaskApplications(this.taskId);
        
        // 所有申请，包括已注册但未批准的
        this.applications = applications.all;
        
        // 只有已批准的才算作参与者
        this.participants = applications.approved;
        
        console.log(`获取到任务 ${this.taskId} 的 ${this.applications.length} 个申请者，其中 ${this.participants.length} 个已批准的参与者`);
      } catch (error) {
        console.error('获取任务参与者失败:', error);
        this.$toast.error('获取任务参与者失败');
      }
    },
  },
};
</script> 