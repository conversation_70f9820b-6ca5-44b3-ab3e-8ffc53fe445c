// 错误处理模块

const state = {
  error: null,
  showError: false
};

const mutations = {
  setError(state, error) {
    state.error = error;
    state.showError = true;
  },
  clearError(state) {
    state.error = null;
    state.showError = false;
  }
};

const actions = {
  setError({ commit }, error) {
    commit('setError', error);
    // 5秒后自动清除错误
    setTimeout(() => {
      commit('clearError');
    }, 5000);
  },
  clearError({ commit }) {
    commit('clearError');
  }
};

const getters = {
  hasError: state => !!state.error,
  errorMessage: state => state.error,
  showError: state => state.showError
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}; 