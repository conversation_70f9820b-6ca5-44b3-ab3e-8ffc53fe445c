/**
 * 用户数据模型
 */
const { query } = require('../db/init');
const bcrypt = require('bcrypt');

class User {
  /**
   * 获取所有用户
   * @returns {Promise<Array>} 用户列表
   */
  static async getAll() {
    try {
      // 不返回密码字段
      return await query('SELECT id, username, email, role, status, last_login, created_at, updated_at FROM users');
    } catch (error) {
      console.error('获取所有用户失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取用户
   * @param {number} id 用户ID
   * @param {boolean} includePassword 是否包含密码，默认不包含
   * @returns {Promise<Object>} 用户详情
   */
  static async getById(id, includePassword = false) {
    try {
      let fields = 'id, username, email, role, status, last_login, created_at, updated_at';
      if (includePassword) {
        fields += ', password';
      }
      
      const users = await query(`SELECT ${fields} FROM users WHERE id = ?`, [id]);
      return users.length > 0 ? users[0] : null;
    } catch (error) {
      console.error(`获取用户(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 根据用户名获取用户
   * @param {string} username 用户名
   * @param {boolean} includePassword 是否包含密码，默认不包含
   * @returns {Promise<Object>} 用户详情
   */
  static async getByUsername(username, includePassword = false) {
    try {
      let fields = 'id, username, email, role, status, last_login, created_at, updated_at';
      if (includePassword) {
        fields += ', password';
      }
      
      const users = await query(`SELECT ${fields} FROM users WHERE username = ?`, [username]);
      return users.length > 0 ? users[0] : null;
    } catch (error) {
      console.error(`获取用户(用户名: ${username})失败:`, error);
      throw error;
    }
  }

  /**
   * 创建用户
   * @param {Object} userData 用户数据
   * @returns {Promise<Object>} 创建的用户
   */
  static async create(userData) {
    try {
      const { username, password, email, role = 'user', status = 'active' } = userData;

      // 检查用户名是否已存在
      const existingUser = await this.getByUsername(username);
      if (existingUser) {
        throw new Error(`用户名 '${username}' 已存在`);
      }

      // 检查邮箱是否已存在
      const existingEmail = await query('SELECT id FROM users WHERE email = ?', [email]);
      if (existingEmail.length > 0) {
        throw new Error(`邮箱 '${email}' 已被使用`);
      }

      // 密码加密
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      // 创建用户
      const result = await query(
        'INSERT INTO users (username, password, email, role, status) VALUES (?, ?, ?, ?, ?)',
        [username, hashedPassword, email, role, status]
      );

      return this.getById(result.insertId);
    } catch (error) {
      console.error('创建用户失败:', error);
      throw error;
    }
  }

  /**
   * 更新用户
   * @param {number} id 用户ID
   * @param {Object} userData 更新的用户数据
   * @returns {Promise<Object>} 更新后的用户
   */
  static async update(id, userData) {
    try {
      const { email, role, status } = userData;
      let { password } = userData;

      // 准备更新字段
      const updates = [];
      const values = [];

      if (email) {
        // 检查邮箱是否已被其他用户使用
        const existingEmail = await query('SELECT id FROM users WHERE email = ? AND id != ?', [email, id]);
        if (existingEmail.length > 0) {
          throw new Error(`邮箱 '${email}' 已被其他用户使用`);
        }
        updates.push('email = ?');
        values.push(email);
      }

      if (password) {
        // 密码加密
        const saltRounds = 10;
        password = await bcrypt.hash(password, saltRounds);
        updates.push('password = ?');
        values.push(password);
      }

      if (role) {
        updates.push('role = ?');
        values.push(role);
      }

      if (status) {
        updates.push('status = ?');
        values.push(status);
      }

      // 如果没有要更新的字段，直接返回用户信息
      if (updates.length === 0) {
        return this.getById(id);
      }

      // 添加ID到参数数组
      values.push(id);

      // 执行更新
      await query(
        `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
        values
      );

      return this.getById(id);
    } catch (error) {
      console.error(`更新用户(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 更新用户最后登录时间
   * @param {number} id 用户ID
   * @returns {Promise<boolean>} 更新结果
   */
  static async updateLastLogin(id) {
    try {
      await query(
        'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?',
        [id]
      );
      return true;
    } catch (error) {
      console.error(`更新用户(ID: ${id})最后登录时间失败:`, error);
      throw error;
    }
  }

  /**
   * 删除用户
   * @param {number} id 用户ID
   * @returns {Promise<boolean>} 删除结果
   */
  static async delete(id) {
    try {
      const result = await query('DELETE FROM users WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`删除用户(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 验证用户密码
   * @param {string} username 用户名
   * @param {string} password 密码
   * @returns {Promise<Object|null>} 验证成功则返回用户信息，失败返回null
   */
  static async authenticate(username, password) {
    try {
      // 获取用户(含密码)
      const user = await this.getByUsername(username, true);
      
      if (!user) {
        return null; // 用户不存在
      }

      // 检查用户状态
      if (user.status !== 'active') {
        throw new Error(`用户账号已${user.status === 'inactive' ? '禁用' : '锁定'}`);
      }

      // 验证密码
      const match = await bcrypt.compare(password, user.password);
      
      if (!match) {
        return null; // 密码不匹配
      }

      // 更新最后登录时间
      await this.updateLastLogin(user.id);
      
      // 返回用户信息(不含密码)
      delete user.password;
      return user;
    } catch (error) {
      console.error(`用户认证失败(用户名: ${username}):`, error);
      throw error;
    }
  }

  /**
   * 修改密码
   * @param {number} id 用户ID
   * @param {string} oldPassword 旧密码
   * @param {string} newPassword 新密码
   * @returns {Promise<boolean>} 修改结果
   */
  static async changePassword(id, oldPassword, newPassword) {
    try {
      // 获取用户(含密码)
      const user = await this.getById(id, true);
      
      if (!user) {
        throw new Error('用户不存在');
      }

      // 验证旧密码
      const match = await bcrypt.compare(oldPassword, user.password);
      
      if (!match) {
        throw new Error('旧密码不正确');
      }

      // 密码加密
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

      // 更新密码
      await query(
        'UPDATE users SET password = ? WHERE id = ?',
        [hashedPassword, id]
      );

      return true;
    } catch (error) {
      console.error(`修改密码失败(用户ID: ${id}):`, error);
      throw error;
    }
  }
}

module.exports = User; 