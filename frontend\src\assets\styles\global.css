/* 全局样式 - 统一界面设计和修复适配问题 */

:root {
  /* 主色调 */
  --primary-color: #1976d2;
  --primary-light: #63a4ff;
  --primary-dark: #004ba0;
  
  /* 辅助色调 */
  --secondary-color: #ff5252;
  --secondary-light: #ff867f;
  --secondary-dark: #c50e29;
  
  /* 中性色调 */
  --background-color: #f5f7fa;
  --surface-color: #ffffff;
  --text-primary: #303133;
  --text-secondary: #606266;
  --text-hint: #909399;
  --text-disabled: #c0c4cc;
  
  /* 功能色 */
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --info-color: #2196f3;
  
  /* 边框和阴影 */
  --border-color: #e4e7ed;
  --border-radius: 12px;
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
  
  /* 间距 */
  --spacing-xs: 8px;
  --spacing-sm: 12px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* 过渡效果 */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* 字体 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
      'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
      'Noto Color Emoji';
}

/* 全局重置和基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: var(--font-family);
  font-size: 16px;
  color: var(--text-primary);
  background-color: var(--background-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

body {
  overflow-y: auto;
  margin: 0;
  padding: 0;
}

#app {
  height: 100%;
  display: flex;
  width: 100%;
  position: relative;
  z-index: 0; /* 确保在最底层 */
  max-width: 100vw;
  overflow-x: hidden;
}

/* 主体内容区域 */
.main-content {
  flex: 1;
  min-height: 100vh;
  transition: all 0.3s ease;
  margin-left: 240px;
  width: calc(100% - 240px);
  overflow-x: hidden;
  position: relative;
  background-color: var(--background-color);
  padding: var(--spacing-md);
  box-sizing: border-box;
  z-index: 50;
}

.main-content.with-sidebar {
  margin-left: 240px;
  width: calc(100% - 240px);
  padding-right: var(--spacing-md);
  padding-left: var(--spacing-md);
  box-shadow: none;
}

/* 统一卡片样式 */
.app-card {
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.app-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* 统一页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.page-header h1 {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.page-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* 统一面板样式 */
.panel {
  background-color: var(--surface-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  width: 100%;
  box-sizing: border-box;
}

.panel-header {
  margin-bottom: var(--spacing-md);
}

.panel-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.panel-content {
  margin-bottom: var(--spacing-md);
}

/* 统一表格样式 */
.app-table {
  width: 100%;
  border-collapse: collapse;
}

.app-table th {
  padding: var(--spacing-sm);
  text-align: left;
  background-color: rgba(0, 0, 0, 0.02);
  font-weight: 600;
  color: var(--text-secondary);
}

.app-table td {
  padding: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.app-table tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* 统一进度条样式 */
.progress-bar {
  height: 8px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  overflow: hidden;
  margin: var(--spacing-sm) 0;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width var(--transition-normal);
}

/* 统一状态徽章样式 */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 50px;
  font-size: 14px;
  font-weight: 500;
}

.status-badge.success {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
}

.status-badge.warning {
  background-color: rgba(255, 152, 0, 0.1);
  color: var(--warning-color);
}

.status-badge.error {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--error-color);
}

.status-badge.info {
  background-color: rgba(33, 150, 243, 0.1);
  color: var(--info-color);
}

/* 统一栅格系统 */
.grid {
  display: grid;
  grid-gap: var(--spacing-md);
  width: 100%;
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* 响应式断点 */
@media (max-width: 1200px) {
  .main-content {
    padding: var(--spacing-md);
  }
  
  .grid-4 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .grid-3, 
  .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: var(--spacing-sm);
    margin-left: 0;
  }
  
  .main-content.expanded {
    margin-left: 0;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
  
  .page-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }
  
  .panel {
    padding: var(--spacing-sm);
  }
}

/* 仪表板容器样式 */
.dashboard-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0;
  margin: 0;
  overflow-x: hidden;
  position: relative;
  z-index: 20;
  background-color: transparent;
}

/* 仪表板卡片布局 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  width: 100%;
  margin-bottom: 24px;
  overflow: visible;
}

/* 面板和卡片组件样式优化 */
.panel, .dashboard-card, .stat-card, .el-card {
  background-color: var(--surface-color) !important;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-md);
  transition: all var(--transition-fast);
  height: 100%;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}

/* 活动任务区域样式 */
.active-tasks-panel {
  margin-top: 20px;
  width: 100%;
}

/* 通知与待办区域样式 */
.notifications-panel {
  margin-top: 20px;
  width: 100%;
}

/* 确保内容在面板内正确显示 */
.panel-content {
  width: 100%;
  overflow-x: auto;
}

/* 增强表格在面板中的显示 */
.app-table {
  width: 100%;
  min-width: 600px; /* 确保表格内容不会过度压缩 */
  border-collapse: collapse;
}

/* 图表区域样式 */
.dashboard-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 20px;
  width: 100%;
  margin: 20px 0;
}

/* 修复标题边距 */
.panel-header h2, .panel-header h3 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
}

/* 确保进度条显示正确 */
.progress-container {
  width: 100%;
  margin: 8px 0;
  position: relative;
}

.progress-bar {
  height: 8px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  overflow: hidden;
  width: 100%;
}

/* 统一加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xl);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 修复边缘颜色分界问题 */
.sidebar + .main-content {
  position: relative;
  z-index: 50;
}

.main-layout {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 页面内容容器 */
.server-home, .client-home, .model-management, .task-management, 
.my-applications, .available-tasks, .client-transactions, .server-transactions {
  width: 100%;
  margin: 0; 
  padding: var(--spacing-md);
  box-sizing: border-box;
  overflow-x: visible;
  position: relative;
  z-index: 10;
  background-color: transparent;
}

/* 主要内容区块 */
.dashboard-main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
  width: 100%;
  position: relative;
  z-index: 10; /* 添加z-index确保内容显示在上层 */
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 修复红框右侧的滚动条问题 */
.notification-body {
  max-height: calc(100vh - 110px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* 调整侧边栏宽度 */
.sidebar {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  width: 240px;
  transition: width var(--transition-normal);
  box-sizing: border-box;
  z-index: 40; /* 降低z-index，确保不会覆盖移动后的内容 */
  background-color: var(--background-color); /* 确保侧边栏有自己的背景色 */
}

.sidebar.collapsed {
  width: 80px;
}

/* 修复右上角红框问题 */
.dashboard-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  overflow: hidden;
  max-width: 200px;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 150px;
}

/* 通知面板固定尺寸 */
.notifications-container {
  max-width: 360px;
  max-height: 80vh;
  overflow: hidden;
}

/* 面包屑导航样式 */
.breadcrumb-container {
  width: 100%;
  margin: 0;
  padding: var(--spacing-md) 0;
  background-color: transparent;
  box-sizing: border-box;
}

/* 内容区域清除样式 - 确保没有任何干扰背景 */
.content-area-clean {
  background-color: transparent !important;
  box-shadow: none !important;
  border: none !important;
  position: relative;
  z-index: 200;
  width: 100%;
  height: 100%;
  max-width: 100%;
  padding: 0;
  margin: 0;
} 