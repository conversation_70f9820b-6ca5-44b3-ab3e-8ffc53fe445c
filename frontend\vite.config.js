import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import { nodePolyfills } from 'vite-plugin-node-polyfills'
import wasm from 'vite-plugin-wasm'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), 'VITE_')

  return {
    plugins: [
      vue(),
      nodePolyfills({
        protocolImports: true,
        globals: {
          Buffer: true,
          process: true,
          crypto: true,
          Web3: true
        },
        exclude: ['fs']
      }),
      wasm()
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@contracts': path.resolve(__dirname, '../blockchain/build/contracts'),
        '@blockchain': path.resolve(__dirname, '../blockchain')
      }
    },
    define: {
      __APP_ENV__: JSON.stringify(env)
    },
    optimizeDeps: {
      include: ['tiny-secp256k1', 'ecpair']
    }
  }
})
