-- 在任务表中添加支付相关字段
ALTER TABLE tasks
ADD COLUMN reward DECIMAL(10,2) DEFAULT 0.00 COMMENT '任务总奖励',
ADD COLUMN reward_distribution VARCHAR(255) DEFAULT 'equal' COMMENT '奖励分配方式: equal, contribution, data',
ADD COLUMN payment_currency VARCHAR(10) DEFAULT 'USD' COMMENT '支付币种: USD, CNY',
ADD COLUMN installment_payment BOOLEAN DEFAULT TRUE COMMENT '是否分期付款',
ADD COLUMN first_payment_rate INTEGER DEFAULT 40 COMMENT '首期付款比例',
ADD COLUMN platform_fee_rate INTEGER DEFAULT 10 COMMENT '平台服务费率',
ADD COLUMN payment_status VARCHAR(50) DEFAULT 'pending' COMMENT '支付状态: pending, first_payment_completed, pending_second_payment, second_payment_completed, rewards_distributed',
ADD COLUMN payment_method VARCHAR(50) DEFAULT NULL COMMENT '支付方式: balance, credit_card, alipay, wechat, paypal',
ADD COLUMN wallet_type VARCHAR(50) DEFAULT NULL COMMENT '区块链钱包类型',
ADD COLUMN blockchain_second_payment_tx VARCHAR(255) DEFAULT NULL COMMENT '区块链二期付款交易哈希'; 