<!-- 服务端任务管理页面 -->
<template>
  <div class="task-management">
    <div class="page-header">
      <h1>训练任务管理</h1>
      <el-button type="primary" @click="createTask" class="create-btn">
        <i class="fas fa-plus"></i> 创建任务
      </el-button>
    </div>
    
    <Breadcrumb />
    
    <!-- 任务过滤和搜索 -->
    <div class="filter-section">
      <el-radio-group v-model="statusFilter" @change="filterTasks" class="status-filter">
        <el-radio-button value="all">全部</el-radio-button>
        <el-radio-button value="draft">草稿</el-radio-button>
        <el-radio-button value="published">已发布</el-radio-button>
        <el-radio-button value="in_progress">进行中</el-radio-button>
        <el-radio-button value="completed">已完成</el-radio-button>
        <el-radio-button value="cancelled">已取消</el-radio-button>
      </el-radio-group>
      
      <div class="search-box">
        <el-input v-model="searchQuery" placeholder="搜索任务名称或描述" clearable @input="filterTasks">
          <template #prefix>
            <i class="fas fa-search"></i>
          </template>
        </el-input>
      </div>
    </div>
    
    <!-- 任务列表 -->
    <el-table
      :data="filteredTasks"
      style="width: 100%"
      v-loading="loading"
      border
      row-key="id"
    >
      <el-table-column type="index" width="50" />
      
      <el-table-column prop="name" label="任务名称" min-width="180">
        <template #default="scope">
          <div class="task-name">
            <router-link :to="`/server/tasks/${scope.row.id}`">{{ scope.row.name }}</router-link>
          </div>
          <div class="task-time">
            创建时间: {{ formatDate(scope.row.createdAt) }}
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="120">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="applications" label="申请数" width="100">
        <template #default="scope">
          {{ Array.isArray(scope.row.applications) ? scope.row.applications.length : (scope.row.applications || 0) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="participants" label="参与者" align="center" width="90">
        <template #default="scope">
          {{ scope.row.approvedParticipants || 0 }}/{{ scope.row.requiredParticipants }}
        </template>
      </el-table-column>
      
      <el-table-column prop="rounds" label="训练轮次" width="140">
        <template #default="scope">
          {{ scope.row.currentRound || 0 }}/{{ scope.row.totalRounds || 5 }}
        </template>
      </el-table-column>
      
      <el-table-column prop="accuracy" label="准确率" width="100">
        <template #default="scope">
          <template v-if="scope.row.accuracy">
            {{ parseFloat(scope.row.accuracy).toFixed(4) }}
          </template>
          <template v-else>
            -
          </template>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <!-- 草稿状态 -->
          <template v-if="scope.row.status === 'draft'">
            <el-button size="small" @click="editTask(scope.row)" type="primary" plain>编辑</el-button>
            <el-button size="small" @click="publishTask(scope.row)" type="success" plain>发布</el-button>
            <el-button size="small" @click="deleteTask(scope.row)" type="danger" plain>删除</el-button>
          </template>
          
          <!-- 已发布状态 -->
          <template v-else-if="scope.row.status === 'published'">
            <el-button size="small" @click="reviewApplications(scope.row)" type="primary" plain>审核申请</el-button>
            <el-button size="small" @click="startTraining(scope.row)" type="success" plain>开始训练</el-button>
            <el-dropdown trigger="click" @command="(command) => handleCommand(command, scope.row)">
              <el-button size="small" type="info" plain>
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="cancel">取消任务</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除任务</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          
          <!-- 进行中状态 -->
          <template v-else-if="scope.row.status === 'in_progress'">
            <el-button size="small" @click="monitorTask(scope.row)" type="primary" plain>监控</el-button>
            <el-dropdown trigger="click" @command="(command) => handleCommand(command, scope.row)">
              <el-button size="small" type="info" plain>
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="stop">停止训练</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除任务</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          
          <!-- 已完成状态 -->
          <template v-else-if="scope.row.status === 'completed'">
            <el-button size="small" @click="viewResults(scope.row)" type="primary" plain>查看结果</el-button>
            <el-dropdown trigger="click" @command="(command) => handleCommand(command, scope.row)">
              <el-button size="small" type="info" plain>
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="download">下载模型</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除任务</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          
          <!-- 已取消状态 -->
          <template v-else-if="scope.row.status === 'cancelled'">
            <el-button size="small" @click="restoreTask(scope.row)" type="primary" plain>恢复</el-button>
            <el-button size="small" @click="deleteTask(scope.row)" type="danger" plain>删除</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalTasks"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import Breadcrumb from '@/components/Breadcrumb.vue';
import { TaskAPI, ParticipantAPI } from '@/services/api';
import axios from 'axios';
import { dbApi } from '@/services/api'; // 导入dbApi实例
import { apiService } from '@/services/api'; // 导入apiService实例

export default {
  name: 'TaskManagement',
  components: {
    Breadcrumb
  },
  setup() {
    const router = useRouter();
    const loading = ref(false);
    const tasks = ref([]);
    const statusFilter = ref('all');
    const searchQuery = ref('');
    const currentPage = ref(1);
    const pageSize = ref(10);
    
    // 加载任务列表
    const loadTasks = async () => {
      loading.value = true;
      try {
        console.log('开始加载任务列表...');
        
        // 调用数据库API获取所有任务
        const allTasks = await TaskAPI.getAll();
        console.log('API返回的原始任务数据:', allTasks);
        
        // 如果API直接返回数组，处理结果
        if (Array.isArray(allTasks)) {
          // 处理状态映射 (数据库状态 -> 前端状态)
          const statusMap = {
            'pending': 'published',  // 数据库中的pending表示前端中的published(已发布)
            'in_progress': 'in_progress',
            'completed': 'completed',
            'stopped': 'cancelled',
            'failed': 'cancelled'
          };
          
          // 处理字段名称差异
          tasks.value = allTasks.map(task => {
            return {
              id: task.id,
              name: task.name,
              description: task.description,
              // 将数据库状态映射到前端状态
              status: statusMap[task.status] || 'draft',
              dataType: task.data_type,
              currentRound: task.current_round || 0,
              totalRounds: task.total_rounds,
              requiredParticipants: task.required_participants,
              participants: [], // 默认空参与者列表
              applications: 0,  // 默认申请数
              accuracy: null,   // 准确率
              learningRate: task.learning_rate,
              batchSize: task.batch_size,
              timeout: task.timeout,
              createdAt: task.created_at,
              updatedAt: task.updated_at,
              startTime: task.start_time,
              endTime: task.end_time
            };
          });
          
          // 异步加载每个任务的参与者数量
          for (const task of tasks.value) {
            try {
              // 获取每个任务的参与者
              const participants = await ParticipantAPI.getByTaskId(task.id);
              // 存储所有参与者
              task.participants = participants;
              // 存储申请数量
              task.applications = participants.length;
              // 计算已批准的参与者（非registered和非rejected状态）
              task.approvedParticipants = participants.filter(p => 
                p.status !== 'registered' && p.status !== 'rejected'
              ).length;
            } catch (err) {
              console.error(`获取任务 ${task.id} 的参与者失败:`, err);
              task.approvedParticipants = 0;
            }
          }
          
          console.log('处理后的任务列表:', tasks.value);
        } else {
          console.warn('API未返回预期的任务列表格式:', allTasks);
          ElMessage.warning('任务数据格式不正确，请联系管理员');
          tasks.value = [];
        }
      } catch (error) {
        console.error('加载任务列表失败:', error);
        if (error.response) {
          console.error('错误响应:', error.response.status, error.response.data);
        }
        ElMessage.error('加载任务列表失败，请重试');
        tasks.value = [];
      } finally {
        loading.value = false;
      }
    };
    
    // 格式化日期
    const formatDate = (date) => {
      if (!date) return '';
      if (typeof date === 'string') {
        date = new Date(date);
      }
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    };
    
    // 获取状态标签类型
    const getStatusTagType = (status) => {
      const types = {
        draft: 'info',
        published: 'warning',
        in_progress: 'success',
        completed: 'primary',
        cancelled: 'danger'
      };
      return types[status] || 'info';
    };
    
    // 获取状态文本
    const getStatusText = (status) => {
      const texts = {
        draft: '草稿',
        published: '已发布',
        in_progress: '进行中',
        completed: '已完成',
        cancelled: '已取消'
      };
      return texts[status] || status;
    };
    
    // 计算过滤后的任务
    const filteredTasks = computed(() => {
      let result = tasks.value;
      
      // 状态过滤
      if (statusFilter.value !== 'all') {
        result = result.filter(task => task.status === statusFilter.value);
      }
      
      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(task => 
          task.name.toLowerCase().includes(query) || 
          (task.description && task.description.toLowerCase().includes(query))
        );
      }
      
      return result;
    });
    
    // 计算总任务数
    const totalTasks = computed(() => filteredTasks.value.length);
    
    // 过滤任务方法
    const filterTasks = () => {
      currentPage.value = 1;  // 重置到第一页
    };
    
    // 页面大小改变
    const handleSizeChange = (size) => {
      pageSize.value = size;
    };
    
    // 当前页改变
    const handleCurrentChange = (page) => {
      currentPage.value = page;
    };
    
    // 创建任务
    const createTask = () => {
      router.push('/server/tasks/create');
    };
    
    // 编辑任务
    const editTask = (task) => {
      router.push(`/server/tasks/${task.id}?edit=true`);
    };
    
    // 发布任务
    const publishTask = async (task) => {
      try {
        await ElMessageBox.confirm(
          '确定要发布此任务吗？发布后将对客户端用户可见，且无法回到草稿状态。',
          '发布确认',
          {
            confirmButtonText: '确定发布',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );
        
        // 更新任务状态
        task.status = 'published';
        
        // 使用TaskAPI更新任务
        await TaskAPI.update(task.id, { status: 'published' });
        
        ElMessage.success(`任务 ${task.name} 已成功发布`);
        
        // 刷新任务列表
        await loadTasks();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('发布任务失败:', error);
          ElMessage.error('发布任务失败，请重试');
        }
      }
    };
    
    // 审核申请
    const reviewApplications = async (task) => {
      router.push(`/server/tasks/${task.id}/applications`);
    };
    
    // 开始训练
    const startTraining = async (task) => {
      try {
        // 检查参与者数量是否满足要求
        const participants = await ParticipantAPI.getByTaskId(task.id);
        // 过滤出已批准的参与者（状态不是registered和rejected的）
        const approvedParticipants = participants.filter(p => 
          p.status !== 'registered' && p.status !== 'rejected'
        );
        const currentParticipants = approvedParticipants.length;
        const requiredParticipants = task.requiredParticipants || 2;
        
        if (currentParticipants < requiredParticipants) {
          ElMessageBox.alert(
            `当前已批准的参与者数量（${currentParticipants}）不足，需要至少 ${requiredParticipants} 名已批准的参与者才能开始训练。`,
            '参与者不足',
            {
              confirmButtonText: '确定',
              type: 'warning',
            }
          );
          return;
        }
        
        await ElMessageBox.confirm(
          `确定要开始训练此任务吗？开始后系统将自动启动所有批准的客户端节点（${currentParticipants}个），无需手动运行命令。`,
          '开始训练',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info',
          }
        );
        
        // 显示启动中的提示
        ElMessage({
          type: 'info',
          message: '正在启动训练并初始化客户端节点，这可能需要一些时间...',
          duration: 0,
          showClose: true,
        });
        
        // 调用专门的启动训练API
        console.log(`调用API启动任务 ${task.id} 的训练...`);
        try {
          console.log(`正在发送POST请求到 http://localhost:3001/api/tasks/${task.id}/start`);
          // 使用dbApi直接调用API而不是TaskAPI.update
          const response = await dbApi.post(
            `/api/tasks/${task.id}/start`,
            {},
            {
              headers: {
                'Content-Type': 'application/json'
              }
            }
          );
          
          console.log('启动训练API响应:', response.data);
          
          // 尝试通知联邦学习服务器开始训练
          try {
            console.log('正在通知联邦学习服务器开始训练...');
            const flowerResponse = await apiService.startTraining({
              taskId: task.id,
              name: task.name,
              minParticipants: task.requiredParticipants,
              rounds: task.totalRounds,
              timeout: task.timeout || 60
            });
            console.log('联邦学习服务器响应:', flowerResponse);
            
            // 关闭所有提示
            ElMessage.closeAll();
            
            ElMessage.success(`任务 ${task.name} 训练已成功启动，并且所有客户端节点已自动初始化`);
          } catch (flError) {
            // 关闭所有提示
            ElMessage.closeAll();
            
            console.error('通知联邦学习服务器失败，但数据库已更新:', flError);
            ElMessage.warning('任务已启动，但联邦学习服务器通信失败，训练将延迟开始');
          }
        } catch (apiError) {
          // 关闭所有提示
          ElMessage.closeAll();
          
          console.error('启动训练API调用失败:', apiError);
          
          if (apiError.response) {
            const errorMsg = apiError.response.data.error || '未知错误';
            ElMessage.error(`启动训练失败: ${errorMsg}`);
          } else {
            ElMessage.error('启动训练失败，服务器连接问题');
          }
          return;
        }
        
        // 刷新任务列表
        await loadTasks();
      } catch (error) {
        // 关闭所有提示
        ElMessage.closeAll();
        
        if (error !== 'cancel') {
          console.error('开始训练失败:', error);
          ElMessage.error('开始训练失败，请重试');
        }
      }
    };
    
    // 监控任务
    const monitorTask = (task) => {
      router.push(`/server/training?task=${task.id}`);
    };
    
    // 停止训练
    const stopTraining = async (task) => {
      try {
        await ElMessageBox.confirm(
          '确定要停止此任务的训练吗？停止后可以选择完成任务或继续训练。',
          '停止训练',
          {
            confirmButtonText: '停止训练',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );
        
        // 更新任务状态
        task.status = 'completed';
        
        // 使用TaskAPI更新任务
        await TaskAPI.update(task.id, { status: 'completed' });
        
        ElMessage.success(`任务 ${task.name} 的训练已停止`);
        
        // 刷新任务列表
        await loadTasks();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('停止训练失败:', error);
          ElMessage.error('停止训练失败，请重试');
        }
      }
    };
    
    // 查看结果
    const viewResults = (task) => {
      router.push(`/server/training?task=${task.id}`);
    };
    
    // 下载模型
    const downloadModel = (task) => {
      ElMessage.success(`模型 ${task.name} 正在下载中...`);
    };
    
    // 取消任务
    const cancelTask = async (task) => {
      try {
        await ElMessageBox.confirm(
          '确定要取消此任务吗？取消后可以恢复，但所有进度将丢失。',
          '取消任务',
          {
            confirmButtonText: '确定取消',
            cancelButtonText: '返回',
            type: 'danger',
          }
        );
        
        // 使用TaskAPI更新任务
        await TaskAPI.update(task.id, { status: 'cancelled' });
        
        ElMessage.success(`任务 ${task.name} 已取消`);
        
        // 刷新任务列表
        await loadTasks();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消任务失败:', error);
          ElMessage.error('取消任务失败，请重试');
        }
      }
    };
    
    // 恢复任务
    const restoreTask = async (task) => {
      try {
        await ElMessageBox.confirm(
          '确定要恢复此任务到草稿状态吗？',
          '恢复任务',
          {
            confirmButtonText: '恢复任务',
            cancelButtonText: '取消',
            type: 'info',
          }
        );
        
        // 使用TaskAPI更新任务
        await TaskAPI.update(task.id, { status: 'draft' });
        
        ElMessage.success(`任务 ${task.name} 已恢复为草稿状态`);
        
        // 刷新任务列表
        await loadTasks();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('恢复任务失败:', error);
          ElMessage.error('恢复任务失败，请重试');
        }
      }
    };
    
    // 删除任务
    const deleteTask = async (task) => {
      try {
        // 针对不同任务状态的确认消息
        let confirmMessage = '确定要删除此任务吗？删除后无法恢复。';
        let confirmTitle = '删除任务';
        let confirmType = 'danger';
        
        if (task.status === 'in_progress') {
          confirmMessage = '任务正在进行中，删除将立即停止训练过程。确定要删除吗？';
        } else if (task.status === 'completed') {
          confirmMessage = '此任务已完成，删除将同时移除训练结果。确定要删除吗？';
        }
        
        await ElMessageBox.confirm(
          confirmMessage,
          confirmTitle,
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: confirmType,
          }
        );
        
        // 如果是进行中的任务，需要先更新状态
        if (task.status === 'in_progress') {
          try {
            // 先更新状态为已停止
            await TaskAPI.update(task.id, { 
              status: 'stopped'
            });
            console.log(`已停止任务 ${task.id} 的训练`);
          } catch (stopError) {
            console.error('停止训练失败:', stopError);
            // 继续删除操作，即使停止训练失败
          }
        }
        
        // 使用TaskAPI删除任务
        await TaskAPI.delete(task.id);
        
        ElMessage.success(`任务 ${task.name} 已删除`);
        
        // 重新加载任务列表
        await loadTasks();
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除任务失败:', error);
          ElMessage.error('删除任务失败，请重试');
        }
      }
    };
    
    // 处理下拉菜单命令
    const handleCommand = (command, task) => {
      console.log(`执行命令: ${command}，任务ID: ${task.id}`);
      switch (command) {
        case 'cancel':
          cancelTask(task);
          break;
        case 'stop':
          stopTraining(task);
          break;
        case 'download':
          downloadModel(task);
          break;
        case 'delete':
          deleteTask(task);
          break;
        default:
          console.log(`未知命令: ${command}`);
      }
    };
    
    // 页面加载时获取任务列表
    onMounted(async () => {
      await loadTasks();
    });
    
    return {
      loading,
      tasks,
      statusFilter,
      searchQuery,
      currentPage,
      pageSize,
      filteredTasks,
      totalTasks,
      formatDate,
      getStatusTagType,
      getStatusText,
      filterTasks,
      handleSizeChange,
      handleCurrentChange,
      createTask,
      editTask,
      publishTask,
      reviewApplications,
      startTraining,
      monitorTask,
      stopTraining,
      viewResults,
      downloadModel,
      cancelTask,
      restoreTask,
      deleteTask,
      handleCommand
    };
  }
};
</script>

<style scoped>
.task-management {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.filter-section {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  align-items: center;
}

.search-box {
  width: 300px;
}

.task-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.task-name a {
  color: #1890ff;
  text-decoration: none;
}

.task-name a:hover {
  text-decoration: underline;
}

.task-time {
  font-size: 12px;
  color: #999;
}

.pagination {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}
</style> 