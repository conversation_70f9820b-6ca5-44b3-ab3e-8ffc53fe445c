/**
 * 模型数据模型
 */
const { query } = require('../db/init');

class Model {
  /**
   * 获取所有模型
   * @returns {Promise<Array>} 模型列表
   */
  static async getAll() {
    try {
      return await query('SELECT * FROM models ORDER BY created_at DESC');
    } catch (error) {
      console.error('获取所有模型失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取模型
   * @param {number} id 模型ID
   * @returns {Promise<Object>} 模型详情
   */
  static async getById(id) {
    try {
      const models = await query('SELECT * FROM models WHERE id = ?', [id]);
      return models.length > 0 ? models[0] : null;
    } catch (error) {
      console.error(`获取模型(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 根据任务ID获取模型
   * @param {number} taskId 任务ID
   * @returns {Promise<Array>} 模型列表
   */
  static async getByTaskId(taskId) {
    try {
      return await query('SELECT * FROM models WHERE task_id = ? ORDER BY created_at DESC', [taskId]);
    } catch (error) {
      console.error(`获取任务(ID: ${taskId})的模型失败:`, error);
      throw error;
    }
  }

  /**
   * 创建模型
   * @param {Object} modelData 模型数据
   * @returns {Promise<Object>} 创建的模型
   */
  static async create(modelData) {
    try {
      const {
        name, type, version, description, 
        task_id, task_name, status, size,
        performance, is_latest, from_training,
        blockchain_tx_hash, file_path
      } = modelData;

      console.log('开始创建模型，接收到的数据:', JSON.stringify(modelData, null, 2));
      
      // 处理性能字段 - 确保转换为JSON格式
      let performanceJson;
      if (typeof performance === 'object') {
        performanceJson = JSON.stringify(performance);
      } else if (typeof performance === 'number' || typeof performance === 'string') {
        // 如果是数字或字符串，创建一个包含accuracy的对象
        const accuracyValue = typeof performance === 'string' ? parseFloat(performance) : performance;
        performanceJson = JSON.stringify({ accuracy: accuracyValue });
      } else {
        // 默认性能值
        performanceJson = JSON.stringify({ accuracy: 0.9 });
      }
      
      console.log(`处理后的性能数据: ${performanceJson}`);
      
      // 确保task_id是整数
      const taskId = task_id ? (typeof task_id === 'string' ? parseInt(task_id, 10) : task_id) : null;
      
      // 处理file_path，确保格式正确
      let finalFilePath = file_path;
      if (!finalFilePath) {
        // 使用默认路径
        finalFilePath = taskId ? `./models/model_${taskId}.h5` : './models/model_default.h5';
      }
      
      console.log(`处理后的文件路径: ${finalFilePath}`);

      // 如果是设置为最新版本，先将其他同类型模型的最新版本标志设为false
      if (is_latest) {
        await query(
          'UPDATE models SET is_latest = 0 WHERE type = ? AND name = ?',
          [type, name]
        );
      }

      const result = await query(
        `INSERT INTO models 
         (name, type, version, description, task_id, task_name, 
          status, size, performance, is_latest, from_training, 
          blockchain_tx_hash, file_path) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          name, type, version, description, 
          taskId, task_name, status, size,
          performanceJson, is_latest ? 1 : 0, from_training ? 1 : 0,
          blockchain_tx_hash, finalFilePath
        ]
      );
      
      console.log(`模型创建成功，ID: ${result.insertId}`);

      return this.getById(result.insertId);
    } catch (error) {
      console.error('创建模型失败:', error);
      throw error;
    }
  }

  /**
   * 更新模型
   * @param {number} id 模型ID
   * @param {Object} modelData 更新的模型数据
   * @returns {Promise<boolean>} 更新结果
   */
  static async update(id, modelData) {
    try {
      // 确保ID是整数
      id = parseInt(id, 10);
      
      console.log(`执行模型更新 - ID: ${id}, 数据:`, JSON.stringify(modelData, null, 2));
      
      // 验证模型是否存在
      const existingModel = await this.getById(id);
      if (!existingModel) {
        console.error(`无法更新 - 模型ID ${id} 不存在`);
        return false;
      }
      
      // 移除不能直接更新的字段
      const { 
        created_at, updated_at, id: modelId, 
        is_latest, ...updateData 
      } = modelData;

      // 如果需要设置为最新版本
      if (is_latest) {
        await query(
          'UPDATE models SET is_latest = 0 WHERE type = ? AND name = ?',
          [existingModel.type, existingModel.name]
        );
        updateData.is_latest = 1;
      }

      // 构建更新SQL
      const keys = Object.keys(updateData);
      const values = Object.values(updateData);

      if (keys.length === 0) {
        console.log('无可更新字段');
        return false; // 没有可更新的数据
      }

      // 添加更新时间
      const now = new Date();
      keys.push('updated_at');
      values.push(now);

      console.log(`构建更新SQL: 字段=${keys.join(',')}`);
      
      const sql = `UPDATE models SET ${keys.map(key => `${key} = ?`).join(', ')} WHERE id = ?`;
      const params = [...values, id];

      console.log(`执行SQL: ${sql}`);
      console.log('参数:', params);
      
      const result = await query(sql, params);
      console.log(`更新结果: affectedRows=${result.affectedRows}`);
      
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`更新模型(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 发布模型
   * @param {number} id 模型ID
   * @returns {Promise<boolean>} 更新结果
   */
  static async publish(id) {
    try {
      const now = new Date();
      const result = await query(
        'UPDATE models SET status = ?, updated_at = ? WHERE id = ?',
        ['published', now, id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`发布模型(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 归档模型
   * @param {number} id 模型ID
   * @returns {Promise<boolean>} 更新结果
   */
  static async archive(id) {
    try {
      const now = new Date();
      const result = await query(
        'UPDATE models SET status = ?, updated_at = ? WHERE id = ?',
        ['archived', now, id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`归档模型(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 恢复模型为草稿状态
   * @param {number} id 模型ID
   * @returns {Promise<boolean>} 更新结果
   */
  static async restoreToDraft(id) {
    try {
      const now = new Date();
      const result = await query(
        'UPDATE models SET status = ?, updated_at = ? WHERE id = ?',
        ['draft', now, id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`恢复模型(ID: ${id})为草稿状态失败:`, error);
      throw error;
    }
  }

  /**
   * 增加模型下载次数
   * @param {number} id 模型ID
   * @returns {Promise<boolean>} 更新结果
   */
  static async incrementDownloads(id) {
    try {
      const result = await query(
        'UPDATE models SET downloads = downloads + 1 WHERE id = ?',
        [id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`增加模型(ID: ${id})下载次数失败:`, error);
      throw error;
    }
  }

  /**
   * 删除模型
   * @param {number} id 模型ID
   * @returns {Promise<boolean>} 删除结果
   */
  static async delete(id) {
    try {
      const result = await query('DELETE FROM models WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`删除模型(ID: ${id})失败:`, error);
      throw error;
    }
  }

  /**
   * 获取统计数据
   * @returns {Promise<Object>} 统计数据
   */
  static async getStatistics() {
    try {
      const totalModels = await query('SELECT COUNT(*) as count FROM models');
      const activeModels = await query('SELECT COUNT(*) as count FROM models WHERE status != ?', ['archived']);
      const publishedModels = await query('SELECT COUNT(*) as count FROM models WHERE status = ?', ['published']);
      const trainingModels = await query('SELECT COUNT(*) as count FROM models WHERE from_training = 1');
      const downloadsSum = await query('SELECT SUM(downloads) as sum FROM models');

      return {
        totalModels: totalModels[0].count || 0,
        activeModels: activeModels[0].count || 0,
        publishedModels: publishedModels[0].count || 0,
        trainingModels: trainingModels[0].count || 0,
        modelDownloads: downloadsSum[0].sum || 0
      };
    } catch (error) {
      console.error('获取模型统计数据失败:', error);
      throw error;
    }
  }
}

module.exports = Model; 